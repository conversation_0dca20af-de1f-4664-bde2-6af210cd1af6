# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Build System & Commands

This is a Maven multi-module Java 17 project with the following common commands:

```bash
# Build entire project
mvn clean install

# Run application locally
mvn spring-boot:run -Dspring.profiles.active=local

# Run code quality checks (PMD, Jacoco)
mvn clean compile -Pcq

# Build Docker image
mvn clean install -Pdocker

# Run tests
mvn test

# Run single test
mvn test -Dtest=ClassName#methodName
```

## Project Architecture

### Multi-Module Structure
- **telecom-parent**: Dependency management and build configuration
- **telecom-api**: DTOs, domain models, and API contracts
- **telecom-core**: Business logic, services, and data access
- **telecom-service**: REST controllers and main Spring Boot application

### Technology Stack
- Java 17
- Spring Boot with Jetty (not Tomcat)
- PostgreSQL + Redis/Redisson
- MyBatis Plus for ORM
- Retrofit for HTTP clients
- Apache POI for document processing

### Main Application
Entry point: `telecom-service/src/main/java/com/patsnap/Application.java`
Context path: `/telecom`

### Business Domains
The application serves two main domains:

1. **Analytics** (`com.patsnap.analytics`): Telecom data retrieval and processing
   - Field value conversion and expansion
   - Parallel data processing via `DistributeService`
   - External API integration (SearchApi, PatentApi)

2. **Drafting** (`com.patsnap.drafting`): AI-powered patent drafting services
   - AI specification generation
   - Patent disclosure creation
   - Translation and image processing
   - Technical report generation

### Architecture Patterns
- **Layered Architecture**: API → Core → Service
- **Domain-Driven Design**: Clear domain boundaries
- **Parallel Processing**: Field processors run in parallel for performance
- **External Service Integration**: Retrofit-based clients with proper error handling

## Development Guidelines

### Code Organization
- Follow the established package structure in README.md
- Single responsibility principle for classes and methods  
- Proper exception handling with defined error codes in `ExceptionDefinition`
- No exception swallowing unless for explicit logical branching

### Profiles Available
- Environment: `local`, `ci`, `qa`, `stage`, `aws_eu`, `aws_us`, `tx_cn`
- Special: `docker` (containerization), `cq` (code quality)

### External Dependencies
- **SearchApi**: Field search functionality
- **PatentApi**: Patent data access
- **AI Services**: GPT, specification drafting
- **Storage**: S3/COS for file storage
- **Messaging**: SQS for AI task processing

### Testing
- JUnit 5 + Mockito 5.3.1
- Spring Boot Test for integration tests
- Test resources in each module's `src/test/resources`

# Telecom Service

本项目为 通信后端服务，提供通信数据获取，对于数据库的通信数据进行转换，高亮等操作

## 项目依赖
- JDK17
- Maven 3+
- Redis
- PostgreSQL


## 项目地址
http://git.patsnap.com/core-product/backend/s-analytics-telecom/pipelines


## 依赖服务
- SearchApi : https://confluence.zhihuiya.com/display/~liujia/%5BPBI-7262%5DTDoc+-+Field+Search#id-[PBI7262]TDocFieldSearch-2.2searchapi
- PatentApi : http://s-platform-patent-api.patsnap.ci/identity/swagger-ui.html

## 核心逻辑
### 取数和字段转换逻辑
 ![img.png](img.png)
  


-------

## 代码规约
### 分层规约
1. 项目遵循以下分层
```shell
├
├── README.md
├── errorcode.md
├── pom.xml
├── telecom-api
│   ├── pom.xml
│   └── src
│       ├── main
│       │   ├── java
│       │   │    └── com.patsnap.analytics  
│       │   │        ├── query              -- 查询相关类DTO
│       │   │        ├── tdoc               -- 通信数据获取DTO
│       │   └── resources
│       └── test
│           ├── java
│           └── resources
├── telecom-core
│   ├── pom.xml
│   └── src
│       ├── main
│       │   └── java
│       │   │    └── com.patsnap.analytics 
│       │   │        ├── constant              -- 常量
│       │   │        ├── service               -- 服务接口
│       │   │            └── impl                 -- 接口实现
│       │   │        ├── model                 -- 业务处理模型（BO模型）
│       │   │        ├── manager               -- 业务处理类
│       │   │        │    └── query               -- 数据查询
│       │   │        │    └── view               -- 数据展示
│       │   │        ├── frame                 -- 基础类定义
│       │   │        │   ├── assembly              -- 参数组装
│       │   │        │   ├── validator             -- 校验
│       │   │        │   └── gateway               -- 网关、防腐层
│       │   │        ├── exception            -- 异常
│       │   │        ├── utils                -- 工具类 
│       │   │        ├── repository           -- 存储层
│       │   │        │   ├── entity              -- 实体类
│       │   │        │   ├── dao                 -- 数据访问类
│       │   │        │   └── service               -- 查询服务
│       │   │        └── infrastructure           -- 基础实施层
│       │   └── resources
│       └── test
│           ├── java
│           └── resources
├── telecom-parent
│   └── pom.xml
├── telecom-service
│   ├── pom.xml
│   └── src
│       ├── main
│       │   └── java
│       │   │    └── com.patsnap.analytics  
│       │   │        ├── faced              -- 接口定义
│       │   │        │   ├── query             -- 查询语句相关接口
│       │   │        │   └── view              -- TDoc数据获取接口
│       │   └── resources
│       └── test
│           ├── java
│           └── resources
├── telecom-parent
│   └── pom.xml
```
2. 新增目录参考团队推荐目录执行

3. 基本开发原则
   单个类不能过大
   方法单一职责，复杂度控制
   遵循一定的开发基本原则：单一职责原则，里氏替换原则，依赖倒置原则等

### 异常处理
1. 过程中不得吞掉任何异常, 除非是捕获某明确类型的异常作为明确的逻辑处理分支
2. 异常定义统一code到ExceptionDefinition中定义
3. 提供清晰的异常消息



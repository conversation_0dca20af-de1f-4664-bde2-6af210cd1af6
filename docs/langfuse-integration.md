# Langfuse 集成指南

## 概述

本文档介绍如何在 Spring Boot 2.7 + Java 17 项目中集成 Langfuse，实现 LLM 应用的可观测性和分析。

## 技术架构

- **基础技术**: OpenTelemetry + Langfuse OTLP 端点 + Langfuse Java SDK
- **集成方式**: 注解驱动 + 手动追踪 + API 访问
- **支持功能**: 用户追踪、会话管理、成本分析、性能监控、提示词管理

## 架构组件

### 1. 依赖组件
- **OpenTelemetry**: 负责追踪数据的采集和导出
- **Langfuse Java SDK**: 提供 API 访问能力，获取提示词、追踪数据等
- **Spring Boot 集成**: 自动配置和健康检查

### 2. 核心类说明
- `LangfuseConfig`: 主配置类，管理所有 Langfuse 相关的 Bean
- `LangfuseProperties`: 配置属性类，统一管理配置参数
- `LangfuseJavaSdkClient`: Java SDK 客户端封装类
- `LangfuseService`: 综合服务类，整合追踪和 API 功能
- `LangfuseTracer`: OpenTelemetry 追踪器封装

## 快速开始

### 1. 添加依赖

项目已经自动包含了必要的依赖：

```xml
<!-- 在 telecom-parent/pom.xml 中 -->
<dependency>
    <groupId>com.langfuse</groupId>
    <artifactId>langfuse-java</artifactId>
    <version>${langfuse.version}</version>
</dependency>

<!-- GitHub Package Registry 仓库 -->
<repositories>
    <repository>
        <id>github</id>
        <name>GitHub Package Registry</name>
        <url>https://maven.pkg.github.com/langfuse/langfuse-java</url>
    </repository>
</repositories>
```

### 2. 配置 Langfuse

在 `application-local.properties` 中添加配置：

```properties
# 启用 Langfuse 追踪
configs.com.patsnap.drafting.langfuse.enabled=true
configs.com.patsnap.drafting.langfuse.public-key=pk-lf-your-public-key
configs.com.patsnap.drafting.langfuse.secret-key=sk-lf-your-secret-key
configs.com.patsnap.drafting.langfuse.host=https://cloud.langfuse.com
configs.com.patsnap.drafting.langfuse.sampling-probability=1.0
```

### 3. 获取 Langfuse API 密钥

1. 访问 [Langfuse Cloud](https://cloud.langfuse.com/) 或部署自托管版本
2. 创建项目并获取 Public Key 和 Secret Key
3. 将密钥配置到相应环境的配置文件中

## 使用方式

### 1. 使用注解进行自动追踪

```java
@Service
public class AiService {
    
    @LangfuseTracing(
        operationName = "ai_chat_completion",
        system = "openai",
        operationType = "chat_completion",
        promptParameter = "prompt",
        modelParameter = "model",
        tags = {"ai", "chat"}
    )
    public String chatCompletion(String model, String prompt) {
        // AI 处理逻辑
        return "AI response";
    }
}
```

### 2. 使用 LangfuseService 进行追踪和 API 访问

```java
@Service
@RequiredArgsConstructor
public class AiTranslationService {
    
    private final LangfuseService langfuseService;
    
    // 方式1: 使用便捷方法自动管理追踪
    public String translateWithAutoTracing(String text, String targetLang) {
        return langfuseService.executeWithTracing(
            "ai_translation",
            "google_translate", 
            "translation",
            text,
            "translate-api-v3",
            () -> {
                // 实际的翻译逻辑
                return callTranslationApi(text, targetLang);
            }
        );
    }
    
    // 方式2: 手动管理追踪生命周期
    public String translateWithManualTracing(String text, String targetLang) {
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("target_language", targetLang);
        metadata.put("text_length", text.length());
        
        var context = langfuseService.createTrace(
            "ai_translation",
            "google_translate",
            "translation", 
            text,
            "translate-api-v3",
            metadata,
            "translation", "ai"
        );
        
        try {
            String result = callTranslationApi(text, targetLang);
            
            var operationResult = AiOperationResult.builder()
                .output(result)
                .success(true)
                .tokenUsage(150)
                .cost(0.003)
                .build();
                
            langfuseService.finishTrace(context, operationResult);
            return result;
            
        } catch (Exception e) {
            langfuseService.recordError(context, e);
            throw e;
        }
    }
}
```

### 3. 使用 Java SDK API 功能

```java
@Service
@RequiredArgsConstructor
public class PromptManagementService {
    
    private final LangfuseService langfuseService;
    
    // 获取所有提示词模板
    public List<PromptMeta> getAllPrompts() {
        return langfuseService.getPrompts()
            .map(response -> response.getData())
            .orElse(Collections.emptyList());
    }
    
    // 获取特定提示词
    public Optional<Object> getPrompt(String name, String version) {
        return langfuseService.getPrompt(name, version);
    }
    
    // 查询追踪详情
    public Optional<TraceWithDetails> getTraceDetails(String traceId) {
        return langfuseService.getTrace(traceId);
    }
}
```

### 4. 手动追踪（原有方式）

```java
@Service
@RequiredArgsConstructor
public class ManualTracingService {
    
    private final LangfuseTracer langfuseTracer;
    
    public String processWithTracing(String userId, String sessionId, String prompt) {
        AiOperationContext context = LangfuseUtils.createOpenAiChatContext(
            "gpt-4", prompt, userId, sessionId);
            
        return langfuseTracer.traceAiOperation("manual_ai_operation", context, () -> {
            // AI 处理逻辑
            String response = callAiService(prompt);
            return AiOperationResult.success(response, 50L, 100L);
        }).toString();
    }
}
```

### 5. 设置用户上下文

```java
@RestController
public class AiController {
    
    @PostMapping("/ai/chat")
    public ResponseEntity<String> chat(@RequestBody ChatRequest request) {
        try {
            // 设置用户上下文
            LangfuseUtils.setUserContext(request.getUserId(), request.getSessionId());
            
            // 调用 AI 服务（会自动使用上下文信息）
            String response = aiService.chatCompletion(request.getModel(), request.getPrompt());
            
            return ResponseEntity.ok(response);
        } finally {
            // 清除上下文
            LangfuseUtils.clearContext();
        }
    }
}
```

## 配置参数说明

### 基础配置

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `enabled` | 是否启用 Langfuse 追踪 | `false` |
| `public-key` | Langfuse 公钥 | - |
| `secret-key` | Langfuse 私钥 | - |
| `host` | Langfuse 主机地址 | `https://cloud.langfuse.com` |
| `service-name` | 服务名称 | `s-analytics-telecom` |
| `sampling-probability` | 采样率 (0.0-1.0) | `1.0` |

### 批量导出配置

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `batch.max-export-batch-size` | 批量大小 | `512` |
| `batch.export-timeout-millis` | 导出超时时间 | `30000` |
| `batch.schedule-delay-millis` | 调度延迟 | `5000` |
| `batch.max-queue-size` | 最大队列大小 | `2048` |

### 超时配置

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `timeout.connect-timeout-millis` | 连接超时 | `10000` |
| `timeout.read-timeout-millis` | 读取超时 | `30000` |
| `timeout.write-timeout-millis` | 写入超时 | `30000` |

## 注解参数说明

### @LangfuseTracing 注解

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `operationName` | 操作名称 | 方法名 |
| `system` | AI 系统名称 | `""` |
| `operationType` | 操作类型 | `""` |
| `recordParameters` | 是否记录参数 | `true` |
| `recordResult` | 是否记录返回值 | `true` |
| `recordException` | 是否记录异常 | `true` |
| `tags` | 自定义标签 | `{}` |
| `promptParameter` | 提示词参数名 | `""` |
| `modelParameter` | 模型参数名 | `""` |

## 最佳实践

### 1. 用户和会话管理

```java
// 在 Controller 层设置用户上下文
@PostMapping("/api/ai/process")
public ResponseEntity<?> processAiRequest(@RequestBody AiRequest request) {
    try {
        // 从请求中获取或生成会话 ID
        String sessionId = request.getSessionId() != null ? 
            request.getSessionId() : LangfuseUtils.generateSessionId();
            
        LangfuseUtils.setUserContext(request.getUserId(), sessionId);
        
        // 业务处理
        return ResponseEntity.ok(aiService.process(request));
    } finally {
        LangfuseUtils.clearContext();
    }
}
```

### 2. 成本追踪

```java
@LangfuseTracing(operationName = "cost_tracked_operation")
public AiOperationResult processWithCostTracking(String model, String prompt) {
    // AI 处理
    String response = callAiService(model, prompt);
    Long promptTokens = 45L;
    Long completionTokens = 120L;
    
    // 计算成本
    BigDecimal cost = LangfuseUtils.calculateOpenAiCost(model, promptTokens, completionTokens);
    
    return LangfuseUtils.createSuccessResultWithCost(response, promptTokens, completionTokens, cost);
}
```

### 3. 错误处理

```java
@LangfuseTracing(recordException = true)
public String processWithErrorHandling(String prompt) {
    try {
        return aiService.process(prompt);
    } catch (Exception e) {
        log.error("AI processing failed", e);
        // 异常会自动记录到 Langfuse
        throw e;
    }
}
```

### 4. 批量处理

```java
public void processBatch(String userId, String[] prompts) {
    try {
        String sessionId = LangfuseUtils.generateSessionId();
        LangfuseUtils.setUserContext(userId, sessionId);
        
        for (int i = 0; i < prompts.length; i++) {
            langfuseTracer.trace("batch_item_" + i, () -> {
                return processItem(prompts[i]);
            });
        }
    } finally {
        LangfuseUtils.clearContext();
    }
}
```

## 健康检查

访问 `/telecom/manage/health` 查看 Langfuse 连接状态：

```json
{
  "status": "UP",
  "components": {
    "langfuseHealthIndicator": {
      "status": "UP",
      "details": {
        "status": "connected",
        "endpoint": "https://cloud.langfuse.com/api/public/otel",
        "serviceName": "s-analytics-telecom",
        "samplingProbability": 1.0
      }
    }
  }
}
```

## 故障排除

### 1. 连接问题

- 检查网络连接和防火墙设置
- 验证 Langfuse 主机地址是否正确
- 确认 API 密钥是否有效

### 2. 追踪数据缺失

- 检查采样率设置
- 验证配置是否正确加载
- 查看应用日志中的错误信息

### 3. 性能问题

- 调整批量导出配置
- 降低采样率
- 检查网络延迟

### 4. 常见错误

```bash
# 配置无效
WARN: Langfuse configuration is invalid, using no-op OpenTelemetry

# 连接失败
ERROR: Failed to configure OpenTelemetry, using no-op implementation

# 认证失败
ERROR: Authentication failed. Please check your credentials and host.
```

## 环境配置示例

### 开发环境
```properties
configs.com.patsnap.drafting.langfuse.enabled=true
configs.com.patsnap.drafting.langfuse.sampling-probability=1.0
```

### 测试环境
```properties
configs.com.patsnap.drafting.langfuse.enabled=true
configs.com.patsnap.drafting.langfuse.sampling-probability=0.1
```

### 生产环境
```properties
configs.com.patsnap.drafting.langfuse.enabled=true
configs.com.patsnap.drafting.langfuse.sampling-probability=0.01
```

## 重要说明

### 当前实现方式

由于 Langfuse Java SDK 还在开发阶段（目前版本为 0.0.1-SNAPSHOT），本集成使用以下方式实现：

1. **OpenTelemetry 追踪**：使用 OpenTelemetry + OTLP 导出器实现 LLM 操作的自动追踪
2. **REST API 访问**：通过 Spring RestTemplate 调用 Langfuse Public API 实现 SDK 功能
3. **统一接口**：提供 `LangfuseService` 统一服务接口，屏蔽底层实现细节

### 未来升级路径

一旦 Langfuse Java SDK 正式发布并稳定后，可以通过以下步骤无缝升级：

1. 更新 `LangfuseJavaSdkClient` 实现，使用官方 SDK 替代 REST API 调用
2. 保持 `LangfuseService` 接口不变，确保上层业务代码无需修改
3. 移除当前的 RestTemplate 实现，使用 SDK 的原生客户端

### API 兼容性

当前实现的 API 接口设计参考了 [Langfuse Public API 文档](https://langfuse.com/docs/api-and-data-platform/features/public-api)，确保：

- 返回数据结构与官方 API 一致
- 支持所有主要的 CRUD 操作
- 包含完整的错误处理和日志记录
- 提供健康检查和连接管理

## 支持和反馈

如有问题或建议，请联系开发团队或查看 [Langfuse 官方文档](https://langfuse.com/docs)。

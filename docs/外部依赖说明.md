# 外部依赖说明

## 目录
1. [依赖服务概述](#依赖服务概述)
2. [AI服务类](#ai服务类)
3. [数据服务类](#数据服务类)
4. [基础设施类](#基础设施类)
5. [存储服务类](#存储服务类)
6. [服务调用关系](#服务调用关系)
7. [配置管理](#配置管理)
8. [容错和监控](#容错和监控)
9. [安全机制](#安全机制)

## 依赖服务概述

S-Analytics-Telecom项目作为一个复杂的企业级应用，依赖多个外部服务来实现完整的业务功能。这些外部服务按功能分为四大类，共计20个核心服务。

### 服务分类统计
| 服务类别 | 服务数量 | 主要用途 |
|----------|----------|----------|
| AI服务类 | 8个 | AI对话、专利撰写、语言检测、分类等 |
| 数据服务类 | 4个 | 电信数据搜索、专利数据获取 |
| 基础设施类 | 5个 | 数据库、缓存、消息队列、配置管理 |
| 存储服务类 | 3个 | 文件存储、对象存储 |

### 服务调用架构图
```mermaid
graph TB
    subgraph "telecom-service"
        A[Controller层]
    end
    
    subgraph "telecom-core"
        B[Manager层]
        C[Service层]
    end
    
    subgraph "AI服务集群"
        D1[GPT服务]
        D2[分类服务]
        D3[语言检测服务]
        D4[说明书撰写服务]
        D5[敏感词检测服务]
    end
    
    subgraph "数据服务集群"
        E1[SearchAPI服务]
        E2[PatentAPI服务]
        E3[聚合API服务]
    end
    
    subgraph "基础设施"
        F1[PostgreSQL]
        F2[Redis集群]
        F3[SQS消息队列]
        F4[Apollo配置中心]
    end
    
    subgraph "存储服务"
        G1[S3存储]
        G2[COS存储]
        G3[文件签名服务]
    end
    
    A --> B
    B --> C
    C --> D1
    C --> D2
    C --> D3
    C --> D4
    C --> D5
    C --> E1
    C --> E2
    C --> E3
    C --> F1
    C --> F2
    C --> F3
    C --> F4
    C --> G1
    C --> G2
    C --> G3
```

## AI服务类

### 1. GPT服务
**服务地址**: `com.patsnap.analytics.service.gpt.base-url`  
**默认地址**: `http://s-patsnaprd-gateway.patsnaprd/v1`

**功能描述**: 提供AI对话和专利撰写核心能力

**主要接口**:
```http
POST /openai_chatgpt_turbo
Content-Type: application/json

{
  "model": "gpt-4",
  "messages": [
    {
      "role": "user",
      "content": "生成专利说明书"
    }
  ],
  "stream": true
}
```

**调用示例**:
```java
@Service
public class OpenAiService {
    
    @Autowired
    private OpenAiApi openAiApi;
    
    public Flux<String> generateContent(String prompt) {
        OpenAiRequest request = OpenAiRequest.builder()
            .model("gpt-4")
            .messages(Arrays.asList(
                OpenAiMessage.builder()
                    .role("user")
                    .content(prompt)
                    .build()))
            .stream(true)
            .build();
        
        return openAiApi.chatCompletion(request);
    }
}
```

### 2. 说明书撰写服务
**服务地址**: `configs.com.patsnap.compute.specification.url`  
**默认地址**: `http://s-patsnaprd-patent-drafting.patsnaprd/compute/patent_drafting/`

**功能描述**: 专门用于AI专利说明书撰写

**主要接口**:
- `POST /compute/patent_drafting/specification_content` - 生成说明书内容
- `POST /compute/patent_drafting/claims_format` - 权利要求格式化
- `POST /compute/patent_drafting/embodiment_generation` - 实施例生成

### 3. 语言检测服务
**服务地址**: `configs.com.patsnap.compute.lang-detect.url`

**功能描述**: 自动检测文本语言类型

**请求示例**:
```http
POST /compute/lang_detect/
{
  "text": "这是一个中文文本"
}

响应:
{
  "language": "zh-CN",
  "confidence": 0.99
}
```

### 4. 分类服务集群

#### IPC分类服务
**服务地址**: `configs.com.patsnap.compute.ipc.url`  
**功能**: 国际专利分类号生成

#### CPC分类服务
**服务地址**: `configs.com.patsnap.compute.cpc.url`  
**功能**: 合作专利分类号生成

### 5. 敏感词检测服务
**服务地址**: `configs.com.patsnap.compute.patsnap-check-input.url`

**功能描述**: 检测用户输入内容的敏感词

**调用流程**:
```java
@Service
public class SensitiveContentValidator {
    
    public boolean validateContent(String content) {
        CheckInputRequest request = new CheckInputRequest(content);
        CheckInputResponse response = checkInputApi.checkInput(request);
        return response.isSafe();
    }
}
```

## 数据服务类

### 1. SearchAPI服务
**服务地址**: `com.patsnap.analytics.service.search-api-url`  
**默认地址**: `http://s-search-search-api.search/searchapi/3.0.0`

**功能描述**: 电信数据搜索的核心服务

**主要接口**:
```http
# 数据搜索
POST /searchapi/3.0.0/v1/telecom/search
{
  "query": "5G技术",
  "filters": {},
  "page": 1,
  "size": 20
}

# 高亮关键词
POST /searchapi/3.0.0/v1/telecom/highlight
{
  "query": "5G技术",
  "content": "要高亮的内容"
}

# 搜索建议
POST /searchapi/3.0.0/v1/telecom/suggest
{
  "prefix": "5G"
}
```

**调用示例**:
```java
@Service
public class SearchApiServiceImpl implements SearchApiService {
    
    @Autowired
    private SearchApiClient searchApiClient;
    
    @Override
    public TelecomQueryResultBO searchTelecomData(TelecomQuery query) {
        SearchRequest request = buildSearchRequest(query);
        try {
            Response<SearchResponse> response = searchApiClient
                .searchTelecom(request).execute();
            if (response.isSuccessful()) {
                return convertToBusinessObject(response.body());
            }
            throw new ExternalServiceException("SearchAPI调用失败");
        } catch (IOException e) {
            log.error("SearchAPI网络异常", e);
            throw new NetworkException("SearchAPI网络异常");
        }
    }
}
```

### 2. PatentAPI服务
**服务地址**: `configs.com.patsnap.patent_api.url`  
**默认地址**: `http://s-platform-patent-api.platform/openapi/3.2.0`

**功能描述**: 专利基础数据获取服务

**主要接口**:
```http
# 获取专利详情
GET /openapi/3.2.0/patent/detail/{patentId}

# 获取专利权利要求
GET /openapi/3.2.0/patent/claims/{patentId}

# 获取专利说明书
GET /openapi/3.2.0/patent/description/{patentId}

# 批量获取专利信息
POST /openapi/3.2.0/patent/batch
{
  "patentIds": ["CN123456A", "US987654B2"]
}
```

### 3. 专利聚合API服务
**服务地址**: `configs.com.patsnap.patent_aggregate_api.url`

**功能描述**: 聚合多个数据源的专利详细信息

### 4. 开放API服务
**服务地址**: `configs.com.patsnap.open-api.url`

**功能描述**: 提供图像搜索、位置预测等通用API

**主要接口**:
- `POST /business-openapi/image/similar` - 图像相似搜索
- `POST /business-openapi/location/predict` - 位置预测

## 基础设施类

### 1. PostgreSQL数据库
**配置路径**: `spring.datasource.dynamic.datasource.telecom.*`

**连接配置**:
```properties
# 主数据库配置
spring.datasource.dynamic.datasource.telecom.url=**********************************************
spring.datasource.dynamic.datasource.telecom.username=${DB_USERNAME}
spring.datasource.dynamic.datasource.telecom.password=${DB_PASSWORD}
spring.datasource.dynamic.datasource.telecom.driver-class-name=org.postgresql.Driver

# 连接池配置
spring.datasource.dynamic.datasource.telecom.hikari.maximum-pool-size=20
spring.datasource.dynamic.datasource.telecom.hikari.minimum-idle=5
spring.datasource.dynamic.datasource.telecom.hikari.connection-timeout=30000
```

**主要数据表**:
- `analytics_web_query` - 查询历史记录
- `analytics_ai_task_history` - AI任务历史
- `analytics_ai_task_content` - AI任务内容
- `analytics_telecom_read_status` - 阅读状态

### 2. Redis/Redisson集群
**配置路径**: `configs.com.patsnap.redis.redisson.*`

**集群配置**:
```properties
# Redis集群配置
configs.com.patsnap.redis.redisson.cluster-servers-config.node-addresses=
  redis://node1:6379,redis://node2:6379,redis://node3:6379
configs.com.patsnap.redis.redisson.cluster-servers-config.password=${REDIS_PASSWORD}
configs.com.patsnap.redis.redisson.cluster-servers-config.connection-pool-size=64
configs.com.patsnap.redis.redisson.cluster-servers-config.idle-connection-timeout=10000
```

**使用场景**:
- 分布式锁管理
- 缓存查询结果
- 会话状态存储
- 高亮信息缓存

### 3. SQS消息队列
**配置路径**: `configs.com.patsnap.data.sqs.ai-task.*`

**队列配置**:
```properties
# AI任务队列配置
configs.com.patsnap.data.sqs.ai-task.queue-url=${AI_TASK_QUEUE_URL}
configs.com.patsnap.data.sqs.ai-task.region=${AWS_REGION}
configs.com.patsnap.data.sqs.ai-task.max-number-of-messages=1
configs.com.patsnap.data.sqs.ai-task.wait-time-seconds=20
configs.com.patsnap.data.sqs.ai-task.visibility-timeout-seconds=1800
```

**消息处理流程**:
```java
@Component
public class AiTaskMessageHandler {
    
    @Value("${configs.com.patsnap.data.sqs.ai-task.queue-url}")
    private String queueUrl;
    
    @Scheduled(fixedDelay = 5000)
    public void processMessages() {
        ReceiveMessageRequest request = ReceiveMessageRequest.builder()
            .queueUrl(queueUrl)
            .maxNumberOfMessages(1)
            .waitTimeSeconds(20)
            .build();
            
        List<Message> messages = sqsClient.receiveMessage(request).messages();
        for (Message message : messages) {
            processMessage(message);
        }
    }
}
```

### 4. Apollo配置中心
**配置路径**: `apollo.meta`

**功能特点**:
- 动态配置管理
- 多环境配置支持
- 配置变更实时推送
- 配置版本管理

### 5. 身份认证服务
**服务地址**: `configs.com.patsnap.resttemplate.identity.base-uri`

**功能描述**: 统一身份认证和授权

## 存储服务类

### 1. 文件签名服务
**服务地址**: `configs.com.patsnap.signature.url`

**功能描述**: 为文件上传下载提供安全签名

**签名流程**:
```java
@Service
public class FileSignatureService {
    
    public String generateUploadSignature(String fileName, String contentType) {
        SignatureRequest request = SignatureRequest.builder()
            .fileName(fileName)
            .contentType(contentType)
            .operation("upload")
            .build();
            
        SignatureResponse response = signatureApi.generateSignature(request);
        return response.getSignedUrl();
    }
}
```

### 2. S3对象存储
**配置路径**: `configs.com.patsnap.analytics.storage.*`

**配置示例**:
```properties
# S3存储配置
configs.com.patsnap.analytics.storage.use-s3=true
configs.com.patsnap.analytics.storage.bucket-name=telecom-storage
configs.com.patsnap.analytics.storage.region=us-west-2
configs.com.patsnap.analytics.storage.bucket-domain=cdn.example.com
```

**使用场景**:
- 导出文件存储
- 图片资源存储
- 临时文件缓存

### 3. COS对象存储
**配置路径**: `configs.com.patsnap.cos.*`

**功能描述**: 腾讯云对象存储，主要用于中国区域

## 服务调用关系

### 典型调用链路图
```mermaid
sequenceDiagram
    participant U as 用户
    participant C as Controller
    participant M as Manager
    participant S as Service
    participant E as 外部服务
    participant D as 数据库
    participant R as Redis
    
    U->>C: 发起请求
    C->>M: 调用业务管理器
    M->>S: 调用业务服务
    
    par 并行调用外部服务
        S->>E: 调用SearchAPI
        E-->>S: 返回搜索结果
    and
        S->>E: 调用PatentAPI
        E-->>S: 返回专利数据
    and
        S->>E: 调用AI服务
        E-->>S: 返回AI结果
    end
    
    S->>D: 保存数据
    S->>R: 更新缓存
    S-->>M: 返回处理结果
    M-->>C: 返回业务结果
    C-->>U: 响应用户请求
```

### 服务依赖层次
```mermaid
graph TD
    A[应用层] --> B[核心数据服务]
    A --> C[AI计算服务]
    A --> D[辅助服务]
    
    B --> B1[SearchAPI]
    B --> B2[PatentAPI]
    
    C --> C1[GPT服务]
    C --> C2[分类服务]
    C --> C3[语言检测]
    
    D --> D1[存储服务]
    D --> D2[消息队列]
    D --> D3[配置中心]
```

## 配置管理

### 多环境配置策略

#### 环境分类
```yaml
environments:
  development:
    - local      # 本地开发
    - ci         # 持续集成
  testing:
    - qa         # 测试环境
    - stage      # 预发布环境
  production:
    - aws_us     # 美国生产环境
    - aws_eu     # 欧洲生产环境
    - tx_cn      # 腾讯云中国环境
```

#### 配置文件结构
```
application.properties              # 基础配置
application-local.properties        # 本地开发配置
application-ci.properties          # CI环境配置
application-qa.properties          # QA环境配置
application-stage.properties       # 预发布配置
application-aws_us.properties      # 美国生产配置
application-aws_eu.properties      # 欧洲生产配置
application-tx_cn.properties       # 中国生产配置
```

#### 配置优先级
1. **环境变量** > **Apollo配置中心** > **Profile配置** > **默认配置**
2. **运行时动态配置** > **编译时静态配置**

### 关键配置示例

#### 外部服务地址配置
```properties
# AI服务配置
com.patsnap.analytics.service.gpt.base-url=${GPT_SERVICE_URL:http://default-gpt-service}
configs.com.patsnap.compute.specification.url=${SPEC_SERVICE_URL:http://default-spec-service}

# 数据服务配置
com.patsnap.analytics.service.search-api-url=${SEARCH_API_URL:http://default-search-api}
configs.com.patsnap.patent_api.url=${PATENT_API_URL:http://default-patent-api}

# 基础设施配置
spring.datasource.dynamic.datasource.telecom.url=${DB_URL:****************************************}
configs.com.patsnap.redis.redisson.cluster-servers-config.node-addresses=${REDIS_NODES}
```

#### 超时和重试配置
```properties
# HTTP客户端配置
configs.com.patsnap.resttemplate.connect-timeout=1000
configs.com.patsnap.resttemplate.read-timeout=30000
configs.com.patsnap.resttemplate.max-conn-total=200
configs.com.patsnap.resttemplate.max-conn-per-route=100

# 重试配置
spring.retry.max-attempts=3
spring.retry.backoff.delay=1000
spring.retry.backoff.max-delay=10000
```

## 容错和监控

### 容错机制

#### 1. HTTP客户端容错
```java
@Configuration
public class RestTemplateConfig {
    
    @Bean
    public RestTemplate restTemplate() {
        RestTemplate restTemplate = new RestTemplate();
        
        // 设置请求工厂
        HttpComponentsClientHttpRequestFactory factory = 
            new HttpComponentsClientHttpRequestFactory();
        factory.setConnectTimeout(1000);
        factory.setReadTimeout(30000);
        
        // 添加重试拦截器
        restTemplate.setInterceptors(Arrays.asList(
            new RetryInterceptor(3, 1000)
        ));
        
        return restTemplate;
    }
}
```

#### 2. 服务降级策略
```java
@Service
public class SearchApiServiceImpl implements SearchApiService {
    
    @Override
    public TelecomQueryResultBO searchTelecomData(TelecomQuery query) {
        try {
            return doSearch(query);
        } catch (ExternalServiceException e) {
            log.warn("SearchAPI服务异常，启用降级策略", e);
            return getCachedResult(query);
        }
    }
    
    private TelecomQueryResultBO getCachedResult(TelecomQuery query) {
        // 从缓存获取结果
        String cacheKey = generateCacheKey(query);
        return redisTemplate.opsForValue().get(cacheKey);
    }
}
```

#### 3. 断路器模式
```java
@Component
public class CircuitBreakerService {
    
    private final CircuitBreaker circuitBreaker = CircuitBreaker.create(
        CircuitBreakerConfig.custom()
            .failureRateThreshold(50)
            .waitDurationInOpenState(Duration.ofMinutes(1))
            .slidingWindowSize(10)
            .build()
    );
    
    public <T> T executeWithCircuitBreaker(Supplier<T> supplier) {
        return circuitBreaker.executeSupplier(supplier);
    }
}
```

### 监控机制

#### 1. 健康检查
```java
@Component
public class ExternalServiceHealthIndicator implements HealthIndicator {
    
    @Autowired
    private SearchApiService searchApiService;
    
    @Override
    public Health health() {
        try {
            searchApiService.healthCheck();
            return Health.up()
                .withDetail("searchApi", "Available")
                .build();
        } catch (Exception e) {
            return Health.down()
                .withDetail("searchApi", "Unavailable")
                .withException(e)
                .build();
        }
    }
}
```

#### 2. 指标监控
```java
@Component
public class ServiceMetrics {
    
    private final Counter apiCallCounter = Counter.build()
        .name("external_api_calls_total")
        .help("外部API调用次数")
        .labelNames("service", "method", "status")
        .register();
    
    private final Histogram apiCallDuration = Histogram.build()
        .name("external_api_call_duration_seconds")
        .help("外部API调用耗时")
        .labelNames("service", "method")
        .register();
    
    public void recordApiCall(String service, String method, String status, double duration) {
        apiCallCounter.labels(service, method, status).inc();
        apiCallDuration.labels(service, method).observe(duration);
    }
}
```

#### 3. 日志监控
```properties
# 日志配置
configs.com.patsnap.resttemplate.logging.enabled=true
configs.com.patsnap.resttemplate.logging.include-headers=true
configs.com.patsnap.resttemplate.logging.include-payload=true
configs.com.patsnap.resttemplate.logging.max-payload-length=1000
```

### 告警配置
```yaml
alerts:
  - name: ExternalServiceDown
    condition: external_service_health == 0
    duration: 1m
    severity: critical
    notification: ["email", "slack"]
    
  - name: HighAPILatency
    condition: external_api_call_duration_p95 > 5s
    duration: 2m
    severity: warning
    notification: ["slack"]
    
  - name: HighErrorRate
    condition: external_api_error_rate > 0.1
    duration: 5m
    severity: warning
    notification: ["email"]
```

## 安全机制

### 认证授权

#### 1. 统一身份认证
```java
@Component
public class IdentityAuthenticator {
    
    @Value("${configs.com.patsnap.resttemplate.identity.base-uri}")
    private String identityServiceUrl;
    
    public AuthenticationResult authenticate(String token) {
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(token);
        
        HttpEntity<Void> entity = new HttpEntity<>(headers);
        
        try {
            ResponseEntity<UserInfo> response = restTemplate.exchange(
                identityServiceUrl + "/verify",
                HttpMethod.GET,
                entity,
                UserInfo.class
            );
            
            return AuthenticationResult.success(response.getBody());
        } catch (Exception e) {
            return AuthenticationResult.failure("认证失败");
        }
    }
}
```

#### 2. API密钥管理
```properties
# API密钥配置
configs.com.patsnap.api.search.api-key=${SEARCH_API_KEY}
configs.com.patsnap.api.patent.api-key=${PATENT_API_KEY}
configs.com.patsnap.api.openai.api-key=${OPENAI_API_KEY}
```

### 数据安全

#### 1. 敏感内容检测
```java
@Service
public class ContentSecurityService {
    
    @Autowired
    private SensitiveWordCheckApi sensitiveWordCheckApi;
    
    public ContentCheckResult checkContent(String content) {
        CheckRequest request = CheckRequest.builder()
            .content(content)
            .checkLevel(CheckLevel.STRICT)
            .build();
            
        return sensitiveWordCheckApi.checkContent(request);
    }
}
```

#### 2. 数据传输加密
```java
@Configuration
public class HttpsConfig {
    
    @Bean
    public RestTemplate httpsRestTemplate() {
        RestTemplate restTemplate = new RestTemplate();
        
        // 配置SSL上下文
        SSLContext sslContext = SSLContextBuilder.create()
            .loadTrustMaterial(trustStore, trustStorePassword)
            .loadKeyMaterial(keyStore, keyStorePassword, keyPassword)
            .build();
            
        // 配置HTTPS连接
        HttpComponentsClientHttpRequestFactory factory = 
            new HttpComponentsClientHttpRequestFactory();
        factory.setHttpClient(HttpClients.custom()
            .setSSLContext(sslContext)
            .build());
            
        restTemplate.setRequestFactory(factory);
        return restTemplate;
    }
}
```

### 访问控制

#### 1. 请求来源验证
```java
@Component
public class RequestSourceValidator {
    
    @Value("${configs.com.patsnap.web.request.headers.x-patsnap-from.required}")
    private boolean requireFromHeader;
    
    public boolean validateRequestSource(HttpServletRequest request) {
        if (!requireFromHeader) {
            return true;
        }
        
        String fromHeader = request.getHeader("X-PatSnap-From");
        return StringUtils.hasText(fromHeader) && 
               isValidSource(fromHeader);
    }
}
```

#### 2. API版本控制
```java
@RestController
@RequestMapping("/telecom/v1")
public class TelecomV1Controller {
    // V1版本接口
}

@RestController
@RequestMapping("/telecom/v2")
public class TelecomV2Controller {
    // V2版本接口
}
```

## 总结

S-Analytics-Telecom项目的外部依赖架构体现了现代微服务应用的典型特征：

### 架构优势
1. **服务解耦**: 通过外部服务实现功能解耦和专业化分工
2. **弹性扩展**: 各服务可独立扩展和升级
3. **容错能力**: 完善的容错和降级机制
4. **监控完善**: 全面的健康检查和指标监控

### 管理挑战
1. **复杂性增加**: 多服务依赖增加了系统复杂性
2. **网络延迟**: 外部调用引入网络延迟
3. **故障传播**: 外部服务故障可能影响整体系统
4. **配置管理**: 多环境配置管理复杂度高

### 改进建议
1. **服务网格**: 引入Istio等服务网格简化服务治理
2. **API网关**: 统一外部服务访问入口
3. **链路追踪**: 完善分布式链路追踪能力
4. **自动化测试**: 增加外部服务集成测试
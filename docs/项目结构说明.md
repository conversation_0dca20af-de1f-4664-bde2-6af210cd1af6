# 项目结构说明

## 目录
1. [项目概述](#项目概述)
2. [模块划分](#模块划分)
3. [代码组织结构](#代码组织结构)
4. [分层架构说明](#分层架构说明)
5. [关键包说明](#关键包说明)
6. [配置文件结构](#配置文件结构)
7. [测试结构](#测试结构)

## 项目概述

S-Analytics-Telecom采用Maven多模块架构，遵循领域驱动设计（DDD）原则，分为四个核心模块。项目采用分层架构模式，每个模块都有明确的职责边界和依赖关系。

### 技术架构特点
- **多模块设计**: Maven多模块项目，模块间职责清晰
- **分层架构**: API → Core → Service的三层结构
- **领域驱动**: Analytics和Drafting两大业务领域
- **依赖管理**: 统一的父级依赖管理

## 模块划分

### 模块依赖关系图
```mermaid
flowchart TD
    A[telecom-parent] --> B[telecom-api]
    A --> C[telecom-core]
    A --> D[telecom-service]
    
    B --> C
    C --> D
    
    subgraph 外部依赖
        E[Spring Boot]
        F[PostgreSQL]
        G[Redis]
        H[AWS Services]
    end
    
    D --> E
    D --> F
    D --> G
    D --> H
    
    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8
    style D fill:#fff3e0
```

### 1. telecom-parent（父级模块）
**路径**: `/`  
**职责**: 依赖管理和构建配置

```
telecom-parent/
├── pom.xml                    # 父级POM，统一依赖版本管理
├── CLAUDE.md                  # 项目开发指南
├── README.md                  # 项目说明文档
└── docs/                      # 项目文档目录
```

**关键依赖管理**:
```xml
<properties>
    <java.version>17</java.version>
    <spring-boot.version>2.7.0</spring-boot.version>
    <mybatis-plus.version>3.5.3.1</mybatis-plus.version>
    <retrofit.version>2.9.0</retrofit.version>
</properties>
```

### 2. telecom-api（API定义模块）
**路径**: `/telecom-api`  
**职责**: DTOs、领域模型和API契约定义

```
telecom-api/
├── pom.xml
└── src/main/java/com/patsnap/
    ├── analytics/
    │   ├── dto/                # Analytics领域DTO
    │   │   ├── request/        # 请求DTO
    │   │   ├── response/       # 响应DTO
    │   │   └── common/         # 通用DTO
    │   └── enums/              # Analytics枚举定义
    └── drafting/
        ├── dto/                # Drafting领域DTO
        │   ├── request/        # AI任务请求DTO
        │   ├── response/       # AI任务响应DTO
        │   └── ai/             # AI相关DTO
        └── enums/              # Drafting枚举定义
            ├── AiTaskTypeEnum.java
            ├── AsyncTaskStatusEnum.java
            └── AiTaskContentTypeEnum.java
```

**关键文件**:
- `analytics/dto/request/CommonRequestDTO.java` - 通用请求DTO
- `drafting/dto/ai/AiTaskCreateReqDTO.java` - AI任务创建请求
- `drafting/enums/AiTaskTypeEnum.java` - AI任务类型枚举

### 3. telecom-core（核心业务模块）
**路径**: `/telecom-core`  
**职责**: 业务逻辑、服务和数据访问层

```
telecom-core/
├── pom.xml
└── src/main/java/com/patsnap/
    ├── analytics/              # Analytics领域核心
    │   ├── frame/              # 框架层
    │   │   ├── gateway/        # 防腐层/网关
    │   │   ├── assembly/       # 参数组装
    │   │   └── validator/      # 校验器
    │   ├── manager/            # 管理器层
    │   │   ├── TelecomViewManager.java
    │   │   └── FieldDistributeService.java
    │   ├── service/            # 服务层
    │   │   ├── SearchApiService.java
    │   │   ├── PatentApiService.java
    │   │   └── TelDataService.java
    │   ├── repository/         # 数据访问层
    │   │   ├── BaseEntityPO.java
    │   │   ├── query/          # 查询相关实体
    │   │   └── view/           # 视图相关实体
    │   └── infrastructure/     # 基础设施层
    │       ├── client/         # 外部服务客户端
    │       ├── cache/          # 缓存实现
    │       └── config/         # 配置类
    ├── drafting/               # Drafting领域核心
    │   ├── frame/              # 框架层
    │   │   ├── gateway/        # AI服务网关
    │   │   ├── assembly/       # 任务组装器
    │   │   └── validator/      # 内容校验器
    │   ├── manager/            # 管理器层
    │   │   ├── AiTaskManager.java
    │   │   ├── AiTranslationManager.java
    │   │   └── TaskContentManager.java
    │   ├── service/            # 服务层
    │   │   ├── AnalyticsAiTaskHistoryService.java
    │   │   ├── AnalyticsAiTaskContentService.java
    │   │   └── ComputeClient.java
    │   ├── repository/         # 数据访问层
    │   │   ├── aitask/         # AI任务实体
    │   │   │   ├── entity/
    │   │   │   │   ├── AnalyticsAiTaskHistoryPO.java
    │   │   │   │   ├── AnalyticsAiTaskContentPO.java
    │   │   │   │   └── AnalyticsAiTranslationTermListPO.java
    │   │   │   ├── mapper/
    │   │   │   └── service/
    │   │   ├── share/          # 分享相关实体
    │   │   └── creditusage/    # 积分使用实体
    │   └── infrastructure/     # 基础设施层
    │       ├── ai/             # AI服务集成
    │       ├── queue/          # 消息队列
    │       └── storage/        # 存储服务
    └── common/                 # 共享组件
        ├── config/             # 通用配置
        ├── exception/          # 异常定义
        ├── utils/              # 工具类
        └── constant/           # 常量定义
```

**关键文件说明**:
- `analytics/manager/TelecomViewManager.java` - 电信数据视图管理器
- `drafting/manager/AiTaskManager.java` - AI任务生命周期管理
- `common/config/RedisConfig.java` - Redis配置
- `common/exception/ExceptionDefinition.java` - 异常定义

### 4. telecom-service（服务接口模块）
**路径**: `/telecom-service`  
**职责**: REST控制器和主Spring Boot应用

```
telecom-service/
├── pom.xml
└── src/
    ├── main/
    │   ├── java/com/patsnap/
    │   │   ├── Application.java           # 应用入口
    │   │   ├── analytics/
    │   │   │   └── facade/
    │   │   │       └── controller/
    │   │   │           ├── TelecomSearchResultController.java
    │   │   │           ├── TelecomViewDetailController.java
    │   │   │           ├── QueryTransferController.java
    │   │   │           └── PermissionController.java
    │   │   ├── drafting/
    │   │   │   └── controller/
    │   │   │       ├── AiSpecificationController.java
    │   │   │       ├── AiTranslationController.java
    │   │   │       ├── AiFtoSearchController.java
    │   │   │       ├── AiNoveltySearchController.java
    │   │   │       ├── AiApplicationHistoryTaskController.java
    │   │   │       ├── PatentController.java
    │   │   │       ├── ImageSearchToolController.java
    │   │   │       └── TechReportController.java
    │   │   └── common/
    │   │       ├── config/                # Web配置
    │   │       ├── interceptor/           # 拦截器
    │   │       └── filter/                # 过滤器
    │   └── resources/
    │       ├── config/
    │       │   ├── application.properties # 主配置文件
    │       │   ├── application-local.properties
    │       │   ├── application-qa.properties
    │       │   └── application-prod.properties
    │       ├── mapper/                    # MyBatis映射文件
    │       └── static/                    # 静态资源
    └── test/
        └── java/                         # 测试代码
```

## 分层架构说明

### 架构分层图
```mermaid
flowchart TD
    subgraph "telecom-service"
        A[Controller层]
        A1[REST接口]
        A2[参数校验]
        A3[响应封装]
    end
    
    subgraph "telecom-core"
        B[Manager层]
        B1[业务编排]
        B2[事务管理]
        B3[异常处理]
        
        C[Service层]
        C1[业务逻辑]
        C2[外部集成]
        C3[数据转换]
        
        D[Repository层]
        D1[数据访问]
        D2[缓存管理]
        D3[持久化]
    end
    
    subgraph "telecom-api"
        E[DTO层]
        E1[数据传输对象]
        E2[枚举定义]
        E3[常量定义]
    end
    
    subgraph "External"
        F[外部服务]
        F1[Database]
        F2[Redis]
        F3[Message Queue]
    end
    
    A --> B
    B --> C
    C --> D
    D --> F
    
    A -.-> E
    B -.-> E
    C -.-> E
```

### 分层职责详述

#### 1. Controller层（控制层）
**位置**: `telecom-service/src/main/java/com/patsnap/*/controller/`

**职责**:
- 接收HTTP请求并进行参数校验
- 调用Manager层进行业务处理
- 封装响应结果并返回

**代码示例**:
```java
@RestController
@RequestMapping("/telecom/search-result")
public class TelecomSearchResultController {
    
    @Autowired
    private TelecomViewManager telecomViewManager;
    
    @PostMapping("/data-list")
    public CommonResponse<TelecomDataListPagination> getDataList(
            @RequestBody @Valid CommonRequestDTO requestDTO) {
        
        TelecomDataListPagination result = telecomViewManager
            .getTelecomResultPageListData(requestDTO);
        return CommonResponse.success(result);
    }
}
```

#### 2. Manager层（管理层）
**位置**: `telecom-core/src/main/java/com/patsnap/*/manager/`

**职责**:
- 业务流程编排和协调
- 事务边界管理
- 异常处理和转换

**代码示例**:
```java
@Component
public class TelecomViewManager {
    
    @Autowired
    private SearchApiService searchApiService;
    
    @Autowired
    private FieldDistributeService fieldDistributeService;
    
    public TelecomDataListPagination getTelecomResultPageListData(
            CommonRequestDTO commonRequestDTO) {
        try {
            return doGetTelecomDataListPagination(commonRequestDTO);
        } catch (Exception e) {
            log.error("获取电信数据列表失败", e);
            return createEmptyPagination(commonRequestDTO);
        }
    }
}
```

#### 3. Service层（服务层）
**位置**: `telecom-core/src/main/java/com/patsnap/*/service/`

**职责**:
- 核心业务逻辑实现
- 外部服务集成
- 数据格式转换

**代码示例**:
```java
@Service
public class SearchApiServiceImpl implements SearchApiService {
    
    @Autowired
    private SearchApiClient searchApiClient;
    
    @Override
    public TelecomQueryResultBO searchTelecomData(TelecomQuery query) {
        SearchRequest request = buildSearchRequest(query);
        SearchResponse response = searchApiClient.search(request);
        return convertToBusinessObject(response);
    }
}
```

#### 4. Repository层（数据访问层）
**位置**: `telecom-core/src/main/java/com/patsnap/*/repository/`

**职责**:
- 数据持久化操作
- 缓存管理
- 数据访问抽象

**代码示例**:
```java
@Service
public class AnalyticsAiTaskHistoryServiceImpl 
    extends ServiceImpl<AnalyticsAiTaskHistoryMapper, AnalyticsAiTaskHistoryPO>
    implements AnalyticsAiTaskHistoryService {
    
    @Override
    public AnalyticsAiTaskHistoryPO getByTaskId(String taskId) {
        return getOne(new LambdaQueryWrapper<AnalyticsAiTaskHistoryPO>()
            .eq(AnalyticsAiTaskHistoryPO::getId, taskId));
    }
}
```

## 关键包说明

### Analytics领域包结构

#### 1. `com.patsnap.analytics.frame`
**用途**: 框架层组件，提供通用的基础设施

```
frame/
├── gateway/                    # 防腐层
│   ├── SearchApiGateway.java   # 搜索API网关
│   └── PatentApiGateway.java   # 专利API网关
├── assembly/                   # 参数组装
│   ├── TelecomDataAssembly.java # 电信数据组装器
│   └── QueryParameterAssembly.java # 查询参数组装器
└── validator/                  # 校验器
    ├── PaginationRequestValidator.java # 分页参数校验
    └── QueryConditionValidator.java   # 查询条件校验
```

#### 2. `com.patsnap.analytics.manager`
**用途**: 业务管理器，协调多个服务完成复杂业务流程

主要类:
- `TelecomViewManager.java` - 电信数据视图管理
- `FieldDistributeService.java` - 字段分发服务
- `HighlightService.java` - 高亮处理服务

#### 3. `com.patsnap.analytics.service`
**用途**: 核心业务服务实现

```
service/
├── SearchApiService.java       # 搜索API服务接口
├── PatentApiService.java       # 专利API服务接口
├── TelDataService.java         # 电信数据服务接口
└── impl/                       # 服务实现
    ├── SearchApiServiceImpl.java
    ├── PatentApiServiceImpl.java
    └── TelDataServiceImpl.java
```

### Drafting领域包结构

#### 1. `com.patsnap.drafting.frame`
**用途**: Drafting领域的框架组件

```
frame/
├── gateway/                    # AI服务网关
│   ├── ComputeServiceGateway.java # 计算服务网关
│   └── OpenAiServiceGateway.java  # OpenAI服务网关
├── assembly/                   # 任务组装器
│   ├── AiTaskAssembly.java     # AI任务组装器
│   └── ContentAssembly.java    # 内容组装器
└── validator/                  # 内容校验器
    ├── SensitiveContentValidator.java # 敏感内容校验
    └── CreditValidator.java           # 积分校验
```

#### 2. `com.patsnap.drafting.manager`
**用途**: AI相关业务管理器

主要类:
- `AiTaskManager.java` - AI任务生命周期管理
- `AiTranslationManager.java` - AI翻译管理
- `TaskContentManager.java` - 任务内容管理
- `AiSpecificationManager.java` - AI说明书管理

#### 3. `com.patsnap.drafting.service`
**用途**: AI业务服务和外部集成

```
service/
├── AnalyticsAiTaskHistoryService.java    # 任务历史服务
├── AnalyticsAiTaskContentService.java    # 任务内容服务
├── ComputeClient.java                    # 计算服务客户端
├── OpenAiClient.java                     # OpenAI客户端
└── queue/                                # 消息队列服务
    ├── AiTaskMessageHandler.java         # 任务消息处理器
    └── SqsMessageSender.java             # SQS消息发送器
```

### 共享组件包结构

#### 1. `com.patsnap.common.config`
**用途**: 通用配置类

```
config/
├── RedisConfig.java            # Redis配置
├── ExecutorConfig.java         # 线程池配置
├── RetrofitConfig.java         # Retrofit配置
├── MyBatisPlusConfig.java      # MyBatis Plus配置
└── SwaggerConfig.java          # Swagger配置
```

#### 2. `com.patsnap.common.exception`
**用途**: 异常定义和处理

```
exception/
├── ExceptionDefinition.java   # 异常定义枚举
├── BusinessException.java     # 业务异常基类
├── ExternalServiceException.java # 外部服务异常
└── GlobalExceptionHandler.java   # 全局异常处理器
```

#### 3. `com.patsnap.common.utils`
**用途**: 通用工具类

```
utils/
├── JsonMapperManager.java      # JSON转换管理器
├── RedissonUtils.java          # Redisson工具类
├── HttpClientUtils.java        # HTTP客户端工具
└── DateUtils.java              # 日期工具类
```

## 配置文件结构

### 主配置文件
```
src/main/resources/config/
├── application.properties              # 主配置文件
├── application-local.properties        # 本地环境配置
├── application-ci.properties          # CI环境配置
├── application-qa.properties          # QA环境配置
├── application-stage.properties       # 预生产环境配置
├── application-aws_eu.properties      # AWS欧洲区配置
├── application-aws_us.properties      # AWS美国区配置
└── application-tx_cn.properties       # 腾讯云中国区配置
```

### 配置文件内容分类

#### 1. 应用基础配置
```properties
# 应用基础信息
spring.application.name=s-analytics-telecom
server.servlet.context-path=/telecom
server.port=8080

# Spring Boot配置
spring.profiles.active=local
spring.main.allow-bean-definition-overriding=true
```

#### 2. 数据库配置
```properties
# PostgreSQL配置
spring.datasource.url=****************************************
spring.datasource.username=${DB_USERNAME:telecom}
spring.datasource.password=${DB_PASSWORD:password}
spring.datasource.driver-class-name=org.postgresql.Driver

# MyBatis Plus配置
mybatis-plus.mapper-locations=classpath:mapper/**/*.xml
mybatis-plus.type-aliases-package=com.patsnap.*.repository.*.entity
```

#### 3. Redis配置
```properties
# Redis配置
spring.redis.host=${REDIS_HOST:localhost}
spring.redis.port=${REDIS_PORT:6379}
spring.redis.password=${REDIS_PASSWORD:}
spring.redis.database=0
spring.redis.timeout=5000ms
```

#### 4. 外部服务配置
```properties
# AI服务配置
com.patsnap.analytics.service.gpt.base-url=http://s-patsnaprd-gateway.patsnaprd/v1
com.patsnap.analytics.service.ai-search-url=http://s-search-ai-search.search
com.patsnap.analytics.service.ai-agent-url=http://s-core-agents-service.core

# 数据API配置
configs.com.patsnap.patent_api.url=http://s-platform-patent-api.platform/openapi/3.2.0
com.patsnap.analytics.service.search-api-url=http://s-search-search-api.search/searchapi/3.0.0
```

### MyBatis映射文件
```
src/main/resources/mapper/
├── analytics/
│   ├── AnalyticsWebQueryMapper.xml
│   └── AnalyticsTelecomReadStatusMapper.xml
└── drafting/
    ├── AnalyticsAiTaskHistoryMapper.xml
    ├── AnalyticsAiTaskContentMapper.xml
    └── AnalyticsAiTranslationTermListMapper.xml
```

## 测试结构

### 测试目录结构
```
src/test/
├── java/com/patsnap/
│   ├── analytics/
│   │   ├── manager/
│   │   │   └── TelecomViewManagerTest.java
│   │   ├── service/
│   │   │   └── SearchApiServiceTest.java
│   │   └── integration/
│   │       └── TelecomSearchIntegrationTest.java
│   ├── drafting/
│   │   ├── manager/
│   │   │   └── AiTaskManagerTest.java
│   │   ├── service/
│   │   │   └── AiTaskServiceTest.java
│   │   └── integration/
│   │       └── AiTaskIntegrationTest.java
│   └── common/
│       └── BaseTest.java
└── resources/
    ├── application-test.properties
    └── test-data/
        ├── analytics/
        └── drafting/
```

### 测试分类

#### 1. 单元测试
**目标**: 测试单个组件的功能
```java
@ExtendWith(MockitoExtension.class)
class TelecomViewManagerTest {
    
    @Mock
    private SearchApiService searchApiService;
    
    @InjectMocks
    private TelecomViewManager telecomViewManager;
    
    @Test
    void shouldReturnDataListWhenValidRequest() {
        // 测试正常情况
    }
}
```

#### 2. 集成测试
**目标**: 测试组件间的协作
```java
@SpringBootTest
@ActiveProfiles("test")
class TelecomSearchIntegrationTest {
    
    @Autowired
    private TelecomSearchResultController controller;
    
    @Test
    void shouldIntegrateWithExternalServices() {
        // 测试集成流程
    }
}
```

#### 3. 性能测试
**目标**: 测试系统性能指标
```java
@Test
void shouldHandleConcurrentRequests() {
    // 并发性能测试
}
```

## 构建和部署

### Maven构建配置
```xml
<!-- 父级POM构建配置 -->
<build>
    <plugins>
        <plugin>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-maven-plugin</artifactId>
        </plugin>
        <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-compiler-plugin</artifactId>
            <configuration>
                <source>17</source>
                <target>17</target>
            </configuration>
        </plugin>
    </plugins>
</build>
```

### Docker配置
```dockerfile
FROM openjdk:17-jre-slim

COPY telecom-service/target/telecom-service-*.jar app.jar

EXPOSE 8080

ENTRYPOINT ["java", "-jar", "/app.jar"]
```

### 部署脚本
```bash
#!/bin/bash
# 构建和部署脚本

# 1. 构建项目
mvn clean install -Pdocker

# 2. 构建Docker镜像
docker build -t telecom-service:latest .

# 3. 部署到Kubernetes
kubectl apply -f k8s/deployment.yaml
```

## 总结

S-Analytics-Telecom项目采用了成熟的多模块架构设计，具有以下特点：

### 架构优势
1. **模块化设计**: 清晰的模块边界和职责分离
2. **分层架构**: 标准的三层架构模式
3. **领域驱动**: 明确的业务领域划分
4. **配置管理**: 多环境配置支持
5. **测试完善**: 完整的测试分类和策略

### 扩展性
1. **水平扩展**: 支持多实例部署
2. **功能扩展**: 插件化的处理器架构
3. **集成扩展**: 标准化的外部服务集成
4. **配置扩展**: 灵活的环境配置管理

### 维护性
1. **代码组织**: 清晰的包结构和命名规范
2. **文档完善**: 详细的代码注释和文档
3. **监控支持**: 完善的日志和监控机制
4. **测试覆盖**: 多层次的测试策略
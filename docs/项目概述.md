# 项目概述

## 项目背景

S-Analytics-Telecom是智慧芽（PatSnap）公司开发的电信领域专利分析与起草平台。该系统主要服务于电信行业的专利分析师和专利工程师，提供专利数据检索、分析和AI驱动的专利起草功能。

## 项目目标

1. **专利数据分析**：为电信行业提供高效的专利数据检索、处理和分析能力
2. **智能专利起草**：利用AI技术自动生成专利说明书、技术报告等文档
3. **并行处理能力**：通过分布式处理提升大规模数据处理性能
4. **多格式支持**：支持多种文档格式的导入导出和处理

## 功能概述

### Analytics（分析）领域功能
- **专利数据检索**：通过SearchApi和PatentApi进行专利数据查询
- **字段值转换**：支持专利字段数据的格式转换和扩展
- **并行数据处理**：通过DistributeService实现大规模数据的并行处理
- **数据导出**：支持多种格式的分析结果导出

### Drafting（起草）领域功能
- **AI专利起草**：基于GPT等AI模型自动生成专利说明书
- **技术报告生成**：自动生成技术分析报告
- **文档翻译**：支持专利文档的多语言翻译
- **图像处理**：处理专利申请中的图像和图表
- **文档格式转换**：支持Word、PDF等多种文档格式处理

## 技术栈

### 开发语言与框架
- **Java 17**：项目主要开发语言
- **Spring Boot**：应用框架，使用Jetty作为Web服务器
- **Maven**：项目构建和依赖管理工具

### 数据存储
- **PostgreSQL**：主数据库，存储业务数据
- **Redis/Redisson**：缓存和分布式锁服务
- **MyBatis Plus**：ORM框架，简化数据库操作

### 外部服务集成
- **Retrofit 2.9.0**：HTTP客户端，用于调用外部API
- **Apache POI 4.1.2**：文档处理库，支持Excel、Word等格式
- **AWS SQS**：消息队列服务，用于异步任务处理
- **S3/COS**：对象存储服务，用于文件存储

### 测试与质量保障
- **JUnit 5**：单元测试框架
- **Mockito 5.3.1**：Mock测试工具
- **Spring Boot Test**：集成测试支持

## 架构类型

该项目采用**分层架构模式**，**非GBF框架**。项目遵循领域驱动设计（DDD）原则，具有清晰的业务领域边界和分层结构。

### 模块划分
- **telecom-parent**：父级依赖管理模块
- **telecom-api**：DTOs、领域模型和API契约定义
- **telecom-core**：业务逻辑、服务和数据访问层
- **telecom-service**：REST控制器和主Spring Boot应用

### 业务领域
1. **Analytics**（分析领域）：专注于专利数据的检索、处理和分析
2. **Drafting**（起草领域）：专注于AI驱动的专利文档生成和处理

## 部署环境

项目支持多环境部署：
- **local**：本地开发环境
- **ci**：持续集成环境
- **qa**：测试环境
- **stage**：预生产环境
- **aws_eu**：AWS欧洲区域生产环境
- **aws_us**：AWS美国区域生产环境
- **tx_cn**：腾讯云中国区域生产环境

## 应用入口

- **主应用类**：`com.patsnap.Application`
- **应用上下文路径**：`/telecom`
- **默认端口**：由Spring Boot配置决定
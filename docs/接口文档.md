# 接口文档

## 目录
1. [接口总览](#接口总览)
2. [Analytics领域接口](#analytics领域接口)
3. [Drafting领域接口](#drafting领域接口)
4. [外部服务接口](#外部服务接口)
5. [通用响应格式](#通用响应格式)
6. [错误码说明](#错误码说明)

## 接口总览

### 基本信息
- **基础路径**: `/telecom`
- **协议**: HTTP/HTTPS
- **数据格式**: JSON
- **字符编码**: UTF-8
- **接口总数**: 188个REST接口

### 接口统计
| 业务领域 | Controller数量 | 接口数量 | 说明 |
|----------|----------------|----------|------|
| Analytics | 4 | 32 | 电信数据分析检索 |
| Drafting | 17 | 156 | AI专利起草服务 |
| **总计** | **21** | **188** | - |

### 请求头要求
| 请求头 | 是否必需 | 说明 |
|--------|----------|------|
| X-PatSnap-From | 是 | 请求来源标识 |
| X-User-Id | 否 | 用户标识 |
| Content-Type | 是 | application/json |

## Analytics领域接口

### 权限管理

#### 权限检查
```
GET /telecom/permission/check
```

**功能描述**: 检查用户权限

**请求参数**: 无

**响应示例**:
```json
{
  "status": true,
  "data": {
    "hasPermission": true
  }
}
```

---

### 查询转换服务

**控制器**: `QueryTransferController`  
**基础路径**: `/telecom/query`

#### 1. 查询转换
```
POST /telecom/query/transfer
```

**功能描述**: 转换查询条件为查询ID

**请求参数**:
```json
{
  "query": "查询条件字符串",
  "filters": {
    "field1": "value1",
    "field2": "value2"
  }
}
```

**响应示例**:
```json
{
  "status": true,
  "data": {
    "queryId": "uuid-generated-id",
    "cacheKey": "cache-key"
  }
}
```

#### 2. 关键词提取
```
POST /telecom/query/keywords
```

**功能描述**: 获取查询的高亮关键词

**请求参数**:
```json
{
  "queryId": "uuid-generated-id",
  "content": "要高亮的内容"
}
```

#### 3. 会议建议
```
POST /telecom/query/meeting-suggest
```

**功能描述**: 获取会议建议提示补全

#### 4. 标准贡献者建议
```
POST /telecom/query/std-source-suggest
```

**功能描述**: 获取标准贡献者建议提示补全

#### 5. 查询统计
```
POST /telecom/query/count
```

**功能描述**: 获取查询数量统计

---

### 搜索结果服务

**控制器**: `TelecomSearchResultController`  
**基础路径**: `/telecom/search-result`

#### 1. 数据列表
```
POST /telecom/search-result/data-list
```

**功能描述**: 获取电信数据列表

**请求参数**:
```json
{
  "queryId": "uuid-generated-id",
  "page": 1,
  "size": 20,
  "sort": "relevance",
  "filters": {}
}
```

**响应示例**:
```json
{
  "status": true,
  "data": {
    "records": [
      {
        "id": "doc-id",
        "title": "文档标题",
        "abstract": "摘要内容",
        "publishDate": "2023-01-01",
        "source": "来源"
      }
    ],
    "total": 1000,
    "current": 1,
    "size": 20
  }
}
```

#### 2. 特定数据
```
POST /telecom/search-result/specific-data
```

**功能描述**: 获取特定数据信息

#### 3. 过滤器相关
```
POST /telecom/search-result/filter/selected    # 获取结构化查询过滤
POST /telecom/search-result/parse-query        # 解析结构化查询
GET  /telecom/search-result/filter/field-definition  # 获取字段定义
POST /telecom/search-result/filter/ranking     # 获取排名过滤
```

---

### 详情查看服务

**控制器**: `TelecomViewDetailController`  
**基础路径**: `/telecom/view`

#### 1. 摘要信息
```
POST /telecom/view/abstract
```

**功能描述**: 获取文档摘要信息

**请求参数**:
```json
{
  "docId": "document-id",
  "queryId": "query-id"
}
```

#### 2. 全文信息
```
POST /telecom/view/fulltext
```

**功能描述**: 获取文档全文信息

#### 3. 结构化全文
```
POST /telecom/view/fulltext-struct
```

**功能描述**: 获取结构化全文数据

#### 4. 图片批量获取
```
POST /telecom/view/fulltext-images
```

**功能描述**: 批量获取文档图片

#### 5. PDF信息
```
POST /telecom/view/pdf
```

**功能描述**: 获取PDF格式数据

#### 6. 其他功能
```
POST /telecom/view/data-list           # 获取数据列表
POST /telecom/view/read               # 标记为已读
POST /telecom/view/fulltext/download  # 全文下载
POST /telecom/view/similar-tdocs      # 获取相似文档
```

## Drafting领域接口

### AI专利说明书

**控制器**: `AiSpecificationController`  
**基础路径**: `/telecom/ai-specification`

#### 核心接口列表

| 接口路径 | HTTP方法 | 功能描述 |
|----------|----------|----------|
| `/language-detection` | POST | 语言检测 |
| `/task-init` | POST | 任务初始化 |
| `/classification` | POST | 获取分类号 |
| `/content-generation` | POST | 内容生成（流式） |
| `/embodiment-generation` | POST | 实施例生成 |
| `/image-upload` | POST | 图片上传 |
| `/format-claims` | POST | 权利要求格式化 |

#### 示例：内容生成接口
```
POST /telecom/ai-specification/content-generation
```

**功能描述**: 流式生成专利说明书内容

**请求参数**:
```json
{
  "taskId": "task-uuid",
  "contentType": "SPECIFICATION_CONTENT",
  "prompt": "生成内容的提示",
  "streaming": true
}
```

**响应格式**: Server-Sent Events (SSE)
```
data: {"type":"content","data":"生成的内容片段"}
data: {"type":"complete","data":"generation completed"}
```

---

### AI翻译服务

**控制器**: `AiTranslationController`  
**基础路径**: `/telecom/ai-translation`

#### 核心接口列表

| 接口路径 | HTTP方法 | 功能描述 |
|----------|----------|----------|
| `/language-detection` | POST | 语言检测 |
| `/task-create` | POST | 创建翻译任务 |
| `/translation-sync` | POST | 同步翻译 |
| `/translation-async` | POST | 异步翻译 |
| `/term-list` | GET/POST | 术语表管理 |
| `/export` | POST | 导出翻译结果 |

---

### AI FTO侵权分析

**控制器**: `AiFtoSearchController`  
**基础路径**: `/telecom/fto-search`

#### 核心流程接口

| 接口路径 | HTTP方法 | 功能描述 |
|----------|----------|----------|
| `/task-submit` | POST | 提交FTO任务 |
| `/feature-extraction` | POST | 特征提取 |
| `/text-summary` | POST | 文本总结 |
| `/tech-feature-comparison` | POST | 技术特征对比 |
| `/agent-execute` | POST | 执行检索agent |
| `/similar-patents` | GET | 获取相似专利列表 |
| `/report-generation` | POST | 生成分析报告 |

---

### AI查新检索

**控制器**: `AiNoveltySearchController`  
**基础路径**: `/telecom/novelty-search`

#### 核心流程接口

| 接口路径 | HTTP方法 | 功能描述 |
|----------|----------|----------|
| `/task-submit` | POST | 提交查新任务 |
| `/task-reset` | POST | 重置任务 |
| `/feature-extraction-confirm` | POST | 确认特征提取 |
| `/search-elements` | GET/POST | 检索要素管理 |
| `/agent-execute` | POST | 执行检索agent |
| `/comparison-literature` | GET | 对比文献分析 |
| `/evaluation-report` | POST | 生成评述报告 |

---

### 任务管理服务

**控制器**: `AiApplicationHistoryTaskController`  
**基础路径**: `/telecom/ai/task`

#### 通用任务接口

| 接口路径 | HTTP方法 | 功能描述 |
|----------|----------|----------|
| `/create` | POST | 创建任务 |
| `/content/{taskId}` | GET | 获取任务内容 |
| `/content/{taskId}` | PUT | 更新任务内容 |
| `/history` | GET | 获取历史记录 |
| `/export/{taskId}` | GET | 导出任务结果 |
| `/status/{taskId}` | GET | 获取任务状态 |

---

### 其他重要服务

#### 专利信息服务 (`/telecom/patent`)
- `POST /description` - 获取专利描述
- `POST /claims` - 获取权利要求
- `POST /batch-info` - 批量获取专利信息
- `GET /ipc-detail` - IPC分类详情

#### 图像检索服务 (`/telecom/image-search/tool`)
- `POST /single-image-similar` - 单图相似检索
- `POST /multi-image-similar` - 多图相似检索
- `POST /feature-comparison` - 特征对比
- `POST /report-generation` - 生成检索报告

#### 技术简报服务 (`/telecom/tech-report`)
- `POST /demand-identification` - 需求识别
- `POST /feature-extraction` - 特征提取
- `POST /enterprise-recommendation` - 企业技术推荐
- `POST /preview-generation` - 预览生成

## 外部服务接口

### 主要外部服务客户端

#### 1. AgentApi - Agent平台服务
```java
@Headers({"Content-Type: application/json"})
public interface AgentApi {
    @POST("/agent/execute")
    Call<AgentResponse> executeAgent(@Body AgentRequest request);
}
```

#### 2. AiSearchApi - AI搜索服务
```java
public interface AiSearchApi {
    @POST("/fto/search")
    Call<FtoSearchResponse> ftoSearch(@Body FtoSearchRequest request);
    
    @POST("/novelty/search")
    Call<NoveltySearchResponse> noveltySearch(@Body NoveltySearchRequest request);
}
```

#### 3. OpenApi - 开放API服务
```java
public interface OpenApi {
    @GET("/patent/info")
    Call<PatentInfo> getPatentInfo(@Query("patentId") String patentId);
}
```

### 外部服务配置

```properties
# AI服务配置
com.patsnap.analytics.service.gpt.base-url=http://s-patsnaprd-gateway.patsnaprd/v1
com.patsnap.analytics.service.ai-search-url=http://s-search-ai-search.search
com.patsnap.analytics.service.ai-agent-url=http://s-core-agents-service.core

# 数据服务配置
configs.com.patsnap.patent_api.url=http://s-platform-patent-api.platform/openapi/3.2.0
com.patsnap.analytics.service.search-api-url=http://s-search-search-api.search/searchapi/3.0.0

# 计算服务配置
configs.com.patsnap.compute.specification.url=http://s-patsnaprd-patent-drafting.patsnaprd/compute/patent_drafting/
com.patsnap.analytics.service.ai-novelty-search-compute-url=http://s-patsnaprd-novelty-ai-search.patsnaprd/compute
```

## 通用响应格式

### 成功响应
```json
{
  "status": true,
  "data": {
    // 响应数据
  },
  "errorMsg": null
}
```

### 错误响应
```json
{
  "status": false,
  "data": null,
  "errorMsg": "错误描述信息"
}
```

### 分页响应
```json
{
  "status": true,
  "data": {
    "records": [
      // 数据记录列表
    ],
    "total": 1000,
    "size": 20,
    "current": 1,
    "pages": 50
  }
}
```

### 流式响应（SSE）
```
Content-Type: text/event-stream
Cache-Control: no-cache
Connection: keep-alive

data: {"type":"content","data":"内容片段"}

data: {"type":"progress","data":{"current":1,"total":10}}

data: {"type":"complete","data":"处理完成"}

data: {"type":"error","data":"错误信息"}
```

## 错误码说明

### 系统级错误码

| 错误码 | 错误信息 | 说明 |
|--------|----------|------|
| 400 | Bad Request | 请求参数错误 |
| 401 | Unauthorized | 未授权访问 |
| 403 | Forbidden | 权限不足 |
| 404 | Not Found | 资源不存在 |
| 500 | Internal Server Error | 服务器内部错误 |

### 业务级错误码

| 错误码 | 错误信息 | 说明 |
|--------|----------|------|
| 1001 | 积分不足 | 用户积分余额不足 |
| 1002 | 任务不存在 | 指定的任务ID不存在 |
| 1003 | 任务状态异常 | 任务状态不允许当前操作 |
| 1004 | 内容生成失败 | AI内容生成过程中出现错误 |
| 1005 | 翻译服务异常 | 翻译服务不可用或返回错误 |
| 1006 | 文件上传失败 | 文件上传过程中出现错误 |
| 1007 | 数据访问异常 | 数据库或缓存访问异常 |

### 外部服务错误码

| 错误码 | 错误信息 | 说明 |
|--------|----------|------|
| 2001 | 搜索服务异常 | 外部搜索API服务异常 |
| 2002 | AI服务异常 | 外部AI服务异常 |
| 2003 | 专利API异常 | 专利数据API服务异常 |
| 2004 | Agent服务异常 | Agent平台服务异常 |

## 接口调用示例

### 创建AI说明书任务
```bash
curl -X POST "http://localhost:8080/telecom/ai-specification/task-init" \
  -H "Content-Type: application/json" \
  -H "X-PatSnap-From: web" \
  -H "X-User-Id: user123" \
  -d '{
    "disclosureContent": "专利交底书内容",
    "jurisdiction": "CNIPA",
    "language": "zh-CN"
  }'
```

### 执行FTO分析
```bash
curl -X POST "http://localhost:8080/telecom/fto-search/task-submit" \
  -H "Content-Type: application/json" \
  -H "X-PatSnap-From: web" \
  -H "X-User-Id: user123" \
  -d '{
    "productDescription": "产品描述",
    "technicalFeatures": ["特征1", "特征2"],
    "jurisdiction": "US"
  }'
```

### 查询电信数据
```bash
curl -X POST "http://localhost:8080/telecom/search-result/data-list" \
  -H "Content-Type: application/json" \
  -H "X-PatSnap-From: web" \
  -H "X-User-Id: user123" \
  -d '{
    "queryId": "generated-query-id",
    "page": 1,
    "size": 20,
    "sort": "relevance"
  }'
```

## 注意事项

1. **请求头验证**: 所有接口都需要包含`X-PatSnap-From`请求头
2. **流式接口**: AI生成类接口支持流式响应，客户端需要支持SSE
3. **异步处理**: 部分接口采用异步处理，需要通过任务ID查询结果
4. **积分控制**: AI相关接口会消耗用户积分，需要进行额度检查
5. **错误处理**: 建议客户端实现完整的错误处理和重试机制
6. **缓存策略**: 查询结果会被缓存，相同查询可以快速返回结果
# 业务流程说明

## 目录
1. [业务流程概述](#业务流程概述)
2. [Analytics领域业务流程](#analytics领域业务流程)
3. [Drafting领域业务流程](#drafting领域业务流程)
4. [跨领域协作流程](#跨领域协作流程)
5. [异常处理机制](#异常处理机制)
6. [性能优化策略](#性能优化策略)
7. [监控和审计](#监控和审计)

## 业务流程概述

S-Analytics-Telecom项目围绕两个核心业务领域构建：**Analytics（电信数据分析）**和**Drafting（AI专利起草）**，提供从专利数据检索、分析到AI辅助起草的完整业务闭环。

### 核心业务特点
- **领域驱动**：明确的业务边界和职责分离
- **异步处理**：支持长时间运行的AI任务
- **并行优化**：多字段并行处理提升性能
- **状态管理**：完善的任务生命周期管理
- **外部集成**：与多个外部AI和数据服务深度集成

## Analytics领域业务流程

### 电信数据检索与分析流程

#### 主流程图
```mermaid
flowchart TD
    A[用户发起查询] --> B[参数校验]
    B --> C[查询条件转换]
    C --> D[调用SearchAPI]
    D --> E[并行字段处理]
    E --> F[数据组装]
    F --> G[缓存高亮信息]
    G --> H[返回结果]
    
    subgraph 并行处理
        E1[字段处理器1]
        E2[字段处理器2]
        E3[字段处理器N]
    end
    
    E --> E1
    E --> E2
    E --> E3
    
    style E fill:#e1f5fe
    style F fill:#f3e5f5
```

#### 详细流程步骤

**第一阶段：请求预处理**
1. **入口点**: `TelecomSearchResultController.getTelecomResultPageListData()`
2. **参数校验**: 验证分页参数、查询条件格式
3. **权限检查**: 验证用户访问权限和积分余额

**第二阶段：查询执行**
```java
// 关键调用链
TelecomViewManager.doGetTelecomDataListPagination()
  ↓
SearchApiService.searchTelecomData()  // 调用外部搜索API
  ↓
TelecomQueryResultBO.buildFromResponse()  // 构建业务对象
```

**第三阶段：并行字段处理**
```mermaid
sequenceDiagram
    participant Manager as TelecomViewManager
    participant Distribute as FieldDistributeService
    participant Processor as FieldProcessor
    participant API as ExternalAPI
    
    Manager->>Distribute: submitFields(fields)
    
    par 并行处理
        Distribute->>Processor: processField1
        Processor->>API: callExternalService
        API-->>Processor: fieldData1
    and
        Distribute->>Processor: processField2
        Processor->>API: callExternalService
        API-->>Processor: fieldData2
    and
        Distribute->>Processor: processField3
        Processor->>API: callExternalService
        API-->>Processor: fieldData3
    end
    
    Processor-->>Distribute: mergedFieldData
    Distribute-->>Manager: processedResults
```

**第四阶段：数据组装与返回**
- **数据转换**: 通过`TelecomDataAssembly`进行格式化
- **高亮缓存**: 为用户交互优化设置关键词高亮
- **分页封装**: 构建`TelecomDataListPagination`对象

#### 关键判断点
| 判断点 | 条件 | 处理路径 |
|--------|------|----------|
| 参数校验 | 分页参数无效 | 返回参数错误 |
| 权限检查 | 用户无权限 | 返回权限错误 |
| 字段处理 | 不支持的字段 | 跳过该字段处理 |
| 外部API | 服务异常 | 记录日志，返回默认值 |

### 详情查看流程

#### 全文获取流程
```mermaid
flowchart TD
    A[用户请求全文] --> B[文档ID校验]
    B --> C[缓存检查]
    C --> D{缓存命中?}
    D -->|是| E[返回缓存内容]
    D -->|否| F[调用PatentAPI]
    F --> G[内容格式化]
    G --> H[更新缓存]
    H --> I[标记已读状态]
    I --> J[返回全文内容]
    
    style C fill:#fff3e0
    style I fill:#e8f5e8
```

**调用链路**:
```
TelecomViewDetailController.getFullTextStructData()
  ↓
TelecomViewManager.getTelecomResultFullTextStruct()
  ↓
TelDataService.getFulltextStructData()
  ↓
PatentApiService.getPatentFulltext()
```

## Drafting领域业务流程

### AI任务生命周期管理

#### 任务状态流转图
```mermaid
stateDiagram-v2
    [*] --> Ready : 创建任务
    Ready --> Running : 开始执行
    Running --> Complete : 成功完成
    Running --> Failed : 执行失败
    Running --> Interrupt : 用户中断
    Failed --> Ready : 重试
    Interrupt --> Ready : 重新启动
    Complete --> [*]
    Failed --> [*] : 超过重试次数
    
    state Running {
        [*] --> 敏感词检测
        敏感词检测 --> 积分校验
        积分校验 --> AI服务调用
        AI服务调用 --> 内容生成
        内容生成 --> 结果存储
        结果存储 --> [*]
    }
```

#### 任务创建流程
```mermaid
sequenceDiagram
    participant U as 用户
    participant C as Controller
    participant M as Manager
    participant V as Validator
    participant S as Service
    participant Q as MessageQueue
    
    U->>C: 提交AI任务请求
    C->>V: 敏感词检测
    V-->>C: 检测结果
    C->>M: 创建任务
    M->>S: 保存任务历史
    M->>S: 保存任务内容
    M->>Q: 发送异步消息
    Q-->>U: 返回任务ID
    Q->>S: 异步处理任务
    S->>S: AI服务调用
    S->>M: 更新任务状态
    M-->>U: 推送处理结果
```

### AI专利说明书生成流程

#### 完整生成流程
```mermaid
flowchart TD
    A[用户提交起草请求] --> B[内容安全检测]
    B --> C[积分校验]
    C --> D[任务初始化]
    D --> E[语言检测]
    E --> F[IPC分类获取]
    F --> G[异步任务创建]
    G --> H[AI内容生成]
    H --> I[流式内容输出]
    I --> J[实施例生成]
    J --> K[权利要求格式化]
    K --> L[任务完成]
    
    subgraph 异步处理
        G1[SQS消息队列]
        G2[分布式锁控制]
        G3[上下文设置]
    end
    
    G --> G1
    G1 --> G2
    G2 --> G3
    G3 --> H
    
    style H fill:#e1f5fe
    style I fill:#f3e5f5
```

**关键代码路径**:
```java
// 任务初始化
AiSpecificationController.taskInit()
  ↓
AiSpecificationManager.initTask()
  ↓
AiTaskManager.createTask()
  ↓
// 异步处理
SQSMessageSender.sendMessage()
  ↓
AiTaskMessageHandler.processMessage()
  ↓
ComputeClient.generateSpecification()
```

#### 流式内容生成机制
```java
// 流式响应处理示例
@GetMapping(value = "/content-generation", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
public Flux<String> generateContent(@RequestParam String taskId) {
    return aiSpecificationManager.generateContentStream(taskId)
        .map(content -> "data: " + jsonMapper.writeValueAsString(content) + "\n\n")
        .onErrorResume(throwable -> {
            log.error("内容生成异常", throwable);
            return Flux.just("data: {\"type\":\"error\",\"message\":\"生成失败\"}\n\n");
        });
}
```

### AI翻译服务流程

#### 翻译任务处理流程
```mermaid
flowchart TD
    A[提交翻译请求] --> B[语言自动检测]
    B --> C[创建翻译任务]
    C --> D{同步还是异步?}
    D -->|同步| E[直接调用翻译API]
    D -->|异步| F[任务入队]
    E --> G[返回翻译结果]
    F --> H[后台处理]
    H --> I[调用翻译服务]
    I --> J[保存翻译结果]
    J --> K[通知任务完成]
    
    style E fill:#e8f5e8
    style H fill:#fff3e0
```

**调用链路**:
```
AiTranslationController.createTranslationTask()
  ↓
AiTranslationManager.processTranslation()
  ↓
TranslationService.translate()
  ↓
ExternalTranslationAPI.callTranslationService()
```

### FTO侵权分析流程

#### 完整分析流程
```mermaid
flowchart TD
    A[提交产品描述] --> B[技术特征提取]
    B --> C[特征确认]
    C --> D[专利检索Agent]
    D --> E[相似专利筛选]
    E --> F[特征对比分析]
    F --> G[侵权风险评估]
    G --> H[生成分析报告]
    
    subgraph Agent处理
        D1[检索策略生成]
        D2[多维度检索]
        D3[结果相关性排序]
    end
    
    D --> D1
    D1 --> D2
    D2 --> D3
    D3 --> E
    
    style F fill:#ffebee
    style G fill:#fff3e0
```

## 跨领域协作流程

### 数据流转协作
```mermaid
flowchart LR
    subgraph Analytics["Analytics领域"]
        A1[专利数据检索]
        A2[技术文档分析]
        A3[相似性计算]
    end
    
    subgraph Drafting["Drafting领域"]
        D1[专利起草]
        D2[新颖性分析]
        D3[FTO分析]
    end
    
    subgraph Shared["共享组件"]
        S1[用户权限]
        S2[积分管理]
        S3[缓存服务]
        S4[消息队列]
        S5[审计日志]
    end
    
    A1 --> D1
    A2 --> D2
    A3 --> D3
    
    Analytics -.-> Shared
    Drafting -.-> Shared
```

### 典型协作场景

#### 场景1：从检索到起草
1. **Analytics阶段**: 用户检索相关专利技术
2. **数据传递**: 检索结果作为起草参考
3. **Drafting阶段**: 基于检索结果生成专利说明书
4. **质量验证**: 新颖性检查确保专利质量

#### 场景2：FTO分析流程
1. **产品分析**: 提取产品技术特征
2. **专利检索**: 在Analytics领域检索相关专利
3. **特征对比**: 技术特征与现有专利对比
4. **风险评估**: 生成侵权风险分析报告

## 异常处理机制

### 异常分类处理

#### 1. 系统级异常
```java
// 全局异常处理器
@ControllerAdvice
public class GlobalExceptionHandler {
    
    @ExceptionHandler(ExternalServiceException.class)
    public ResponseEntity<CommonResponse<?>> handleExternalService(ExternalServiceException e) {
        log.error("外部服务异常", e);
        return ResponseEntity.ok(CommonResponse.fail("外部服务暂时不可用"));
    }
    
    @ExceptionHandler(CreditInsufficientException.class)
    public ResponseEntity<CommonResponse<?>> handleCreditInsufficient(CreditInsufficientException e) {
        log.warn("用户积分不足: {}", e.getMessage());
        return ResponseEntity.ok(CommonResponse.fail("积分余额不足"));
    }
}
```

#### 2. 业务级异常处理
```java
// Analytics领域异常处理
public TelecomDataListPagination getTelecomData(CommonRequestDTO request) {
    try {
        return doGetTelecomDataListPagination(request);
    } catch (SearchApiException e) {
        log.error("搜索API异常", e);
        // 返回空结果而非抛出异常
        return new TelecomDataListPagination(request.getPage(), request.getRows(), 0L);
    }
}
```

### 重试机制

#### AI任务重试策略
```java
// 消息处理重试逻辑
private void processMessageWithRetry(Message message) {
    AiTaskBO aiTask = parseMessage(message);
    
    if (aiTask.getRetryCount() >= MAX_RETRY_COUNT) {
        handleMaxRetryReached(aiTask);
        return;
    }
    
    try {
        processTask(aiTask);
    } catch (Exception e) {
        aiTask.incrementRetryCount();
        scheduleRetry(aiTask);
    }
}
```

**重试参数配置**:
- 最大重试次数: 10次
- 重试间隔: 指数退避（1s, 2s, 4s, 8s...）
- 死信队列: 超过重试次数的消息进入死信队列

### 降级策略

#### 服务降级机制
1. **外部服务降级**: API调用失败时返回缓存数据
2. **功能降级**: 非核心功能异常时跳过处理
3. **性能降级**: 高负载时限制并发处理数量

## 性能优化策略

### 并发处理优化

#### 字段并行处理
```java
// 并行字段处理实现
public CompletableFuture<Map<String, JSONObject>> processFieldsParallel(
    List<FieldWrapper> fields, TelecomDataFetchBO fetchBO) {
    
    List<CompletableFuture<Pair<String, JSONObject>>> futures = fields.stream()
        .map(field -> CompletableFuture.supplyAsync(() -> 
            processField(field, fetchBO), commonExecutor))
        .collect(Collectors.toList());
    
    return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
        .thenApply(v -> collectResults(futures));
}
```

#### 线程池配置
```properties
# 通用线程池配置
common.executor.core-pool-size=10
common.executor.max-pool-size=20
common.executor.queue-capacity=100

# 内容更新线程池
update.content.executor.core-pool-size=5
update.content.executor.max-pool-size=10
```

### 缓存优化策略

#### 多级缓存架构
```mermaid
flowchart TD
    A[用户请求] --> B[L1: 本地缓存]
    B --> C{命中?}
    C -->|是| D[返回结果]
    C -->|否| E[L2: Redis缓存]
    E --> F{命中?}
    F -->|是| G[更新L1缓存]
    F -->|否| H[数据库查询]
    G --> D
    H --> I[更新L2缓存]
    I --> G
```

#### 缓存策略配置
```java
// 高亮缓存配置
@CacheConfig(cacheNames = "highlight")
public class HighlightService {
    
    @Cacheable(key = "#queryMd5", unless = "#result == null")
    public Map<String, HighlightMatchDetail> getHighlight(String queryMd5) {
        return computeHighlight(queryMd5);
    }
}
```

### 数据库优化

#### 索引优化策略
```sql
-- 任务历史表索引
CREATE INDEX idx_task_user_type ON analytics_ai_task_history(user_id, task_type, create_time);
CREATE INDEX idx_task_status ON analytics_ai_task_history(async_status);

-- 任务内容表索引
CREATE INDEX idx_content_task_type ON analytics_ai_task_content(task_id, content_type);
```

#### 分页查询优化
```java
// 分页查询优化
public IPage<AnalyticsAiTaskHistoryPO> getTaskHistory(String userId, Integer page, Integer size) {
    LambdaQueryWrapper<AnalyticsAiTaskHistoryPO> wrapper = 
        new LambdaQueryWrapper<AnalyticsAiTaskHistoryPO>()
        .eq(AnalyticsAiTaskHistoryPO::getUserId, userId)
        .orderByDesc(AnalyticsAiTaskHistoryPO::getCreateTime);
    
    return page(new Page<>(page, size), wrapper);
}
```

## 监控和审计

### 业务指标监控

#### 关键指标定义
| 指标类型 | 指标名称 | 监控目标 |
|---------|---------|----------|
| 性能指标 | API响应时间 | < 2秒 |
| 业务指标 | 任务成功率 | > 95% |
| 资源指标 | 并发任务数 | < 100 |
| 质量指标 | 错误率 | < 1% |

#### 监控实现
```java
// 自定义指标收集
@Component
public class BusinessMetrics {
    
    private final Counter taskCounter = Counter.build()
        .name("ai_task_total")
        .help("AI任务总数")
        .labelNames("task_type", "status")
        .register();
    
    public void recordTaskCompletion(String taskType, String status) {
        taskCounter.labels(taskType, status).inc();
    }
}
```

### 审计日志记录

#### 用户操作审计
```java
// 操作审计切面
@Aspect
@Component
public class AuditAspect {
    
    @AfterReturning("@annotation(Auditable)")
    public void auditOperation(JoinPoint joinPoint) {
        String userId = getCurrentUserId();
        String operation = joinPoint.getSignature().getName();
        Object[] args = joinPoint.getArgs();
        
        auditLogService.recordOperation(userId, operation, args);
    }
}
```

#### 审计日志格式
```json
{
  "timestamp": "2024-01-01T12:00:00.000Z",
  "userId": "user123",
  "operation": "createAiTask",
  "taskType": "AI_SPECIFICATION",
  "parameters": {
    "taskId": "task-uuid",
    "language": "zh-CN"
  },
  "result": "success",
  "executionTime": 150
}
```

### 告警机制

#### 告警规则配置
```yaml
alerts:
  - name: HighTaskFailureRate
    condition: task_failure_rate > 0.05
    duration: 5m
    notification: ["email", "slack"]
    
  - name: SlowAPIResponse
    condition: api_response_time_p95 > 5s
    duration: 2m
    notification: ["email"]
    
  - name: HighConcurrency
    condition: concurrent_tasks > 80
    duration: 1m
    notification: ["slack"]
```

## 总结

S-Analytics-Telecom项目通过完善的业务流程设计，实现了从数据检索到AI辅助创作的完整业务闭环。系统在异常处理、性能优化、监控审计等方面都有完善的设计，体现了企业级应用的成熟度。

**流程设计亮点**:
1. **并行处理**: 字段处理和AI任务的并行执行机制
2. **状态管理**: 完善的任务生命周期状态流转
3. **异步架构**: 基于消息队列的异步任务处理
4. **容错机制**: 多层次的异常处理和重试策略
5. **性能优化**: 多级缓存和数据库优化策略

**持续改进方向**:
1. **流程可视化**: 增加业务流程的实时监控看板
2. **智能调度**: 基于负载的任务调度优化
3. **预测性维护**: 通过指标预测系统瓶颈
4. **用户体验**: 流程节点的用户反馈收集和优化
# 领域模型说明

## 目录
1. [领域模型概述](#领域模型概述)
2. [核心实体关系图](#核心实体关系图)
3. [Analytics领域模型](#analytics领域模型)
4. [Drafting领域模型](#drafting领域模型)
5. [聚合设计分析](#聚合设计分析)
6. [关键业务场景](#关键业务场景)
7. [数据流转关系](#数据流转关系)

## 领域模型概述

S-Analytics-Telecom项目采用领域驱动设计（DDD）原则，围绕两个核心业务领域构建：

- **Analytics领域**：专注于电信专利数据的检索、分析和处理
- **Drafting领域**：专注于AI驱动的专利文档生成和智能起草

项目采用分层架构，实体分为持久化实体（PO）、业务对象（BO）和数据传输对象（DTO）三个层次，每个层次都有明确的职责边界。

## 核心实体关系图

```mermaid
classDiagram
    class BaseEntityPO {
        +String id
        +String createBy
        +Date createTime
        +String updateBy
        +Date updateTime
        +validateId()
        +setAuditFields()
    }
    
    class AnalyticsWebQueryPO {
        +String queryBodyMd5
        +String userId
        +String queryContent
        +Date queryTime
        +String queryType
        +recordQuery()
        +validateQuery()
    }
    
    class AnalyticsAiTaskHistoryPO {
        +String taskId
        +String userId
        +String taskType
        +String taskStatus
        +String taskVersion
        +Integer stepCount
        +Date createTime
        +startTask()
        +updateStatus()
        +completeTask()
    }
    
    class AnalyticsAiTaskContentPO {
        +String contentId
        +String taskId
        +String contentType
        +String direction
        +String contentValue
        +Integer version
        +Boolean hidden
        +updateContent()
        +hideContent()
    }
    
    class TelecomDataFetchBO {
        +TelecomQuery query
        +List<Field> fields
        +QueryResult result
        +processQuery()
        +transformData()
    }
    
    class AiTaskBO {
        +String taskId
        +AiTaskTypeEnum taskType
        +AsyncTaskStatusEnum status
        +List<TaskStep> steps
        +Map<String, Object> context
        +executeTask()
        +nextStep()
    }
    
    BaseEntityPO <|-- AnalyticsWebQueryPO
    BaseEntityPO <|-- AnalyticsAiTaskHistoryPO
    BaseEntityPO <|-- AnalyticsAiTaskContentPO
    
    AnalyticsAiTaskHistoryPO "1" --> "n" AnalyticsAiTaskContentPO : 包含
    TelecomDataFetchBO --> AnalyticsWebQueryPO : 使用
    AiTaskBO --> AnalyticsAiTaskHistoryPO : 映射
```

## Analytics领域模型

### 核心实体说明

#### BaseEntityPO（基础实体）
**文件路径**: `telecom-core/src/main/java/com/patsnap/analytics/repository/BaseEntityPO.java`

| 属性名 | 类型 | 说明 |
|--------|------|------|
| id | String | UUID主键，分布式友好 |
| createBy | String | 创建人ID |
| createTime | Date | 创建时间 |
| updateBy | String | 更新人ID |
| updateTime | Date | 更新时间 |

**业务规则**：
- 所有持久化实体的基类，提供统一的审计字段
- ID生成策略使用UUID，避免分布式环境下的主键冲突
- 审计字段支持数据变更追踪

#### AnalyticsWebQueryPO（查询记录实体）
**文件路径**: `telecom-core/src/main/java/com/patsnap/analytics/repository/query/entity/AnalyticsWebQueryPO.java`

| 属性名 | 类型 | 说明 |
|--------|------|------|
| queryBodyMd5 | String | 查询体MD5哈希，用于去重和缓存 |
| userId | String | 用户ID |
| queryContent | String | 查询内容JSON格式 |
| queryTime | Date | 查询时间 |
| queryType | String | 查询类型（web/api） |

**业务规则**：
- queryBodyMd5作为唯一性约束，防止重复查询
- 查询内容以JSON格式存储，支持复杂查询条件
- 支持查询历史追踪和用户行为分析

#### AnalyticsTelecomReadStatusPO（阅读状态实体）
**文件路径**: `telecom-core/src/main/java/com/patsnap/analytics/repository/view/entity/AnalyticsTelecomReadStatusPO.java`

| 属性名 | 类型 | 说明 |
|--------|------|------|
| userId | String | 用户ID |
| patentId | String | 专利ID |
| readStatus | Integer | 阅读状态（0-未读，1-已读） |
| readTime | Date | 阅读时间 |

**业务规则**：
- 跟踪用户对专利文档的阅读状态
- 支持已读/未读状态切换
- 用于用户体验优化和数据分析

### 字段处理模型

#### Field（字段定义）
```java
public class Field {
    private String fieldName;        // 字段名称
    private String fieldPath;        // 字段路径
    private Type fieldType;          // 字段类型
    private List<String> values;     // 字段值列表
}
```

#### FieldWrapper（字段包装器）
```java
public class FieldWrapper {
    private Field field;                    // 基础字段
    private String convertService;          // 转换服务名
    private FieldExpand expand;            // 展开配置
    private Boolean parallelSupport;       // 并行支持
}
```

**字段类型枚举**：
- LIST：列表类型
- MAP：映射类型  
- STRING：字符串类型
- INTEGER：整数类型
- BOOLEAN：布尔类型
- NULL：空值类型

## Drafting领域模型

### AI任务体系

#### AnalyticsAiTaskHistoryPO（AI任务历史）
**文件路径**: `telecom-core/src/main/java/com/patsnap/drafting/repository/aitask/entity/AnalyticsAiTaskHistoryPO.java`

| 属性名 | 类型 | 说明 |
|--------|------|------|
| taskId | String | 任务唯一标识 |
| userId | String | 用户ID |
| taskType | String | 任务类型枚举值 |
| taskStatus | String | 任务状态（Ready/Running/Complete） |
| taskVersion | String | 任务版本号 |
| stepCount | Integer | 步骤总数 |
| currentStep | Integer | 当前步骤 |
| dataVersion | String | 数据版本，支持兼容性检查 |

**业务规则**：
- 任务状态严格按照生命周期流转
- 支持版本控制，确保数据兼容性
- 步骤管理支持任务中断和恢复

#### AnalyticsAiTaskContentPO（AI任务内容）
**文件路径**: `telecom-core/src/main/java/com/patsnap/drafting/repository/aitask/entity/AnalyticsAiTaskContentPO.java`

| 属性名 | 类型 | 说明 |
|--------|------|------|
| contentId | String | 内容唯一标识 |
| taskId | String | 关联任务ID |
| contentType | String | 内容类型（100+种类型） |
| direction | String | 内容方向（INPUT/OUTPUT/CONFIG） |
| contentValue | String | 内容值（JSON格式） |
| version | Integer | 内容版本号 |
| hidden | Boolean | 是否隐藏显示 |

**业务规则**：
- 内容按方向分类：用户输入、系统输出、配置参数
- 支持内容版本控制和增量更新
- 隐藏机制支持敏感内容保护

### 任务类型体系

```mermaid
classDiagram
    class AiTaskTypeEnum {
        <<enumeration>>
        AI_TRANSLATION
        AI_PATENT_DISCLOSURE
        AI_SPECIFICATION
        AI_NOVELTY_SEARCH
        AI_FTO_SEARCH
        AI_TECH_REPORT
        AI_COPILOT
        +getDescription()
        +getCreditCost()
        +getDataVersion()
    }
    
    class AiTaskContentTypeEnum {
        <<enumeration>>
        DISCLOSURE_CONTENT
        SPECIFICATION_CONTENT
        TRANSLATION_CONTENT
        SEARCH_RESULT
        FEATURE_TREE
        CLAIM_CONTENT
        +getStorageObjectType()
        +getCreditValue()
        +getDirection()
    }
    
    class AsyncTaskStatusEnum {
        <<enumeration>>
        Ready
        Running
        Complete
        Failed
        +isTerminal()
        +canTransitionTo()
    }
    
    AiTaskTypeEnum --> AiTaskContentTypeEnum : 包含
    AiTaskTypeEnum --> AsyncTaskStatusEnum : 使用
```

### 专利起草模型

#### SpecificationTechWrapperBO（技术三要素）
```java
public class SpecificationTechWrapperBO {
    private String technicalProblem;    // 技术问题
    private String technicalMethods;    // 技术手段  
    private String benefit;             // 技术功效
    private String backgroundArt;       // 背景技术
}
```

#### FeatureTreeBO（特征树）
```java
public class FeatureTreeBO {
    private String num;                     // 特征编号
    private String simpleClaim;             // 简化权利要求
    private List<ClaimFeature> claimFeatures; // 权利要求特征
    private FeatureTreeBO parent;           // 父节点
    private List<FeatureTreeBO> children;   // 子节点
}
```

#### ClaimFeatureContentBO（权利要求特征）
```java
public class ClaimFeatureContentBO {
    private String feature;                 // 特征描述
    private List<String> concepts;          // 概念列表
    private Boolean featureDeleted;         // 删除标记
    private String featureType;             // 特征类型
}
```

## 聚合设计分析

### AI任务聚合
**聚合根**: `AnalyticsAiTaskHistoryPO`

```mermaid
classDiagram
    class AITaskAggregate {
        <<aggregate>>
    }
    
    class AnalyticsAiTaskHistoryPO {
        <<aggregate root>>
        +String taskId
        +String taskType
        +String taskStatus
        +manageTaskLifecycle()
        +validateTransition()
    }
    
    class AnalyticsAiTaskContentPO {
        +String contentId
        +String taskId
        +String contentType
        +manageContent()
    }
    
    class AnalyticsAiTranslationTermListPO {
        +String termListId
        +String taskId
        +String terms
        +manageTerms()
    }
    
    class AiTaskShareLinkPO {
        +String linkId
        +String taskId
        +String shareType
        +manageSharing()
    }
    
    AITaskAggregate --> AnalyticsAiTaskHistoryPO
    AnalyticsAiTaskHistoryPO "1" --> "n" AnalyticsAiTaskContentPO
    AnalyticsAiTaskHistoryPO "1" --> "0..n" AnalyticsAiTranslationTermListPO
    AnalyticsAiTaskHistoryPO "1" --> "0..n" AiTaskShareLinkPO
```

**聚合业务规则**：
1. 任务状态变更必须通过聚合根进行
2. 内容的增删改查受任务状态约束
3. 分享链接的有效性依赖于任务状态
4. 术语表仅在翻译任务中有效

### 电信查询聚合
**聚合根**: `AnalyticsWebQueryPO`

```mermaid
classDiagram
    class TelecomQueryAggregate {
        <<aggregate>>
    }
    
    class AnalyticsWebQueryPO {
        <<aggregate root>>
        +String queryBodyMd5
        +String queryContent
        +executeQuery()
        +cacheResult()
    }
    
    class AnalyticsTelecomReadStatusPO {
        +String userId
        +String patentId
        +Integer readStatus
        +updateReadStatus()
    }
    
    TelecomQueryAggregate --> AnalyticsWebQueryPO
    AnalyticsWebQueryPO "1" --> "n" AnalyticsTelecomReadStatusPO
```

## 关键业务场景

### 场景1：AI专利说明书生成流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant C as Controller
    participant S as Service
    participant T as TaskHistory
    participant TC as TaskContent
    
    U->>C: 提交生成请求
    C->>S: 创建AI任务
    S->>T: 初始化任务记录
    T->>TC: 保存输入内容
    S->>S: 执行AI生成
    S->>TC: 保存输出内容
    S->>T: 更新任务状态
    T-->>U: 返回生成结果
```

### 场景2：电信数据查询与分析

```mermaid
sequenceDiagram
    participant U as 用户
    participant API as SearchAPI
    participant Q as QueryPO
    participant F as FieldProcessor
    participant R as ReadStatus
    
    U->>API: 发起查询请求
    API->>Q: 记录查询历史
    API->>F: 并行处理字段
    F->>F: 字段转换与展开
    F-->>API: 返回处理结果
    API->>R: 更新阅读状态
    API-->>U: 返回查询结果
```

## 数据流转关系

### 数据流向图

```mermaid
flowchart TD
    A[用户请求] --> B{请求类型}
    B -->|查询| C[Analytics领域]
    B -->|生成| D[Drafting领域]
    
    C --> E[QueryPO记录]
    C --> F[字段处理器]
    F --> G[外部API调用]
    G --> H[结果转换]
    H --> I[ReadStatus更新]
    
    D --> J[TaskHistory创建]
    J --> K[TaskContent管理]
    K --> L[AI服务调用]
    L --> M[内容生成]
    M --> N[结果存储]
    
    I --> O[响应用户]
    N --> O
    
    style C fill:#e1f5fe
    style D fill:#f3e5f5
    style O fill:#e8f5e8
```

### 领域间协作

1. **共享基础设施**
   - 统一的实体基类（BaseEntityPO）
   - 共享的缓存和存储服务
   - 统一的错误处理和日志记录

2. **业务协作点**
   - Analytics查询结果可作为Drafting的输入
   - 用户权限和积分管理跨领域共享
   - 审计日志统一记录两个领域的操作

3. **数据一致性**
   - 使用事务确保跨表操作的一致性
   - 通过领域事件进行异步数据同步
   - 定期数据校验和修复机制

## 技术约束与业务规则

### 数据约束
1. **唯一性约束**：查询MD5、任务ID、内容ID等关键字段
2. **外键约束**：确保关联实体的引用完整性
3. **状态约束**：任务状态转换必须遵循预定义的流程

### 业务规则
1. **版本兼容性**：数据版本升级时确保向后兼容
2. **积分控制**：不同操作消耗不同积分，实现使用限制
3. **权限管理**：基于用户角色的功能访问控制
4. **内容安全**：敏感内容检测和过滤机制
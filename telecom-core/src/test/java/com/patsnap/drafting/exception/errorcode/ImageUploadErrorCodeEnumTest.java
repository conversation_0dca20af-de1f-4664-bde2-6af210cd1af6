package com.patsnap.drafting.exception.errorcode;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * 图片上传错误码枚举测试类
 *
 * <AUTHOR> Assistant
 * @date 2024/12/19
 */
@DisplayName("图片上传错误码枚举测试")
class ImageUploadErrorCodeEnumTest {

    @Test
    @DisplayName("测试文件相关错误码")
    void testFileRelatedErrorCodes() {
        // 测试文件为空错误
        ImageUploadErrorCodeEnum fileEmpty = ImageUploadErrorCodeEnum.FILE_EMPTY;
        assertEquals(140001, fileEmpty.getNumericErrCode());
        assertEquals("errors.com.patsnap.drafting.image.file_empty", fileEmpty.getErrCode());
        assertEquals("上传的文件为空", fileEmpty.getPattern());

        // 测试文件过大错误
        ImageUploadErrorCodeEnum fileTooLarge = ImageUploadErrorCodeEnum.FILE_TOO_LARGE;
        assertEquals(140002, fileTooLarge.getNumericErrCode());
        assertEquals("errors.com.patsnap.drafting.image.file_too_large", fileTooLarge.getErrCode());
        assertEquals("文件大小超过限制，最大支持1MB", fileTooLarge.getPattern());

        // 测试文件数量超限错误
        ImageUploadErrorCodeEnum fileCountExceeded = ImageUploadErrorCodeEnum.FILE_COUNT_EXCEEDED;
        assertEquals(140003, fileCountExceeded.getNumericErrCode());
        assertEquals("errors.com.patsnap.drafting.image.file_count_exceeded", fileCountExceeded.getErrCode());
        assertEquals("文件数量超过限制，最多支持20个文件", fileCountExceeded.getPattern());

        // 测试不支持的文件类型错误
        ImageUploadErrorCodeEnum fileTypeNotSupported = ImageUploadErrorCodeEnum.FILE_TYPE_NOT_SUPPORTED;
        assertEquals(140005, fileTypeNotSupported.getNumericErrCode());
        assertEquals("errors.com.patsnap.drafting.image.file_type_not_supported", fileTypeNotSupported.getErrCode());
        assertEquals("不支持的文件类型，仅支持jpg、jpeg、png格式", fileTypeNotSupported.getPattern());
    }

    @Test
    @DisplayName("测试上传相关错误码")
    void testUploadRelatedErrorCodes() {
        // 测试上传失败错误
        ImageUploadErrorCodeEnum uploadFailed = ImageUploadErrorCodeEnum.UPLOAD_FAILED;
        assertEquals(140021, uploadFailed.getNumericErrCode());
        assertEquals("errors.com.patsnap.drafting.image.upload_failed", uploadFailed.getErrCode());
        assertEquals("文件上传失败", uploadFailed.getPattern());

        // 测试存储服务不可用错误
        ImageUploadErrorCodeEnum storageUnavailable = ImageUploadErrorCodeEnum.STORAGE_UNAVAILABLE;
        assertEquals(140023, storageUnavailable.getNumericErrCode());
        assertEquals("errors.com.patsnap.drafting.image.storage_unavailable", storageUnavailable.getErrCode());
        assertEquals("存储服务不可用", storageUnavailable.getPattern());
    }

    @Test
    @DisplayName("测试参数相关错误码")
    void testParameterRelatedErrorCodes() {
        // 测试请求参数无效错误
        ImageUploadErrorCodeEnum requestParamInvalid = ImageUploadErrorCodeEnum.REQUEST_PARAM_INVALID;
        assertEquals(140041, requestParamInvalid.getNumericErrCode());
        assertEquals("errors.com.patsnap.drafting.image.request_param_invalid", requestParamInvalid.getErrCode());
        assertEquals("请求参数无效", requestParamInvalid.getPattern());

        // 测试文件夹路径无效错误
        ImageUploadErrorCodeEnum folderPathInvalid = ImageUploadErrorCodeEnum.FOLDER_PATH_INVALID;
        assertEquals(140042, folderPathInvalid.getNumericErrCode());
        assertEquals("errors.com.patsnap.drafting.image.folder_path_invalid", folderPathInvalid.getErrCode());
        assertEquals("文件夹路径无效", folderPathInvalid.getPattern());
    }

    @Test
    @DisplayName("测试权限相关错误码")
    void testPermissionRelatedErrorCodes() {
        // 测试用户未认证错误
        ImageUploadErrorCodeEnum userNotAuthenticated = ImageUploadErrorCodeEnum.USER_NOT_AUTHENTICATED;
        assertEquals(140062, userNotAuthenticated.getNumericErrCode());
        assertEquals("errors.com.patsnap.drafting.image.user_not_authenticated", userNotAuthenticated.getErrCode());
        assertEquals("用户未认证", userNotAuthenticated.getPattern());

        // 测试上传权限被拒绝错误
        ImageUploadErrorCodeEnum uploadPermissionDenied = ImageUploadErrorCodeEnum.UPLOAD_PERMISSION_DENIED;
        assertEquals(140061, uploadPermissionDenied.getNumericErrCode());
        assertEquals("errors.com.patsnap.drafting.image.upload_permission_denied", uploadPermissionDenied.getErrCode());
        assertEquals("没有上传权限", uploadPermissionDenied.getPattern());
    }

    @Test
    @DisplayName("测试系统相关错误码")
    void testSystemRelatedErrorCodes() {
        // 测试系统错误
        ImageUploadErrorCodeEnum systemError = ImageUploadErrorCodeEnum.SYSTEM_ERROR;
        assertEquals(140081, systemError.getNumericErrCode());
        assertEquals("errors.com.patsnap.drafting.image.system_error", systemError.getErrCode());
        assertEquals("系统内部错误", systemError.getPattern());

        // 测试服务不可用错误
        ImageUploadErrorCodeEnum serviceUnavailable = ImageUploadErrorCodeEnum.SERVICE_UNAVAILABLE;
        assertEquals(140082, serviceUnavailable.getNumericErrCode());
        assertEquals("errors.com.patsnap.drafting.image.service_unavailable", serviceUnavailable.getErrCode());
        assertEquals("图片上传服务暂时不可用", serviceUnavailable.getPattern());
    }

    @Test
    @DisplayName("测试错误码唯一性")
    void testErrorCodeUniqueness() {
        ImageUploadErrorCodeEnum[] values = ImageUploadErrorCodeEnum.values();
        
        // 验证数字错误码的唯一性
        long uniqueNumericCodes = java.util.Arrays.stream(values)
                .mapToInt(ImageUploadErrorCodeEnum::getNumericErrCode)
                .distinct()
                .count();
        assertEquals(values.length, uniqueNumericCodes, "数字错误码应该是唯一的");

        // 验证错误码字符串的唯一性
        long uniqueErrCodes = java.util.Arrays.stream(values)
                .map(ImageUploadErrorCodeEnum::getErrCode)
                .distinct()
                .count();
        assertEquals(values.length, uniqueErrCodes, "错误码字符串应该是唯一的");
    }

    @Test
    @DisplayName("测试错误码范围")
    void testErrorCodeRange() {
        ImageUploadErrorCodeEnum[] values = ImageUploadErrorCodeEnum.values();
        
        for (ImageUploadErrorCodeEnum errorCode : values) {
            int numericCode = errorCode.getNumericErrCode();
            assertTrue(numericCode >= 140001 && numericCode <= 140100, 
                    "错误码应该在140001-140100范围内，实际值: " + numericCode);
        }
    }

    @Test
    @DisplayName("测试ErrorCode接口实现")
    void testErrorCodeInterface() {
        ImageUploadErrorCodeEnum fileEmpty = ImageUploadErrorCodeEnum.FILE_EMPTY;
        
        // 验证实现了ErrorCode接口
        assertTrue(fileEmpty instanceof ErrorCode);
        
        // 验证接口方法返回值不为空
        assertNotNull(fileEmpty.getNumericErrCode());
        assertNotNull(fileEmpty.getErrCode());
        assertNotNull(fileEmpty.getPattern());
        
        // 验证返回值格式
        assertTrue(fileEmpty.getErrCode().startsWith("errors.com.patsnap.drafting.image."));
        assertFalse(fileEmpty.getPattern().isEmpty());
    }
} 
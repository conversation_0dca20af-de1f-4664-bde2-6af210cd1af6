package com.patsnap.drafting.manager.imagesearch;

import com.patsnap.drafting.client.OpenAiClient;
import com.patsnap.drafting.client.OpenApiClient;
import com.patsnap.drafting.client.PatentApiClient;
import com.patsnap.drafting.client.model.ModelCompletionDTO;
import com.patsnap.drafting.enums.common.RiskLevelEnum;
import com.patsnap.drafting.enums.prompt.ScenarioEnum;
import com.patsnap.drafting.manager.IdentityAccountManager;
import com.patsnap.drafting.manager.agent.model.AgentToolResponseDTO;
import com.patsnap.drafting.request.imagesearch.ImageLocPredictRequest;
import com.patsnap.drafting.request.imagesearch.ImageMultipleSimilarSearchRequest;
import com.patsnap.drafting.request.imagesearch.ImageSimilarSearchRequest;
import com.patsnap.drafting.response.imagesearch.ComparisonsItem;
import com.patsnap.drafting.response.imagesearch.ImageFeatureComparisonResponse;
import com.patsnap.drafting.response.imagesearch.ImageLocPredictResponse;
import com.patsnap.drafting.response.imagesearch.ImageSimilarSearchData;
import com.patsnap.drafting.response.imagesearch.ImageSimilarSearchResponse;
import com.patsnap.drafting.response.imagesearch.ImageSimilarityResponse;
import com.patsnap.drafting.response.imagesearch.ImageSimilarityResult;
import com.patsnap.drafting.response.imagesearch.PatentMessagesItem;
import com.patsnap.drafting.util.ImagePromptUtils;
import com.patsnap.drafting.util.RedissonUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.redisson.api.RedissonClient;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static com.patsnap.drafting.constants.PatentDetailFieldConstants.COUNTRY;
import static com.patsnap.drafting.constants.PatentDetailFieldConstants.SIMPLE_LEGAL_STATUS;
import static com.patsnap.drafting.enums.prompt.PromptKeyEnum.IMAGE_SIMILARITY_COMPARISON;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * ImageSearchToolManager 单元测试类
 */
@RunWith(MockitoJUnitRunner.class)
public class ImageSearchToolManagerTest {

    @InjectMocks
    private ImageSearchToolManager imageSearchToolManager;

    @Mock
    private OpenAiClient openAiClient;

    @Mock
    private OpenApiClient openApiClient;

    @Mock
    private RedissonClient redissonClient;

    @Mock
    private PatentApiClient patentApiClient;

    @Mock
    private ImagePromptUtils imagePromptUtils;

    @Mock
    private RedissonUtils redissonUtils;

    @Mock
    private IdentityAccountManager identityAccountManager;

    private String taskId;
    private String imageUrl;
    private List<String> countries;
    private ImageSimilarSearchRequest singleRequest;
    private ImageMultipleSimilarSearchRequest multipleRequest;
    private ImageSimilarSearchResponse searchResponse;
    private ImageFeatureComparisonResponse comparisonResponse;
    private ImageLocPredictResponse locPredictResponse;
    private ImageSimilarityResponse similarityResponse;

    @Before
    public void setUp() {
        // 初始化测试数据
        taskId = "test-task-id";
        imageUrl = "http://example.com/image.jpg";
        countries = Collections.singletonList("US");

        // 初始化单图像搜索请求
        singleRequest = new ImageSimilarSearchRequest();
        singleRequest.setUrl(imageUrl);
        singleRequest.setCountry(List.of("US"));

        // 初始化多图像搜索请求
        multipleRequest = new ImageMultipleSimilarSearchRequest();
        multipleRequest.setUrls(Arrays.asList(imageUrl, "http://example.com/image2.jpg"));
        multipleRequest.setCountry(List.of("US"));

        // 初始化搜索响应
        searchResponse = createMockSearchResponse();

        // 初始化图像特征比较响应
        comparisonResponse = createMockComparisonResponse();

        // 初始化位置预测响应
        locPredictResponse = createMockLocPredictResponse();

        // 初始化相似度响应
        similarityResponse = createMockSimilarityResponse();

        // 配置Mock行为
        setupMocks();
    }

    /**
     * 创建模拟的搜索响应
     */
    private ImageSimilarSearchResponse createMockSearchResponse() {
        ImageSimilarSearchResponse response = new ImageSimilarSearchResponse();
        
        // 创建专利消息列表
        List<PatentMessagesItem> patentMessages = new ArrayList<>();
        PatentMessagesItem item = new PatentMessagesItem();
        item.setUrl("http://example.com/patent-image.jpg");
        item.setPatentId("123456");
        item.setPatentPn("US12345678");
        item.setTitle("测试专利");
        patentMessages.add(item);
        
        // 设置数据
        ImageSimilarSearchData data = new ImageSimilarSearchData();
        data.setPatentMessages(patentMessages);
        response.setData(data);
        
        return response;
    }

    /**
     * 创建模拟的特征比较响应
     */
    private ImageFeatureComparisonResponse createMockComparisonResponse() {
        ImageFeatureComparisonResponse response = new ImageFeatureComparisonResponse();
        
        // 创建比较项列表
        List<ComparisonsItem> comparisons = new ArrayList<>();
        ComparisonsItem item = new ComparisonsItem();
        item.setPn("US12345678");
        item.setPatentId("123456");
        item.setTitle("测试专利");
        item.setSimilarScore(0.85);
        item.setSimilar("true");
        item.setRiskLevel(RiskLevelEnum.HIGH.getValue());
        item.setRiskDesc("高风险");
        item.setDifference("细微差别");
        item.setSuggestion("建议修改");
        item.setConfusion("true");
        item.setCountry("US");
        item.setStatus(1);
        comparisons.add(item);
        
        response.setComparisons(comparisons);
        response.setUserImage("image-key");
        
        return response;
    }

    /**
     * 创建模拟的位置预测响应
     */
    private ImageLocPredictResponse createMockLocPredictResponse() {
        ImageLocPredictResponse response = new ImageLocPredictResponse();
        
        // 创建位置预测数据
        ImageLocPredictResponse.ImageLocPredictData data = new ImageLocPredictResponse.ImageLocPredictData();
        List<ImageLocPredictResponse.LocPrediction> locPredictions = new ArrayList<>();
        
        ImageLocPredictResponse.LocPrediction prediction = new ImageLocPredictResponse.LocPrediction();
        prediction.setLoc("A43B");
        prediction.setDesc("鞋类");
        prediction.setScore(0.95);  // 设置高分数确保通过过滤器 (score > 0.6)
        locPredictions.add(prediction);
        
        // 添加另一个预测
        ImageLocPredictResponse.LocPrediction prediction2 = new ImageLocPredictResponse.LocPrediction();
        prediction2.setLoc("A43C");
        prediction2.setDesc("鞋类配件");
        prediction2.setScore(0.8);  // 高于0.6的分数将通过过滤器
        locPredictions.add(prediction2);
        
        data.setLoc(locPredictions);
        response.setData(data);
        response.setStatus(true);
        
        return response;
    }

    /**
     * 创建模拟的相似度响应
     */
    private ImageSimilarityResponse createMockSimilarityResponse() {
        ImageSimilarityResponse response = new ImageSimilarityResponse();
        
        // 创建相似度结果列表
        List<ImageSimilarityResult> results = new ArrayList<>();
        ImageSimilarityResult result = new ImageSimilarityResult();
        result.setPn("US12345678");
        result.setSimilarScore(0.85);
        result.setSimilar("true");
        result.setRiskLevel(RiskLevelEnum.HIGH.getValue());
        result.setRiskDesc("高风险");
        result.setDifference("细微差别");
        result.setSuggestion("建议修改");
        result.setConfusion("true");
        results.add(result);
        
        response.setResults(results);
        
        return response;
    }

    /**
     * 配置Mock行为
     */
    private void setupMocks() {
        // OpenApiClient Mock
        when(openApiClient.searchImageBySingle(any(ImageSimilarSearchRequest.class))).thenReturn(searchResponse);
        when(openApiClient.searchImageByMultiple(any(ImageMultipleSimilarSearchRequest.class))).thenReturn(searchResponse);
        when(openApiClient.predictImageLoc(any(ImageLocPredictRequest.class))).thenReturn(locPredictResponse);

        // RedissonUtils Mock
        doNothing().when(redissonUtils).set(eq(redissonClient), anyString(), any(), eq(ImageSearchToolManager.EXPIRE_TIME), eq(TimeUnit.HOURS));
        when(redissonUtils.get(eq(redissonClient), eq(ImageSearchToolManager.SIMILAR_IMAGE_INPUT_REDIS_KEY + taskId))).thenReturn(imageUrl);
        when(redissonUtils.get(eq(redissonClient), eq(ImageSearchToolManager.SIMILAR_IMAGE_COUNTRY_REDIS_KEY + taskId))).thenReturn(countries);
        when(redissonUtils.get(eq(redissonClient), eq(ImageSearchToolManager.SIMILAR_IMAGE_SEARCH_REDIS_KEY + taskId))).thenReturn(searchResponse);
        when(redissonUtils.get(eq(redissonClient), eq(ImageSearchToolManager.SIMILAR_IMAGE_FEATURE_COMPARISON_REDIS_KEY + taskId))).thenReturn(comparisonResponse);

        // OpenAiClient Mock
        when(openAiClient.buildPromptByPlatform(eq(IMAGE_SIMILARITY_COMPARISON.getValue()), any())).thenReturn("模拟的提示词");
        when(openAiClient.callGptByPrompt(any(ModelCompletionDTO.class), eq(ScenarioEnum.IMAGE_SIMILAR_SEARCH), eq(ImageSimilarityResponse.class))).thenReturn(similarityResponse);

        // ImagePromptUtils Mock
        when(imagePromptUtils.buildModelCompletion(anyString(), eq(ImageSearchToolManager.DEFAULT_MODEL))).thenReturn(new ModelCompletionDTO());

        // PatentApiClient Mock
        Map<String, Map<String, Object>> patentDetailsMap = new HashMap<>();
        Map<String, Object> details = new HashMap<>();
        details.put(COUNTRY, "US");
        details.put(SIMPLE_LEGAL_STATUS, 1);
        patentDetailsMap.put("123456", details);
        when(patentApiClient.getPatentFieldsMap(anyList(), anyList())).thenReturn(patentDetailsMap);
        
        // IdentityAccountManager Mock
        when(identityAccountManager.getShowValueByUserId(any())).thenReturn("测试用户");
    }

    /**
     * 测试单图像相似搜索 - 启用法律状态过滤
     */
    @Test
    public void testSearchSimilarImageBySingle() {
        // 执行测试 - 启用法律状态过滤
        AgentToolResponseDTO result = imageSearchToolManager.searchSimilarImageBySingle(taskId, singleRequest, true);
        
        // 验证结果
        assertNotNull(result);
        
        // 验证API调用
        verify(openApiClient, times(1)).searchImageBySingle(singleRequest);
        
        // 验证Redis存储
        verify(redissonUtils, times(1)).set(eq(redissonClient), 
                eq(ImageSearchToolManager.SIMILAR_IMAGE_INPUT_REDIS_KEY + taskId), 
                eq(singleRequest.getUrl()), 
                eq(ImageSearchToolManager.EXPIRE_TIME), 
                eq(TimeUnit.HOURS));
        
        verify(redissonUtils, times(1)).set(eq(redissonClient), 
                eq(ImageSearchToolManager.SIMILAR_IMAGE_COUNTRY_REDIS_KEY + taskId), 
                eq(singleRequest.getCountry()), 
                eq(ImageSearchToolManager.EXPIRE_TIME), 
                eq(TimeUnit.HOURS));
        
        verify(redissonUtils, times(1)).set(eq(redissonClient), 
                eq(ImageSearchToolManager.SIMILAR_IMAGE_SEARCH_REDIS_KEY + taskId), 
                eq(result), 
                eq(ImageSearchToolManager.EXPIRE_TIME), 
                eq(TimeUnit.HOURS));
    }

    /**
     * 测试单图像相似搜索 - 禁用法律状态过滤
     */
    @Test
    public void testSearchSimilarImageBySingleWithoutLegalStatusFilter() {
        // 执行测试 - 禁用法律状态过滤
        AgentToolResponseDTO result = imageSearchToolManager.searchSimilarImageBySingle(taskId, singleRequest, false);
        
        // 验证结果
        assertNotNull(result);
        
        // 验证API调用
        verify(openApiClient, times(1)).searchImageBySingle(singleRequest);
        
        // 验证请求参数中的simpleLegalStatus应该为null
        assertEquals(null, singleRequest.getSimpleLegalStatus());
    }

    /**
     * 测试多图像相似搜索
     */
    @Test
    public void testSearchSimilarImageByMultiple() {
        // 执行测试
        ImageSimilarSearchResponse result = imageSearchToolManager.searchSimilarImageByMultiple(taskId, multipleRequest);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(searchResponse, result);
        
        // 验证API调用
        verify(openApiClient, times(1)).searchImageByMultiple(multipleRequest);
        
        // 验证Redis存储
        verify(redissonUtils, times(1)).set(eq(redissonClient), 
                eq(ImageSearchToolManager.SIMILAR_IMAGE_INPUT_REDIS_KEY + taskId), 
                eq(String.join(",", multipleRequest.getUrls())), 
                eq(ImageSearchToolManager.EXPIRE_TIME), 
                eq(TimeUnit.HOURS));
    }

    /**
     * 测试比较图像特征
     */
    @Test
    public void testCompareImageFeature() {
        // 执行测试
        AgentToolResponseDTO result = imageSearchToolManager.compareImageFeature(taskId, 20, 5);
        
        // 验证结果
        assertNotNull(result);
        
        // 验证API调用
        verify(openAiClient, times(1)).buildPromptByPlatform(eq(IMAGE_SIMILARITY_COMPARISON.getValue()), any());
        verify(openAiClient, times(1)).callGptByPrompt(any(), eq(ScenarioEnum.IMAGE_SIMILAR_SEARCH), eq(ImageSimilarityResponse.class));
        
        // 验证Redis存储
        verify(redissonUtils, times(1)).set(eq(redissonClient), 
                eq(ImageSearchToolManager.SIMILAR_IMAGE_FEATURE_COMPARISON_REDIS_KEY + taskId), 
                any(ImageFeatureComparisonResponse.class), 
                eq(ImageSearchToolManager.EXPIRE_TIME), 
                eq(TimeUnit.HOURS));
    }

    /**
     * 测试图像位置预测
     */
    @Test
    public void testPredictImageLoc() {
        // 创建请求对象
        ImageLocPredictRequest request = new ImageLocPredictRequest();
        request.setUrl(imageUrl);
        
        // 执行测试
        ImageLocPredictResponse result = imageSearchToolManager.predictImageLoc(request);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(locPredictResponse, result);
        
        // 验证API调用
        verify(openApiClient, times(1)).predictImageLoc(request);
    }

    /**
     * 测试专利详细信息填充
     */
    @Test
    public void testEnrichPatentDetails() {
        // 准备测试数据
        List<String> patentIds = Collections.singletonList("123456");
        List<ComparisonsItem> items = new ArrayList<>();
        ComparisonsItem item = new ComparisonsItem();
        item.setPatentId("123456");
        items.add(item);
        
        // 执行测试
        List<ComparisonsItem> result = imageSearchToolManager.enrichPatentDetails(patentIds, items);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("US", result.get(0).getCountry());
        assertEquals(Integer.valueOf(1), result.get(0).getStatus());
        
        // 验证API调用
        verify(patentApiClient, times(1)).getPatentFieldsMap(
                eq(patentIds), 
                eq(Arrays.asList(COUNTRY, SIMPLE_LEGAL_STATUS))
        );
    }

    /**
     * 测试空专利ID列表的情况
     */
    @Test
    public void testEnrichPatentDetailsWithEmptyPatentIds() {
        // 准备测试数据
        List<String> patentIds = Collections.emptyList();
        List<ComparisonsItem> items = new ArrayList<>();
        ComparisonsItem item = new ComparisonsItem();
        item.setPatentId("123456");
        items.add(item);
        
        // 执行测试
        List<ComparisonsItem> result = imageSearchToolManager.enrichPatentDetails(patentIds, items);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(items, result);
        
        // 验证没有API调用
        verify(patentApiClient, times(0)).getPatentFieldsMap(anyList(), anyList());
    }

    /**
     * 测试分页查询功能 - 两页都返回满页数据
     */
    @Test
    public void testExecutePagedSearch_BothPagesReturnFullData() {
        // 准备测试数据
        ImageSimilarSearchResponse firstPageResponse = createMockSearchResponseWithCount(100);
        ImageSimilarSearchResponse secondPageResponse = createMockSearchResponseWithCount(100);

        // 模拟API调用
        when(openApiClient.searchImageBySingle(any(ImageSimilarSearchRequest.class)))
                .thenReturn(firstPageResponse)
                .thenReturn(secondPageResponse);

        // 执行测试 - 使用反射调用私有方法
        try {
            java.lang.reflect.Method method = ImageSearchToolManager.class.getDeclaredMethod("executePagedSearch", ImageSimilarSearchRequest.class);
            method.setAccessible(true);
            ImageSimilarSearchResponse result = (ImageSimilarSearchResponse) method.invoke(imageSearchToolManager, singleRequest);

            // 验证结果
            assertNotNull(result);
            assertNotNull(result.getData());
            assertEquals(200, result.getData().getPatentMessages().size());
            
            // 验证API被调用了2次
            verify(openApiClient, times(2)).searchImageBySingle(any(ImageSimilarSearchRequest.class));
        } catch (Exception e) {
            throw new RuntimeException("测试执行失败", e);
        }
    }

    /**
     * 测试分页查询功能 - 第二页返回部分数据
     */
    @Test
    public void testExecutePagedSearch_SecondPageReturnPartialData() {
        // 准备测试数据
        ImageSimilarSearchResponse firstPageResponse = createMockSearchResponseWithCount(100);
        ImageSimilarSearchResponse secondPageResponse = createMockSearchResponseWithCount(50);

        // 模拟API调用
        when(openApiClient.searchImageBySingle(any(ImageSimilarSearchRequest.class)))
                .thenReturn(firstPageResponse)
                .thenReturn(secondPageResponse);

        // 执行测试 - 使用反射调用私有方法
        try {
            java.lang.reflect.Method method = ImageSearchToolManager.class.getDeclaredMethod("executePagedSearch", ImageSimilarSearchRequest.class);
            method.setAccessible(true);
            ImageSimilarSearchResponse result = (ImageSimilarSearchResponse) method.invoke(imageSearchToolManager, singleRequest);

            // 验证结果
            assertNotNull(result);
            assertNotNull(result.getData());
            assertEquals(150, result.getData().getPatentMessages().size());
            
            // 验证API被调用了2次
            verify(openApiClient, times(2)).searchImageBySingle(any(ImageSimilarSearchRequest.class));
        } catch (Exception e) {
            throw new RuntimeException("测试执行失败", e);
        }
    }

    /**
     * 测试分页查询功能 - 第一页返回空数据
     */
    @Test
    public void testExecutePagedSearch_FirstPageReturnEmptyData() {
        // 准备测试数据
        ImageSimilarSearchResponse emptyResponse = createMockSearchResponseWithCount(0);

        // 模拟API调用
        when(openApiClient.searchImageBySingle(any(ImageSimilarSearchRequest.class)))
                .thenReturn(emptyResponse);

        // 执行测试 - 使用反射调用私有方法
        try {
            java.lang.reflect.Method method = ImageSearchToolManager.class.getDeclaredMethod("executePagedSearch", ImageSimilarSearchRequest.class);
            method.setAccessible(true);
            ImageSimilarSearchResponse result = (ImageSimilarSearchResponse) method.invoke(imageSearchToolManager, singleRequest);

            // 验证结果
            assertNotNull(result);
            assertNotNull(result.getData());
            assertEquals(0, result.getData().getPatentMessages().size());
            
            // 验证API只被调用了1次
            verify(openApiClient, times(1)).searchImageBySingle(any(ImageSimilarSearchRequest.class));
        } catch (Exception e) {
            throw new RuntimeException("测试执行失败", e);
        }
    }

    /**
     * 创建指定数量专利的模拟响应数据
     */
    private ImageSimilarSearchResponse createMockSearchResponseWithCount(int patentCount) {
        ImageSimilarSearchResponse response = new ImageSimilarSearchResponse();
        ImageSimilarSearchData data = new ImageSimilarSearchData();
        
        List<PatentMessagesItem> patentMessages = new ArrayList<>();
        for (int i = 0; i < patentCount; i++) {
            PatentMessagesItem item = new PatentMessagesItem();
            item.setPatentId("patent-" + i);
            item.setPatentPn("PN-" + i);
            item.setTitle("Patent Title " + i);
            item.setScore(0.8 + (i % 20) * 0.01); // 模拟不同的相似度分数
            patentMessages.add(item);
        }
        
        data.setPatentMessages(patentMessages);
        response.setData(data);
        
        return response;
    }
} 
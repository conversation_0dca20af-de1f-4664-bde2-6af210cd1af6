package com.patsnap.drafting.manager.aispecification;

import com.patsnap.drafting.enums.task.AiTaskContentTypeEnum;
import com.patsnap.drafting.manager.aitask.AiTaskManager;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ActiveProfiles;

import java.util.concurrent.TimeUnit;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doNothing;

/**
 * AlgorithmAsyncProcessor 异步功能测试
 * 
 * <AUTHOR> Assistant
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
class SpecificationManagerAsyncTest {

    @Autowired
    private AlgorithmAsyncProcessor algorithmAsyncProcessor;

    @MockBean
    private AiTaskManager aiTaskManager;

    @Test
    @DisplayName("测试异步处理附图算法调用")
    void testProcessFigureAlgorithmAsync() throws InterruptedException {
        // Mock 依赖
        doNothing().when(aiTaskManager).updateTaskContent(anyString(), any(AiTaskContentTypeEnum.class), any());

        String taskId = "test-task-id-001";
        
        log.info("开始测试异步处理附图算法调用");
        
        // 调用异步方法
        algorithmAsyncProcessor.processAlgorithmAsync(taskId, AiTaskContentTypeEnum.FIGURES_PRE_INFO);
        
        log.info("异步方法调用完成，等待处理结果...");
        
        // 等待异步处理完成（模拟的处理时间是5秒）
        TimeUnit.SECONDS.sleep(6);
        
        log.info("异步处理测试完成");
    }

    @Test
    @DisplayName("测试异步处理术语算法调用")
    void testProcessTermAlgorithmAsync() throws InterruptedException {
        // Mock 依赖
        doNothing().when(aiTaskManager).updateTaskContent(anyString(), any(AiTaskContentTypeEnum.class), any());

        String taskId = "test-task-id-002";
        
        log.info("开始测试异步处理术语算法调用");
        
        // 调用异步方法
        algorithmAsyncProcessor.processAlgorithmAsync(taskId, AiTaskContentTypeEnum.TERMS);
        
        log.info("异步方法调用完成，等待处理结果...");
        
        // 等待异步处理完成（模拟的处理时间是3秒）
        TimeUnit.SECONDS.sleep(4);
        
        log.info("异步处理测试完成");
    }

    @Test
    @DisplayName("测试并发异步处理")
    void testConcurrentAsyncProcessing() throws InterruptedException {
        // Mock 依赖
        doNothing().when(aiTaskManager).updateTaskContent(anyString(), any(AiTaskContentTypeEnum.class), any());

        log.info("开始测试并发异步处理");
        
        // 同时启动多个异步任务
        for (int i = 1; i <= 5; i++) {
            String taskId = "concurrent-task-" + i;
            algorithmAsyncProcessor.processAlgorithmAsync(taskId, AiTaskContentTypeEnum.FIGURES);
            algorithmAsyncProcessor.processAlgorithmAsync(taskId, AiTaskContentTypeEnum.TERMS);
        }
        
        log.info("所有异步任务已启动，等待处理完成...");
        
        // 等待所有任务完成
        TimeUnit.SECONDS.sleep(8);
        
        log.info("并发异步处理测试完成");
    }
} 
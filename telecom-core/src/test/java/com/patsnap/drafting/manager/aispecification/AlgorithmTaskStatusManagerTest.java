package com.patsnap.drafting.manager.aispecification;

import com.patsnap.drafting.enums.task.AiTaskContentTypeEnum;
import com.patsnap.drafting.util.RedissonUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.redisson.api.RedissonClient;

import java.util.Map;

import static com.patsnap.drafting.manager.aispecification.AlgorithmTaskStatusManager.STATUS_COMPLETED;
import static com.patsnap.drafting.manager.aispecification.AlgorithmTaskStatusManager.STATUS_FAILED;
import static com.patsnap.drafting.manager.aispecification.AlgorithmTaskStatusManager.STATUS_PROCESSING;
import static com.patsnap.drafting.manager.aispecification.AlgorithmTaskStatusManager.TaskStatusInfo;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * 算法任务状态管理器测试类
 * 
 * <AUTHOR> Assistant
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("算法任务状态管理器测试")
class AlgorithmTaskStatusManagerTest {

    @Mock
    private RedissonClient redissonClient;

    @Mock
    private RedissonUtils redissonUtils;

    @InjectMocks
    private AlgorithmTaskStatusManager statusManager;

    private static final String TEST_TASK_ID = "test-task-123";
    private static final AiTaskContentTypeEnum TEST_CONTENT_TYPE = AiTaskContentTypeEnum.FIGURE_CONFIG;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
    }

    @Test
    @DisplayName("测试更新任务状态")
    void testUpdateTaskStatus() {
        // Given
        String status = STATUS_PROCESSING;
        
        // When
        statusManager.updateTaskStatus(TEST_TASK_ID, TEST_CONTENT_TYPE, status);
        
        // Then
        verify(redissonUtils, times(1)).set(
            eq(redissonClient), 
            anyString(), 
            any(TaskStatusInfo.class), 
            anyLong(), 
            any()
        );
    }

    @Test
    @DisplayName("测试获取任务状态")
    void testGetTaskStatus() {
        // Given
        TaskStatusInfo statusInfo = new TaskStatusInfo(STATUS_COMPLETED, TEST_CONTENT_TYPE.getType());
        when(redissonUtils.get(eq(redissonClient), anyString())).thenReturn(statusInfo);
        
        // When
        String result = statusManager.getTaskStatus(TEST_TASK_ID, TEST_CONTENT_TYPE);
        
        // Then
        assertEquals(STATUS_COMPLETED, result);
        verify(redissonUtils, times(1)).get(eq(redissonClient), anyString());
    }

    @Test
    @DisplayName("测试获取任务状态信息")
    void testGetTaskStatusInfo() {
        // Given
        TaskStatusInfo expectedStatusInfo = new TaskStatusInfo(STATUS_PROCESSING, TEST_CONTENT_TYPE.getType());
        when(redissonUtils.get(eq(redissonClient), anyString())).thenReturn(expectedStatusInfo);
        
        // When
        TaskStatusInfo result = statusManager.getTaskStatusInfo(TEST_TASK_ID, TEST_CONTENT_TYPE);
        
        // Then
        assertNotNull(result);
        assertEquals(STATUS_PROCESSING, result.getStatus());
        assertEquals(TEST_CONTENT_TYPE.getType(), result.getContentType());
        verify(redissonUtils, times(1)).get(eq(redissonClient), anyString());
    }

    @Test
    @DisplayName("测试获取算法结果")
    void testGetAlgorithmResult() {
        // Given
        Object expectedResult = Map.of("status", "success", "data", "test data");
        when(redissonUtils.get(eq(redissonClient), anyString())).thenReturn(expectedResult);
        
        // When
        Object result = statusManager.getAlgorithmResult(TEST_TASK_ID, TEST_CONTENT_TYPE);
        
        // Then
        assertNotNull(result);
        assertEquals(expectedResult, result);
        verify(redissonUtils, times(1)).get(eq(redissonClient), anyString());
    }

    @Test
    @DisplayName("测试保存错误信息")
    void testSaveErrorInfo() {
        // Given
        Exception testException = new RuntimeException("Test error message");
        
        // When
        statusManager.saveErrorInfo(TEST_TASK_ID, TEST_CONTENT_TYPE, testException);
        
        // Then
        verify(redissonUtils, times(1)).set(
            eq(redissonClient), 
            anyString(), 
            any(Map.class), 
            anyLong(), 
            any()
        );
    }

    @Test
    @DisplayName("测试检查任务是否正在处理中")
    void testIsTaskProcessing() {
        // Given
        TaskStatusInfo statusInfo = new TaskStatusInfo(STATUS_PROCESSING, TEST_CONTENT_TYPE.getType());
        when(redissonUtils.get(eq(redissonClient), anyString())).thenReturn(statusInfo);
        
        // When
        boolean result = statusManager.isTaskProcessing(TEST_TASK_ID, TEST_CONTENT_TYPE);
        
        // Then
        assertTrue(result);
    }

    @Test
    @DisplayName("测试检查任务是否已完成")
    void testIsTaskCompleted() {
        // Given
        TaskStatusInfo statusInfo = new TaskStatusInfo(STATUS_COMPLETED, TEST_CONTENT_TYPE.getType());
        when(redissonUtils.get(eq(redissonClient), anyString())).thenReturn(statusInfo);
        
        // When
        boolean result = statusManager.isTaskCompleted(TEST_TASK_ID, TEST_CONTENT_TYPE);
        
        // Then
        assertTrue(result);
    }

    @Test
    @DisplayName("测试检查任务是否失败")
    void testIsTaskFailed() {
        // Given
        TaskStatusInfo statusInfo = new TaskStatusInfo(STATUS_FAILED, TEST_CONTENT_TYPE.getType());
        when(redissonUtils.get(eq(redissonClient), anyString())).thenReturn(statusInfo);
        
        // When
        boolean result = statusManager.isTaskFailed(TEST_TASK_ID, TEST_CONTENT_TYPE);
        
        // Then
        assertTrue(result);
    }

    @Test
    @DisplayName("测试获取任务状态 - 状态为空时")
    void testGetTaskStatusWhenNull() {
        // Given
        when(redissonUtils.get(eq(redissonClient), anyString())).thenReturn(null);
        
        // When
        String result = statusManager.getTaskStatus(TEST_TASK_ID, TEST_CONTENT_TYPE);
        
        // Then
        assertNull(result);
    }

    @Test
    @DisplayName("测试更新任务状态带错误信息")
    void testUpdateTaskStatusWithError() {
        // Given
        String status = STATUS_FAILED;
        String errorMessage = "Test error message";
        
        // When
        statusManager.updateTaskStatus(TEST_TASK_ID, TEST_CONTENT_TYPE, status, errorMessage);
        
        // Then
        verify(redissonUtils, times(1)).set(
            eq(redissonClient), 
            anyString(), 
            any(TaskStatusInfo.class), 
            anyLong(), 
            any()
        );
    }
} 
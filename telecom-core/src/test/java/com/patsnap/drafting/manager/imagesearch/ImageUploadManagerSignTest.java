package com.patsnap.drafting.manager.imagesearch;

import com.patsnap.drafting.manager.FileManager;
import com.patsnap.drafting.request.imagesearch.FileSignRequestDTO;
import com.patsnap.drafting.response.imagesearch.FileSignResponseDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * ImageUploadManager 签名功能测试
 *
 * <AUTHOR> Assistant
 * @date 2024/12/19
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("ImageUploadManager 签名功能测试")
class ImageUploadManagerSignTest {

    @Mock
    private FileManager fileManager;

    @InjectMocks
    private ImageUploadManager imageUploadManager;

    private FileSignRequestDTO request;

    @BeforeEach
    void setUp() {
        request = new FileSignRequestDTO();
    }

    @Test
    @DisplayName("批量签名文件成功")
    void testSignMultipleFilesSuccess() {
        // 准备测试数据
        List<String> s3Keys = Arrays.asList(
                "ai_drafting/images/user123/image_1734567890123_test.jpg",
                "ai_drafting/images/user123/image_1734567890124_test2.png"
        );
        request.setS3Keys(s3Keys);

        // Mock FileManager 返回成功的签名URL
        when(fileManager.signFile(anyString()))
                .thenReturn("https://example.com/signed-url-1")
                .thenReturn("https://example.com/signed-url-2");

        // 执行测试
        FileSignResponseDTO result = imageUploadManager.signMultipleFiles(request);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.getTotalCount());
        assertEquals(2, result.getSuccessCount());
        assertEquals(0, result.getFailedCount());
        assertEquals(2, result.getSignedFiles().size());
        assertEquals(0, result.getFailedFiles().size());

        // 验证签名成功的文件信息
        FileSignResponseDTO.SignedFileInfo signedFile1 = result.getSignedFiles().get(0);
        assertEquals(s3Keys.get(0), signedFile1.getS3Key());
        assertEquals("https://example.com/signed-url-1", signedFile1.getSignedUrl());
        assertEquals(FileManager.EXPIRE, signedFile1.getExpireSeconds());

        FileSignResponseDTO.SignedFileInfo signedFile2 = result.getSignedFiles().get(1);
        assertEquals(s3Keys.get(1), signedFile2.getS3Key());
        assertEquals("https://example.com/signed-url-2", signedFile2.getSignedUrl());
        assertEquals(FileManager.EXPIRE, signedFile2.getExpireSeconds());

        // 验证 FileManager 被调用两次
        verify(fileManager, times(2)).signFile(anyString());
    }

    @Test
    @DisplayName("部分文件签名失败")
    void testSignMultipleFilesPartialFailure() {
        // 准备测试数据
        List<String> s3Keys = Arrays.asList(
                "ai_drafting/images/user123/valid_file.jpg",
                "ai_drafting/images/user123/invalid_file.png"
        );
        request.setS3Keys(s3Keys);

        // Mock FileManager - 第一个成功，第二个失败（返回空字符串）
        when(fileManager.signFile("ai_drafting/images/user123/valid_file.jpg"))
                .thenReturn("https://example.com/signed-url");
        when(fileManager.signFile("ai_drafting/images/user123/invalid_file.png"))
                .thenReturn("");

        // 执行测试
        FileSignResponseDTO result = imageUploadManager.signMultipleFiles(request);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.getTotalCount());
        assertEquals(1, result.getSuccessCount());
        assertEquals(1, result.getFailedCount());
        assertEquals(1, result.getSignedFiles().size());
        assertEquals(1, result.getFailedFiles().size());

        // 验证成功的文件
        FileSignResponseDTO.SignedFileInfo signedFile = result.getSignedFiles().get(0);
        assertEquals("ai_drafting/images/user123/valid_file.jpg", signedFile.getS3Key());
        assertEquals("https://example.com/signed-url", signedFile.getSignedUrl());

        // 验证失败的文件
        FileSignResponseDTO.FailedFileInfo failedFile = result.getFailedFiles().get(0);
        assertEquals("ai_drafting/images/user123/invalid_file.png", failedFile.getS3Key());
        assertNotNull(failedFile.getErrorMessage());
    }

    @Test
    @DisplayName("FileManager抛出异常时的处理")
    void testSignMultipleFilesWithException() {
        // 准备测试数据
        List<String> s3Keys = Arrays.asList("ai_drafting/images/user123/test.jpg");
        request.setS3Keys(s3Keys);

        // Mock FileManager 抛出异常
        when(fileManager.signFile(anyString()))
                .thenThrow(new RuntimeException("S3 service unavailable"));

        // 执行测试
        FileSignResponseDTO result = imageUploadManager.signMultipleFiles(request);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.getTotalCount());
        assertEquals(0, result.getSuccessCount());
        assertEquals(1, result.getFailedCount());
        assertEquals(0, result.getSignedFiles().size());
        assertEquals(1, result.getFailedFiles().size());

        // 验证失败的文件信息
        FileSignResponseDTO.FailedFileInfo failedFile = result.getFailedFiles().get(0);
        assertEquals("ai_drafting/images/user123/test.jpg", failedFile.getS3Key());
        assertTrue(failedFile.getErrorMessage().contains("S3 service unavailable"));
    }

    @Test
    @DisplayName("无效的S3Key格式")
    void testSignMultipleFilesWithInvalidS3Key() {
        // 准备测试数据 - 包含无效的S3Key
        List<String> s3Keys = Arrays.asList(
                "ai_drafting/images/user123/valid_file.jpg",
                "/invalid/path/with/leading/slash.jpg",
                "path/with/../double/dots.jpg"
        );
        request.setS3Keys(s3Keys);

        // Mock FileManager 为有效文件返回签名URL
        when(fileManager.signFile("ai_drafting/images/user123/valid_file.jpg"))
                .thenReturn("https://example.com/signed-url");

        // 执行测试
        FileSignResponseDTO result = imageUploadManager.signMultipleFiles(request);

        // 验证结果
        assertNotNull(result);
        assertEquals(3, result.getTotalCount());
        assertEquals(1, result.getSuccessCount());
        assertEquals(2, result.getFailedCount());
        assertEquals(1, result.getSignedFiles().size());
        assertEquals(2, result.getFailedFiles().size());

        // 验证成功的文件
        FileSignResponseDTO.SignedFileInfo signedFile = result.getSignedFiles().get(0);
        assertEquals("ai_drafting/images/user123/valid_file.jpg", signedFile.getS3Key());

        // 验证失败的文件都是因为格式无效
        result.getFailedFiles().forEach(failedFile -> {
            assertNotNull(failedFile.getErrorMessage());
        });

        // 验证 FileManager 只被调用一次（只有有效的S3Key）
        verify(fileManager, times(1)).signFile(anyString());
    }

    @Test
    @DisplayName("空的S3Key列表")
    void testSignMultipleFilesWithEmptyS3Keys() {
        // 准备测试数据 - 空列表
        request.setS3Keys(Arrays.asList());

        // 执行测试
        FileSignResponseDTO result = imageUploadManager.signMultipleFiles(request);

        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.getTotalCount());
        assertEquals(0, result.getSuccessCount());
        assertEquals(0, result.getFailedCount());
        assertEquals(0, result.getSignedFiles().size());
        assertEquals(0, result.getFailedFiles().size());

        // 验证 FileManager 没有被调用
        verify(fileManager, never()).signFile(anyString());
    }
} 
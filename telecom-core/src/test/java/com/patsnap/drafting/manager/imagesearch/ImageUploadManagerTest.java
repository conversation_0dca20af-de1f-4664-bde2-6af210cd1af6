package com.patsnap.drafting.manager.imagesearch;

import com.patsnap.common.request.UserIdHolder;
import com.patsnap.drafting.exception.BizException;
import com.patsnap.drafting.manager.FileManager;
import com.patsnap.drafting.request.imagesearch.ImageUploadRequestDTO;
import com.patsnap.drafting.response.imagesearch.ImageUploadResponseDTO;
import org.apache.http.entity.ContentType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * ImageUploadManager 单元测试
 *
 * <AUTHOR> Assistant
 * @date 2024/12/19
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("图片上传管理器测试")
class ImageUploadManagerTest {

    @Mock
    private FileManager fileManager;

    @InjectMocks
    private ImageUploadManager imageUploadManager;

    @BeforeEach
    void setUp() {
        // 设置用户ID
        UserIdHolder.set("test-user-123");
    }

    @Test
    @DisplayName("成功上传单个图片文件")
    void testUploadSingleImageSuccess() {
        // 准备测试数据
        MockMultipartFile file = new MockMultipartFile(
                "file",
                "test-image.jpg",
                "image/jpeg",
                "test image content".getBytes()
        );
        MultipartFile[] files = {file};

        ImageUploadRequestDTO request = new ImageUploadRequestDTO();
        request.setFolderPath("test/images");
        request.setFilePrefix("test");
        request.setGenerateUniqueName(true);

        // Mock FileManager 返回成功的URL
        when(fileManager.uploadFile2AmazonS3(any(byte[].class), anyString(), any(ContentType.class)))
                .thenReturn("https://example.com/test-image.jpg");

        // 执行测试
        ImageUploadResponseDTO result = imageUploadManager.uploadMultipleImages(files, request);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.getTotalCount());
        assertEquals(1, result.getSuccessCount());
        assertEquals(0, result.getFailedCount());
        assertEquals(1, result.getUploadedFiles().size());
        assertEquals(0, result.getFailedFiles().size());

        ImageUploadResponseDTO.UploadedFileInfo uploadedFile = result.getUploadedFiles().get(0);
        assertEquals("test-image.jpg", uploadedFile.getOriginalName());
        assertEquals("https://example.com/test-image.jpg", uploadedFile.getFileUrl());
        assertTrue(uploadedFile.getStoredName().startsWith("test_"));
        assertTrue(uploadedFile.getS3Key().contains("test-user-123"));

        // 验证 FileManager 被调用
        verify(fileManager, times(1)).uploadFile2AmazonS3(any(byte[].class), anyString(), any(ContentType.class));
    }

    @Test
    @DisplayName("上传多个图片文件")
    void testUploadMultipleImagesSuccess() {
        // 准备测试数据
        MockMultipartFile file1 = new MockMultipartFile(
                "file1",
                "image1.png",
                "image/png",
                "test image 1".getBytes()
        );
        MockMultipartFile file2 = new MockMultipartFile(
                "file2",
                "image2.jpg",
                "image/jpeg",
                "test image 2".getBytes()
        );
        MultipartFile[] files = {file1, file2};

        ImageUploadRequestDTO request = new ImageUploadRequestDTO();

        // Mock FileManager 返回成功的URL
        when(fileManager.uploadFile2AmazonS3(any(byte[].class), anyString(), any(ContentType.class)))
                .thenReturn("https://example.com/uploaded-image.jpg");

        // 执行测试
        ImageUploadResponseDTO result = imageUploadManager.uploadMultipleImages(files, request);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.getTotalCount());
        assertEquals(2, result.getSuccessCount());
        assertEquals(0, result.getFailedCount());
        assertEquals(2, result.getUploadedFiles().size());

        // 验证 FileManager 被调用两次
        verify(fileManager, times(2)).uploadFile2AmazonS3(any(byte[].class), anyString(), any(ContentType.class));
    }

    @Test
    @DisplayName("上传空文件数组应该抛出异常")
    void testUploadEmptyFilesArray() {
        MultipartFile[] files = {};
        ImageUploadRequestDTO request = new ImageUploadRequestDTO();

        // 执行测试并验证异常
        assertThrows(BizException.class, () -> {
            imageUploadManager.uploadMultipleImages(files, request);
        });
    }

    @Test
    @DisplayName("上传不支持的文件类型")
    void testUploadUnsupportedFileType() {
        // 准备测试数据 - 不支持的文件类型
        MockMultipartFile file = new MockMultipartFile(
                "file",
                "document.txt",
                "text/plain",
                "test content".getBytes()
        );
        MultipartFile[] files = {file};

        ImageUploadRequestDTO request = new ImageUploadRequestDTO();

        // 执行测试
        ImageUploadResponseDTO result = imageUploadManager.uploadMultipleImages(files, request);

        // 验证结果 - 应该有一个失败的文件
        assertNotNull(result);
        assertEquals(1, result.getTotalCount());
        assertEquals(0, result.getSuccessCount());
        assertEquals(1, result.getFailedCount());
        assertEquals(1, result.getFailedFiles().size());

        // 验证 FileManager 没有被调用
        verify(fileManager, never()).uploadFile2AmazonS3(any(byte[].class), anyString(), any(ContentType.class));
    }

    @Test
    @DisplayName("FileManager 上传失败的情况")
    void testFileManagerUploadFailure() {
        // 准备测试数据
        MockMultipartFile file = new MockMultipartFile(
                "file",
                "test-image.jpg",
                "image/jpeg",
                "test image content".getBytes()
        );
        MultipartFile[] files = {file};

        ImageUploadRequestDTO request = new ImageUploadRequestDTO();

        // Mock FileManager 抛出异常
        when(fileManager.uploadFile2AmazonS3(any(byte[].class), anyString(), any(ContentType.class)))
                .thenThrow(new RuntimeException("S3 upload failed"));

        // 执行测试
        ImageUploadResponseDTO result = imageUploadManager.uploadMultipleImages(files, request);

        // 验证结果 - 应该有一个失败的文件
        assertNotNull(result);
        assertEquals(1, result.getTotalCount());
        assertEquals(0, result.getSuccessCount());
        assertEquals(1, result.getFailedCount());
        assertEquals(1, result.getFailedFiles().size());

        ImageUploadResponseDTO.FailedFileInfo failedFile = result.getFailedFiles().get(0);
        assertEquals("test-image.jpg", failedFile.getOriginalName());
        assertNotNull(failedFile.getErrorMessage());
    }

    @Test
    @DisplayName("测试文件名生成逻辑")
    void testFileNameGeneration() {
        // 准备测试数据
        MockMultipartFile file = new MockMultipartFile(
                "file",
                "test-image.jpg",
                "image/jpeg",
                "test image content".getBytes()
        );
        MultipartFile[] files = {file};

        ImageUploadRequestDTO request = new ImageUploadRequestDTO();
        request.setFilePrefix("custom");
        request.setGenerateUniqueName(false); // 不生成唯一名称

        // Mock FileManager 返回成功的URL
        when(fileManager.uploadFile2AmazonS3(any(byte[].class), anyString(), any(ContentType.class)))
                .thenReturn("https://example.com/test-image.jpg");

        // 执行测试
        ImageUploadResponseDTO result = imageUploadManager.uploadMultipleImages(files, request);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.getSuccessCount());

        ImageUploadResponseDTO.UploadedFileInfo uploadedFile = result.getUploadedFiles().get(0);
        assertEquals("test-image.jpg", uploadedFile.getStoredName()); // 应该使用原始文件名
    }
} 
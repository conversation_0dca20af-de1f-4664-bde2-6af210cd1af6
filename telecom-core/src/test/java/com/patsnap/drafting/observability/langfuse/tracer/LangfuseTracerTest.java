package com.patsnap.drafting.observability.langfuse.tracer;

import com.patsnap.drafting.observability.langfuse.config.LangfuseProperties;
import io.opentelemetry.api.OpenTelemetry;
import io.opentelemetry.api.trace.Tracer;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.Callable;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * Langfuse 追踪器测试
 *
 * <AUTHOR> Assistant
 * @date 2025/01/31
 */
@ExtendWith(MockitoExtension.class)
class LangfuseTracerTest {

    @Mock
    private Tracer tracer;

    @Mock
    private LangfuseProperties properties;

    private LangfuseTracer langfuseTracer;

    @BeforeEach
    void setUp() {
        when(properties.isEnabled()).thenReturn(true);
        when(properties.isValid()).thenReturn(true);
        langfuseTracer = new LangfuseTracer(tracer, properties);
    }

    @Test
    void testIsEnabled() {
        assertTrue(langfuseTracer.isEnabled());

        when(properties.isEnabled()).thenReturn(false);
        assertFalse(langfuseTracer.isEnabled());

        when(properties.isEnabled()).thenReturn(true);
        when(properties.isValid()).thenReturn(false);
        assertFalse(langfuseTracer.isEnabled());
    }

    @Test
    void testTraceSimpleOperation() throws Exception {
        // 准备测试数据
        String spanName = "test-operation";
        String expectedResult = "test-result";
        
        Callable<String> operation = () -> expectedResult;

        // 由于 OpenTelemetry 的复杂性，这里主要测试方法调用不抛异常
        String result = langfuseTracer.trace(spanName, operation);
        assertEquals(expectedResult, result);
    }

    @Test
    void testTraceOperationWithUserContext() throws Exception {
        String spanName = "test-operation";
        String userId = "user123";
        String sessionId = "session456";
        String expectedResult = "test-result";
        
        Callable<String> operation = () -> expectedResult;

        String result = langfuseTracer.trace(spanName, userId, sessionId, operation);
        assertEquals(expectedResult, result);
    }

    @Test
    void testAiOperationContextBuilder() {
        AiOperationContext context = AiOperationContext.builder()
                .system("openai")
                .model("gpt-4")
                .prompt("Test prompt")
                .userId("user123")
                .sessionId("session456")
                .operationType("chat_completion")
                .temperature(0.7)
                .tags(Arrays.asList("test", "demo"))
                .build();

        assertEquals("openai", context.getSystem());
        assertEquals("gpt-4", context.getModel());
        assertEquals("Test prompt", context.getPrompt());
        assertEquals("user123", context.getUserId());
        assertEquals("session456", context.getSessionId());
        assertEquals("chat_completion", context.getOperationType());
        assertEquals(0.7, context.getTemperature());
        assertEquals(Arrays.asList("test", "demo"), context.getTags());
    }

    @Test
    void testAiOperationResultBuilder() {
        String completion = "Test completion";
        Long promptTokens = 50L;
        Long completionTokens = 100L;

        AiOperationResult result = AiOperationResult.success(completion, promptTokens, completionTokens);

        assertEquals(completion, result.getCompletion());
        assertEquals(promptTokens, result.getPromptTokens());
        assertEquals(completionTokens, result.getCompletionTokens());
        assertEquals(150L, result.getTotalTokens());
        assertTrue(result.isSuccess());
        assertNull(result.getError());
    }

    @Test
    void testAiOperationResultFailure() {
        String errorMessage = "Test error";
        AiOperationResult result = AiOperationResult.failure(errorMessage);

        assertEquals(errorMessage, result.getError());
        assertFalse(result.isSuccess());
        assertNull(result.getCompletion());
    }

    @Test
    void testChatCompletionContextFactory() {
        String model = "gpt-4";
        String prompt = "Test prompt";

        AiOperationContext context = AiOperationContext.chatCompletion(model, prompt);

        assertEquals("openai", context.getSystem());
        assertEquals(model, context.getModel());
        assertEquals(prompt, context.getPrompt());
        assertEquals("chat_completion", context.getOperationType());
    }

    @Test
    void testChatCompletionContextWithUser() {
        String model = "gpt-4";
        String prompt = "Test prompt";
        String userId = "user123";
        String sessionId = "session456";

        AiOperationContext context = AiOperationContext.chatCompletion(model, prompt, userId, sessionId);

        assertEquals("openai", context.getSystem());
        assertEquals(model, context.getModel());
        assertEquals(prompt, context.getPrompt());
        assertEquals(userId, context.getUserId());
        assertEquals(sessionId, context.getSessionId());
        assertEquals("chat_completion", context.getOperationType());
    }

    @Test
    void testEmbeddingContext() {
        String model = "text-embedding-ada-002";
        String text = "Test text for embedding";

        AiOperationContext context = AiOperationContext.embedding(model, text);

        assertEquals("openai", context.getSystem());
        assertEquals(model, context.getModel());
        assertEquals(text, context.getPrompt());
        assertEquals("embedding", context.getOperationType());
    }

    @Test
    void testCustomContext() {
        String system = "custom-ai";
        String operationType = "custom-operation";

        AiOperationContext context = AiOperationContext.custom(system, operationType);

        assertEquals(system, context.getSystem());
        assertEquals(operationType, context.getOperationType());
    }
}

package com.patsnap.drafting.observability.langfuse.config;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Langfuse 配置属性测试
 *
 * <AUTHOR> Assistant
 * @date 2025/01/31
 */
@SpringBootTest(classes = LangfuseProperties.class)
@TestPropertySource(properties = {
        "configs.com.patsnap.drafting.langfuse.enabled=true",
        "configs.com.patsnap.drafting.langfuse.public-key=pk-lf-test-key",
        "configs.com.patsnap.drafting.langfuse.secret-key=sk-lf-test-key",
        "configs.com.patsnap.drafting.langfuse.host=https://test.langfuse.com",
        "configs.com.patsnap.drafting.langfuse.service-name=test-service",
        "configs.com.patsnap.drafting.langfuse.sampling-probability=0.5"
})
class LangfusePropertiesTest {

    @Test
    void testBasicProperties() {
        LangfuseProperties properties = new LangfuseProperties();
        properties.setEnabled(true);
        properties.setPublicKey("pk-lf-test-key");
        properties.setSecretKey("sk-lf-test-key");
        properties.setHost("https://test.langfuse.com");
        properties.setServiceName("test-service");
        properties.setSamplingProbability(0.5);

        assertTrue(properties.isEnabled());
        assertEquals("pk-lf-test-key", properties.getPublicKey());
        assertEquals("sk-lf-test-key", properties.getSecretKey());
        assertEquals("https://test.langfuse.com", properties.getHost());
        assertEquals("test-service", properties.getServiceName());
        assertEquals(0.5, properties.getSamplingProbability());
    }

    @Test
    void testOtlpEndpointGeneration() {
        LangfuseProperties properties = new LangfuseProperties();
        properties.setHost("https://test.langfuse.com");

        assertEquals("https://test.langfuse.com/api/public/otel", properties.getOtlpEndpoint());

        // 测试自定义端点
        properties.setOtlpEndpoint("https://custom.endpoint.com/otel");
        assertEquals("https://custom.endpoint.com/otel", properties.getOtlpEndpoint());
    }

    @Test
    void testAuthStringGeneration() {
        LangfuseProperties properties = new LangfuseProperties();
        properties.setPublicKey("pk-test");
        properties.setSecretKey("sk-test");

        String authString = properties.getAuthString();
        assertNotNull(authString);
        
        // 验证 Base64 编码
        String decoded = new String(java.util.Base64.getDecoder().decode(authString));
        assertEquals("pk-test:sk-test", decoded);
    }

    @Test
    void testAuthStringWithNullKeys() {
        LangfuseProperties properties = new LangfuseProperties();
        
        assertNull(properties.getAuthString());
        
        properties.setPublicKey("pk-test");
        assertNull(properties.getAuthString());
        
        properties.setSecretKey("sk-test");
        assertNotNull(properties.getAuthString());
    }

    @Test
    void testConfigurationValidation() {
        LangfuseProperties properties = new LangfuseProperties();
        
        // 默认情况下无效
        assertFalse(properties.isValid());
        
        // 启用但缺少密钥
        properties.setEnabled(true);
        assertFalse(properties.isValid());
        
        // 添加公钥
        properties.setPublicKey("pk-test");
        assertFalse(properties.isValid());
        
        // 添加私钥
        properties.setSecretKey("sk-test");
        assertFalse(properties.isValid());
        
        // 添加主机
        properties.setHost("https://test.langfuse.com");
        assertTrue(properties.isValid());
        
        // 禁用后无效
        properties.setEnabled(false);
        assertFalse(properties.isValid());
    }

    @Test
    void testBatchConfigDefaults() {
        LangfuseProperties properties = new LangfuseProperties();
        LangfuseProperties.BatchConfig batch = properties.getBatch();
        
        assertNotNull(batch);
        assertEquals(512, batch.getMaxExportBatchSize());
        assertEquals(30000, batch.getExportTimeoutMillis());
        assertEquals(5000, batch.getScheduleDelayMillis());
        assertEquals(2048, batch.getMaxQueueSize());
    }

    @Test
    void testTimeoutConfigDefaults() {
        LangfuseProperties properties = new LangfuseProperties();
        LangfuseProperties.TimeoutConfig timeout = properties.getTimeout();
        
        assertNotNull(timeout);
        assertEquals(10000, timeout.getConnectTimeoutMillis());
        assertEquals(30000, timeout.getReadTimeoutMillis());
        assertEquals(30000, timeout.getWriteTimeoutMillis());
    }

    @Test
    void testRetryConfigDefaults() {
        LangfuseProperties properties = new LangfuseProperties();
        LangfuseProperties.RetryConfig retry = properties.getRetry();
        
        assertNotNull(retry);
        assertEquals(3, retry.getMaxRetries());
        assertEquals(1000, retry.getRetryDelayMillis());
        assertEquals(2.0, retry.getRetryDelayMultiplier());
    }
}

# 字段加解密配置示例
security:
  encrypt:
    # 是否启用加解密功能
    enabled: true
    
    # AES加密密钥（建议32位字符，生产环境使用环境变量）
    secret-key: ${ENCRYPT_SECRET_KEY:1870577f29b17d6787782f35998c4a79}
    
    # 加密算法（可选）
    algorithm: AES/ECB/PKCS5Padding
    
    # 字符编码（可选）
    encoding: UTF-8
    
    # 是否启用调试日志（开发环境建议开启）
    debug-log: false
    
    # 加密失败时的处理策略（可选）
    # RETURN_ORIGINAL: 返回原始值（推荐）
    # THROW_EXCEPTION: 抛出异常
    failure-strategy: RETURN_ORIGINAL

# 日志配置
logging:
  level:
    com.patsnap.security: DEBUG  # 开发环境可开启DEBUG日志 
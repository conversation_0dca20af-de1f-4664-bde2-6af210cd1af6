package com.patsnap.drafting.manager.aitranslation.handler;

import com.patsnap.common.request.CorrelationIdHolder;
import com.patsnap.core.common.copilot.streaming.chat.AssistantMessage;
import com.patsnap.core.common.copilot.streaming.handler.SplitListJsonOutputParser;
import com.patsnap.core.common.copilot.streaming.handler.SplitListJsonStreamingResponseHandler;
import com.patsnap.core.common.copilot.streaming.model.GptResponseDTO;
import com.patsnap.core.common.copilot.streaming.response.Response;
import com.patsnap.drafting.model.aitranslation.TranslationBO;
import com.patsnap.drafting.util.ParagraphUtils;
import com.patsnap.spring.boot.autoconfigure.web.entity.CommonResponse;

import static com.patsnap.drafting.manager.aitranslation.operate.TranslationConstant.FIELD_SEPARATOR_REGEX;
import static com.patsnap.drafting.manager.aitranslation.operate.TranslationConstant.LINE_SEPARATOR_REGEX;

import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;

import com.fasterxml.jackson.core.type.TypeReference;

import reactor.core.publisher.FluxSink;

/**
 * AI全文翻译的流式输出处理器
 *
 * <AUTHOR>
 * @date 2024/07/19
 */
public class AiTranslationStreamingResponseHandler extends
        SplitListJsonStreamingResponseHandler<List<TranslationBO>> {

    private final String correlationId;

    public AiTranslationStreamingResponseHandler(
            FluxSink<CommonResponse<GptResponseDTO<List<TranslationBO>>>> sink,
            String input) {
        super(sink, LINE_SEPARATOR_REGEX, FIELD_SEPARATOR_REGEX, new TypeReference<>() {
        });
        this.correlationId = CorrelationIdHolder.get();
    }

    @Override
    public void onError(Throwable error) {
        CorrelationIdHolder.set(correlationId);
        super.onError(error);
    }

    @Override
    public void onComplete(Response<AssistantMessage> response) {
        CorrelationIdHolder.set(correlationId);
        super.onComplete(response);
    }

    @Override
    protected void doAfterComplete(String content) {
    }

    @Override
    protected List<TranslationBO> convertContent(String content) {
        return covertContent(content);
    }

    public static @Nullable List<TranslationBO> covertContent(String content) {
        List<TranslationBO> sentenceTrans = new SplitListJsonOutputParser<List<TranslationBO>>(LINE_SEPARATOR_REGEX,
                FIELD_SEPARATOR_REGEX).convertContent(content, new TypeReference<>() {
        });
        // 根据标识符计算每个句子所属的段落index
        ParagraphUtils.addParagraphIndex(sentenceTrans);
        if (CollectionUtils.isEmpty(sentenceTrans)) {
            return sentenceTrans;
        }
        // 移除翻译结果中的段落标识符
        sentenceTrans.forEach(t -> {
            if (StringUtils.isNotBlank(t.getTranslatedText())) {
                t.setTranslatedText(t.getTranslatedText().replace(ParagraphUtils.PARAGRAPH_PREFIX, ""));
            }
        });
        return sentenceTrans;
    }
}

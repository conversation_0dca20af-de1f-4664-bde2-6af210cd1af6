package com.patsnap.drafting.manager.aidisclosure;

import com.patsnap.common.request.UserIdHolder;
import com.patsnap.core.common.copilot.gpt.enums.GPTModelEnum;
import com.patsnap.core.common.copilot.streaming.StreamingModelBuilder;
import com.patsnap.core.common.copilot.streaming.handler.StringStreamingResponseHandler;
import com.patsnap.core.common.copilot.streaming.model.GptResponseDTO;
import com.patsnap.core.common.identity.AccountInfo;
import com.patsnap.drafting.annotation.TaskContentCache;
import com.patsnap.drafting.client.ComputeClient;
import com.patsnap.drafting.client.OpenAiClient;
import com.patsnap.drafting.client.PatentApiClient;
import com.patsnap.drafting.client.model.DisclosureReqDTO;
import com.patsnap.drafting.config.UrlConfig;
import com.patsnap.drafting.enums.aidisclosure.DisclosureModeEnum;
import com.patsnap.drafting.enums.prompt.ScenarioEnum;
import com.patsnap.drafting.enums.task.AiDisclosureStepEnum;
import com.patsnap.drafting.enums.task.AiTaskContentTypeEnum;
import com.patsnap.drafting.enums.task.AiTaskTypeEnum;
import com.patsnap.drafting.handler.disclosure.PatentDisclosureStreamingResponseHandler;
import com.patsnap.drafting.manager.FileManager;
import com.patsnap.drafting.manager.IdentityAccountManager;
import com.patsnap.drafting.manager.aitask.AiTaskManager;
import com.patsnap.drafting.manager.content.TaskContentManager;
import com.patsnap.drafting.model.aidisclosure.RecommendSettingBO;
import com.patsnap.drafting.model.aidisclosure.StructUserInputBO;
import com.patsnap.drafting.repository.aitask.entity.AnalyticsAiTaskHistoryPO;
import com.patsnap.drafting.request.GenerateContentRequestDTO;
import com.patsnap.drafting.request.aidisclosure.DisclosureExportReqDTO;
import com.patsnap.drafting.request.aidisclosure.DisclosureSettingUpdateReqDTO;
import com.patsnap.drafting.request.aidisclosure.DisclosureTechMeansReqDTO;
import com.patsnap.drafting.request.aidisclosure.DisclosureTechMeansUpdateReqDTO;
import com.patsnap.drafting.request.aitask.AiTaskCreateReqDTO;
import com.patsnap.drafting.request.aitask.AiTaskReqDTO;
import com.patsnap.drafting.response.aidisclosure.DisclosureResDTO;
import com.patsnap.drafting.response.aidisclosure.DisclosureSettingResDTO;
import com.patsnap.drafting.response.aidisclosure.DisclosureTechMeansResDTO;
import com.patsnap.drafting.util.ReferenceReplacer;
import com.patsnap.spring.boot.autoconfigure.web.entity.CommonResponse;
import com.patsnap.core.common.copilot.streaming.handling.StreamingChatLanguageModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.entity.ContentType;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Stream;

import static com.patsnap.drafting.enums.task.AiTaskContentTypeEnum.USER_INPUT;

@Slf4j
@Service
public class PatentDisclosureManager {

    private static final GPTModelEnum DEFAULT_GPT_MODEL = GPTModelEnum.DISCLOSURE_GPT;
    private final ComputeClient computeService;
    private final OpenAiClient openAiClient;
    private final AiTaskManager aiTaskManager;
    private final StreamingModelBuilder streamingModelBuilder;
    private final IdentityAccountManager identityAccountManager;
    private final FileManager fileManager;
    private final PatentDisclosureWordGenerator patentDisclosureWordGenerator;
    private final TaskContentManager taskContentManager;
    private final PatentApiClient patentApiClient;  // 生成内容的标识



    public PatentDisclosureManager(ComputeClient computeService, OpenAiClient openAiClient,
            AiTaskManager aiTaskManager, UrlConfig urlConfig,
            IdentityAccountManager identityAccountManager, FileManager fileManager,
            PatentDisclosureWordGenerator patentDisclosureWordGenerator, TaskContentManager taskContentManager,
            PatentApiClient patentApiClient) {
        this.computeService = computeService;
        this.openAiClient = openAiClient;
        this.aiTaskManager = aiTaskManager;
        this.streamingModelBuilder = new StreamingModelBuilder(urlConfig.getGptBaseUrl(), urlConfig.getGptApiKey());
        this.identityAccountManager = identityAccountManager;
        this.fileManager = fileManager;
        this.patentDisclosureWordGenerator = patentDisclosureWordGenerator;
        this.taskContentManager = taskContentManager;
        this.patentApiClient = patentApiClient;
    }

    /**
     * 请求SA接口获取不同场景的prompt后, 请求GPT获取结果
     *
     * @param taskInfo 交底书撰写任务信息
     * @param mode     SA定义的操作模式 {@link DisclosureModeEnum}
     * @param text     如果mode为simplify/expand/polish/other_extract，则该字段为待处理的文本信息
     * @param clazz    返回结果类型
     * @return GPT结果
     */
    public <T> T getGptResult(AnalyticsAiTaskHistoryPO taskInfo, DisclosureModeEnum mode, String modelName, String text,
            Class<T> clazz) {
        DisclosureReqDTO params = buildRdDisclosure(taskInfo, mode, text);
        DisclosureResDTO disclosurePrompt = computeService.getDisclosurePrompt(params);
        return openAiClient.callGptByPrompt(getGptModelEnum(modelName),
                disclosurePrompt.getPrompt(), ScenarioEnum.PATENT_DISCLOSURE, clazz);
    }

    /**
     * 构建R&D接口请求参数
     *
     * @param taskInfo 交底书撰写任务信息
     * @param mode     SA定义的操作模式 {@link DisclosureModeEnum}
     * @param text     如果mode为simplify/expand/polish，则该字段为待处理的文本信息
     * @return R&D接口请求参数
     */
    private DisclosureReqDTO buildRdDisclosure(AnalyticsAiTaskHistoryPO taskInfo, DisclosureModeEnum mode,
            String text) {
        Map<AiTaskContentTypeEnum, Object> taskDetail = aiTaskManager.getTaskDetail(taskInfo.getId());

        DisclosureSettingResDTO disclosureSetting = (DisclosureSettingResDTO) taskDetail.get(AiTaskContentTypeEnum.SETTING);
        DisclosureReqDTO requestParams = new DisclosureReqDTO(mode.getMode());
        requestParams.setTitle(taskInfo.getTitle());
        requestParams.setText(text);
        requestParams.setPatentIds(disclosureSetting.getPatentIds());
        requestParams.setTypeSelect((String) taskDetail.get(AiTaskContentTypeEnum.TYPE_SELECT));
        requestParams.setEmbodiment((String) taskDetail.get(AiTaskContentTypeEnum.EMBODIMENT));
        requestParams.setBackground(getBackground(taskDetail));
        requestParams.setTechEffect((String) taskDetail.get(AiTaskContentTypeEnum.TECH_EFFECT));
        requestParams.setTechProblem((String) taskDetail.get(AiTaskContentTypeEnum.TECH_PROBLEM));
        requestParams.setTechMeans((String) taskDetail.get(AiTaskContentTypeEnum.TECH_MEANS));
        requestParams.setDrawbacks(getDrawbacks(taskDetail));
        requestParams.setReference((String) taskDetail.get(AiTaskContentTypeEnum.REFERENCE));
        requestParams.setTechField((String) taskDetail.get(AiTaskContentTypeEnum.TECH_FIELD));
        requestParams.setOriginUserInput((String) taskDetail.get(USER_INPUT));
        requestParams.setStructUserInput(buildStructInfo(disclosureSetting));
        return requestParams;
    }

    private static String getDrawbacks(Map<AiTaskContentTypeEnum, Object> taskDetail) {
        String drawbacks = (String) taskDetail.get(AiTaskContentTypeEnum.DRAWBACKS);
        return cleanContent(drawbacks);
    }

    private static String getBackground(Map<AiTaskContentTypeEnum, Object> taskDetail) {
        String background = (String) taskDetail.get(AiTaskContentTypeEnum.BACKGROUND);
        return cleanContent(background);
    }

    /**
     *  去除 content 中的 [] 以及 [] 中的内容
     */
    private static String cleanContent(String content) {
        if (StringUtils.isBlank(content)) {
            return null;
        }
        // 去除 content 中的 [] 以及 [] 中的内容
        return content.replaceAll("\\[.*?]", "");
    }

    private StructUserInputBO buildStructInfo(DisclosureSettingResDTO setting) {
        if (setting == null) {
            return null;
        }
        StructUserInputBO structUserInput = new StructUserInputBO();
        Optional.ofNullable(setting.getApplicationField()).orElseGet(Collections::emptyList).stream()
                .findFirst()
                .ifPresent(structUserInput::setApplicationField);
        structUserInput.setEmbodiment(setting.getEmbodiment());
        Optional.ofNullable(setting.getInventionSubject()).orElseGet(Collections::emptyList).stream()
                .findFirst()
                .ifPresent(structUserInput::setInventionSubject);
        structUserInput.setTechBackground(setting.getTechBackground());
        structUserInput.setTechEffect(setting.getTechEffect());
        structUserInput.setTechMeans(setting.getTechMeans());
        structUserInput.setTechProblem(setting.getTechProblem());
        return structUserInput;
    }

    @TaskContentCache(contentType = AiTaskContentTypeEnum.SETTING)
    public DisclosureSettingResDTO generateTechMeans(DisclosureTechMeansReqDTO request, String modelName) {
        aiTaskManager.checkSensitiveWords(List.of(request.getInput()));
        DisclosureSettingResDTO disclosureSettingRes = generateDisclosureSettingResDTO(request, modelName);
        // TechMeans 存在时，才创建任务，若不存在，则不创建任务，交给 updateTechMeans 方法创建
        if (StringUtils.isNotBlank(disclosureSettingRes.getTechMeans())) {
            aiTaskManager.createTask(buildAiTaskCreateReqDTO(request));
        }
        return disclosureSettingRes;
    }

    private AiTaskCreateReqDTO buildAiTaskCreateReqDTO(DisclosureTechMeansReqDTO request) {
        AiTaskCreateReqDTO createReq = new AiTaskCreateReqDTO();
        createReq.setTaskId(request.getTaskId());
        createReq.setType(AiTaskTypeEnum.AI_PATENT_DISCLOSURE.getType());
        createReq.setContent(Map.of(AiTaskContentTypeEnum.USER_INPUT.getType(), request.getInput()));
        return createReq;
    }

    private DisclosureSettingResDTO generateDisclosureSettingResDTO(DisclosureTechMeansReqDTO request,
            String modelName) {
        DisclosureReqDTO params = new DisclosureReqDTO(DisclosureModeEnum.EXTRACT.getMode());
        params.setOriginUserInput(request.getInput());
        DisclosureResDTO disclosurePrompt = computeService.getDisclosurePrompt(params);
        log.info("Generate Disclosure Prompt : {}",disclosurePrompt.getPrompt());
        StructUserInputBO techMeans = openAiClient.callGptByPrompt(
                getGptModelEnum(modelName), disclosurePrompt.getPrompt(), ScenarioEnum.PATENT_DISCLOSURE,
                StructUserInputBO.class);
        DisclosureSettingResDTO res = buildDisclosureTechMeansRes(techMeans, disclosurePrompt.getPatentIds());
        // add recommend
        addRecommendInfo(res, techMeans, params, modelName);
        return res;
    }

    /**
     * 添加改进主体,应用领域的推荐信息
     *
     * @param res       技术手段响应
     * @param techMeans 结构化用户输入
     * @param params    SA接口请求参数
     */
    private void addRecommendInfo(DisclosureSettingResDTO res, StructUserInputBO techMeans, DisclosureReqDTO params,
            String modelName) {
        params.setMode(DisclosureModeEnum.RECOMMEND.getMode());
        params.setStructUserInput(techMeans);
        params.setPatentIds(res.getPatentIds());
        DisclosureResDTO disclosurePrompt = computeService.getDisclosurePrompt(params);
        RecommendSettingBO recommends = openAiClient.callGptByPrompt(
                getGptModelEnum(modelName), disclosurePrompt.getPrompt(), ScenarioEnum.PATENT_DISCLOSURE,
                RecommendSettingBO.class);
        if (recommends != null) {
            res.setRecommendInventionSubject(recommends.getRecommendInventionSubject());
            res.setRecommendApplicationField(recommends.getRecommendApplicationField());
        }
    }

    /**
     * 构建提取技术手段响应
     *
     * @param techMeans 技术手段
     * @param patentIds 语义检索后top20专利号
     * @return 提取技术手段响应
     */
    private DisclosureSettingResDTO buildDisclosureTechMeansRes(StructUserInputBO techMeans, List<String> patentIds) {
        if (techMeans == null) {
            return new DisclosureSettingResDTO();
        }
        DisclosureSettingResDTO res = new DisclosureSettingResDTO();
        if (techMeans.getInventionSubject() != null) {
            res.setInventionSubject(Collections.singletonList(techMeans.getInventionSubject()));
        }
        if (techMeans.getApplicationField() != null) {
            res.setApplicationField(Collections.singletonList(techMeans.getApplicationField()));
        }
        res.setTechMeans(techMeans.getTechMeans());
        res.setTechEffect(techMeans.getTechEffect());
        res.setTechBackground(techMeans.getTechBackground());
        res.setTechProblem(techMeans.getTechProblem());
        res.setEmbodiment(techMeans.getEmbodiment());
        res.setOther(getOther(res));
        res.setPatentIds(patentIds);
        return res;
    }

    /**
     * 将技术背景,技术问题,实施例拼接到一起, 用于前端展示
     *
     * @param res 技术手段响应
     * @return 技术背景,技术问题,实施例拼接到一起
     */
    public String getOther(DisclosureTechMeansResDTO res) {
        List<String> items = Stream.of(res.getTechBackground(), res.getTechProblem(), res.getEmbodiment())
                .filter(StringUtils::isNotBlank).toList();
        if (CollectionUtils.isEmpty(items)) {
            return null;
        }
        return String.join("\n", items);
    }

    /**
     * 更新技术手段
     *
     * @param request 更新技术手段请求参数
     * @return 更新技术手段响应
     */
    public DisclosureSettingResDTO updateTechMeans(DisclosureTechMeansUpdateReqDTO request) {
        aiTaskManager.checkSensitiveWords(List.of(request.getTechMeans()));
        aiTaskManager.checkPermission(request.getTaskId());
        DisclosureSettingResDTO settingRes = aiTaskManager.getTaskContent(request.getTaskId(), AiTaskContentTypeEnum.SETTING);
        settingRes.setTechMeans(request.getTechMeans());
        aiTaskManager.updateTaskContent(request.getTaskId(), AiTaskContentTypeEnum.SETTING, settingRes);
        return settingRes;
    }

    /**
     * 更新交底书设置,更新交底书设置时, 若other发生变化, 需要重新抽取技术背景,技术问题,实施例，同时需要重新生成内容，清理历史内容
     *
     * @param request 更新交底书设置请求参数
     * @return 更新交底书设置响应
     */
    public DisclosureSettingResDTO updateSetting(DisclosureSettingUpdateReqDTO request, String modelName) {
        aiTaskManager.checkSensitiveWords(List.of(request.getOther(), request.getTechEffect(), request.getTechMeans()));
        AnalyticsAiTaskHistoryPO aiTask = aiTaskManager.checkPermission(request.getTaskId());
        DisclosureSettingResDTO settingRes = aiTaskManager.getTaskContent(request.getTaskId(), AiTaskContentTypeEnum.SETTING);
        changeSettingOther(request, settingRes, aiTask, modelName);
        aiTaskManager.updateTaskContent(request.getTaskId(), AiTaskContentTypeEnum.SETTING, settingRes);
        regenerate(request);
        return settingRes;
    }

    /**
     * 更新配置信息到settingRes, 同时判断other有没发生变化, 若发生了改变需要重新抽取技术背景,技术问题,实施例
     *
     * @param request    更新交底书设置请求参数
     * @param settingRes 交底书设置响应
     */
    private void changeSettingOther(DisclosureSettingUpdateReqDTO request, DisclosureTechMeansResDTO settingRes,
            AnalyticsAiTaskHistoryPO aiTask, String modelName) {
        settingRes.setInventionSubject(request.getInventionSubject());
        settingRes.setApplicationField(request.getApplicationField());
        settingRes.setTechMeans(request.getTechMeans());
        settingRes.setTechEffect(request.getTechEffect());
        if (!StringUtils.equals(settingRes.getOther(), request.getOther())) {
            StructUserInputBO structUserInputBO = getGptResult(aiTask, DisclosureModeEnum.OTHER_EXTRACT, modelName,
                    request.getOther(), StructUserInputBO.class);
            settingRes.setTechBackground(structUserInputBO.getTechBackground());
            settingRes.setTechProblem(structUserInputBO.getTechProblem());
            settingRes.setEmbodiment(structUserInputBO.getEmbodiment());
        }
        settingRes.setOther(request.getOther());
    }

    /**
     * 生成交底书标题
     */
    @TaskContentCache(contentType = AiTaskContentTypeEnum.TITLE)
    public String generateTitle(AiTaskReqDTO request, String modelName) {
        AnalyticsAiTaskHistoryPO aiTask = aiTaskManager.checkPermission(request.getTaskId());
        // 获取用于输入内容的类型
        String typeSelect = getGptResult(aiTask, DisclosureModeEnum.CLASSIFY, modelName, null, String.class);
        aiTaskManager.updateTaskContent(request.getTaskId(),AiTaskContentTypeEnum.TYPE_SELECT, typeSelect);
        // 获取交底书标题
        return getGptResult(aiTask, DisclosureModeEnum.TITLE, modelName, null, String.class);
    }

    /**
     * 根据类型生成交底书正文
     *
     * @param request 生成交底书正文请求参数
     * @return 交底书正文
     */
    @TaskContentCache(contentTypeExpression = "#request.contentType")
    public Flux<CommonResponse<GptResponseDTO<String>>> generateContent(GenerateContentRequestDTO request) {
        AnalyticsAiTaskHistoryPO task = aiTaskManager.checkPermission(request.getTaskId());

        DisclosureModeEnum disclosureMode = DisclosureModeEnum.fromValue(request.getContentType());
        DisclosureReqDTO params = buildRdDisclosure(task, disclosureMode, request.getText());
        DisclosureResDTO disclosurePrompt = computeService.getDisclosurePrompt(params);

        return getResponseFlux(disclosurePrompt, params);
    }

    private @NotNull Flux<CommonResponse<GptResponseDTO<String>>> getResponseFlux(
            DisclosureResDTO disclosurePrompt, DisclosureReqDTO params) {
        GPTModelEnum modelEnum = getGptModelEnum(null);
        StreamingChatLanguageModel model = streamingModelBuilder.build(modelEnum, ScenarioEnum.PATENT_DISCLOSURE.getValue());
        return Flux.create(sink -> {
            StringStreamingResponseHandler handler = new PatentDisclosureStreamingResponseHandler(sink,
                    disclosurePrompt.getPrompt(), new ReferenceReplacer(params.getPatentIds(), params.getMode(), patentApiClient));
            model.generate(disclosurePrompt.getPrompt(), handler);
            sink.onCancel(handler::onCancel);
        });
    }

    /**
     * 根据类型生成交底书正文
     *
     * @param request 生成交底书正文请求参数
     * @return 交底书正文
     */
    @TaskContentCache(contentTypeExpression = "#request.contentType")
    public Flux<CommonResponse<GptResponseDTO<String>>> optimizeContent(GenerateContentRequestDTO request) {
        AnalyticsAiTaskHistoryPO task = aiTaskManager.checkPermission(request.getTaskId());

        DisclosureModeEnum disclosureMode = DisclosureModeEnum.fromValue(request.getOperateType());
        DisclosureReqDTO params = buildRdDisclosure(task, disclosureMode, request.getText());
        DisclosureResDTO disclosurePrompt = computeService.getDisclosurePrompt(params);

        return getResponseFlux(disclosurePrompt, params);
    }

    public Boolean regenerate(AiTaskReqDTO request) {
        AnalyticsAiTaskHistoryPO task = aiTaskManager.checkPermission(request.getTaskId());
        List<String> subStepList = new ArrayList<>(AiDisclosureStepEnum.DISCLOSURE_RESULT.getSubStepList());
        subStepList.removeAll(List.of(AiTaskContentTypeEnum.SETTING.getType(), AiTaskContentTypeEnum.TITLE.getType()));
        return aiTaskManager.regenerate(task, subStepList);
    }

    private static @NotNull GPTModelEnum getGptModelEnum(String modelName) {
        GPTModelEnum modelEnum = Optional.ofNullable(modelName).map(GPTModelEnum::getGptModelEnum)
                .orElse(DEFAULT_GPT_MODEL);
        log.info("call gpt with model:[{}]", modelEnum.getModelName());
        return modelEnum;
    }

    public String exportDisclosure(DisclosureExportReqDTO request) {
        AccountInfo accountInfo = identityAccountManager.getAccountInfoByUserId(UserIdHolder.get());
        if (accountInfo != null) {
            request.setAuthor(accountInfo.getNickname());
            request.setEmail(accountInfo.getEmail());
            request.setPhone(accountInfo.getMobileNumber());
            request.setInventor(accountInfo.getNickname());
        }
        byte[] bytes = patentDisclosureWordGenerator.generateWord(request);
        String fileName = StringUtils.defaultIfBlank(request.getTitle(), String.valueOf(System.currentTimeMillis()));
        String s3key =
                "ai_lab/" + UserIdHolder.get() + "/" + System.currentTimeMillis() + "/" + fileName + ".docx";
        return fileManager.uploadFile2AmazonS3(bytes, s3key, ContentType.MULTIPART_FORM_DATA);
    }
}

package com.patsnap.drafting.manager.aitranslation.operate.impl;

import com.patsnap.core.common.copilot.gpt.enums.GPTModelEnum;
import com.patsnap.core.common.copilot.streaming.handler.StringStreamingResponseHandler;
import com.patsnap.core.common.copilot.streaming.model.GptResponseDTO;
import com.patsnap.drafting.client.OpenAiClient;
import com.patsnap.drafting.config.AiTranslationConfig;
import com.patsnap.drafting.config.UrlConfig;
import com.patsnap.drafting.enums.prompt.PromptKeyEnum;
import com.patsnap.drafting.enums.prompt.ScenarioEnum;
import com.patsnap.drafting.manager.aitranslation.handler.AiTranslationSentenceStreamingResponseHandler;
import com.patsnap.drafting.manager.aitranslation.operate.TranslationRewriteService;
import com.patsnap.drafting.manager.content.TaskContentManager;
import com.patsnap.drafting.model.aitranslation.AiTransContextBo;
import com.patsnap.drafting.model.aitranslation.TranslationBO;
import com.patsnap.spring.boot.autoconfigure.web.entity.CommonResponse;
import static com.patsnap.drafting.manager.aitranslation.operate.TranslationConstant.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Component;
import com.patsnap.core.common.copilot.streaming.handling.StreamingChatLanguageModel;
import reactor.core.publisher.Flux;

@Component
public class RewriteGptModel4TurboImpl extends TranslationBasicImpl implements
        TranslationRewriteService {

    private static GPTModelEnum GPT_MODEL = GPTModelEnum.GPT_MODEL_4_TURBO;

    private final TaskContentManager taskContentManager;

    public RewriteGptModel4TurboImpl(UrlConfig urlConfig, AiTranslationConfig aiTranslationConfig,
            OpenAiClient openAiClient, TaskContentManager taskContentManager) {
        super(urlConfig, aiTranslationConfig, openAiClient);
        this.taskContentManager = taskContentManager;
    }

    @Override
    public String model() {
        return GPT_MODEL.getModelName();
    }

    @Override
    public Flux<CommonResponse<GptResponseDTO<String>>> generate(AiTransContextBo contextBo,
            int index,
            String original) {
        // 翻译
        String msg = openAiClient.buildPromptByPlatform(PromptKeyEnum.SENTENCE_REWRITING.getValue(),
                buildPromptParams(index, original, contextBo));
        StreamingChatLanguageModel model = streamingModelBuilder.build(
                GPTModelEnum.GPT_MODEL_4_TURBO,
                ScenarioEnum.AI_TRANSLATION.getValue());
        return Flux.create(sink -> {
            StringStreamingResponseHandler handler =
                    new AiTranslationSentenceStreamingResponseHandler(
                    sink, msg, contextBo.getTaskId(),
                    index, original, taskContentManager);
            model.generate(msg, handler);
            sink.onCancel(handler::onCancel);
        });
    }

    private Map<String, String> buildPromptParams(int index, String original,
            AiTransContextBo contextBo) {
        String keywordsStr = "";
        Map<String, String> params = new HashMap<>();
        params.put(INPUT, original);
        params.put(CONTEXT, buildContext(original, index, new ArrayList<>()));
        params.put(TARGET_LANG, contextBo.getTargetLang());
        params.put(KEYWORDS, keywordsStr);
        params.put(TECH_TOPIC, contextBo.getTechTopic());
        return params;
    }

    /**
     * 构建当前修改的待翻译句子的上下文, 上下文取当前句子前3句+后3句
     *
     * @param originalText  更改后的原文内容
     * @param originalIndex 更改的原文在整体翻译句子List中的索引
     * @param sentenceTrans
     * @return
     */
    private String buildContext(String originalText, Integer originalIndex,
            List<TranslationBO> sentenceTrans) {
        int from = Math.max(0, originalIndex - 3);
        int to = Math.min(sentenceTrans.size(), originalIndex + 3);
        // 获取上下文, 需要翻译的句子用标签<need_translate></need_translate>标记
        StringBuilder sb = new StringBuilder();
        for (int i = from; i < to; i++) {
            if (i == originalIndex) {
                sb.append("<need_translate>").append(originalText).append("</need_translate>");
            } else {
                sb.append(sentenceTrans.get(i).getSrcText());
            }
        }
        return sb.toString();
    }
}

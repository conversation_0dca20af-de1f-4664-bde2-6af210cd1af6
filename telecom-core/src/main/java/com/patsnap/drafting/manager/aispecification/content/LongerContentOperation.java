package com.patsnap.drafting.manager.aispecification.content;

import com.patsnap.drafting.client.OpenAiClient;
import com.patsnap.drafting.config.UrlConfig;
import com.patsnap.drafting.enums.common.OperateTypeEnum;
import com.patsnap.drafting.manager.aispecification.content.base.AbstractStreamingContentOperation;
import com.patsnap.drafting.manager.aitask.AiTaskManager;
import com.patsnap.drafting.manager.credit.CreditManager;
import org.springframework.stereotype.Service;

import static com.patsnap.drafting.enums.prompt.SpecificationPromptKeyEnum.LONGER_CONTENT;

/**
 * AI说明书撰写 - 内容扩写
 */
@Service
public class LongerContentOperation extends AbstractStreamingContentOperation {


    protected LongerContentOperation(AiTaskManager aiTaskManager, UrlConfig urlConfig, OpenAiClient openAiClient,
            CreditManager creditManager) {
        super(aiTaskManager, urlConfig, openAiClient, creditManager);
    }

    @Override
    public OperateTypeEnum getOperationType() {
        return OperateTypeEnum.EXPAND;
    }

    @Override
    protected String getPromptKey(String contenType) {
        return LONGER_CONTENT.getValue();
    }
}

package com.patsnap.drafting.manager.aispecification.content.strategy;

import com.patsnap.core.common.copilot.streaming.model.GptResponseDTO;
import com.patsnap.drafting.enums.common.OperateTypeEnum;
import com.patsnap.drafting.exception.BizException;
import com.patsnap.drafting.exception.errorcode.ContentErrorCodeEnum;
import com.patsnap.drafting.manager.AbstractGenerateStreamingContent;
import com.patsnap.drafting.manager.aispecification.content.base.ContentOperation;
import com.patsnap.drafting.manager.aitask.AiTaskManager;
import com.patsnap.drafting.request.GenerateContentRequestDTO;
import com.patsnap.spring.boot.autoconfigure.web.entity.CommonResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;


@Service
@Slf4j
public class SpecificationContentManager {

    @Autowired
    private ContentGenerateHandlerFactory contentGenerateHandlerFactory;
    @Autowired
    private AiTaskManager aiTaskManager;


    /**
     * 获取AI生成的内容
     *
     * @param request 请求对象OperateTypeEnum.value
     * @return Flux对象
     */
    public Flux<CommonResponse<GptResponseDTO<Object>>> getAiContent(GenerateContentRequestDTO request) {
        //获取内容类型
        String contentType = request.getContentType();
        
        AbstractGenerateStreamingContent handler = contentGenerateHandlerFactory.getHandler(
                contentType);
        return handler.doGenerate(request);
    }


    /**
     * 修改内容，优化改写，扩写，简写，续写
     *
     * @param request   请求对象OperateTypeEnum.value
     * @param operation 操作内容对应
     * @return Flux对象
     */
    public Flux<CommonResponse<GptResponseDTO<String>>> modifyContent(GenerateContentRequestDTO request,
            String operation) {
        aiTaskManager.checkEditPermission(request.getTaskId());
        OperateTypeEnum operateTypeEnum = OperateTypeEnum.fromValue(operation);
        if (operateTypeEnum == null) {
            throw new BizException(ContentErrorCodeEnum.OPERATION_TYPE_INVALID);
        }
        ContentOperation handler = contentGenerateHandlerFactory.getHandler(operateTypeEnum);
        return handler.getContent(request);
    }
}

package com.patsnap.drafting.manager.init;

import com.patsnap.common.request.RoleIdsHolder;
import com.patsnap.common.request.UserIdHolder;
import com.patsnap.core.common.acl.AclConstants;
import com.patsnap.core.common.acl.AclResourceConstants;
import com.patsnap.core.common.identity.AccountInfo;
import com.patsnap.drafting.manager.IdentityAccountManager;
import com.patsnap.drafting.manager.acl.AclCheckManager;
import com.patsnap.drafting.manager.credit.CreditManager;
import com.patsnap.drafting.response.init.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTimeZone;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.patsnap.drafting.constants.Constant.*;

@Service
@Slf4j
@RequiredArgsConstructor
public class AIDraftingInitManager {
    
    public static final String DEFAULT_ZONE_ID = "UTC";
    
    private final IdentityAccountManager identityAccountManager;
    private final AclCheckManager aclCheckManager;
    private final CreditManager creditManager;

    public AiDraftingInitResDTO init() {
        //获取用户时区信息
        TimeZone timeZone = getUserTimeZone();
        
        //获取查新次数使用情况
        NoveltySearchUsage noveltySearchUsageRes = getNoveltySearchUsage();

        //获取FTO次数使用情况
        FtoSearchUsage ftoSearchUsageRes = getFtoSearchUsage();

        // 获取翻译次数使用情况
        TranslationUsage translationUsage = getTranslationUsage();

        // 获取专利交底书次数使用情况
        PatentDisclosureUsage patentDisclosureUsage = getPatentDisclosureUsage();

        // 获取专利说明书次数使用情况(CNIPA,USPTO,EPO,UNIFIED)
        PatentSpecificationUsage cnPatentSpecificationUsage = getCnPatentSpecificationUsage();
        PatentSpecificationUsage usPatentSpecificationUsage = getUsPatentSpecificationUsage();
        PatentSpecificationUsage euPatentSpecificationUsage = getEuPatentSpecificationUsage();
        PatentSpecificationUsage unifiedPatentSpecificationUsage = getUnifiedPatentSpecificationUsage();

        //获取权限信息
        DraftingPermission permission = getDraftingPermission();
        //如果有专利说明书统一权限但没有单独容量权限，则cu,us,eu说明书也一并拥有权限，并且则cn,us,eu说明书的容量都使用统一的容量
        if (permission.isAiSpecificationUnified() && !permission.isAiSpecificationCn() && !permission.isAiSpecificationUs() && !permission.isAiSpecificationEu()) {
            permission.setAiSpecificationCn(true);
            permission.setAiSpecificationUs(true);
            permission.setAiSpecificationEu(true);

            cnPatentSpecificationUsage = unifiedPatentSpecificationUsage;
            usPatentSpecificationUsage = unifiedPatentSpecificationUsage;
            euPatentSpecificationUsage = unifiedPatentSpecificationUsage;
        }
        
        return AiDraftingInitResDTO.builder()
                .timeZone(timeZone)
                .permission(permission)
                .noveltySearchUsage(noveltySearchUsageRes)
                .ftoSearchUsage(ftoSearchUsageRes)
                .translationUsage(translationUsage)
                .patentDisclosureUsage(patentDisclosureUsage)
                .cnPatentSpecificationUsage(cnPatentSpecificationUsage)
                .usPatentSpecificationUsage(usPatentSpecificationUsage)
                .euPatentSpecificationUsage(euPatentSpecificationUsage)
                .unifiedPatentSpecificationUsage(unifiedPatentSpecificationUsage)
                .build();
    }

    private FtoSearchUsage getFtoSearchUsage() {
        Integer ftoSearchLimit = creditManager.getSearchLimit(AI_FTO_SEARCH_LIMIT);
        Integer ftoSearchUsage = creditManager.getSearchUsage(AI_FTO_SEARCH_LIMIT);
        return FtoSearchUsage.builder()
                .total(ftoSearchLimit).usage(ftoSearchUsage).build();
    }

    private NoveltySearchUsage getNoveltySearchUsage() {
        Integer noveltySearchLimit = creditManager.getSearchLimit(AI_NOVELTY_SEARCH_LIMIT);
        Integer noveltySearchUsage = creditManager.getSearchUsage(AI_NOVELTY_SEARCH_LIMIT);
        return NoveltySearchUsage.builder()
                .total(noveltySearchLimit).usage(noveltySearchUsage).build();
    }

    private TranslationUsage getTranslationUsage() {
        Integer translationLimit = creditManager.getSearchLimit(AI_TRANSLATION_LIMIT);
        Integer translationUsage = creditManager.getSearchUsage(AI_TRANSLATION_LIMIT);
        return TranslationUsage.builder()
                .total(translationLimit).usage(translationUsage).build();
    }

    private PatentDisclosureUsage getPatentDisclosureUsage() {
        Integer patentDisclosureLimit = creditManager.getSearchLimit(AI_PATENT_DISCLOSURE_LIMIT);
        Integer patentDisclosureUsage = creditManager.getSearchUsage(AI_PATENT_DISCLOSURE_LIMIT);
        return PatentDisclosureUsage.builder()
                .total(patentDisclosureLimit).usage(patentDisclosureUsage).build();
    }

    private PatentSpecificationUsage getCnPatentSpecificationUsage() {
        Integer cnPatentSpecificationLimit = creditManager.getSearchLimit(AI_SPECIFICATION_CN_LIMIT);
        Integer cnPatentSpecificationUsage = creditManager.getSearchUsage(AI_SPECIFICATION_CN_LIMIT);
        return PatentSpecificationUsage.builder()
                .total(cnPatentSpecificationLimit).usage(cnPatentSpecificationUsage).build();
    }

    private PatentSpecificationUsage getUsPatentSpecificationUsage() {
        Integer usPatentSpecificationLimit = creditManager.getSearchLimit(AI_SPECIFICATION_US_LIMIT);
        Integer usPatentSpecificationUsage = creditManager.getSearchUsage(AI_SPECIFICATION_US_LIMIT);
        return PatentSpecificationUsage.builder()
                .total(usPatentSpecificationLimit).usage(usPatentSpecificationUsage).build();
    }
    
    private PatentSpecificationUsage getEuPatentSpecificationUsage() {
        Integer euPatentSpecificationLimit = creditManager.getSearchLimit(AI_SPECIFICATION_EU_LIMIT);
        Integer euPatentSpecificationUsage = creditManager.getSearchUsage(AI_SPECIFICATION_EU_LIMIT);
        return PatentSpecificationUsage.builder()
                .total(euPatentSpecificationLimit).usage(euPatentSpecificationUsage).build();
    }

    private PatentSpecificationUsage getUnifiedPatentSpecificationUsage() {
        Integer unifiedPatentSpecificationLimit = creditManager.getSearchLimit(AI_SPECIFICATION_UNIFIED_LIMIT);
        Integer unifiedPatentSpecificationUsage = creditManager.getSearchUsage(AI_SPECIFICATION_UNIFIED_LIMIT);
        return PatentSpecificationUsage.builder()
                .total(unifiedPatentSpecificationLimit).usage(unifiedPatentSpecificationUsage).build();
    }
    
    private DraftingPermission getDraftingPermission() {
        List<AclConstants.AclCheckObject> resourcesList = List.of(AclResourceConstants.DRAFTING_AI_TRANSLATION,
                AclResourceConstants.DRAFTING_AI_DISCLOSURE, AclResourceConstants.DRAFTING_AI_SPECIFICATION_CN,
                AclResourceConstants.DRAFTING_AI_SPECIFICATION_US,AclResourceConstants.DRAFTING_AI_SPECIFICATION_EU,
                AclResourceConstants.DRAFTING_AI_NOVELTY_SEARCH, AclResourceConstants.DRAFTING_AI_FTO_SEARCH,
                AclResourceConstants.DRAFTING_AI_SPECIFICATION_UNIFIED);
        Map<String, Boolean> aclMap = aclCheckManager.batchCheck(RoleIdsHolder.get(), resourcesList);
        return DraftingPermission.builder()
                .aiTranslation(aclMap.get(AclResourceConstants.DRAFTING_AI_TRANSLATION.getResourceName()))
                .aiDisclosure(aclMap.get(AclResourceConstants.DRAFTING_AI_DISCLOSURE.getResourceName()))
                .aiSpecificationCn(aclMap.get(AclResourceConstants.DRAFTING_AI_SPECIFICATION_CN.getResourceName()))
                .aiSpecificationUs(aclMap.get(AclResourceConstants.DRAFTING_AI_SPECIFICATION_US.getResourceName()))
                .aiSpecificationEu(aclMap.get(AclResourceConstants.DRAFTING_AI_SPECIFICATION_EU.getResourceName()))
                .aiNoveltySearch(aclMap.get(AclResourceConstants.DRAFTING_AI_NOVELTY_SEARCH.getResourceName()))
                .aiFtoSearch(aclMap.get(AclResourceConstants.DRAFTING_AI_FTO_SEARCH.getResourceName()))
                .aiSpecificationUnified(aclMap.get(AclResourceConstants.DRAFTING_AI_SPECIFICATION_UNIFIED.getResourceName()))
                .build();
    }
    
    private TimeZone getUserTimeZone() {
        AccountInfo accountInfo = identityAccountManager.getAccountInfoByUserId(UserIdHolder.get());
        String timeZoneId = Optional.ofNullable(accountInfo).map(AccountInfo::getTimezoneId).orElse(DEFAULT_ZONE_ID);
        // 获取时区偏移量(单位：分钟)
        int offset = 0;
        try {
            offset = DateTimeZone.forID(timeZoneId).getOffset(0) / 60000;
        } catch (Exception e) {
            log.warn("Failed to get user time zone offset, use default value 0", e);
        }
        return TimeZone.builder().value(timeZoneId).utcOffset(offset).build();
    }
}

package com.patsnap.drafting.manager.aispecification.content.base;

import com.patsnap.core.common.copilot.gpt.enums.GPTModelEnum;
import com.patsnap.core.common.copilot.streaming.StreamingModelBuilder;
import com.patsnap.core.common.copilot.streaming.handler.StringStreamingResponseHandler;
import com.patsnap.core.common.copilot.streaming.handling.StreamingChatLanguageModel;
import com.patsnap.core.common.copilot.streaming.model.GptResponseDTO;
import com.patsnap.drafting.annotation.CreditCheckLimit;
import com.patsnap.drafting.client.OpenAiClient;
import com.patsnap.drafting.config.UrlConfig;
import com.patsnap.drafting.enums.aispecification.JurisdictionEnum;
import com.patsnap.drafting.enums.prompt.ScenarioEnum;
import com.patsnap.drafting.enums.task.AiTaskContentTypeEnum;
import com.patsnap.drafting.manager.aitask.AiTaskManager;
import com.patsnap.drafting.manager.credit.CreditManager;
import com.patsnap.drafting.model.aispecification.SpecificationInitializationBO;
import com.patsnap.drafting.model.base.StreamingModelBO;
import com.patsnap.drafting.request.GenerateContentRequestDTO;
import com.patsnap.spring.boot.autoconfigure.web.entity.CommonResponse;
import org.apache.commons.lang.StringUtils;
import reactor.core.publisher.Flux;

import java.util.List;
import java.util.Map;

/**
 * 流式处理-不同操作类型
 */
public abstract class AbstractStreamingContentOperation implements ContentOperation {

    protected final AiTaskManager aiTaskManager;

    protected OpenAiClient openAiClient;

    protected StreamingModelBuilder streamingModelBuilder;

    protected CreditManager creditManager;

    protected AbstractStreamingContentOperation(AiTaskManager aiTaskManager, UrlConfig urlConfig,
            OpenAiClient openAiClient,CreditManager creditManager) {
        this.aiTaskManager = aiTaskManager;
        this.openAiClient = openAiClient;
        this.streamingModelBuilder = new StreamingModelBuilder(urlConfig.getGptBaseUrl(),
                urlConfig.getGptApiKey());
        this.creditManager = creditManager;

    }

    @Override
    public StreamingModelBO getModelBO(GenerateContentRequestDTO generateContentRequestDTO) {
        aiTaskManager.checkPermission(generateContentRequestDTO.getTaskId());

        SpecificationInitializationBO initializationBO = aiTaskManager.getTaskContent(
                generateContentRequestDTO.getTaskId(), AiTaskContentTypeEnum.INITIALIZATION);

        String category = aiTaskManager.getTaskContent(generateContentRequestDTO.getTaskId(),
                AiTaskContentTypeEnum.CATEGORY);
        String techField = aiTaskManager.getTaskContent(generateContentRequestDTO.getTaskId(),
                AiTaskContentTypeEnum.SPECIFICATION_TECH_FIELD);

        List<String> classification = aiTaskManager.getTaskContent(generateContentRequestDTO.getTaskId(),
                AiTaskContentTypeEnum.CLASSIFICATION);

        String jurisdiction = initializationBO.getJurisdiction();
        //需要操作的内容，如果前端不传，需要取一次
        // 在专利说明书的实施例生成模块，前端会传一个特定的contentType=embodiment_content,同时也必然会传text=xxx,代表修改那一小块的实施例文本
        // 否则根据这个特殊的contentType是无法从pg表捞取数据，因为后端的AiTaskContentTypeEnum并没有这个类型
        String text = generateContentRequestDTO.getText();
        if (StringUtils.isEmpty(text)) {
            text = aiTaskManager.getTaskContent(generateContentRequestDTO.getTaskId(),
                    AiTaskContentTypeEnum.fromType(generateContentRequestDTO.getContentType()));
        }

        String prompt = openAiClient.buildPromptByPlatform(getPromptKey(generateContentRequestDTO.getContentType()),
                Map.of("input", text, "category", category,
                        "techField", techField,
                        "classification", StringUtils.join(classification, ",")),
                JurisdictionEnum.fromName(jurisdiction).getValue());
        StreamingModelBO streamingModelBO = new StreamingModelBO();
        streamingModelBO.setPrompt(prompt);
        StreamingChatLanguageModel model = streamingModelBuilder.build(GPTModelEnum.GPT_MODEL_4_O,
                ScenarioEnum.AI_SPECIFICATION.getValue());
        streamingModelBO.setModel(model);
        return streamingModelBO;
    }

    @Override
    @CreditCheckLimit(contentTypeExpression = "#generateContentRequestDTO.contentType" )
    public Flux<CommonResponse<GptResponseDTO<String>>> getContent(
            GenerateContentRequestDTO generateContentRequestDTO) {
        StreamingModelBO streamingModelBO = getModelBO(generateContentRequestDTO);
        String prompt = streamingModelBO.getPrompt();
        return Flux.create(sink -> {
            StringStreamingResponseHandler handler = new StringStreamingResponseHandler(sink);
            streamingModelBO.getModel().generate(prompt, handler);
        });
    }

    protected abstract String getPromptKey(String contentType);
}

package com.patsnap.drafting.manager.aispecification.content;

import com.fasterxml.jackson.core.type.TypeReference;
import com.patsnap.core.common.copilot.gpt.enums.GPTModelEnum;
import com.patsnap.core.common.copilot.streaming.handler.JsonStreamingResponseHandler;
import com.patsnap.core.common.copilot.streaming.handling.StreamingChatLanguageModel;
import com.patsnap.core.common.copilot.streaming.model.GptResponseDTO;
import com.patsnap.drafting.client.OpenAiClient;
import com.patsnap.drafting.config.UrlConfig;
import com.patsnap.drafting.enums.aispecification.JurisdictionEnum;
import com.patsnap.drafting.enums.prompt.ScenarioEnum;
import com.patsnap.drafting.enums.task.AiTaskContentTypeEnum;
import com.patsnap.drafting.manager.AbstractGenerateStreamingContent;
import com.patsnap.drafting.manager.aitask.AiTaskManager;
import com.patsnap.drafting.model.aispecification.SpecificationInitializationBO;
import com.patsnap.drafting.model.aispecification.SpecificationTechWrapperBO;
import com.patsnap.drafting.model.base.StreamingModelBO;
import com.patsnap.drafting.request.GenerateContentRequestDTO;
import com.patsnap.spring.boot.autoconfigure.web.entity.CommonResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

import java.util.Map;

import static com.patsnap.drafting.enums.prompt.SpecificationPromptKeyEnum.TECH_WRAPPER;

/**
 * 获取技术三要素
 */
@Component
public class SpecificationTechWrapperContent extends AbstractGenerateStreamingContent<SpecificationTechWrapperBO> {

    public static final String LINE_SEPARATOR_REGEX = "\\|\\|\\|";
    public static final String FIELD_SEPARATOR_REGEX = "\\$\\$\\$";
    @Autowired
    private OpenAiClient openAiClient;

    protected SpecificationTechWrapperContent(AiTaskManager aiTaskManager, UrlConfig urlConfig) {
        super(aiTaskManager, urlConfig);
    }

    @Override
    public String getContentType() {
        return AiTaskContentTypeEnum.TECH_WRAPPER.getType();
    }


    @Override
    protected StreamingModelBO getStreamingModelBO(GenerateContentRequestDTO generateContentRequestDTO) {
        SpecificationInitializationBO initializationBO = aiTaskManager.getTaskContent(
                generateContentRequestDTO.getTaskId(),
                AiTaskContentTypeEnum.INITIALIZATION);
        //用户输入的交底书
        String inputDisclosure = initializationBO.getDisclosure();
        String jurisdiction = initializationBO.getJurisdiction();
        //get prompt
        String prompt = openAiClient.buildPromptByPlatform(TECH_WRAPPER.getValue(),
                Map.of("input", inputDisclosure, "authority", JurisdictionEnum.fromName(jurisdiction).getAuthority()),
                JurisdictionEnum.fromName(jurisdiction).getValue());
        StreamingModelBO streamingModelBO = new StreamingModelBO();
        streamingModelBO.setPrompt(prompt);
        StreamingChatLanguageModel model = streamingModelBuilder.build(GPTModelEnum.GPT_CLAUDE_3_7_SONNET,
                ScenarioEnum.AI_SPECIFICATION.getValue());
        streamingModelBO.setModel(model);
        return streamingModelBO;
    }

    @Override
    protected Flux<CommonResponse<GptResponseDTO<SpecificationTechWrapperBO>>> getContent(
            StreamingModelBO streamingModelBO) {
        String prompt = streamingModelBO.getPrompt();
        return Flux.create(sink -> {
            JsonStreamingResponseHandler<SpecificationTechWrapperBO> handler = new JsonStreamingResponseHandler<>(
                    sink,
                    30,
                    LINE_SEPARATOR_REGEX, FIELD_SEPARATOR_REGEX, prompt,
                    new TypeReference<>() {
                    }, new SpecificationTechWrapperBO());
            streamingModelBO.getModel().generate(prompt, handler);
        });
    }
}

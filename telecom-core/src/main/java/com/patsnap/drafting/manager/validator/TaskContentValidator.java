package com.patsnap.drafting.manager.validator;

import com.patsnap.drafting.enums.task.AiTaskContentTypeEnum;
import com.patsnap.drafting.exception.BizException;
import com.patsnap.drafting.exception.errorcode.ContentErrorCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 任务内容验证器
 */
@Slf4j
@Component
public class TaskContentValidator {
    
    /**
     * 验证任务内容
     *
     * @param taskId 任务ID
     * @param contentType 内容类型
     * @param content 内容
     */
    public void validate(String taskId, AiTaskContentTypeEnum contentType, String content) {
        validate(taskId);
        
        if (StringUtils.isBlank(content)) {
            log.warn("必填内容为空. taskId: {}, contentType: {}", taskId, contentType);
            throw new BizException(ContentErrorCodeEnum.CONTENT_EMPTY, contentType.getDesc());
        }
    }
    
    public void validate(String taskId) {
        if (StringUtils.isBlank(taskId)) {
            throw new BizException(ContentErrorCodeEnum.TASK_ID_EMPTY);
        }
    }
} 
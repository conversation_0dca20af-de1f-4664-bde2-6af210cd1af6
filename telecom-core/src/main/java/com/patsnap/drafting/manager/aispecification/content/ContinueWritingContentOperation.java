package com.patsnap.drafting.manager.aispecification.content;

import com.patsnap.core.common.copilot.gpt.enums.GPTModelEnum;
import com.patsnap.core.common.copilot.streaming.handling.StreamingChatLanguageModel;
import com.patsnap.drafting.client.OpenAiClient;
import com.patsnap.drafting.config.UrlConfig;
import com.patsnap.drafting.enums.aispecification.JurisdictionEnum;
import com.patsnap.drafting.enums.common.OperateTypeEnum;
import com.patsnap.drafting.enums.prompt.ScenarioEnum;
import com.patsnap.drafting.enums.task.AiTaskContentTypeEnum;
import com.patsnap.drafting.exception.BizException;
import com.patsnap.drafting.exception.errorcode.ContentErrorCodeEnum;
import com.patsnap.drafting.manager.aispecification.content.base.AbstractStreamingContentOperation;
import com.patsnap.drafting.manager.aitask.AiTaskManager;
import com.patsnap.drafting.manager.credit.CreditManager;
import com.patsnap.drafting.model.aispecification.SpecificationInitializationBO;
import com.patsnap.drafting.model.aispecification.SpecificationTechWrapperBO;
import com.patsnap.drafting.model.base.StreamingModelBO;
import com.patsnap.drafting.request.GenerateContentRequestDTO;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

import static com.patsnap.drafting.enums.prompt.SpecificationPromptKeyEnum.BACKGROUND_CONTINUE_WRITING;
import static com.patsnap.drafting.enums.prompt.SpecificationPromptKeyEnum.SUMMARY_CONTINUE_WRITING;

/**
 * 续写内容
 */
@Component
public class ContinueWritingContentOperation extends AbstractStreamingContentOperation {


    protected ContinueWritingContentOperation(AiTaskManager aiTaskManager, UrlConfig urlConfig,
            OpenAiClient openAiClient,
            CreditManager creditManager) {
        super(aiTaskManager, urlConfig, openAiClient, creditManager);
    }

    @Override
    protected String getPromptKey(String contentType) {
        if (AiTaskContentTypeEnum.SUMMARY.getType().equals(contentType)) {
            return SUMMARY_CONTINUE_WRITING.getValue();
        } else if (AiTaskContentTypeEnum.BACKGROUND.getType().equals(contentType)) {
            return BACKGROUND_CONTINUE_WRITING.getValue();
        } else {
            throw new BizException(ContentErrorCodeEnum.CONTENT_TYPE_INVALID);
        }
    }

    @Override
    public StreamingModelBO getModelBO(GenerateContentRequestDTO generateContentRequestDTO) {
        // 获取基础数据
        SpecificationInitializationBO initializationBO = aiTaskManager.getTaskContent(
                generateContentRequestDTO.getTaskId(), AiTaskContentTypeEnum.INITIALIZATION);
        String category = aiTaskManager.getTaskContent(generateContentRequestDTO.getTaskId(),
                AiTaskContentTypeEnum.CATEGORY);
        String techField = aiTaskManager.getTaskContent(generateContentRequestDTO.getTaskId(),
                AiTaskContentTypeEnum.SPECIFICATION_TECH_FIELD);

        List<String> classification = aiTaskManager.getTaskContent(generateContentRequestDTO.getTaskId(),
                AiTaskContentTypeEnum.CLASSIFICATION);

        // 构建prompt参数
        Map<String, String> promptParams = buildPromptParams(generateContentRequestDTO, category, techField,
                classification);

        // 获取jurisdiction
        String jurisdiction = initializationBO.getJurisdiction();
        String jurisdictionValue = JurisdictionEnum.fromName(jurisdiction).getValue();

        // 构建prompt
        String prompt = openAiClient.buildPromptByPlatform(getPromptKey(generateContentRequestDTO.getContentType()),
                promptParams, jurisdictionValue);

        // 构建返回对象
        StreamingModelBO streamingModelBO = new StreamingModelBO();
        streamingModelBO.setPrompt(prompt);
        StreamingChatLanguageModel model = streamingModelBuilder.build(GPTModelEnum.GPT_MODEL_4_O,
                ScenarioEnum.AI_SPECIFICATION.getValue());
        streamingModelBO.setModel(model);

        return streamingModelBO;
    }


    /**
     * 构建prompt参数
     */
    private Map<String, String> buildPromptParams(GenerateContentRequestDTO request, String category,
            String techField, List<String> classification) {
        if (AiTaskContentTypeEnum.SUMMARY.getType().equals(request.getContentType())) {
            return buildSummaryPromptParams(request, category, techField, classification);
        } else if (AiTaskContentTypeEnum.BACKGROUND.getType().equals(request.getContentType())) {
            return buildBgPromptParams(request, category, techField, classification);
        } else {
            throw new BizException(ContentErrorCodeEnum.CONTENT_TYPE_INVALID);
        }
    }


    private Map<String, String> buildBgPromptParams(GenerateContentRequestDTO request, String category,
            String techField, List<String> classification) {
        String background = aiTaskManager.getTaskContent(request.getTaskId(),
                AiTaskContentTypeEnum.SPECIFICATION_BACKGROUND);
        return Map.of(
                "input", background,
                "category", category,
                "techField", techField,
                "classification", StringUtils.join(classification, ",")
        );
    }

    protected Map<String, String> buildSummaryPromptParams(GenerateContentRequestDTO request, String category,
            String techField, List<String> classification) {
        SpecificationTechWrapperBO techWrapper = aiTaskManager.getTaskContent(
                request.getTaskId(), AiTaskContentTypeEnum.TECH_WRAPPER);
        String summary = aiTaskManager.getTaskContent(request.getTaskId(),
                AiTaskContentTypeEnum.SUMMARY);
        return Map.of(
                "input", summary,
                "category", category,
                "techField", techField,
                "classification", StringUtils.join(classification, ","),
                "benefit", techWrapper.getBenefit()
        );

    }


    @Override
    public OperateTypeEnum getOperationType() {
        return OperateTypeEnum.CONTINUE;
    }
}

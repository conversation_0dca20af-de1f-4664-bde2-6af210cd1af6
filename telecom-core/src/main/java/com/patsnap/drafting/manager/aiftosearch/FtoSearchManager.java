package com.patsnap.drafting.manager.aiftosearch;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.patsnap.core.common.identity.AccountInfo;
import com.patsnap.core.common.identity.AccountUtils;
import com.patsnap.core.common.identity.UserInfo;
import com.patsnap.drafting.annotation.TaskContentCache;
import com.patsnap.drafting.client.AiFtoSearchComputeClient;
import com.patsnap.drafting.client.AiNoveltySearchComputeClient;
import com.patsnap.drafting.client.AiSearchClient;
import com.patsnap.drafting.client.ComputeClient;
import com.patsnap.drafting.client.model.*;
import com.patsnap.drafting.constants.Constant;
import com.patsnap.drafting.constants.FtoSearchConstants;
import com.patsnap.drafting.enums.aiftosearch.FtoSearchTagEnum;
import com.patsnap.drafting.enums.ainoveltysearch.NoveltySearchTagEnum;
import com.patsnap.drafting.enums.common.Lang;
import com.patsnap.drafting.enums.common.RiskLevelEnum;
import com.patsnap.drafting.enums.task.AiTaskContentTypeEnum;
import com.patsnap.drafting.enums.task.AiTaskTypeEnum;
import com.patsnap.drafting.exception.BizException;
import com.patsnap.drafting.exception.errorcode.ContentErrorCodeEnum;
import com.patsnap.drafting.manager.IdentityAccountManager;
import com.patsnap.drafting.manager.aitask.AiTaskManager;
import com.patsnap.drafting.manager.content.logic.*;
import com.patsnap.drafting.manager.patent.PatentInfoManager;
import com.patsnap.drafting.repository.aitask.entity.AnalyticsAiTaskHistoryPO;
import com.patsnap.drafting.request.aiftosearch.FtoSearchSubmitRequestDTO;
import com.patsnap.drafting.request.ainoveltysearch.*;
import com.patsnap.drafting.request.aitask.AiTaskCreateReqDTO;
import com.patsnap.drafting.request.aitask.AiTaskReqDTO;
import com.patsnap.drafting.request.patentinfo.PatentInfoRequestDTO;
import com.patsnap.drafting.response.aiftosearch.FtoSearchReportTitleResponseDTO;
import com.patsnap.drafting.response.aiftosearch.FtoSearchSubmitResponseDTO;
import com.patsnap.drafting.response.ainoveltysearch.FeatureComparisonFinalResFeature;
import com.patsnap.drafting.response.ainoveltysearch.FeatureComparisonFinalResItem;
import com.patsnap.drafting.response.ainoveltysearch.FeatureComparisonResponseDTO;
import com.patsnap.drafting.response.ainoveltysearch.FeatureExactionResponseDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import com.patsnap.drafting.util.DateUtils;
import static com.patsnap.drafting.exception.errorcode.ContentErrorCodeEnum.INPUT_TOO_LONG;
import static com.patsnap.drafting.exception.errorcode.ContentErrorCodeEnum.LANG_NOT_MATCH;
import static com.patsnap.drafting.manager.aispecification.SpecificationManager.SUPPORT_LANG_LIST;
import static com.patsnap.drafting.util.DateUtils.formatDateStringByLang;

import com.patsnap.drafting.constants.ExportConstant;

/**
 * FTO搜索管理器
 * 负责处理FTO(Freedom to Operate)搜索相关的业务逻辑
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class FtoSearchManager {

    private final ComputeClient computeClient;
    private final AiTaskManager aiTaskManager;
    private final AiFtoSearchComputeClient aiFtoSearchComputeClient;
    private final AiNoveltySearchComputeClient aiNoveltySearchComputeClient;
    private final IdentityAccountManager identityAccountManager;
    private final AiSearchClient aiSearchClient;
    private final PatentInfoManager patentInfoManager;

    public static final String DEFAULT_ZONE_ID = "UTC";

    /**
     * 提交FTO搜索任务
     * @param request 包含输入文本、语言等搜索参数的请求
     * @return 提交响应结果，包含任务ID和输入文本
     */
    public FtoSearchSubmitResponseDTO submit(FtoSearchSubmitRequestDTO request) {
        aiTaskManager.checkSensitiveWords(List.of(request.getInput()));
        String lang = checkLangForUserInput(request.getInputLang());
        aiTaskManager.createTask(buildCreateReq(request));
        checkLangForUserInput(request.getInput(), lang);
        
        return FtoSearchSubmitResponseDTO.builder()
                .taskId(request.getTaskId())
                .text(request.getInput())
                .build();
    }

    /**
     * 提取技术特征
     * @param request 任务请求参数
     * @return 提取的技术特征响应
     */
    @TaskContentCache(contentType = AiTaskContentTypeEnum.FTO_SEARCH_TECH_FEATURE)
    public FeatureExactionResponseDTO extractTechFeature(AiTaskReqDTO request) {
        String userInput = getUserInput(request.getTaskId());
        
        FtoSearchRequestDTO ftoSearchRequest = new FtoSearchRequestDTO()
                .setTag(FtoSearchTagEnum.KEYWORDS_EXTRACT.getValue())
                .setText(userInput);
        
        FtoSearchResponseDTO.Data ftoSearchData = aiFtoSearchComputeClient.ftoSearch(ftoSearchRequest);
        return getFeatureExactionResponseDTO(ftoSearchData, userInput);
    }

    /**
     * 执行FTO搜索
     * @param request 任务请求参数
     * @return 搜索任务ID
     */
    @TaskContentCache(contentType = AiTaskContentTypeEnum.FTO_SEARCH_TASK_ID)
    public String ftoSearchAgent(AiTaskReqDTO request) {
        String userInput = getUserInput(request.getTaskId());
        FeatureExactionResponseDTO featureExaction = aiTaskManager.getTaskContent(
                request.getTaskId(), AiTaskContentTypeEnum.FTO_SEARCH_TECH_FEATURE);

        AiFtoSearchAgentRequest aiSearchAgentRequest = buildSearchAgentRequest(request, userInput, featureExaction);
        List<Map<String, Object>> filters = buildSearchFilters(request.getTaskId());
        aiSearchAgentRequest.setFilters(filters);

        List<Map<String, Object>> qFilters = buildSearchQFilters(request.getTaskId());
        aiSearchAgentRequest.setQFilters(qFilters);

        Map<String, Object> collapse = buildCollapse();
        aiSearchAgentRequest.setCollapse(collapse);
        AiSearchAgentResponse aiSearchAgentResponse = aiSearchClient.ftoAiSearchAgent(aiSearchAgentRequest);
        return aiSearchAgentResponse.getTaskId();
    }

    private Map<String, Object> buildCollapse() {
        Map<String, Object> collapse = new HashMap<>();
        collapse.put("field", "APNO_FACET");
        List<Map<String, String>> rules = new ArrayList<>();
        Map<String, String> rule = new HashMap<>();
        rule.put("type", "max");
        rule.put("field", "PBDT_YEARMONTHDAY");
        rules.add(rule);
        collapse.put("rules", rules);
        return collapse;
    }

    /**
     * 获取FTO搜索结果
     * @param request 结果请求参数
     * @return 搜索结果数据
     */
    @TaskContentCache(contentType = AiTaskContentTypeEnum.FTO_SEARCH_AGENT_RESULT, 
            logicClass = FtoSearchResultContentCacheLogic.class)
    public AiSearchResultDTO fetchFtoSearchResult(AiSearchResultReqDTO request) {
        String saTaskId = aiTaskManager.getTaskContent(request.getTaskId(), 
                AiTaskContentTypeEnum.FTO_SEARCH_TASK_ID);
        
        AiSearchAgentResponse aiSearchAgentResponse = 
                aiSearchClient.ftoAiSearchResult(saTaskId, request.getIsFinished());
        
        List<AiSearchFinalResult> finalResults = processFinalResults(
                aiSearchAgentResponse.getFinalResult(),
                aiSearchAgentResponse.getCcThreshold(),
                request.getTaskId()
        );
        
        AiSearchResultDTO result = new AiSearchResultDTO();
        aiSearchAgentResponse.setFinalResult(finalResults);
        BeanUtils.copyProperties(aiSearchAgentResponse, result);
        return result;
    }

    /**
     * 获取输入文本的语言
     */
    @TaskContentCache(contentType = AiTaskContentTypeEnum.FTO_SEARCH_INPUT_LANG)
    public String getInputLang(AiTaskReqDTO request) {
        return computeClient.getCheckLang(request.getText());
    }

    /**
     * 生成文本摘要
     */
    @TaskContentCache(contentType = AiTaskContentTypeEnum.FTO_SEARCH_INPUT_SUMMARY)
    public String summaryText(AiTaskReqDTO request) {
        String userInput = getUserInput(request.getTaskId());
        if(StringUtils.isBlank(userInput)) {
            userInput = request.getText();
        }
        
        FtoSearchRequestDTO ftoSearchRequest = new FtoSearchRequestDTO()
                .setTag(FtoSearchTagEnum.SUMMARY.getValue())
                .setText(userInput);
        
        FtoSearchResponseDTO.Data ftoSearchData = aiFtoSearchComputeClient.ftoSearch(ftoSearchRequest);
        return ftoSearchData.getSolution();
    }

    /**
     * 生成标题
     */
    @TaskContentCache(contentType = AiTaskContentTypeEnum.TITLE)
    public String generateTitle(AiTaskReqDTO request) {
        String userInput = getUserInput(request.getTaskId());
        FtoSearchRequestDTO ftoSearchRequest = new FtoSearchRequestDTO()
                .setTag(FtoSearchTagEnum.TITLE_GENERATE.getValue())
                .setText(userInput);
        
        FtoSearchResponseDTO.Data ftoSearchData = aiFtoSearchComputeClient.ftoSearch(ftoSearchRequest);
        return ftoSearchData.getTitle();
    }

    /**
     * 确认技术特征
     */
    @TaskContentCache(contentType = AiTaskContentTypeEnum.FTO_SEARCH_TECH_FEATURE, logicClass = FtoSearchUpdateTechFeatureContentCacheLogic.class)
    public FeatureExactionResponseDTO confirmTechnicalFeature(AiSearchConfirmFeatureReqDTO request) {
        FeatureExactionResponseDTO featureExaction = aiTaskManager.getTaskContent(
                request.getTaskId(), AiTaskContentTypeEnum.FTO_SEARCH_TECH_FEATURE);
        if (featureExaction == null) {
            return null;
        }

        // 1. 获取原有特征列表，并重置状态
        List<AiSearchAgentFeature> originalFeatures = featureExaction.getFeatures();
        originalFeatures.forEach(feature -> {
            feature.setSelect(false);
            feature.setCore(false);
        });

        // 2. 创建特征映射用于快速查找
        Map<String, AiSearchAgentFeature> featureMap = new HashMap<>();
        originalFeatures.forEach(feature -> featureMap.put(feature.getTechFeature(), feature));

        // 3. 收集需要新增的特征和核心标记
        List<String> userAddTechFeatures = new ArrayList<>();
        Set<String> coreTag = new HashSet<>();
        
        // 4. 处理请求中的特征，保持原有顺序
        for (AiSearchAgentFeature techFeatureObj : request.getTechFeatures()) {
            if (techFeatureObj.getCore()) {
                coreTag.add(techFeatureObj.getTechFeature());
            }
            
            AiSearchAgentFeature existingFeature = featureMap.get(techFeatureObj.getTechFeature());
            if (existingFeature == null) {
                // 收集新增的特征，保持请求中的顺序
                userAddTechFeatures.add(techFeatureObj.getTechFeature());
            } else {
                // 更新现有特征的状态
                existingFeature.setSelect(true);
                existingFeature.setCore(techFeatureObj.getCore());
                existingFeature.setUserAdd(techFeatureObj.getUserAdd());
            }
        }

        // 5. 获取新增特征，这些特征会按照入参顺序追加
        List<AiSearchAgentFeature> newFeatures = pointExtract(request.getTaskId(), userAddTechFeatures, coreTag);

        // 6. 合并特征列表，保持原有顺序，新增特征追加在末尾
        List<AiSearchAgentFeature> allFeatures = new ArrayList<>(originalFeatures);
        allFeatures.addAll(newFeatures);

        // 7. 更新并返回
        featureExaction.setFeatures(allFeatures.stream().filter(Objects::nonNull).toList());
        return featureExaction;
    }

    public List<AiSearchAgentFeature> pointExtract(String taskId, List<String> userAddTechFeatures, Set<String> coreTag) {
        if (CollUtil.isEmpty(userAddTechFeatures)) {
            return List.of();
        }
        // 1. 获取用户输入
        String userInput = getUserInput(taskId);
        // 2. 根据用户输入和技术特征清单，来得到技术特征和对应得分
        NoveltySearchRequestDTO noveltySearchRequest = new NoveltySearchRequestDTO()
                .setTag(NoveltySearchTagEnum.POINT_EXTRACT.getValue())
                .setModel("gpt-4.1")
                .setText(userInput)
                .setTechFeatures(userAddTechFeatures);
        NoveltySearchPointExtractResponseDTO noveltySearchData =
                aiNoveltySearchComputeClient.noveltyPointExtractSearch(noveltySearchRequest);
        if (CollUtil.isEmpty(noveltySearchData.getData())) {
            return List.of();
        }
        List<AiSearchAgentFeature> features = new ArrayList<>();
        noveltySearchData.getData().forEach(feature -> {
            feature.setUserAdd(true);
            feature.setCore(coreTag.contains(feature.getTechFeature()));
            feature.setSelect(true);
            features.add(feature);
        });
        return features;
    }

    /**
     * 获取相似列表
     */
    public List<AiSearchFinalResult> getSimilarList(String taskId) {
        AiSearchResultDTO aiSearchResult =  aiTaskManager.getTaskContent(taskId,
                AiTaskContentTypeEnum.FTO_SEARCH_AGENT_RESULT);
        return aiSearchResult.getFinalResult();
    }

    /**
     * 生成报告标题
     */
    @TaskContentCache(contentType = AiTaskContentTypeEnum.FTO_SEARCH_REPORT_TITLE)
    public FtoSearchReportTitleResponseDTO generateReportTitle(AiTaskReqDTO request) {
        AnalyticsAiTaskHistoryPO taskHistoryPO = aiTaskManager.getTaskById(request.getTaskId());
        AiSearchResultDTO resultDTO = aiTaskManager.getTaskContent(
                request.getTaskId(), AiTaskContentTypeEnum.FTO_SEARCH_AGENT_RESULT);
        if (resultDTO == null) {
            return null;
        }

        List<LinkedHashMap<String, Object>> confirmList = aiTaskManager.getTaskContent(request.getTaskId(), AiTaskContentTypeEnum.FTO_SEARCH_FEATURE_CONFIRM);
        List<String> includeCompanyList = aiTaskManager.getTaskContent(request.getTaskId(), AiTaskContentTypeEnum.FTO_SEARCH_INCLUDE_COMPANY);
        List<String> excludeCompanyList = aiTaskManager.getTaskContent(request.getTaskId(), AiTaskContentTypeEnum.FTO_SEARCH_EXCLUDE_COMPANY);
        List<String> receivingOfficeList = aiTaskManager.getTaskContent(request.getTaskId(), AiTaskContentTypeEnum.FTO_SEARCH_RECEIVING_OFFICES);
        List<Integer> legalStatusList = aiTaskManager.getTaskContent(request.getTaskId(), AiTaskContentTypeEnum.FTO_SEARCH_LEGAL_STATUS);
        String apdStart = aiTaskManager.getTaskContent(request.getTaskId(), AiTaskContentTypeEnum.FTO_SEARCH_APD_START);
        
        return buildReportTitleResponse(taskHistoryPO, resultDTO, confirmList, includeCompanyList, excludeCompanyList, receivingOfficeList, legalStatusList, apdStart);
    }

    /**
     * 更新最终结果详情
     * @param finalResults 最终结果列表
     * @param taskId 任务ID
     * @return 更新后的结果列表
     */
    public List<AiSearchFinalResult> updateFinalResultDetail(List<AiSearchFinalResult> finalResults, String taskId) {
        if (CollUtil.isEmpty(finalResults)) {
            return finalResults;
        }
        
        // 获取专利基本信息
        List<String> patentIds = finalResults.stream()
                .map(AiSearchFinalResult::getPatentId)
                .toList();
                
        PatentInfoRequestDTO requestDTO = new PatentInfoRequestDTO();
        requestDTO.setPatentIds(new ArrayList<>(patentIds));
        JSONObject patentInfoMap = patentInfoManager.batchFetchPatentBasicInfo(requestDTO);
        
        // 更新每个结果的详细信息
        finalResults.forEach(result -> updateSingleResultDetail(result, patentInfoMap));
        
        return finalResults;
    }

    /**
     * 更新单个结果的详细信息
     */
    private void updateSingleResultDetail(AiSearchFinalResult result, JSONObject patentInfoMap) {
        JSONObject patentInfo = patentInfoMap.getJSONObject(result.getPatentId());
        if (patentInfo == null) {
            return;
        }
        
        // 提取专利信息
        String country = extractCountry(patentInfo);
        String expirationDate = patentInfo.getString("EXDT");
        String legalStatus = patentInfo.getString("SIMPLE_LEGAL_STATUS");

        // 处理权利要求等同信息
        // 如果前端已经传值，那就没必要重新计算一遍等同信息，而是以用户的修改为准
        if(CollectionUtils.isEmpty(result.getClaimEquivalentInfo())) {
            processClaimEquivalentInfo(result);
        }

        // 专利下面众多权利要求，只要有一个是侵权，生成结论时就按照侵权处理
        boolean existInfringementClaim = CollectionUtils.isNotEmpty(result.getClaimEquivalentInfo()) && result.getClaimEquivalentInfo().stream().anyMatch(ClaimEquivalentItem::getInfringement);
                
        // 生成结论
        // 某条专利里面是否侵权=Yes的权利要求编号也需要加到结论文本里,用顿号分隔
        String infringementClaims = "";
        if(CollectionUtils.isNotEmpty(result.getClaimEquivalentInfo())) {
            infringementClaims = result.getClaimEquivalentInfo().stream()
                    .filter(ClaimEquivalentItem::getInfringement)
                    .map(ClaimEquivalentItem::getClaimNum)
                    .map(String::valueOf)
                    .collect(Collectors.joining("、"));
        }
        Map<String, String> conclusion = generateConclusion(existInfringementClaim, legalStatus, country, expirationDate,infringementClaims);
        
        result.setComparisonConclusion(conclusion);

        // 计算等同独权数量，独权数量，等同从权数量，从权数量
        calculateResult(result, result.getClaimEquivalentInfo());

        // 处理风险等级
        // 如果已经有前端传值，则不需要自动计算
        if(StringUtils.isBlank(result.getRiskLevel())) {
            // 计算风险等级
            String riskLevel = calculateRiskLevel(result.getClaimEquivalentInfo(), legalStatus);
            result.setRiskLevel(riskLevel);
        }
    }

    /**
     * 计算等同独权数量，独权数量，等同从权数量，从权数量，风险等级
     */
    private void calculateResult(AiSearchFinalResult result, List<ClaimEquivalentItem> claimEquivalentInfo) {
        List<AiSearchFinalResultFeature> features = result.getFeatures();
        if (features == null || features.isEmpty()) {
            return;
        }

        List<Integer> infringementClaimNumList = claimEquivalentInfo.stream()
                .filter(ClaimEquivalentItem::getInfringement)
                .map(ClaimEquivalentItem::getClaimNum)
                .distinct()
                .toList();

        // 遍历特征列表，拿到所有的独权，从权编号，再继续计算等同数量
        List<Integer> independentClaimNumList = new ArrayList<>();
        List<Integer> dependentClaimNumList = new ArrayList<>();
        for (AiSearchFinalResultFeature feature : features) {
            String claimType = feature.getClaimType();

            if ("independent".equalsIgnoreCase(claimType)) {
                independentClaimNumList.add(feature.getClaimNum());
            } else if ("dependent".equalsIgnoreCase(claimType)) {
                dependentClaimNumList.add(feature.getClaimNum());
            }
        }
        independentClaimNumList = independentClaimNumList.stream().distinct().toList();
        dependentClaimNumList = dependentClaimNumList.stream().distinct().toList();

        int equivalentIndependentCount = (int) independentClaimNumList.stream().filter(infringementClaimNumList::contains).distinct().count();
        int  equivalentDependentCount = (int) dependentClaimNumList.stream().filter(infringementClaimNumList::contains).distinct().count();

        // 设置统计结果
        result.setEquivalentIndependentClaimNum(equivalentIndependentCount);
        result.setIndependentClaimNum(independentClaimNumList.size());
        result.setEquivalentDependentClaimNum(equivalentDependentCount);
        result.setDependentClaimNum(dependentClaimNumList.size());
    }

    /**
     * 计算风险等级
     */
    private String calculateRiskLevel(List<ClaimEquivalentItem> claimEquivalentItems, String legalStatus) {
        // 高风险：独权/从权 判定等同(有一个等同就算)，且该专利简单法律状态为有效
        // 中风险：独权/从权 判定等同(有一个等同就算)，且该专利简单法律状态为审中或未确认
        // 低分险：其他情况

        boolean isValid = FtoSearchConstants.LegalStatus.VALID.equals(legalStatus);
        boolean isPendingOrUndetermined = FtoSearchConstants.LegalStatus.UNDETERMINED.equals(legalStatus) || FtoSearchConstants.LegalStatus.PENDING.equals(legalStatus);
        // 独权判定等同
        boolean infringement = CollectionUtils.isNotEmpty(claimEquivalentItems) && claimEquivalentItems.stream().anyMatch(ClaimEquivalentItem::getInfringement);
        if(infringement) {
            if(isValid) {
                return RiskLevelEnum.HIGH.getValue();
            } else if(isPendingOrUndetermined) {
                return RiskLevelEnum.MEDIUM.getValue();
            }
        }
        return RiskLevelEnum.LOW.getValue();
    }

    /**
     * 从专利信息中提取国家代码
     */
    private String extractCountry(JSONObject patentInfo) {
        String pn = patentInfo.getString("PN");
        return pn != null ? pn.substring(0, 2) : "";
    }

    /**
     * 生成结论
     */
    private Map<String, String> generateConclusion(
            boolean existInfringementClaim,
            String legalStatus, 
            String country, 
            String expirationDate, String infringementClaims) {
        Map<String, String> conclusion = new HashMap<>();

        if (existInfringementClaim) {
            // 存在任何一个权利要求的是否等同是Yes，即可认为侵权
            // 中文结论
            if (FtoSearchConstants.LegalStatus.VALID.equals(legalStatus)) {
                // 法律状态=有效
                conclusion.put("zh", String.format(FtoSearchConstants.Conclusion.ZH.SIMILAR_VALID,
                        infringementClaims,infringementClaims,infringementClaims,
                        expirationDate, country));
                // 英文结论
                conclusion.put("en", "");
            } else if (FtoSearchConstants.LegalStatus.PENDING.equals(legalStatus)) {
                // 法律状态=审中
                conclusion.put("zh", String.format(FtoSearchConstants.Conclusion.ZH.SIMILAR_PENDING,infringementClaims,infringementClaims,infringementClaims,infringementClaims));
                conclusion.put("en", "");
            } else {
                // 其他法律状态
                conclusion.put("zh", String.format(FtoSearchConstants.Conclusion.ZH.SIMILAR_OTHER,infringementClaims,infringementClaims,infringementClaims,infringementClaims));
                conclusion.put("en", "");
            }
            
        } else {
            // 专利所有的权利要求都不侵权
            conclusion.put("zh", String.format(FtoSearchConstants.Conclusion.ZH.NOT_SIMILAR, country));
            conclusion.put("en", "");
        }
        
        return conclusion;
    }

    // Private helper methods
    private void updateTechFeatures(List<AiSearchAgentFeature> techFeatures, List<String> selectedFeatures) {
        techFeatures.forEach(feature -> 
            feature.setSelect(selectedFeatures.contains(feature.getTechFeature()))
        );
    }

    private FtoSearchReportTitleResponseDTO buildReportTitleResponse(
            AnalyticsAiTaskHistoryPO taskHistoryPO, 
            AiSearchResultDTO resultDTO,
            List<LinkedHashMap<String, Object>> confirmList,
            List<String> includeCompanyList,
            List<String> excludeCompanyList,
            List<String> receivingOfficeList,
            List<Integer> legalStatusList,
            String apdStart
    ) {
            
        FtoSearchReportTitleResponseDTO response = new FtoSearchReportTitleResponseDTO();
        AccountInfo accountInfo = identityAccountManager.getAccountInfoByUserId(taskHistoryPO.getCreatedBy());
        UserInfo userInfo = AccountUtils.getUserInfo(accountInfo);
        
        response.setCreator(AccountUtils.getShowValue(userInfo));
        response.setTitle(taskHistoryPO.getTitle());
        response.setGeneratedTime(taskHistoryPO.getUpdatedAt().getMillis());
        setHeaderInfo(response, resultDTO, confirmList);
        setSearchScope(response, confirmList, getUserTimeZone(accountInfo), includeCompanyList, excludeCompanyList, receivingOfficeList, legalStatusList, apdStart);
        return response;
    }

    private DateTimeZone getUserTimeZone(AccountInfo accountInfo) {
        String timeZoneId = Optional.ofNullable(accountInfo)
                .map(AccountInfo::getTimezoneId)
                .orElse(DEFAULT_ZONE_ID);
        return DateTimeZone.forID(timeZoneId);
    }

    private void setHeaderInfo(FtoSearchReportTitleResponseDTO response, AiSearchResultDTO resultDTO,
                               List<LinkedHashMap<String, Object>> confirmList) {
        Map<String, String> headerMap = new HashMap<>();
        if (CollUtil.isEmpty(resultDTO.getFinalResult())) {
            resultDTO.setFinalResult(Collections.emptyList());
        }
        if (CollUtil.isEmpty(confirmList)) {
            confirmList = Collections.emptyList();
        }
        headerMap.put("zh", String.format(FtoSearchConstants.ReportHeader.ZH, resultDTO.getExcuteTimes(),
                resultDTO.getFinalResult().size(), confirmList.size()));
        headerMap.put("en", String.format(FtoSearchConstants.ReportHeader.EN, resultDTO.getExcuteTimes(),
                resultDTO.getFinalResult().size(), confirmList.size()));
        headerMap.put("jp", String.format(FtoSearchConstants.ReportHeader.JP, resultDTO.getExcuteTimes(),
                resultDTO.getFinalResult().size(), confirmList.size()));
        response.setHeader(headerMap);
    }

    /**
     * 设置搜索范围说明
     */
    private void setSearchScope(FtoSearchReportTitleResponseDTO response,
                                List<LinkedHashMap<String, Object>> confirmList,
                                DateTimeZone timeZone,
                                List<String> includeCompanyList,
                                List<String> excludeCompanyList,
                                List<String> receivingOfficeList,
                                List<Integer> legalStatusList,
                                String apdStart) {
        DateTime currentTime = new DateTime();
        Map<String, String> searchScopeMap = new HashMap<>();
        
        // 英文搜索范围
        // String enDate = DateUtils.formatDateTimeByLang(timeZone, currentTime, Lang.EN);
        // searchScopeMap.put("en", String.format(FtoSearchConstants.SearchScope.EN, enDate, enDate));
        searchScopeMap.put("en", generateSearchDateBefore(timeZone, Lang.EN) +
                generateApplicationDate(apdStart, Lang.EN) +
                generateReceivingOffice(receivingOfficeList, Lang.EN) +
                generateLegalStatus(legalStatusList, Lang.EN) +
                generateIncludeCompany(includeCompanyList, Lang.EN) +
                generateExcludeCompany(excludeCompanyList, Lang.EN) +
                generateSearchDateAfter(timeZone, Lang.EN));
        
        // 中文搜索范围
        // String zhDate = DateUtils.formatDateTimeByLang(timeZone, currentTime, Lang.CN);
        // searchScopeMap.put("zh", String.format(FtoSearchConstants.SearchScope.ZH, zhDate, zhDate));
        searchScopeMap.put("zh", generateSearchDateBefore(timeZone, Lang.CN) +
                generateApplicationDate(apdStart, Lang.CN) +
                generateReceivingOffice(receivingOfficeList, Lang.CN) +
                generateLegalStatus(legalStatusList, Lang.CN) +
                generateIncludeCompany(includeCompanyList, Lang.CN) +
                generateExcludeCompany(excludeCompanyList, Lang.CN) +
                generateSearchDateAfter(timeZone, Lang.CN));
        
        // 日文搜索范围
        // String jpDate = DateUtils.formatDateTimeByLang(timeZone, currentTime, Lang.JP);
        // searchScopeMap.put("jp", String.format(FtoSearchConstants.SearchScope.JP, jpDate, jpDate));
        searchScopeMap.put("jp", generateSearchDateBefore(timeZone, Lang.JP) +
                generateApplicationDate(apdStart, Lang.JP) +
                generateReceivingOffice(receivingOfficeList, Lang.JP) +
                generateLegalStatus(legalStatusList, Lang.JP) +
                generateIncludeCompany(includeCompanyList, Lang.JP) +
                generateExcludeCompany(excludeCompanyList, Lang.JP) +
                generateSearchDateAfter(timeZone, Lang.JP));
        
        response.setSearchScope(searchScopeMap);
    }

    // 生成检索日期文案(头部)
    private String generateSearchDateBefore(DateTimeZone timeZone, Lang lang) {
        DateTime currentTime = new DateTime();
        String formattedDate = DateUtils.formatDateTimeByLang(timeZone, currentTime, lang);
        String prefix;
        String suffix;
        if(Lang.CN.equals(lang)) {
            prefix = "本次检索分析主要针对：检索日期";
            suffix = "（包括当日）前，";
        } else if(Lang.JP.equals(lang)) {
            prefix = "本検索分析は、公開日が";
            suffix = "（当日を含む）以前、";
        } else {
            prefix = "This search and analysis targets published patent documents relevant to the present technical solution, with a publication date on or before ";
            suffix = " (inclusive)";
        }
        return prefix + formattedDate + suffix;
    }

    // 生成申请日文案
    private String generateApplicationDate(String apdStart, Lang lang) {
        if(StrUtil.isBlank(apdStart)) {
            return "";
        }

        String prefix;
        String suffix;
        if(Lang.CN.equals(lang)) {
            prefix = "申请日期";
            suffix = "（包括当日）起，";
        } else if(Lang.JP.equals(lang)) {
            prefix = "かつ出願日が";
            suffix = "当日を含む）以降、";
        } else {
            prefix = " and an application date on or after ";
            suffix = "(inclusive), ";
        }
        return prefix + formatDateStringByLang(apdStart, lang) + suffix;
    }

    // 生成简单法律状态文案
    private String generateLegalStatus(List<Integer> legalStatusList, Lang lang) {
        if(CollectionUtils.isEmpty(legalStatusList)) {
            return "";
        }

        Map<Integer, String> simpleLegalStatusMap;
        if(Lang.CN.equals(lang)) {
            simpleLegalStatusMap = ExportConstant.SIMPLE_LEGAL_STATUS_MAP_CN;
        } else if(Lang.JP.equals(lang)) {
            simpleLegalStatusMap = ExportConstant.SIMPLE_LEGAL_STATUS_MAP_JP;
        } else {
            simpleLegalStatusMap = ExportConstant.SIMPLE_LEGAL_STATUS_MAP_EN;
        }
        List<String> simpleLegalStatusNames = legalStatusList.stream()
                .map(code -> simpleLegalStatusMap.getOrDefault(code, ""))
                .collect(Collectors.toList());
        String prefix;
        String suffix;
        if(Lang.CN.equals(lang)) {
            prefix = "简单法律状态为";
            suffix = "，";
        } else if(Lang.JP.equals(lang)) {
            prefix = "簡易法的ステータスが";
            suffix = "、";
        } else {
            prefix = "with a simple legal status of:";
            suffix = ", ";
        }
        return prefix + String.join("、", simpleLegalStatusNames) + suffix;
    }

    // 生成受理局文案
    private String generateReceivingOffice(List<String> receivingOfficeList, Lang lang) {
        if(CollectionUtils.isEmpty(receivingOfficeList)) {
            return "";
        }

        String result;
        String prefix;
        String suffix;
        Map<String, String> countryCodeNameMap;
        if(Lang.CN.equals(lang)) {
            prefix = "检索区域为";
            countryCodeNameMap = ExportConstant.COUNTRY_CODE_NAME_MAP_CN;
            suffix = "，";
        } else if(Lang.JP.equals(lang)) {
            prefix = "検索管轄：";
            countryCodeNameMap = ExportConstant.COUNTRY_CODE_NAME_MAP_JP;
            suffix = "、";
        } else {
            prefix = "for the authorities: ";
            countryCodeNameMap = ExportConstant.COUNTRY_CODE_NAME_MAP_EN;
            suffix = ", ";
        }

        if(receivingOfficeList.size() <= 5) {
            // ≤5个，全部显示：检索区域为韩国、美国、日本、德国
            List<String> countryNames = receivingOfficeList.stream()
                    .map(code -> countryCodeNameMap.getOrDefault(code, ""))
                    .collect(Collectors.toList());
            result = prefix + String.join("、", countryNames) + suffix;
        } else {
            // >5个，显示前5个并标注总数：检索区域为韩国、美国、日本、德国、法国等共10个区域
            List<String> firstFiveNames = receivingOfficeList.stream()
                    .limit(5)
                    .map(code -> countryCodeNameMap.getOrDefault(code, ""))
                    .collect(Collectors.toList());
            if(Lang.CN.equals(lang)) {
                result = prefix + String.join("、", firstFiveNames) + "等共" + receivingOfficeList.size() + "个区域" + suffix;
            } else if(Lang.JP.equals(lang)) {
                result = prefix + String.join("、", firstFiveNames) + "など、合計" + receivingOfficeList.size() + "官庁" + suffix;
            } else {
                result = prefix + String.join("、", firstFiveNames) + ", for a total of " + receivingOfficeList.size() + " authorities" + suffix;
            }
        }
        return result;
    }

    // 生成包含公司文案
    private String generateIncludeCompany(List<String> includeCompanyList, Lang lang) {
        if (CollUtil.isEmpty(includeCompanyList)) {
            return "";
        }

        String prefix;
        String suffix;
        if (Lang.CN.equals(lang)) {
            prefix = "仅包含";
            suffix = "，";
        } else if (Lang.JP.equals(lang)) {
            prefix = "権利者が";
            suffix = "（これを含む）、";
        } else {
            prefix = "including companies: ";
            suffix = ",";
        }

        return prefix + String.join("、", includeCompanyList) + suffix;
    }

    // 生成排除公司文案
    private String generateExcludeCompany(List<String> excludeCompanyList, Lang lang) {
        if (CollUtil.isEmpty(excludeCompanyList)) {
            return "";
        }

        String prefix;
        String suffix;
        if (Lang.CN.equals(lang)) {
            prefix = "排除";
            suffix = "，";
        } else if (Lang.JP.equals(lang)) {
            prefix = "";
            suffix = "（これを除く）である、";
        } else {
            prefix = "and excluding companies:";
            suffix = ".";
        }

        return prefix + String.join("、", excludeCompanyList) + suffix;
    }

    // 生成检索日期文案（尾部）
    private String generateSearchDateAfter(DateTimeZone timeZone, Lang lang) {
        DateTime currentTime = new DateTime();
        String formattedDate = DateUtils.formatDateTimeByLang(timeZone, currentTime, lang);
        String prefix;
        String suffix;
        if(Lang.CN.equals(lang)) {
            prefix = "与本技术方案相关的公开专利文献。\n 不排除有于 检索日期";
            suffix = "前提交申请，但未在检索区域公开的专利申请可能涵盖本技术方案。";
        } else if(Lang.JP.equals(lang)) {
            prefix = "本技術案に関連する公開特許文献を対象とします。\n";
            suffix = "より前に出願され、まだ検索対象の管轄官庁で公開されていない特許出願が、本技術案を包含する可能性を排除するものではありません。";
        } else {
            prefix = "\n This does not exclude the possibility that patent applications filed before ";
            suffix = " , but not yet published within the searched authorities, may read on the present technical solution.";
        }
        return prefix + formattedDate + suffix;
    }

    private static void checkLangForUserInput(String userInput, String inputLang) {
        if ((Constant.CN.equalsIgnoreCase(inputLang) && userInput.length() > 5000) || 
            (Constant.EN.equalsIgnoreCase(inputLang) && userInput.length() > 5000)) {
            throw new BizException(LANG_NOT_MATCH);
        }
    }

    private String checkLangForUserInput(String inputLang) {
        if (!(SUPPORT_LANG_LIST.contains(inputLang))) {
            throw new BizException(INPUT_TOO_LONG);
        }
        return inputLang;
    }

    private static @NotNull AiTaskCreateReqDTO buildCreateReq(FtoSearchSubmitRequestDTO request) {
        AiTaskCreateReqDTO createReq = new AiTaskCreateReqDTO();
        createReq.setTaskId(request.getTaskId());
        createReq.setType(AiTaskTypeEnum.AI_FTO_SEARCH.getType());
        Map<String, Object> content = new HashMap<>();
        content.put(AiTaskContentTypeEnum.USER_INPUT.getType(), request.getInput());
        content.put(AiTaskContentTypeEnum.FTO_SEARCH_INPUT_LANG.getType(), request.getInputLang());
        content.put(AiTaskContentTypeEnum.FTO_SEARCH_RECEIVING_OFFICES.getType(), request.getCountry());
        content.put(AiTaskContentTypeEnum.FTO_SEARCH_EXCLUDE_COMPANY.getType(), request.getExcludeCompany());
        content.put(AiTaskContentTypeEnum.FTO_SEARCH_INCLUDE_COMPANY.getType(), request.getIncludeCompany());
        content.put(AiTaskContentTypeEnum.FTO_SEARCH_LEGAL_STATUS.getType(), request.getLegalStatus());
        if (StrUtil.isNotEmpty(request.getApdStart())) {
            content.put(AiTaskContentTypeEnum.FTO_SEARCH_APD_START.getType(), request.getApdStart());
        }
        if (StrUtil.isNotEmpty(request.getApdEnd())) {
            content.put(AiTaskContentTypeEnum.FTO_SEARCH_APD_END.getType(), request.getApdEnd());
        }
        createReq.setContent(content);
        return createReq;
    }

    private String getUserInput(String taskId) {
        Map<AiTaskContentTypeEnum, Object> taskDetail = aiTaskManager.getTaskDetail(taskId);
        return Optional.ofNullable(taskDetail.get(AiTaskContentTypeEnum.FTO_SEARCH_INPUT_SUMMARY))
                .map(Object::toString)
                .orElseGet(() -> (String) taskDetail.get(AiTaskContentTypeEnum.USER_INPUT));
    }

    private @NotNull FeatureExactionResponseDTO getFeatureExactionResponseDTO(
            FtoSearchResponseDTO.Data ftoSearchData, String userInput) {
        FeatureExactionResponseDTO featureExactionResponse = new FeatureExactionResponseDTO();
        featureExactionResponse.setThreshold(ftoSearchData.getThreshold());
        featureExactionResponse.setFeatures(buildFeatures(ftoSearchData));
        featureExactionResponse.setText(userInput);
        return featureExactionResponse;
    }

    private List<AiSearchAgentFeature> buildFeatures(FtoSearchResponseDTO.Data ftoSearchData) {
        List<AiSearchAgentFeature> features = Optional.ofNullable(ftoSearchData).map(
                FtoSearchResponseDTO.Data::getFeatures).orElse(List.of());
        for (AiSearchAgentFeature feature : features) {
            feature.setSelect(feature.getScore() > ftoSearchData.getThreshold());
        }
        AiSearchAgentFeature maxScoreFeature = features.stream()
                .max(Comparator.comparing(AiSearchAgentFeature::getScore))
                .orElse(null);
        if (maxScoreFeature != null) {
            maxScoreFeature.setCore(true);
        }
        return features;
    }

    @TaskContentCache(contentType = AiTaskContentTypeEnum.FTO_SEARCH_INPUT_SUMMARY,
            logicClass = FtoSearchUpdateTextContentCacheLogic.class)
    public String updateTechnicalEssential(AiTaskReqDTO request) {
        return request.getText();
    }

    private List<Map<String, Object>> buildSearchQFilters(String taskId) {
        List<Map<String, Object>> qFilters = new ArrayList<>();
        List<String> excludeCompany = (List<String>) aiTaskManager.getTaskContent(
                taskId, AiTaskContentTypeEnum.FTO_SEARCH_EXCLUDE_COMPANY);

        List<String> includeCompany = (List<String>) aiTaskManager.getTaskContent(
                taskId, AiTaskContentTypeEnum.FTO_SEARCH_INCLUDE_COMPANY);
        if (CollUtil.isNotEmpty(excludeCompany)) {
            Map<String, Object> excludeCompanyMap = new HashMap<>();
            excludeCompanyMap.put("field", "ANCS");
            excludeCompanyMap.put("mode", "structured");
            excludeCompanyMap.put("logic", "NOT");
            List<String> excludeCompanyList = new ArrayList<>();
            for (String company : excludeCompany) {
                excludeCompanyList.add("TREE@\"" + company + "\"");
            }
            excludeCompanyMap.put("value", excludeCompanyList);
            qFilters.add(excludeCompanyMap);
        }
        if (CollUtil.isNotEmpty(includeCompany)) {
            Map<String, Object> includeCompanyMap = new HashMap<>();
            includeCompanyMap.put("field", "ANCS");
            includeCompanyMap.put("mode", "structured");
            includeCompanyMap.put("logic", "AND");
            List<String> includeCompanyList = new ArrayList<>();
            for (String company : includeCompany) {
                includeCompanyList.add("TREE@\"" + company + "\"");
            }
            includeCompanyMap.put("value", includeCompanyList);
            qFilters.add(includeCompanyMap);
        }
        return qFilters;
    }

    private List<Map<String, Object>> buildSearchFilters(String taskId) {
        List<Map<String, Object>> filters = new ArrayList<>();
        List<String> country = (List<String>) aiTaskManager.getTaskContent(
                taskId, AiTaskContentTypeEnum.FTO_SEARCH_RECEIVING_OFFICES);

        List<Integer> legalStatus = (List<Integer>) aiTaskManager.getTaskContent(
                taskId, AiTaskContentTypeEnum.FTO_SEARCH_LEGAL_STATUS);
        if (CollUtil.isNotEmpty(country)) {
            Map<String, Object> countryMap = new HashMap<>();
            countryMap.put("field", "COUNTRY");
            countryMap.put("value", country);
            filters.add(countryMap);
        }
        if (CollUtil.isNotEmpty(legalStatus)) {
            Map<String, Object> legalStatusMap = new HashMap<>();
            legalStatusMap.put("field", "SIMPLE_LEGAL_STATUS");
            legalStatusMap.put("value", legalStatus);
            filters.add(legalStatusMap);
        }

        String apdStart = aiTaskManager.getTaskContent(
                taskId, AiTaskContentTypeEnum.FTO_SEARCH_APD_START);
        String apdEnd = aiTaskManager.getTaskContent(
                taskId, AiTaskContentTypeEnum.FTO_SEARCH_APD_END);
        if (StrUtil.isNotBlank(apdStart) || StrUtil.isNotBlank(apdEnd)) {
            Map<String, Object> apdMap = new HashMap<>();
            apdMap.put("field", "APD");
            apdMap.put("begin", StrUtil.isNotBlank(apdStart) ? Integer.valueOf(apdStart) : "*");
            apdMap.put("end", StrUtil.isNotBlank(apdEnd) ? Integer.valueOf(apdEnd) : "*");
            filters.add(apdMap);
        }
        return filters;
    }

    private AiFtoSearchAgentRequest buildSearchAgentRequest(AiTaskReqDTO request, String userInput, FeatureExactionResponseDTO featureExaction) {
        AiFtoSearchAgentRequest aiSearchAgentRequest = new AiFtoSearchAgentRequest()
                .setText(userInput)
                .setFeatures(getSelectedFeatures(featureExaction));
        if(Objects.nonNull(request.getCcNum())) {
            aiSearchAgentRequest.handleTestParams(request.getCcNum());
        }
        return aiSearchAgentRequest;
    }

    private List<AiSearchAgentFeature> getSelectedFeatures(FeatureExactionResponseDTO featureExactionResponse) {
        return featureExactionResponse.getFeatures().stream().filter(AiSearchAgentFeature::getSelect).toList();
    }

    @TaskContentCache(contentType = AiTaskContentTypeEnum.FTO_SEARCH_FEATURE_CONFIRM, logicClass = FtoSearchConfirmContentCacheLogic.class)
    public List<AiSearchFinalResult> updateSimilarList(AiSearchFinalResultUpdateReqDTO request) {
        List<AiSearchFinalResult> similarList = getSimilarList(request.getTaskId());
        Map<String, AiSearchFinalResult> updateMap = request.getFinalResult().stream()
                .collect(Collectors.toMap(AiSearchFinalResult::getPatentId, Function.identity(), (existing, replacement) -> existing));
        
        return similarList.stream()
                .filter(similar -> updateMap.containsKey(similar.getPatentId()))
                .map(similar -> updateSimilarResult(similar, updateMap.get(similar.getPatentId())))
                .toList();
    }

    @TaskContentCache(contentType = AiTaskContentTypeEnum.FTO_SEARCH_TECH_FEATURE_COMPARISON_SCORE,
            logicClass = FtoSearchUserAddContentCacheLogic.class)
    public FeatureComparisonResponseDTO featureComparison(FeatureComparisonRequestDTO request) {
        String userInput = getUserInput(request.getTaskId());
        FeatureExactionResponseDTO techFeatureInfo = aiTaskManager.getTaskContent(
                request.getTaskId(), AiTaskContentTypeEnum.FTO_SEARCH_TECH_FEATURE);
        
        FtoSearchRequestDTO searchRequest = buildFeatureComparisonRequest(
                userInput, techFeatureInfo, request);
        
        FtoSearchResponseDTO.Data ftoSearchData = aiFtoSearchComputeClient.ftoSearch(searchRequest);
        // 如果PN号无效或者PN号对应的专利没有原始权利要求，那么算法接口就会返回空数据，这种情况需要抛出异常，进行页面提示
        if(Objects.isNull(ftoSearchData) || CollectionUtils.isEmpty(ftoSearchData.getFinalRes())) {
            throw new BizException(ContentErrorCodeEnum.INPUT_INVALID);
        }
        return getFeatureComparisonResponseDTO(ftoSearchData);
    }

    private FtoSearchRequestDTO buildFeatureComparisonRequest(
            String userInput, 
            FeatureExactionResponseDTO techFeatureInfo, 
            FeatureComparisonRequestDTO request) {
            
        return new FtoSearchRequestDTO()
                .setTag(FtoSearchTagEnum.FEATURE_COMPARISON.getValue())
                .setModel("gpt-4.1")
                .setFeatureScore(request.generateFeatureScoreMap(getSelectedFeatures(techFeatureInfo)))
                .setText(userInput)
                .setPatentIds(request.getPatentPns())
                .setFto(Boolean.TRUE)
                .setSupportPn(Boolean.TRUE);
    }

    private @NotNull FeatureComparisonResponseDTO getFeatureComparisonResponseDTO(
            FtoSearchResponseDTO.Data ftoSearchData) {
        FeatureComparisonResponseDTO response = new FeatureComparisonResponseDTO();
        response.setFinalRes(ftoSearchData.getFinalRes());
        response.setThreshold(ftoSearchData.getThreshold());
        return response;
    }

    private AiSearchFinalResult updateSimilarResult(
            AiSearchFinalResult original, 
            AiSearchFinalResult update) {
        original.setSelected(update.isSelected());
        // 更新风险等级(以前端传值为准)
        original.setRiskLevel(update.getRiskLevel());
        // 更新权利要求等同信息(以前端传值为准)
        original.setClaimEquivalentInfo(update.getClaimEquivalentInfo());
        updateFeatures(original.getFeatures(), update.getFeatures());
        // 更新完features后，就可以重新计算等同独权数量，独权数量，等同从权数量，从权数量
        calculateClaimNum(original);
        return original;
    }

    /**
     * 计算等同独权数量，独权数量，等同从权数量，从权数量
     */
    private void calculateClaimNum(AiSearchFinalResult result) {
        List<AiSearchFinalResultFeature> features = result.getFeatures();
        if (features == null || features.isEmpty()) {
            return;
        }

        // 初始化计数器
        int equivalentIndependentCount = 0;
        int independentCount = 0;
        int equivalentDependentCount = 0;
        int dependentCount = 0;

        // 遍历特征列表统计各类型数量
        for (AiSearchFinalResultFeature feature : features) {
            String claimType = feature.getClaimType();
            boolean similar = feature.isSimilar();

            if ("independent".equalsIgnoreCase(claimType)) {
                // 独权统计
                independentCount++;
                if (similar) {
                    // 等同独权
                    equivalentIndependentCount++;
                }
            } else if ("dependent".equalsIgnoreCase(claimType)) {
                // 从权统计
                dependentCount++;
                if (similar) {
                    // 等同从权
                    equivalentDependentCount++;
                }
            }
        }

        // 设置统计结果
        result.setEquivalentIndependentClaimNum(equivalentIndependentCount);
        result.setIndependentClaimNum(independentCount);
        result.setEquivalentDependentClaimNum(equivalentDependentCount);
        result.setDependentClaimNum(dependentCount);
    }

    /**
     * 处理权利要求等同信息
     */
    private void processClaimEquivalentInfo(AiSearchFinalResult result) {
        List<AiSearchFinalResultFeature> features = result.getFeatures();
        if (features == null || features.isEmpty()) {
            return;
        }

        // 按 claim_num 进行分组
        Map<Integer, List<AiSearchFinalResultFeature>> claimGroups = features.stream()
                .filter(feature -> feature.getClaimNum() != null)
                .collect(Collectors.groupingBy(AiSearchFinalResultFeature::getClaimNum));

        // 创建权利要求等同信息列表
        List<ClaimEquivalentItem> claimEquivalentItems = new ArrayList<>();

        for (Map.Entry<Integer, List<AiSearchFinalResultFeature>> entry : claimGroups.entrySet()) {
            Integer claimNum = entry.getKey();
            List<AiSearchFinalResultFeature> claimFeatures = entry.getValue();

            // 检查该权利要求下所有特征是否都相似
            boolean allSimilar = claimFeatures.stream()
                    .allMatch(AiSearchFinalResultFeature::isSimilar);

            // 创建权利要求等同项
            ClaimEquivalentItem item = new ClaimEquivalentItem();
            item.setClaimNum(claimNum);
            item.setInfringement(allSimilar);

            claimEquivalentItems.add(item);
        }

        // 按权利要求编号排序
        claimEquivalentItems.sort(Comparator.comparing(ClaimEquivalentItem::getClaimNum));

        // 设置到结果中
        result.setClaimEquivalentInfo(claimEquivalentItems);
    }

    private void updateFeatures(
            List<AiSearchFinalResultFeature> originalFeatures,
            List<AiSearchFinalResultFeature> updateFeatures) {
        Map<String, AiSearchFinalResultFeature> updateFeatureMap = updateFeatures.stream()
                .collect(Collectors.toMap(
                    feature -> feature.getClaimNum() + "_" + feature.getTechFeature(),
                    Function.identity()
                ));
                
        originalFeatures.forEach(feature -> {
            String key = feature.getClaimNum() + "_" + feature.getTechFeature();
            AiSearchFinalResultFeature updateFeature = updateFeatureMap.get(key);
            if (updateFeature != null) {
                feature.setSimilar(updateFeature.isSimilar());
            }
        });
    }

    private List<AiSearchFinalResult> processFinalResults(
            List<AiSearchFinalResult> finalResults, 
            float threshold, 
            String taskId) {
        if (CollUtil.isEmpty(finalResults)) {
            return finalResults;
        }

        for (int i = 0; i < finalResults.size(); i++) {
            processResult(finalResults.get(i), i < 3, threshold);
        }
        return updateFinalResultDetail(finalResults, taskId);
    }

    private void processResult(AiSearchFinalResult result, boolean isSelected, float threshold) {
        result.setSelected(isSelected);
        List<AiSearchFinalResultFeature> features = result.getFeatures();
        features = features.stream()
                .collect(Collectors.toMap(
                        AiSearchFinalResultFeature::getTechFeature,
                        Function.identity(),
                        (oldValue, newValue) -> oldValue,
                        LinkedHashMap::new
                ))
                .values()
                .stream()
                .toList();
        result.setFeatures(features);
        result.getFeatures().forEach(feature ->
            feature.setSimilar(feature.getScore() >= threshold)
        );
    }

    // FTO历史数据处理(权利要求编号，权利要求类型)
    public String handleHistoryTaskContents(String specialTaskId) {
        log.info("Starting to handle fto-history-task contents, specialTaskId: {}", specialTaskId);
        
        // FTO历史人物的任务，以下两个步骤的数据需要增加新字段,claim_num=1,claim_type=independent，也就是历史上的数据都算是权利要求1的独权数据
        // content_type：FTO_SEARCH_TECH_FEATURE_COMPARISON_SCORE，FTO_SEARCH_AGENT_RESULT
        // 依赖这两个新字段的其他字段也需要一并计算
        List<AnalyticsAiTaskHistoryPO> ftoTasks;
        if(StringUtils.isNotBlank(specialTaskId)) {
            ftoTasks = Collections.singletonList(aiTaskManager.getTaskById(specialTaskId));
        } else {
            ftoTasks = aiTaskManager.getTasksByType(AiTaskTypeEnum.AI_FTO_SEARCH.getType());
        }
        
        if (CollUtil.isEmpty(ftoTasks)) {
            log.info("No fto-history-task found to process");
            return "no-fto-tasks-found";
        }
        
        log.info("Found {} fto-history-task to process", ftoTasks.size());
        
        int successCount = 0;
        int failureCount = 0;
        
        for (AnalyticsAiTaskHistoryPO task : ftoTasks) {
            try {
                String taskId = task.getId();
                log.debug("Processing fto-history-task: {}", taskId);
                
                FeatureComparisonResponseDTO comparisonResponse = aiTaskManager.getTaskContentForHistoryHandle(
                    taskId, AiTaskContentTypeEnum.FTO_SEARCH_TECH_FEATURE_COMPARISON_SCORE);
                AiSearchResultDTO aiSearchResult = aiTaskManager.getTaskContentForHistoryHandle(
                    taskId, AiTaskContentTypeEnum.FTO_SEARCH_AGENT_RESULT);
                
                // 先更新agentResult步骤的数据，再更新techFeatureComparisonScore步骤的数据
                updateAgentResult(aiSearchResult, taskId);
                updateTechFeatureComparisonScore(comparisonResponse, taskId);
                
                successCount++;
                log.debug("Successfully processed fto-history-task: {}", taskId);
            } catch (Exception e) {
                failureCount++;
                log.error("Failed to process fto-history-task: {}, error: {}", task.getId(), e.getMessage(), e);
                // 继续处理其他任务，不中断整个批处理过程
            }
        }

        String handleResult = String.format("fto-history-task contents processing completed. Success: %d, Failures: %d", successCount, failureCount);
        log.info(handleResult);
        return handleResult;
    }

    /**
     * 更新搜索结果历史数据
     * 为历史数据添加 claim_num=1, claim_type=independent
     *
     * @param aiSearchResult 搜索结果数据
     * @param taskId 任务ID
     */
    private void updateAgentResult(AiSearchResultDTO aiSearchResult, String taskId) {
        if (Objects.isNull(aiSearchResult)) {
            log.info("aiSearchResult is null for taskId: {}", taskId);
            return;
        }

        try {
            List<AiSearchFinalResult> finalResults = aiSearchResult.getFinalResult();
            if (CollUtil.isEmpty(finalResults)) {
                log.info("finalResults is empty for taskId: {}", taskId);
                return;
            }

            // 处理历史数据，为每个 finalResItem 设置默认的权利要求信息
            finalResults.forEach(finalResItem -> {
                if (CollUtil.isNotEmpty(finalResItem.getFeatures())) {
                    for (AiSearchFinalResultFeature feature : finalResItem.getFeatures()) {
                        // 为历史数据设置默认的权利要求信息：claim_num=1, claim_type=independent
                        if (Objects.isNull(feature.getClaimNum())) {
                            feature.setClaimNum(1);
                        }
                        if (StringUtils.isBlank(feature.getClaimType())) {
                            feature.setClaimType("independent");
                        }
                    }
                }
            });

            // 更新最终结果详情
            finalResults = updateFinalResultDetail(finalResults, taskId);
            aiSearchResult.setFinalResult(finalResults);
            aiTaskManager.updateTaskContentOnly(taskId, AiTaskContentTypeEnum.FTO_SEARCH_AGENT_RESULT, aiSearchResult);
        } catch (Exception e) {
            log.error("Failed to update agent result for taskId: {}", taskId, e);
        }
    }
    
    /**
     * 更新技术特征对比评分历史数据
     * 为历史数据添加 claim_num=1, claim_type=independent
     * 
     * @param comparisonResponse 特征对比响应数据
     * @param taskId 任务ID
     */
    private void updateTechFeatureComparisonScore(FeatureComparisonResponseDTO comparisonResponse, String taskId) {
        if (Objects.isNull(comparisonResponse)) {
            log.info("comparisonResponse is null for taskId: {}", taskId);
            return;
        }
        
        try {
            // 获取对应的搜索结果数据
            AiSearchResultDTO aiSearchResult = aiTaskManager.getTaskContentForHistoryHandle(taskId, AiTaskContentTypeEnum.FTO_SEARCH_AGENT_RESULT);
            if (Objects.isNull(aiSearchResult)) {
                log.warn("aiSearchResult is null for taskId: {}", taskId);
                return;
            }
            List<FeatureComparisonFinalResItem> finalRes = comparisonResponse.getFinalRes();
            if (CollUtil.isEmpty(finalRes)) {
                log.info("finalRes is empty for taskId: {}", taskId);
                return;
            }
            
            // 处理历史数据，为每个 finalResItem 设置默认的权利要求信息
            comparisonResponse.getFinalRes().forEach(finalResItem -> {
                if (CollUtil.isNotEmpty(finalResItem.getFeatures())) {
                    for (FeatureComparisonFinalResFeature feature : finalResItem.getFeatures()) {
                        // 为历史数据设置默认的权利要求信息：claim_num=1, claim_type=independent
                        if (Objects.isNull(feature.getClaimNum())) {
                            feature.setClaimNum(1);
                        }
                        if (StringUtils.isBlank(feature.getClaimType())) {
                            feature.setClaimType("independent");
                        }
                    }
                }
            });
            finalRes = comparisonResponse.getFinalRes();

            List<AiSearchFinalResult> usefulResults = new ArrayList<>();
            finalRes.forEach(finalResItem -> {
                AiSearchFinalResult finalResult = new AiSearchFinalResult();
                BeanUtil.copyProperties(finalResItem, finalResult);
                usefulResults.add(finalResult);
            });
            
            // 合并到现有结果中
            usefulResults.addAll(aiSearchResult.getFinalResult());
            if (CollUtil.isEmpty(usefulResults)) {
                log.info("usefulResults is empty after merge for taskId: {}", taskId);
                return;
            }
            
            // 更新最终结果详情
            aiSearchResult.setFinalResult(updateFinalResultDetail(usefulResults, taskId));
            
            // 批量更新任务内容
            Map<AiTaskContentTypeEnum, Object> contentMap = new HashMap<>();
            contentMap.put(AiTaskContentTypeEnum.FTO_SEARCH_TECH_FEATURE_COMPARISON_SCORE, comparisonResponse);
            contentMap.put(AiTaskContentTypeEnum.FTO_SEARCH_AGENT_RESULT, aiSearchResult);
            aiTaskManager.batchUpdateTaskContentOnly(taskId, contentMap);
            
            log.info("Successfully updated tech feature comparison score for taskId: {}", taskId);
        } catch (Exception e) {
            log.error("Failed to update tech feature comparison score for taskId: {}", taskId, e);
        }
    }
}

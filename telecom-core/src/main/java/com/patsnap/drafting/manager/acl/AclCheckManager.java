package com.patsnap.drafting.manager.acl;

import com.patsnap.core.common.acl.AclConstants;
import com.patsnap.core.common.acl.AclService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version Id: AclManager, v 0.1 2025/2/14 16:13  Exp $
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class AclCheckManager {
    
    private final AclService aclService;
    
    public Boolean checkAcl(List<String> groupIds, AclConstants.AclCheckObject aclCheckObject) {
        return aclService.check(groupIds, aclCheckObject);
    }
    
    public Map<String, Boolean> batchCheck(List<String> groupIds, List<AclConstants.AclCheckObject> resources) {
        return aclService.batchCheck(groupIds, resources);
    }

}

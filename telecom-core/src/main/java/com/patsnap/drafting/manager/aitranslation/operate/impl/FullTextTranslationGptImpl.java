package com.patsnap.drafting.manager.aitranslation.operate.impl;

import com.patsnap.common.request.CorrelationIdHolder;
import com.patsnap.common.request.TenantIdHolder;
import com.patsnap.common.request.UserIdHolder;
import com.patsnap.core.common.copilot.gpt.enums.GPTModelEnum;
import com.patsnap.core.common.copilot.streaming.StreamingModelBuilder;
import com.patsnap.core.common.copilot.streaming.handler.SplitListJsonStreamingResponseHandler;
import com.patsnap.core.common.copilot.streaming.handling.StreamingChatLanguageModel;
import com.patsnap.core.common.copilot.streaming.model.GptResponseDTO;
import com.patsnap.drafting.client.OpenAiClient;
import com.patsnap.drafting.config.AiTranslationConfig;
import com.patsnap.drafting.config.UrlConfig;
import com.patsnap.drafting.constants.Constant;
import com.patsnap.drafting.enums.prompt.PromptKeyEnum;
import com.patsnap.drafting.enums.prompt.ScenarioEnum;
import com.patsnap.drafting.manager.aitranslation.handler.AiTranslationGptStreamingResponseHandler;
import com.patsnap.drafting.manager.aitranslation.operate.TermContextUtil;
import com.patsnap.drafting.manager.aitranslation.operate.TranslationFullTextService;
import com.patsnap.drafting.model.aitranslation.AiTransContextBo;
import com.patsnap.drafting.model.aitranslation.TranslationBO;
import com.patsnap.drafting.util.ParagraphUtils;
import com.patsnap.spring.boot.autoconfigure.web.entity.CommonResponse;
import static com.patsnap.core.common.copilot.restclient.OpenAIClient.HEADER_AI_ENGINE;
import static com.patsnap.core.common.copilot.restclient.OpenAIClient.HEADER_AI_FEATURE;
import static com.patsnap.drafting.manager.aitranslation.operate.TranslationConstant.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Component;
import com.google.common.collect.ImmutableMap;
import reactor.core.publisher.Flux;

@Component
public class FullTextTranslationGptImpl extends TranslationBasicImpl implements
        TranslationFullTextService {
    private static final GPTModelEnum GPT_MODEL = GPTModelEnum.TRANSLATION_GPT;

    public FullTextTranslationGptImpl(UrlConfig urlConfig, AiTranslationConfig aiTranslationConfig,
            OpenAiClient openAiClient) {
        super(urlConfig, aiTranslationConfig, openAiClient);
    }

    @Override
    public String model() {
        return GPT_MODEL.getModelName();
    }

    @Override
    public Flux<CommonResponse<GptResponseDTO<List<TranslationBO>>>> generate(
            AiTransContextBo contextBo) {
        String sourceInput = contextBo.getInput();
        Map<String, String> terms = contextBo.getMatchCustomTerms();
        sourceInput = ParagraphUtils.addParagraphPrefix(sourceInput);
        List<Integer> paragraphIndexes = ParagraphUtils.getParagraphIndexes(sourceInput);
        sourceInput = TermContextUtil.addKeywordsToInput(sourceInput, contextBo.getSourceLang(),
                terms);
        PromptKeyEnum promptKeyEnum = Constant.CN.equals(contextBo.getTargetLang()) ?
                PromptKeyEnum.SELF_MODEL_TRANSLATION_EN2CN
                : PromptKeyEnum.SELF_MODEL_TRANSLATION_CN2EN;
        String translationMsg = openAiClient.buildPromptByPlatform(
                promptKeyEnum.getValue(),
                ImmutableMap.of(INPUT, sourceInput));
        return createFluxFromModel(translationMsg, contextBo.getTargetLang(),
                contextBo.getSourceLang(), paragraphIndexes, terms);
    }

    private Flux<CommonResponse<GptResponseDTO<List<TranslationBO>>>> createFluxFromModel(
            String msg, String targetLang, String sourceLang,
            List<Integer> paragraphIndexes,
            Map<String, String> terms) {
        StreamingChatLanguageModel model = build(GPT_MODEL,
                ScenarioEnum.AI_TRANSLATION.getValue(), null);
        return Flux.create(sink -> {
            SplitListJsonStreamingResponseHandler<List<TranslationBO>> handler =
                    new AiTranslationGptStreamingResponseHandler(
                    sink, msg, terms, targetLang, sourceLang, paragraphIndexes);
            model.generate(msg, handler);
            sink.onCancel(handler::onCancel);
        });
    }

    /**
     * 构建StreamingChatLanguageModel
     *
     * @param modelName 模型名称 {@link GPTModelEnum}
     * @param scenario  AI场景
     * @param temperature  温度
     * @return
     */
    public StreamingChatLanguageModel build(GPTModelEnum modelName, String scenario, Double temperature) {

        this.streamingModelBuilder = new StreamingModelBuilder(urlConfig.getGptBaseUrl(), urlConfig.getGptApiKey());
        return streamingModelBuilder.build(modelName, scenario, temperature);
//        return CustomAiStreamingChatModel.builder()
//                .customHeaders(buildCustomHeaders(modelName, scenario))
//                .baseUrl(urlConfig.getGptBaseUrl())
//                .apiKey(urlConfig.getGptApiKey())
//                .temperature(temperature)
//                .modelName(modelName.getName())
//                .maxTokens(modelName.getLimit())
//                .logRequests(true)
//                .logResponses(true)
//                .build();
    }

    /**
     * 构建通用请求头
     *
     * @param modelName 模型名称 {@link GPTModelEnum}
     * @param scenario  AI场景
     * @return
     */
    protected Map<String, String> buildCustomHeaders(GPTModelEnum modelName, String scenario) {
        Map<String, String> customHeaders = new HashMap<>();
        customHeaders.put(HEADER_AI_FEATURE, scenario);
        String gptEngine = GPTModelEnum.getGptEngine(modelName);
        if (gptEngine != null) {
            customHeaders.put(HEADER_AI_ENGINE, gptEngine);
        }
        String tenantId = TenantIdHolder.get();
        if (tenantId != null) {
            customHeaders.put(TenantIdHolder.X_TENANT_ID, tenantId);
        }
        String userId = UserIdHolder.get();
        if (userId != null) {
            customHeaders.put(UserIdHolder.X_USER_ID, userId);
        }
        String correlationId = CorrelationIdHolder.get();
        if (correlationId != null) {
            customHeaders.put(CorrelationIdHolder.X_CORRELATION_ID, correlationId);
        }
        return customHeaders;
    }
}

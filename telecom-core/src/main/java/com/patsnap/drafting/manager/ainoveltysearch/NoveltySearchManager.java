package com.patsnap.drafting.manager.ainoveltysearch;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Maps;
import com.patsnap.core.common.identity.AccountInfo;
import com.patsnap.core.common.identity.AccountUtils;
import com.patsnap.core.common.identity.UserInfo;
import com.patsnap.drafting.annotation.TaskContentCache;
import com.patsnap.drafting.client.AiNoveltySearchComputeClient;
import com.patsnap.drafting.client.AiSearchClient;
import com.patsnap.drafting.client.ComputeClient;
import com.patsnap.drafting.client.model.*;
import com.patsnap.drafting.client.model.ainovelty.AiNoveltySearchResponse;
import com.patsnap.drafting.constants.Constant;
import com.patsnap.drafting.enums.ainoveltysearch.NoveltySearchElementSourceEnum;
import com.patsnap.drafting.enums.ainoveltysearch.NoveltySearchPatentTypeEnum;
import com.patsnap.drafting.enums.ainoveltysearch.NoveltySearchTagEnum;
import com.patsnap.drafting.enums.task.AiTaskContentTypeEnum;
import com.patsnap.drafting.enums.task.AiTaskTypeEnum;
import com.patsnap.drafting.enums.task.AsyncTaskStatusEnum;
import com.patsnap.drafting.exception.BizException;
import com.patsnap.drafting.manager.IdentityAccountManager;
import com.patsnap.drafting.manager.aitask.AiTaskManager;
import com.patsnap.drafting.manager.content.logic.*;
import com.patsnap.drafting.manager.patent.PatentInfoManager;
import com.patsnap.drafting.repository.aitask.entity.AnalyticsAiTaskHistoryPO;
import com.patsnap.drafting.repository.creditusage.service.AnalyticsCreditUsageDetailService;
import com.patsnap.drafting.request.ainoveltysearch.*;
import com.patsnap.drafting.request.ainoveltysearch.element.BasicElement;
import com.patsnap.drafting.request.aitask.AiTaskCreateReqDTO;
import com.patsnap.drafting.request.aitask.AiTaskReqDTO;
import com.patsnap.drafting.request.patentinfo.PatentInfoRequestDTO;
import com.patsnap.drafting.response.ainoveltysearch.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.patsnap.drafting.exception.errorcode.ContentErrorCodeEnum.LANG_NOT_MATCH;
import static com.patsnap.drafting.manager.aispecification.SpecificationManager.SUPPORT_LANG_LIST;

@Slf4j
@RequiredArgsConstructor
@Service
public class NoveltySearchManager {

    /**
     * 常量定义
     */
    private static final int MAX_INPUT_LENGTH = 5000;
    private static final int DEFAULT_SELECTED_COUNT = 3;
    public static final int MAX_SELECTED_COUNT = 5;
    private static final String GPT_MODEL = "gpt-4.1";
    private static final String SUP_SEARCH_TYPE = "sup_search";
    private static final String KEY_SEARCH_TYPE = "key_search";
    private static final String FEATURE_TEXT_ORIGINAL_KEY = "feature_text_original";
    private static final String FEATURE_TYPE_KEY = "feature_type";
    private static final String EXPRESS_KEY = "express";
    private static final String WORD_KEY = "word";
    private static final String EXTEND_KEY = "extend";
    private static final String IPC_KEY = "ipc";
    private static final String KEY_FIELD = "key";
    private static final String SOURCE = "source";
    private static final String MOST_SIMILAR_KEY = "most_similar";
    
    private final AiSearchClient aiSearchClient;
    
    private final AiNoveltySearchComputeClient aiNoveltySearchComputeClient;
    
    private final ComputeClient computeClient;
    
    private final AiTaskManager aiTaskManager;

    private final ObjectMapper objectMapper;

    private final IdentityAccountManager identityAccountManager;

    private final PatentInfoManager patentInfoManager;

    private final AnalyticsCreditUsageDetailService creditUsageDetailService;

    public NoveltySearchSubmitResponseDTO submit(FeatureExactionRequestDTO request) {
        aiTaskManager.checkSensitiveWords(List.of(request.getInput()));
        // 1. 校验用户输入语言是否合法,不合法直接返回
        String lang = validateInputLanguage(request.getInputLang());
        // 3. 创建任务
        aiTaskManager.createTask(buildCreateReq(request));
        // 4. 校验用户输入文本长度
        validateUserInputLength(request.getInput(), lang);
        // 5. 结果返回
        return NoveltySearchSubmitResponseDTO.builder().taskId(request.getTaskId()).text(request.getInput()).build();
    }

    public String resetTask(AiTaskReqDTO request) {
        AnalyticsAiTaskHistoryPO taskHistoryPO = aiTaskManager.checkPermission(request.getTaskId());
        if (taskHistoryPO.getDataVersion() >= AiTaskTypeEnum.AI_NOVELTY_SEARCH.getDataVersion()) {
            return taskHistoryPO.getId();
        }
        String lang = computeClient.getCheckLang(aiTaskManager.getTaskContent(request.getTaskId(), AiTaskContentTypeEnum.USER_INPUT));
        LambdaUpdateWrapper<AnalyticsAiTaskHistoryPO> updateWrapper = new LambdaUpdateWrapper<AnalyticsAiTaskHistoryPO>()
                .eq(AnalyticsAiTaskHistoryPO::getId, request.getTaskId())
                .set(AnalyticsAiTaskHistoryPO::getDataVersion, AiTaskTypeEnum.AI_NOVELTY_SEARCH.getDataVersion())
                .set(AnalyticsAiTaskHistoryPO::getAsyncStatus, AsyncTaskStatusEnum.Ready.getValue());
        aiTaskManager.updateTask(updateWrapper);
        aiTaskManager.deleteTaskContent(request.getTaskId());
        aiTaskManager.updateTaskContent(request.getTaskId(), AiTaskContentTypeEnum.NOVELTY_SEARCH_INPUT_LANG, lang);
        return request.getTaskId();
    }

    @TaskContentCache(contentType = AiTaskContentTypeEnum.NOVELTY_SEARCH_INPUT_LANG)
    public String getInputLang(AiTaskReqDTO request) {
        return computeClient.getCheckLang(request.getText());
    }

    @TaskContentCache(contentType = AiTaskContentTypeEnum.TITLE)
    public String generateTitle(AiTaskReqDTO request) {
        // 1. 获取 用户输入
        String userInput = getUserInput(request.getTaskId());
        // 2. 根据 用户输入 总结文本
        NoveltySearchRequestDTO noveltySearchRequest = new NoveltySearchRequestDTO().setTag(
                NoveltySearchTagEnum.TITLE_GENERATE.getValue()).setText(userInput);
        NoveltySearchResponseDTO.Data noveltySearchData = aiNoveltySearchComputeClient.noveltySearch(
                noveltySearchRequest);
        // 3. 构建 FeatureExactionResponseDTO 返回
        return noveltySearchData.getTitle();
    }

    private static @NotNull AiTaskCreateReqDTO buildCreateReq(FeatureExactionRequestDTO request) {
        AiTaskCreateReqDTO createReq = new AiTaskCreateReqDTO();
        createReq.setTaskId(request.getTaskId());
        createReq.setType(AiTaskTypeEnum.AI_NOVELTY_SEARCH.getType());

        Map<String, Object> reqContent = Maps.newHashMap();
        reqContent.put(AiTaskContentTypeEnum.USER_INPUT.getType(), request.getInput());
        reqContent.put(AiTaskContentTypeEnum.NOVELTY_SEARCH_INPUT_LANG.getType(), request.getInputLang());
        // 设置公开日筛选属性
        Optional.ofNullable(request.getPbdStart())
                .filter(StrUtil::isNotEmpty)
                .ifPresent(pbdStart -> reqContent.put(AiTaskContentTypeEnum.NOVELTY_SEARCH_PBD_START.getType(), pbdStart));
        
        Optional.ofNullable(request.getPbdEnd())
                .filter(StrUtil::isNotEmpty)
                .ifPresent(pbdEnd -> reqContent.put(AiTaskContentTypeEnum.NOVELTY_SEARCH_PBD_END.getType(), pbdEnd));
        
        createReq.setContent(reqContent);
        return createReq;
    }
    
    /**
     * 校验用户输入文本长度
     *
     * @param userInput 用户输入文本
     * @param inputLang 输入语言
     */
    private static void validateUserInputLength(String userInput, String inputLang) {
        boolean isChineseOrEnglish = Constant.CN.equalsIgnoreCase(inputLang) || Constant.EN.equalsIgnoreCase(inputLang);
        if (isChineseOrEnglish && userInput.length() > MAX_INPUT_LENGTH) {
            throw new BizException(LANG_NOT_MATCH);
        }
    }
    
    /**
     * 校验输入语言是否支持
     *
     * @param inputLang 输入语言
     * @return 校验通过的语言
     */
    private String validateInputLanguage(String inputLang) {
        if (!SUPPORT_LANG_LIST.contains(inputLang)) {
            throw new BizException(LANG_NOT_MATCH);
        }
        return inputLang;
    }

    @TaskContentCache(contentType = AiTaskContentTypeEnum.NOVELTY_SEARCH_TECH_FEATURE)
    public NoveltySearchFeatureExtractResDTO extractTechFeature(AiTaskReqDTO request) {
        // 2. 根据 用户输入 获取 技术特征
        NoveltySearchRequestDTO noveltySearchRequest = new NoveltySearchRequestDTO().setTag(
                NoveltySearchTagEnum.FEATURE_EXTRACT.getValue());
        NoveltySearchSummaryResDTO.Data summary = aiTaskManager.getTaskContent(request.getTaskId(),
                AiTaskContentTypeEnum.NOVELTY_SEARCH_INPUT_SUMMARY);
        
        // 设置数据
        if (summary != null) {
            noveltySearchRequest.setSummaryResult(summary);
        }
        return aiNoveltySearchComputeClient.aiNoveltySearchFeatureExtract(noveltySearchRequest);
    }

    @TaskContentCache(contentType = AiTaskContentTypeEnum.NOVELTY_SEARCH_RETRIEVAL_ELEMENTS)
    public NoveltySearchElementResDTO getRetrievalElements(AiTaskReqDTO request) {
        // 1. 构建查询请求
        NoveltySearchRequestDTO noveltySearchRequest = buildNoveltySearchRequestDTO(request);
        
        // 2. 调用搜索服务
        NoveltySearchRetrievalElementResDTO retrievalElementResDTO = 
                aiNoveltySearchComputeClient.aiNoveltySearchRetrievalElements(noveltySearchRequest);
        
        // 3. 处理返回结果
        NoveltySearchElementResDTO elementResDTO = new NoveltySearchElementResDTO();
        if (retrievalElementResDTO == null || retrievalElementResDTO.getData() == null) {
            return elementResDTO;
        }
        
        // 4. 数据转换与处理
        List<NoveltySearchElementDataDTO> elementDataList = convertToElementDataList(retrievalElementResDTO);
        elementResDTO.setData(elementDataList);
        
        return elementResDTO;
    }
    
    /**
     * 构建查询请求对象
     */
    private NoveltySearchRequestDTO buildNoveltySearchRequestDTO(AiTaskReqDTO request) {
        NoveltySearchRequestDTO noveltySearchRequest = new NoveltySearchRequestDTO();
        noveltySearchRequest.setTag(NoveltySearchTagEnum.KEYWORDS_EXTRACT.getValue());
        
        // 获取摘要和特征数据
        NoveltySearchSummaryResDTO.Data summary = aiTaskManager.getTaskContent(
                request.getTaskId(), AiTaskContentTypeEnum.NOVELTY_SEARCH_INPUT_SUMMARY);
        NoveltySearchFeatureExtractResDTO featureExtract = aiTaskManager.getTaskContent(
                request.getTaskId(), AiTaskContentTypeEnum.NOVELTY_SEARCH_TECH_FEATURE);
        
        // 设置数据
        if (summary != null) {
            noveltySearchRequest.setSummaryResult(summary);
        }
        if (featureExtract != null && featureExtract.getData() != null) {
            // 过滤掉feature_weight为空的数据，同时保留原有顺序
            List<NoveltySearchFeatureExtractDataDTO> filteredData = featureExtract.getData().stream()
                    .filter(feature -> feature.getSelected())
                    .collect(Collectors.toList());
            noveltySearchRequest.setFeatureExtractResult(filteredData);
        }
        
        return noveltySearchRequest;
    }
    
    /**
     * 将检索结果转换为元素数据列表
     */
    private List<NoveltySearchElementDataDTO> convertToElementDataList(NoveltySearchRetrievalElementResDTO retrievalElementResDTO) {
        return retrievalElementResDTO.getData().stream()
                .filter(Objects::nonNull)
                .map(this::convertToElementData)
                .collect(Collectors.toList());
    }
    
    /**
     * 转换单个特征搜索实体到元素数据
     */
    private NoveltySearchElementDataDTO convertToElementData(NoveltySearchRetrievalElementResDTO.FeatureSearchEntity entity) {
        NoveltySearchElementDataDTO elementData = new NoveltySearchElementDataDTO();
        
        // 设置特征和特征类型
        if (entity.getFeatureTextOriginal() != null) {
            elementData.setFeatureTextOriginal(entity.getFeatureTextOriginal());
        }
        if (entity.getFeatureType() != null) {
            elementData.setFeatureType(entity.getFeatureType());
            if (SUP_SEARCH_TYPE.equals(elementData.getFeatureType())){
                elementData.setSelected(false);
            }
        }
        
        // 处理表达式列表
        if (entity.getExpress() != null) {
            List<NoveltySearchElementDataDTO.Element> expressElements = entity.getExpress().stream()
                    .filter(Objects::nonNull)
                    .map(this::convertToElement)
                    .collect(Collectors.toList());
            
            elementData.setExpress(expressElements);
        }
        
        return elementData;
    }
    
    /**
     * 转换表达式到元素
     */
    private NoveltySearchElementDataDTO.Element convertToElement(NoveltySearchRetrievalElementResDTO.FeatureSearchEntity.Express express) {
        NoveltySearchElementDataDTO.Element element = new NoveltySearchElementDataDTO.Element();
        
        // 处理文本内容
        if (express.getWord() != null && express.getWord().getKey() != null) {
            BasicElement word = new BasicElement();
            word.setKey(express.getWord().getKey());
            word.setSource(NoveltySearchElementSourceEnum.KEYWORDS_EXTRACT.getValue());
            element.setWord(word);
        }
        
        // 注意：根据NoveltySearchElementDataDTO.Element的定义，keyword和ipc是List<BasicElement>类型
        // 这里需要将Express对象中的IPC列表转换为Element要求的格式
        if (express.getIpc() != null && !express.getIpc().isEmpty()) {
            List<BasicElement> ipcList = express.getIpc().stream()
                .filter(Objects::nonNull).filter(ipc -> ipc.getKey() != null)
                    .map(ipc -> {
                    BasicElement basicElement = new BasicElement();
                    basicElement.setKey(ipc.getKey());
                    basicElement.setSource(NoveltySearchElementSourceEnum.KEYWORDS_EXTRACT.getValue());
                    return basicElement;
                })
                .collect(Collectors.toList());
            element.setIpc(ipcList);
        }
        
        // 处理keyword，假设需要从Word对象转换
        if (express.getExtend() != null && !express.getExtend().isEmpty()) {
            List<BasicElement> extendList = express.getExtend().stream()
                    .filter(Objects::nonNull).filter(extend -> extend.getKey() != null)
                    .map(extend -> {
                        BasicElement basicElement = new BasicElement();
                        basicElement.setKey(extend.getKey());
                        basicElement.setSource(NoveltySearchElementSourceEnum.KEYWORDS_EXTRACT.getValue());
                        return basicElement;
                    })
                    .collect(Collectors.toList());
            element.setExtend(extendList);
        }
        
        return element;
    }

    @TaskContentCache(contentType = AiTaskContentTypeEnum.NOVELTY_SEARCH_TECH_FEATURE_COMPARISON,
        logicClass = NoveltySearchUserAddContentCacheLogic.class)
    public FeatureComparisonResponseDTO featureComparisonForAddPatent(FeatureComparisonRequestDTO request) {
        return featureComparison(request);
    }

    @TaskContentCache(contentType = AiTaskContentTypeEnum.NOVELTY_SEARCH_TECH_FEATURE_COMPARISON,
            logicClass = NoveltySearchSyncCcCacheLogic.class)
    public FeatureComparisonResponseDTO featureComparisonForCc(FeatureComparisonRequestDTO request) {
        return featureComparison(request);
    }

    /**
     * 特征对比
     *
     * @param request
     * @return
     */
    private FeatureComparisonResponseDTO featureComparison(FeatureComparisonRequestDTO request) {
        aiTaskManager.checkPermission(request.getTaskId());
        // 1. 获取用户输入
        NoveltySearchFeatureExtractResDTO featureExtract = aiTaskManager.getTaskContent(
                request.getTaskId(), AiTaskContentTypeEnum.NOVELTY_SEARCH_TECH_FEATURE);
        List<NoveltySearchFeatureExtractDataDTO> filteredData = new ArrayList<>();
        if (featureExtract != null && featureExtract.getData() != null) {
            // 过滤掉feature_weight为空的数据，同时保留原有顺序
            filteredData = featureExtract.getData().stream()
                    .filter(feature -> feature.getSelected())
                    .collect(Collectors.toList());
        }
        // 2. 根据用户输入和技术特征清单，来得到技术特征和对应得分
        NoveltySearchRequestDTO noveltySearchRequest = new NoveltySearchRequestDTO()
                .setTag(NoveltySearchTagEnum.FEATURE_COMPARISON.getValue())
                .setModel(GPT_MODEL).setFeatureExtractResult(filteredData)
                .setPatentIds(request.getPatentPns()).setSupportPn(Boolean.TRUE);
        NoveltySearchResponseDTO.Data noveltySearchData = aiNoveltySearchComputeClient.aiNoveltySearchPatentFeatureComparison(noveltySearchRequest);
        // 3. 构建 FeatureComparisonResponseDTO 返回
        return getFeatureComparisonResponseDTO(noveltySearchData);
    }

    private List<NoveltySearchFeatureExtractDataDTO> getSelectedFeatures(NoveltySearchFeatureExtractResDTO featureExactionResponse) {
        return featureExactionResponse.getData().stream().filter(NoveltySearchFeatureExtractDataDTO::getSelected).toList();
    }
    
    /**
     * 获取用户输入的文本，优先使用文本总结（如果存在的话）
     *
     * @param taskId 任务ID
     * @return 用户输入文本或其文本总结
     */
    public String getUserInput(String taskId) {
        aiTaskManager.checkPermission(taskId);
        Map<AiTaskContentTypeEnum, Object> taskDetail = aiTaskManager.getTaskDetail(taskId);
        NoveltySearchSummaryResDTO.Data summary = (NoveltySearchSummaryResDTO.Data)
                taskDetail.get(AiTaskContentTypeEnum.NOVELTY_SEARCH_INPUT_SUMMARY);
        if (summary != null) {
            return summary.getTechSolution();
        }
        return  (String) taskDetail.get(AiTaskContentTypeEnum.USER_INPUT);
    }

    private @NotNull FeatureComparisonResponseDTO getFeatureComparisonResponseDTO (
            NoveltySearchResponseDTO.Data noveltySearchData) {
        FeatureComparisonResponseDTO featureComparisonResponse = new FeatureComparisonResponseDTO();
        featureComparisonResponse.setFinalRes(noveltySearchData.getFinalRes());
        featureComparisonResponse.setThreshold(noveltySearchData.getThreshold());
        return featureComparisonResponse;
    }
    
    /**
     * 计算特征是否被选中（基于特征的得分与阈值的比较）
     *
     * @param noveltySearchData 新奇性搜索响应数据，包含特征列表和阈值
     * @return 返回经过处理的特征列表，其中每个特征都根据其得分和阈值进行了计算
     */
    private List<AiSearchAgentFeature> buildFeatures(NoveltySearchResponseDTO.Data noveltySearchData) {
        List<AiSearchAgentFeature> features = Optional.ofNullable(noveltySearchData).map(
                NoveltySearchResponseDTO.Data::getFeatures).orElse(List.of());
        for (AiSearchAgentFeature feature : features) {
            feature.setSelect(feature.getScore() > noveltySearchData.getThreshold());
        }
        AiSearchAgentFeature maxScoreFeature = features.stream()
                .max(Comparator.comparing(AiSearchAgentFeature::getScore))
                .orElse(null);
        if (maxScoreFeature != null) {
            maxScoreFeature.setCore(true);
        }
        return features;
    }
    
    @TaskContentCache(contentType = AiTaskContentTypeEnum.NOVELTY_SEARCH_INPUT_SUMMARY)
    public NoveltySearchSummaryResDTO.Data summaryText(AiTaskReqDTO request) {
        // 1. 获取 用户输入
        String userInput = getUserInput(request.getTaskId());
        // 如果取不到，就使用前端传递的原始文本
        if(StringUtils.isBlank(userInput)) {
            userInput = request.getText();
        }
        // 2. 根据 用户输入 总结文本
        NoveltySearchRequestDTO noveltySearchRequest = new NoveltySearchRequestDTO().setTag(
                NoveltySearchTagEnum.SUMMARY.getValue()).setText(userInput);
        NoveltySearchSummaryResDTO noveltySearchData = aiNoveltySearchComputeClient.aiNoveltySearchSummary(
                noveltySearchRequest);
        return noveltySearchData.getData();
    }

    @TaskContentCache(contentType = AiTaskContentTypeEnum.NOVELTY_SEARCH_INPUT_SUMMARY,
            logicClass = NoveltySearchUpdateTextContentCacheLogic.class)
    public String updateTechnicalEssential(AiTaskReqDTO request) {
        return request.getText();
    }

    @TaskContentCache(contentType = AiTaskContentTypeEnum.NOVELTY_SEARCH_TECH_FEATURE,
            logicClass = NoveltySearchUpdateTechFeatureContentCacheLogic.class)
    public NoveltySearchFeatureExtractResDTO confirmTechnicalFeature(AiNoveltySearchConfirmFeatureReqDTO request) {
        aiTaskManager.checkPermission(request.getTaskId());
        NoveltySearchFeatureExtractResDTO featureExaction = aiTaskManager.getTaskContent(
                request.getTaskId(), AiTaskContentTypeEnum.NOVELTY_SEARCH_TECH_FEATURE);
        if (featureExaction == null || CollUtil.isEmpty(request.getTechFeatures())) {
            return null;
        }
        List<NoveltySearchFeatureExtractDataDTO> dataDTOS = new ArrayList<>(request.getTechFeatures().size());
        for(AiNoveltySearchConfirmFeatureReqDTO.FeatureDTO feature : request.getTechFeatures()) {
            NoveltySearchFeatureExtractDataDTO featureExtractDataDTO = new NoveltySearchFeatureExtractDataDTO();
            BeanUtil.copyProperties(feature, featureExtractDataDTO);
            dataDTOS.add(featureExtractDataDTO);
        }
        featureExaction.setData(dataDTOS);
        return featureExaction;
    }


    @TaskContentCache(contentType = AiTaskContentTypeEnum.NOVELTY_SEARCH_TASK_ID)
    public String noveltySearchAgent(AiTaskReqDTO request) {
        aiTaskManager.checkPermission(request.getTaskId());
        
        // 1. 获取任务相关数据
        String userInput = aiTaskManager.getTaskContent(request.getTaskId(), AiTaskContentTypeEnum.USER_INPUT);
        String lang = aiTaskManager.getTaskContent(request.getTaskId(), AiTaskContentTypeEnum.NOVELTY_SEARCH_INPUT_LANG);
        NoveltySearchSummaryResDTO.Data summary = getTaskSummary(request.getTaskId());
        NoveltySearchFeatureExtractResDTO featureExtract = aiTaskManager.getTaskContent(
                request.getTaskId(), AiTaskContentTypeEnum.NOVELTY_SEARCH_TECH_FEATURE);
        NoveltySearchElementResDTO searchElementResDTO = aiTaskManager.getTaskContent(
                request.getTaskId(), AiTaskContentTypeEnum.NOVELTY_SEARCH_RETRIEVAL_ELEMENTS);
        List<NoveltySearchFeatureExtractDataDTO> extractData = getSelectedFeatures(featureExtract);
        List<Map<String, Object>> keywordsExtractResultList = buildKeywordsExtractResultList(searchElementResDTO);
        
        // 2. 构建AI搜索代理请求
        AiSearchAgentRequest aiSearchAgentRequest = buildAiSearchAgentRequest(
                userInput, summary, extractData, keywordsExtractResultList);

        // 2.1 添加折叠参数
        if (searchElementResDTO != null && searchElementResDTO.getFamilyMergeEnabled()){
            Map<String, Object> collapse = buildCollapse(lang);
            aiSearchAgentRequest.setCollapse(collapse);
        }

        // 2.2 过滤参数 （公开日)
        String pbdStart = aiTaskManager.getTaskContent(request.getTaskId(), AiTaskContentTypeEnum.NOVELTY_SEARCH_PBD_START);
        String pbdEnd = aiTaskManager.getTaskContent(request.getTaskId(), AiTaskContentTypeEnum.NOVELTY_SEARCH_PBD_END);
        aiSearchAgentRequest.setFilters(
            Stream.of(pbdStart, pbdEnd).anyMatch(StrUtil::isNotBlank)
                ? List.of(Map.of(
                "field", "PBD",
                "begin", Optional.ofNullable(pbdStart).filter(StrUtil::isNotBlank).orElse("*"),
                "end", Optional.ofNullable(pbdEnd).filter(StrUtil::isNotBlank).orElse("*")
                ))
                : List.of()
        );
        
        // 3. 处理测试参数（如果有）
        handleTestParams(request, aiSearchAgentRequest);
        
        // 4. 调用AI搜索代理并返回任务ID
        AiNoveltySearchResponse aiSearchAgentResponse = aiSearchClient.noveltyAiSearchAgent(aiSearchAgentRequest);
        return aiSearchAgentResponse.getTaskId();
    }

    private Map<String, Object> buildCollapse(String lang) {
        Map<String, Object> collapse = new HashMap<>();
        collapse.put("field", "FAMILY_ID");
        List<Map<String, String>> rules = new ArrayList<>();
        Map<String, String> rule1 = new HashMap<>();
        rule1.put("type", "sort");
        rule1.put("field", "COUNTRY");
        if ("CN".equalsIgnoreCase(lang)) {
            rule1.put("value", "CN,WO,US,EP");
        }else {
            rule1.put("value", "US,EP,WO,CN");
        }
        rules.add(rule1);
        Map<String, String> rule2 = new HashMap<>();
        rule2.put("type", "relevant");
        rule2.put("field", "score");
        rules.add(rule2);
        collapse.put("rules", rules);
        return collapse;
    }


    /**
     * 获取任务摘要数据
     */
    private NoveltySearchSummaryResDTO.Data getTaskSummary(String taskId) {
        return aiTaskManager.getTaskContent(taskId, AiTaskContentTypeEnum.NOVELTY_SEARCH_INPUT_SUMMARY);
    }
    
    /**
     * 构建关键词提取结果列表
     */
    private List<Map<String, Object>> buildKeywordsExtractResultList(NoveltySearchElementResDTO searchElementResDTO) {
        List<Map<String, Object>> keywordsExtractResultList = new ArrayList<>();
        
        searchElementResDTO.getData().forEach(element -> {
            if (!element.getSelected()) {
                return;
            }
            
            Map<String, Object> keywordsExtractResult = buildSingleKeywordsExtractResult(element);
            keywordsExtractResultList.add(keywordsExtractResult);
        });
        
        return keywordsExtractResultList;
    }
    
    /**
     * 构建单个关键词提取结果
     *
     * @param element 元素数据
     * @return 关键词提取结果Map
     */
    private Map<String, Object> buildSingleKeywordsExtractResult(NoveltySearchElementDataDTO element) {
        Map<String, Object> keywordsExtractResult = new HashMap<>();
        keywordsExtractResult.put(FEATURE_TEXT_ORIGINAL_KEY, element.getFeatureTextOriginal());
        keywordsExtractResult.put(FEATURE_TYPE_KEY, 
                SUP_SEARCH_TYPE.equals(element.getFeatureType()) ? KEY_SEARCH_TYPE : element.getFeatureType());
        
        List<Map<String, Object>> expressList = buildExpressList(element.getExpress());
        keywordsExtractResult.put(EXPRESS_KEY, expressList);
        
        return keywordsExtractResult;
    }
    
    /**
     * 构建表达式列表
     */
    private List<Map<String, Object>> buildExpressList(List<NoveltySearchElementDataDTO.Element> expressElements) {
        return expressElements.stream()
                .map(this::buildSingleExpressMap)
                .collect(Collectors.toList());
    }
    
    /**
     * 构建单个表达式Map
     *
     * @param express 表达式元素
     * @return 表达式Map
     */
    private Map<String, Object> buildSingleExpressMap(NoveltySearchElementDataDTO.Element express) {
        Map<String, Object> expressMap = new HashMap<>();
        
        // 构建word部分
        Map<String, String> expressWordMap = new HashMap<>();
        expressWordMap.put(KEY_FIELD, express.getWord().getKey());
        expressWordMap.put(SOURCE, express.getWord().getSource());
        expressMap.put(WORD_KEY, expressWordMap);
        
        // 构建extend和ipc部分
        expressMap.put(EXTEND_KEY, buildBasicElementMapList(express.getExtend()));
        expressMap.put(IPC_KEY, buildBasicElementMapList(express.getIpc()));
        
        return expressMap;
    }
    
    /**
     * 构建基础元素Map列表
     *
     * @param basicElements 基础元素列表
     * @return 元素Map列表
     */
    private List<Map<String, String>> buildBasicElementMapList(List<BasicElement> basicElements) {
        return basicElements.stream()
                .map(element -> {
                    Map<String, String> elementMap = new HashMap<>();
                    elementMap.put(KEY_FIELD, element.getKey());
                    elementMap.put(SOURCE, element.getSource());
                    return elementMap;
                })
                .collect(Collectors.toList());
    }
    
    /**
     * 构建AI搜索代理请求
     */
    private AiSearchAgentRequest buildAiSearchAgentRequest(
            String userInput,
            NoveltySearchSummaryResDTO.Data summary,
            List<NoveltySearchFeatureExtractDataDTO> extractData,
            List<Map<String, Object>> keywordsExtractResultList) {
        
        return new AiSearchAgentRequest()
                .setText(userInput)
                .setSummaryResult(summary)
                .setFeatureExtractResult(extractData)
                .setKeywordsExtractResult(keywordsExtractResultList);
    }
    
    /**
     * 处理测试参数
     */
    private void handleTestParams(AiTaskReqDTO request, AiSearchAgentRequest aiSearchAgentRequest) {
        if (Objects.nonNull(request.getCcNum())) {
            aiSearchAgentRequest.handleTestParams(request.getCcNum());
        }
    }

    @TaskContentCache(contentType = AiTaskContentTypeEnum.NOVELTY_SEARCH_AGENT_RESULT,
            logicClass = NoveltySearchResultContentCacheLogic.class)
    public AiNoveltySearchResponse fetchNoveltySearchResult(AiSearchResultReqDTO request) {
        aiTaskManager.checkPermission(request.getTaskId());

        // 1. 获取搜索代理任务ID并调用算法接口
        String saTaskId = getSaTaskId(request.getTaskId());
        AiNoveltySearchResponse aiSearchAgentResponse = callNoveltySearchResult(saTaskId, request.getIsFinished());
        
        // 2. 处理搜索结果（完全保持原有逻辑）
        List<AiSearchFinalResult> finalResults = aiSearchAgentResponse.getFinalResult();
        float threshold = aiSearchAgentResponse.getCcThreshold();
        processEachResult(finalResults, threshold);
        
        // 3. 更新结果类型分类
        finalResults = updateFinalResultType(finalResults, request.getTaskId());
        
        // 4. 设置最相似标记
        setMostSimilarFlag(finalResults);
        
        // 5. 构建并返回结果
        return buildFinalResponse(aiSearchAgentResponse, finalResults);
    }
    
    /**
     * 获取搜索代理任务ID
     */
    private String getSaTaskId(String taskId) {
        return aiTaskManager.getTaskContent(taskId, AiTaskContentTypeEnum.NOVELTY_SEARCH_TASK_ID);
    }
    
    /**
     * 调用新奇性搜索结果接口
     */
    private AiNoveltySearchResponse callNoveltySearchResult(String saTaskId, Boolean isFinished) {
        return aiSearchClient.noveltyAiSearchResult(saTaskId, isFinished);
    }
    
    /**
     * 处理每个结果（完全保持原有逻辑）
     */
    /**
     * 处理每个搜索结果
     *
     * @param finalResults 最终结果列表
     * @param threshold 阈值
     */
    private void processEachResult(List<AiSearchFinalResult> finalResults, float threshold) {
        for (int i = 0; i < finalResults.size(); i++) {
            // 前3个设置为选中状态
            if (i < DEFAULT_SELECTED_COUNT) {
                finalResults.get(i).setSelected(true);
            }
            
            // 处理特征去重
            List<AiSearchFinalResultFeature> features = finalResults.get(i).getFeatures();
            features = deduplicateFeatures(features);
            finalResults.get(i).setFeatures(features);
            
            // 设置特征相似性
            setFeatureSimilarity(finalResults.get(i).getFeatures(), threshold);
        }
    }
    
    /**
     * 特征去重（保持原有顺序，相同技术特征只保留第一个）
     */
    private List<AiSearchFinalResultFeature> deduplicateFeatures(List<AiSearchFinalResultFeature> features) {
        // features如果为null则返回空列表
        return Optional.ofNullable(features).map(
                list -> list.stream()
                        .collect(Collectors.toMap(
                                AiSearchFinalResultFeature::getTechFeature,
                                Function.identity(),
                                (oldValue, newValue) -> oldValue,
                                LinkedHashMap::new
                        ))
                        .values()
                        .stream()
                        .toList()
        ).orElse(List.of());
    }
    
    /**
     * 设置特征相似性
     */
    private void setFeatureSimilarity(List<AiSearchFinalResultFeature> features, float threshold) {
        features.forEach(feature -> feature.setSimilar(feature.getScore() >= threshold));
    }
    
    /**
     * 设置最相似标记
     */
    private void setMostSimilarFlag(List<AiSearchFinalResult> finalResults) {
        if (CollUtil.isNotEmpty(finalResults)) {
            finalResults.get(0).setMostSimilar(true);
        }
    }
    
    /**
     * 构建最终响应结果
     */
    private AiNoveltySearchResponse buildFinalResponse(AiNoveltySearchResponse originalResponse, 
                                                      List<AiSearchFinalResult> processedResults) {
        AiNoveltySearchResponse result = new AiNoveltySearchResponse();
        originalResponse.setFinalResult(processedResults);
        BeanUtils.copyProperties(originalResponse, result);
        return result;
    }

    public List<AiSearchFinalResult> getSimilarList(String taskId) {
        aiTaskManager.checkPermission(taskId);
        AiNoveltySearchResponse aiSearchResult = aiTaskManager.getTaskContent(taskId,
                AiTaskContentTypeEnum.NOVELTY_SEARCH_AGENT_RESULT);
        return aiSearchResult.getFinalResult();
    }

    @TaskContentCache(contentType = AiTaskContentTypeEnum.NOVELTY_SEARCH_PATENT_CONFIRM_FEATURE,
            logicClass = NoveltySearchConfirmContentCacheLogic.class)
    public List<AiSearchFinalResult> preUpdateSimilarList(AiSearchFinalResultUpdateReqDTO request) {
        return updateSimilarList(request);
    }

    @TaskContentCache(contentType = AiTaskContentTypeEnum.NOVELTY_SEARCH_FEATURE_CONFIRM,
            logicClass = NoveltySearchConfirmContentCacheLogic.class)
    public List<AiSearchFinalResult> confirmUpdateSimilarList(AiSearchFinalResultUpdateReqDTO request) {
        return updateSimilarList(request);
    }

    private List<AiSearchFinalResult> updateSimilarList(AiSearchFinalResultUpdateReqDTO request) {
        List<AiSearchFinalResult> similarList = getSimilarList(request.getTaskId());
        Map<String, AiSearchFinalResult> updateMap = request.getFinalResult().stream()
                .collect(Collectors.toMap(AiSearchFinalResult::getPatentId, Function.identity()));
        return similarList.stream()
                .filter(similar -> updateMap.containsKey(similar.getPatentId()))
                .map(similar -> updateSimilarResult(similar, updateMap.get(similar.getPatentId())))
                .toList();
    }

    @TaskContentCache(contentType = AiTaskContentTypeEnum.NOVELTY_SEARCH_REPORT_TITLE)
    public NoveltySearchReportTitleResponseDTO generateReportTitle(AiTaskReqDTO request) {
        aiTaskManager.checkPermission(request.getTaskId());
        AnalyticsAiTaskHistoryPO taskHistoryPO = aiTaskManager.getTaskById(request.getTaskId());
        AiNoveltySearchResponse resultDTO = aiTaskManager.getTaskContent(request.getTaskId(), AiTaskContentTypeEnum.NOVELTY_SEARCH_AGENT_RESULT);
        List<LinkedHashMap<String, Object>> confirmList = aiTaskManager.getTaskContent(request.getTaskId(), AiTaskContentTypeEnum.NOVELTY_SEARCH_FEATURE_CONFIRM);
        if (resultDTO == null) {
            return null;
        }
        NoveltySearchReportTitleResponseDTO response = new NoveltySearchReportTitleResponseDTO();
        AccountInfo accountInfo = identityAccountManager.getAccountInfoByUserId(taskHistoryPO.getCreatedBy());
        UserInfo userInfo = AccountUtils.getUserInfo(accountInfo);
        response.setCreator(AccountUtils.getShowValue(userInfo));
        response.setTitle(taskHistoryPO.getTitle());
        response.setSearchQty(resultDTO.getExcuteTimes());
        response.setGeneratedTime(taskHistoryPO.getUpdatedAt().getMillis());
        response.setComparativeQty(resultDTO.getFinalResult().size());
        if (CollUtil.isEmpty(confirmList)) {
            response.setNearestQty(0);
            response.setSecondaryApproachQty(0);
        } else {
            response.setNearestQty(
                    (int) confirmList.stream().filter(it -> MapUtils.getBooleanValue(it, MOST_SIMILAR_KEY, false)).count());
            response.setSecondaryApproachQty(confirmList.size() - response.getNearestQty());
        }

        return response;
    }

    @TaskContentCache(contentType = AiTaskContentTypeEnum.NOVELTY_SEARCH_REPORT_COMPARATIVE_LITERATURE)
    public NoveltySearchComputeDTO getReportCompute(AiTaskReqDTO request) {
        NoveltyAiSearchComputeResDTO responseA = getNoveltySearchComputeResDTO(request, NoveltySearchPatentTypeEnum.A.getValue());
        NoveltySearchComputeDTO response = new NoveltySearchComputeDTO();
        response.setComparativeLiteratures(createPatentReports(responseA, request.getTaskId()));
        return response;
    }

    @TaskContentCache(contentType = AiTaskContentTypeEnum.NOVELTY_SEARCH_REPORT_REVIEW_OF_NOVELTY)
    public NoveltySearchComputeDTO getReportReviewOfNovelty(AiTaskReqDTO request) {
        NoveltyAiSearchComputeResDTO responseX = getNoveltySearchComputeResDTO(request, NoveltySearchPatentTypeEnum.X.getValue());
        NoveltySearchComputeDTO response = new NoveltySearchComputeDTO();
        if (responseX.getData() == null) {
            return response;
        }
        response.setComparativeLiteratures(createPatentReports(responseX, request.getTaskId()));
        response.setReport(responseX.getData().getReports());
        return response;
    }

    @TaskContentCache(contentType = AiTaskContentTypeEnum.NOVELTY_SEARCH_REPORT_REVIEW_OF_CREATIVE)
    public NoveltySearchComputeDTO getReportReviewOfCreative(AiTaskReqDTO request) {
        NoveltyAiSearchComputeResDTO responseY = getNoveltySearchComputeResDTO(request, NoveltySearchPatentTypeEnum.Y.getValue());
        NoveltySearchComputeDTO response = new NoveltySearchComputeDTO();
        if (responseY.getData() == null) {
            return response;
        }
        response.setComparativeLiteratures(createPatentReports(responseY, request.getTaskId()));
        response.setReport(responseY.getData().getReports());
        return response;
    }

    private NoveltyAiSearchComputeResDTO getNoveltySearchComputeResDTO(AiTaskReqDTO request, String type) {
        NoveltyAiSearchComputeResDTO response = new NoveltyAiSearchComputeResDTO();
        NoveltyAiSearchComputeReqDTO.Data reqData = createRequestData(request, type);
        List<NoveltyAiSearchComputeReqDTO.Data.PatentComparisonData> patents = getPatentComparisonData(request, type);
        reqData.setPatents(patents);
        if (CollUtil.isEmpty(patents)) {
            return response;
        }
        log.info("novelty search compute request: {}", reqData);
        response = computeNoveltySearch(reqData);
        return response;
    }


    public Boolean getReportResult(AiTaskReqDTO request) {
        return true;
    }

    private NoveltyAiSearchComputeReqDTO.Data createRequestData(AiTaskReqDTO request, String type) {
        NoveltyAiSearchComputeReqDTO.Data reqData = new NoveltyAiSearchComputeReqDTO.Data();
        reqData.setTag(NoveltySearchTagEnum.REPORT_GENERATE.getValue());
        // 1. 获取用户输入
        NoveltySearchFeatureExtractResDTO featureExtract = aiTaskManager.getTaskContent(
                request.getTaskId(), AiTaskContentTypeEnum.NOVELTY_SEARCH_TECH_FEATURE);
        List<NoveltySearchFeatureExtractDataDTO> filteredData = new ArrayList<>();
        if (featureExtract != null && featureExtract.getData() != null) {
            // 过滤掉feature_weight为空的数据，同时保留原有顺序
            filteredData = featureExtract.getData().stream()
                    .filter(feature -> feature.getSelected())
                    .collect(Collectors.toList());
        }
        if(!NoveltySearchPatentTypeEnum.A.getValue().equals(type)) {
            reqData.setType(type);
        }
        reqData.setText(getUserInput(request.getTaskId()));
        reqData.setFeatureExtractResult(filteredData);
        return reqData;
    }

    private List<NoveltyAiSearchComputeReqDTO.Data.PatentComparisonData> getPatentComparisonData(AiTaskReqDTO request,
                                                                                                 String type) {
        aiTaskManager.checkPermission(request.getTaskId());
        List<LinkedHashMap<String, Object>> confirmMapList = aiTaskManager.getTaskContent(
                request.getTaskId(), AiTaskContentTypeEnum.NOVELTY_SEARCH_FEATURE_CONFIRM);
        if (CollUtil.isEmpty(confirmMapList)) {
            return Collections.emptyList();
        }
        List<AiSearchFinalResult> allFinalResults = confirmMapList.stream()
                .map(map -> objectMapper.convertValue(map, AiSearchFinalResult.class)).toList();
        List<AiSearchFinalResult> finalResults = new ArrayList<>();
        for (AiSearchFinalResult result : allFinalResults) {
            if (!result.isSelected()) {
                continue;
            }
            if (!result.getComparativeLiteratureType().equals(type)) {
                continue;
            }
            finalResults.add(result);
        }
        return finalResults.stream().map(this::createPatentComparisonData).toList();

    }

    private NoveltyAiSearchComputeReqDTO.Data.PatentComparisonData createPatentComparisonData(AiSearchFinalResult confirm) {
        NoveltyAiSearchComputeReqDTO.Data.PatentComparisonData patent = new NoveltyAiSearchComputeReqDTO.Data.PatentComparisonData();
        patent.setPatentId(confirm.getPatentId());

        List<NoveltyAiSearchComputeReqDTO.Data.FeaturePair> featurePairs = confirm.getFeatures().stream()
                .filter(feature -> feature.isSimilar())
                .map(feature -> {
                    NoveltyAiSearchComputeReqDTO.Data.FeaturePair featurePair = new NoveltyAiSearchComputeReqDTO.Data.FeaturePair();
                    featurePair.setTechFeature(feature.getTechFeature());
                    featurePair.setComparisonFeature(feature.getComparisonFeature());
                    return featurePair;
                })
                .collect(Collectors.toList());

        patent.setFeaturePair(featurePairs);
        return patent;
    }

    /**
     * 计算新颖性搜索结果
     *
     * @param reqData 请求数据
     * @return 计算结果
     */
    private NoveltyAiSearchComputeResDTO computeNoveltySearch(NoveltyAiSearchComputeReqDTO.Data reqData) {
        NoveltyAiSearchComputeReqDTO req = new NoveltyAiSearchComputeReqDTO();
        reqData.setModel(GPT_MODEL);
        req.setData(reqData);
        return aiNoveltySearchComputeClient.noveltyAiSearchCompute(req);
    }

    private List<NoveltySearchComparativeLiteratureResDTO> createPatentReports(NoveltyAiSearchComputeResDTO response, String taskId) {
        if (ObjectUtil.isEmpty(response.getData())) {
            return Collections.emptyList();
        }
        Map<String, AiSearchFinalResult> searchFinalResultMap = getSearchFinalResultMap(taskId);
        Map<String, String> patentPnMap = getPatentPnMap(searchFinalResultMap.keySet());
        return response.getData().getParas().stream()
                .map(patent -> {
                    NoveltySearchComparativeLiteratureResDTO patentReport = new NoveltySearchComparativeLiteratureResDTO();
                    patentReport.setPatentId(patent.getPatentId());
                    patentReport.setRelatedPara(patent.getRelatedPara());
                    patentReport.setPublicScore(searchFinalResultMap.get(patent.getPatentId()).getPublicScore());
                    patentReport.setPn(patentPnMap.get(patent.getPatentId()));
                    return patentReport;
                })
                .collect(Collectors.toList());
    }

    private Map<String, String> getPatentPnMap(Set<String> strings) {
        if (strings == null || strings.isEmpty()) {
            return Collections.emptyMap(); // 如果输入为空，则返回空 Map
        }

        PatentInfoRequestDTO requestDTO = new PatentInfoRequestDTO();
        requestDTO.setPatentIds(new ArrayList<>(strings));
        JSONObject jsonObject = patentInfoManager.batchFetchPatentBasicInfo(requestDTO);
        if (jsonObject == null) {
            return Collections.emptyMap();
        }

        Map<String, String> patentPnMap = new HashMap<>();
        for (String patentId : strings) {
            if (jsonObject.containsKey(patentId)) {
                JSONObject patentInfo = jsonObject.getJSONObject(patentId);
                if (patentInfo != null && patentInfo.containsKey("PN")) {
                    patentPnMap.put(patentId, patentInfo.getString("PN"));
                } else {
                    patentPnMap.put(patentId, "");
                }
            } else {
                patentPnMap.put(patentId, "");
            }
        }
        return patentPnMap;
    }

    private Map<String, AiSearchFinalResult> getSearchFinalResultMap(String taskId) {
        aiTaskManager.checkPermission(taskId);
        List<LinkedHashMap<String, Object>> confirmMapList = aiTaskManager.getTaskContent(
                taskId, AiTaskContentTypeEnum.NOVELTY_SEARCH_FEATURE_CONFIRM);

        return confirmMapList.stream()
                .map(map -> objectMapper.convertValue(map, AiSearchFinalResult.class))
                .collect(Collectors.toMap(AiSearchFinalResult::getPatentId, Function.identity()));
    }


    private AiSearchFinalResult updateSimilarResult(AiSearchFinalResult original,
            AiSearchFinalResult update) {
        original.setMostSimilar(update.isMostSimilar());
        original.setSelected(update.isSelected());
        updateFeatures(original.getFeatures(), update.getFeatures());
        return original;
    }

    private void updateFeatures(List<AiSearchFinalResultFeature> originalFeatures,
            List<AiSearchFinalResultFeature> updateFeatures) {
        Map<String, AiSearchFinalResultFeature> updateFeatureMap = updateFeatures.stream()
                .collect(Collectors.toMap(AiSearchFinalResultFeature::getTechFeature,
                        Function.identity()));
        originalFeatures.forEach(feature -> {
            AiSearchFinalResultFeature updateFeature = updateFeatureMap.get(feature.getTechFeature());
            if (updateFeature != null) {
                feature.setSimilar(updateFeature.isSimilar());
            }
        });
    }

    /**
     * 更新最终结果的专利类型（X类、Y类或A类文献）
     * X类：单篇文献包含所有技术特征
     * Y类：多篇文献组合包含所有技术特征，且包含主要技术特征
     * A类：其他相关文献
     *
     * @param finalResults 待分类的专利结果列表
     * @param taskId 任务ID
     * @return 更新后的专利结果列表
     */
    public List<AiSearchFinalResult> updateFinalResultType(List<AiSearchFinalResult> finalResults, String taskId) {
        aiTaskManager.checkPermission(taskId);
        // 获取技术特征信息
        NoveltySearchFeatureExtractResDTO featureDTO = aiTaskManager.getTaskContent(taskId, AiTaskContentTypeEnum.NOVELTY_SEARCH_TECH_FEATURE);

        if (featureDTO == null || CollUtil.isEmpty(featureDTO.getData())) {
            // 如果没有技术特征信息，将所有专利标记为A类
            finalResults.forEach(result -> result.setComparativeLiteratureType(NoveltySearchPatentTypeEnum.A.getValue()));
            return finalResults;
        }

        List<NoveltySearchFeatureExtractDataDTO> features = getSelectedFeatures(featureDTO);

        // 分离已选择和未选择的结果，同时将所有专利初始化为A类
        Map<Boolean, List<AiSearchFinalResult>> partitionedResults = finalResults.stream()
                .peek(result -> result.setComparativeLiteratureType(NoveltySearchPatentTypeEnum.A.getValue()))
                .collect(Collectors.partitioningBy(AiSearchFinalResult::isSelected));
                
        List<AiSearchFinalResult> selectedResults = partitionedResults.get(true);
        List<AiSearchFinalResult> unselectedResults = partitionedResults.get(false);

        // 对已选择的结果进行分类
        List<AiSearchFinalResult> classifiedResults = classifyPatentLiterature(
                selectedResults,
                features.size(),
                features.get(0).getFeatureTextOriginal()
        );

        classifiedResults = finalResultSort(classifiedResults);

        // 对未选中的结果进行用户添加类型分组
        Map<Boolean, List<AiSearchFinalResult>> unselectedPartitionedResults = unselectedResults.stream()
                .collect(Collectors.partitioningBy(AiSearchFinalResult::isUserAdded));

        List<AiSearchFinalResult> unselectedUserAddedResults = unselectedPartitionedResults.get(true);
        List<AiSearchFinalResult> unselectedNotUserAddedResults = unselectedPartitionedResults.get(false);

        // 对未选中的用户添加结果进行分类
        unselectedUserAddedResults.sort(Comparator.comparingDouble(AiSearchFinalResult::getPublicScore).reversed());
        unselectedNotUserAddedResults.sort(Comparator.comparingDouble(AiSearchFinalResult::getPublicScore).reversed());

        // 先添加用户，再添加普通
        classifiedResults.addAll(unselectedUserAddedResults);
        classifiedResults.addAll(unselectedNotUserAddedResults);
        return classifiedResults;
    }

    private List<AiSearchFinalResult> finalResultSort(List<AiSearchFinalResult> classifiedResults) {
        if(CollUtil.isEmpty(classifiedResults)) {
            return new ArrayList<>();
        }
        // 根据 comparativeLiteratureType 进行分组
        Map<String, List<AiSearchFinalResult>> groupedResults = classifiedResults.stream()
                .collect(Collectors.groupingBy(AiSearchFinalResult::getComparativeLiteratureType));
        // 重新排序 classifiedResults
        List<AiSearchFinalResult> sortedList = new ArrayList<>();
        // 处理 X/Y/A 三类数据
        processAndAddResults(sortedList,
                groupedResults.getOrDefault(NoveltySearchPatentTypeEnum.X.getValue(), Collections.emptyList()));
        processAndAddResults(sortedList,
                groupedResults.getOrDefault(NoveltySearchPatentTypeEnum.Y.getValue(), Collections.emptyList()));
        processAndAddResults(sortedList,
                groupedResults.getOrDefault(NoveltySearchPatentTypeEnum.A.getValue(), Collections.emptyList()));
        return sortedList;
    }

    /**
     * 处理专利列表，将 isMostSimilar() 为 true 的对象优先排序，并按照 publicScore 降序排列
     */
    private void processAndAddResults(List<AiSearchFinalResult> targetList, List<AiSearchFinalResult> patents) {
        if (patents.isEmpty()) {
            return;
        }

        List<AiSearchFinalResult> sortedResults = patents.stream()
                .sorted(Comparator.comparingDouble(AiSearchFinalResult::getPublicScore).reversed())
                .collect(Collectors.toList());

        // 拆分为最相似的和其他的
        List<AiSearchFinalResult> mostSimilar = new ArrayList<>();
        List<AiSearchFinalResult> others = new ArrayList<>();

        for (AiSearchFinalResult patent : sortedResults) {
            if (patent.isMostSimilar()) {
                mostSimilar.add(patent);
            } else {
                others.add(patent);
            }
        }

        // 先添加最相似的，再添加其他的
        targetList.addAll(mostSimilar);
        targetList.addAll(others);
    }

    /**
     * 对专利文献进行分类（X类、Y类、A类）
     *
     * @param selectedResults 已选择的专利结果
     * @param totalFeatures 技术特征总数
     * @param mainFeature 主要技术特征
     * @return 分类后的专利列表
     */
    private List<AiSearchFinalResult> classifyPatentLiterature(
            List<AiSearchFinalResult> selectedResults,
            int totalFeatures,
            String mainFeature) {
        
        // 获取所有可能的组合并进行分类
        List<List<AiSearchFinalResult>> resultGroups = generatePatentGroups(selectedResults);
        Map<String, Object> classification = identifyXAndPotentialYPatents(resultGroups, totalFeatures);
        
        // 获取分类结果
        List<AiSearchFinalResult> xPatents = (List<AiSearchFinalResult>) classification.get("xPatents");
        List<List<AiSearchFinalResult>> potentialYGroups = (List<List<AiSearchFinalResult>>) classification.get("potentialYGroups");
        List<AiSearchFinalResult> yPatents = determineYPatents(potentialYGroups, mainFeature);
        
        return assembleClassificationResults(selectedResults, xPatents, yPatents);
    }

    /**
     * 组装分类结果，设置专利类型并处理剩余专利
     *
     * @param selectedResults 所有已选择的专利
     * @param xPatents X类专利列表
     * @param yPatents Y类专利列表
     * @return 完整的分类结果列表
     */
    private List<AiSearchFinalResult> assembleClassificationResults(
            List<AiSearchFinalResult> selectedResults,
            List<AiSearchFinalResult> xPatents,
            List<AiSearchFinalResult> yPatents) {
        
        List<AiSearchFinalResult> results = new ArrayList<>();
        Map<String, AiSearchFinalResult> processedPatents = new HashMap<>();
        
        // 处理X类专利
        processPatentGroup(xPatents, NoveltySearchPatentTypeEnum.X.getValue(), results, processedPatents);
        
        // 处理Y类专利
        processPatentGroup(yPatents, NoveltySearchPatentTypeEnum.Y.getValue(), results, processedPatents);
        
        // 处理剩余专利（A类）
        processRemainingPatents(selectedResults, processedPatents, results);
        
        return results;
    }

    /**
     * 处理指定类型的专利组
     *
     * @param patents 待处理的专利列表
     * @param literatureType 文献类型
     * @param results 结果列表
     * @param processedPatents 已处理专利映射
     */
    private void processPatentGroup(
            List<AiSearchFinalResult> patents,
            String literatureType,
            List<AiSearchFinalResult> results,
            Map<String, AiSearchFinalResult> processedPatents) {
        
        for (AiSearchFinalResult patent : patents) {
            patent.setComparativeLiteratureType(literatureType);
            processedPatents.put(patent.getPatentId(), patent);
            results.add(patent);
        }
    }

    /**
     * 处理剩余的专利（归类为A类）
     *
     * @param selectedResults 所有已选择的专利
     * @param processedPatents 已处理专利映射
     * @param results 结果列表
     */
    private void processRemainingPatents(
            List<AiSearchFinalResult> selectedResults,
            Map<String, AiSearchFinalResult> processedPatents,
            List<AiSearchFinalResult> results) {
        
        for (AiSearchFinalResult patent : selectedResults) {
            if (!processedPatents.containsKey(patent.getPatentId())) {
                patent.setComparativeLiteratureType(NoveltySearchPatentTypeEnum.A.getValue());
                results.add(patent);
            }
        }
    }

    /**
     * 生成专利的所有可能组合
     * 
     * @param patents 专利列表
     * @return 所有可能的专利组合
     */
    private List<List<AiSearchFinalResult>> generatePatentGroups(List<AiSearchFinalResult> patents) {
        List<List<AiSearchFinalResult>> groups = new ArrayList<>();
        for (int size = 1; size <= patents.size(); size++) {
            generateCombinations(patents, size, 0, new ArrayList<>(), groups);
        }
        //根据每个元素的size进行排序
        groups.sort(Comparator.comparingInt(List::size));
        return groups;
    }

    /**
     * 递归生成专利组合
     * 
     * @param patents 专利列表
     * @param targetSize 目标组合大小
     * @param startIndex 开始索引
     * @param currentGroup 当前组合
     * @param allGroups 所有组合结果
     */
    private void generateCombinations(
            List<AiSearchFinalResult> patents,
            int targetSize,
            int startIndex,
            List<AiSearchFinalResult> currentGroup,
            List<List<AiSearchFinalResult>> allGroups) {
        
        if (currentGroup.size() == targetSize) {
            allGroups.add(new ArrayList<>(currentGroup));
            return;
        }

        for (int i = startIndex; i < patents.size(); i++) {
            currentGroup.add(patents.get(i));
            generateCombinations(patents, targetSize, i + 1, currentGroup, allGroups);
            currentGroup.remove(currentGroup.size() - 1);
        }
    }

    /**
     * 检查专利组是否覆盖所有技术特征
     * 
     * @param patents 专利组
     * @param requiredFeatureCount 所需技术特征数量
     * @return 是否覆盖所有特征
     */
    private boolean coversAllFeatures(List<AiSearchFinalResult> patents, int requiredFeatureCount) {
        Set<String> coveredFeatures = patents.stream()
                .flatMap(patent -> patent.getFeatures().stream())
                .filter(AiSearchFinalResultFeature::isSimilar)
                .map(AiSearchFinalResultFeature::getTechFeature)
                .collect(Collectors.toSet());
        
        return coveredFeatures.size() == requiredFeatureCount;
    }

    /**
     * 从潜在Y类组中确定最终Y类文献
     * 
     * @param potentialYGroups 潜在Y类组列表
     * @param mainFeature 主要技术特征
     * @return 最终确定的Y类文献列表。如果没有找到合适的组合，返回空列表
     */
    private List<AiSearchFinalResult> determineYPatents(
            List<List<AiSearchFinalResult>> potentialYGroups,
            String mainFeature) {
        
        if (potentialYGroups == null || potentialYGroups.isEmpty()) {
            return Collections.emptyList();
        }

        float maxY1Score = -1;
        List<AiSearchFinalResult> selectedYGroup = new ArrayList<>();
        int minGroupSize = Integer.MAX_VALUE;

        // 遍历所有潜在的Y类组
        for (List<AiSearchFinalResult> group : potentialYGroups) {
            // 在每个组中寻找包含主要特征的最高分专利
            Optional<AiSearchFinalResult> bestPatentInGroup = group.stream()
                    .filter(patent -> hasMainFeature(patent, mainFeature))
                    .max(Comparator.comparing(AiSearchFinalResult::getPublicScore));
            
            if (bestPatentInGroup.isEmpty()) {
                continue;
            }

            float currentScore = bestPatentInGroup.get().getPublicScore();
            // 更新选择：如果找到更高分的专利，或者相同分数但组合更小
            if (currentScore > maxY1Score || 
                (currentScore == maxY1Score && group.size() < minGroupSize)) {
                maxY1Score = currentScore;
                selectedYGroup = new ArrayList<>(group);
                minGroupSize = group.size();
            }
        }

        return selectedYGroup;
    }

    /**
     * 检查专利是否包含相似的主要特征
     */
    private boolean hasMainFeature(AiSearchFinalResult patent, String mainFeature) {
        return patent.getFeatures().stream()
                .anyMatch(feature -> feature.getTechFeature().equals(mainFeature) 
                        && feature.isSimilar());
    }

    /**
     * 识别X类专利和潜在的Y类专利组
     * 
     * @param groups 专利组列表
     * @param totalFeatures 技术特征总数
     * @return Map包含X类专利列表和潜在Y类专利组列表，key分别为"xPatents"和"potentialYGroups"
     */
    private Map<String, Object> identifyXAndPotentialYPatents(
            List<List<AiSearchFinalResult>> groups,
            int totalFeatures) {
        
        // 1. 首先识别X类专利和初步Y类专利组
        List<AiSearchFinalResult> xPatents = new ArrayList<>();
        List<List<AiSearchFinalResult>> potentialYGroups = new ArrayList<>();
        
        identifyInitialPatentGroups(groups, totalFeatures, xPatents, potentialYGroups);
        
        // 2. 过滤掉包含X类专利的Y类组
        List<List<AiSearchFinalResult>> filteredYGroups = filterYGroupsWithoutXPatents(potentialYGroups, xPatents);
        
        // 3. 构建返回结果
        Map<String, Object> result = new HashMap<>();
        result.put("xPatents", xPatents);
        result.put("potentialYGroups", filteredYGroups);
        return result;
    }

    /**
     * 初步识别X类专利和Y类专利组
     * 
     * @param groups 所有专利组合
     * @param totalFeatures 技术特征总数
     * @param xPatents 用于存储识别出的X类专利
     * @param potentialYGroups 用于存储识别出的潜在Y类专利组
     */
    private void identifyInitialPatentGroups(
            List<List<AiSearchFinalResult>> groups,
            int totalFeatures,
            List<AiSearchFinalResult> xPatents,
            List<List<AiSearchFinalResult>> potentialYGroups) {
        
        for (List<AiSearchFinalResult> group : groups) {
            if (!coversAllFeatures(group, totalFeatures)) {
                continue;
            }

            if (group.size() == 1) {
                xPatents.add(group.get(0));
            } else {
                potentialYGroups.add(group);
            }
        }
    }

    /**
     * 过滤掉包含X类专利的Y类组
     * 
     * @param potentialYGroups 潜在Y类专利组
     * @param xPatents X类专利列表
     * @return 过滤后的Y类专利组
     */
    private List<List<AiSearchFinalResult>> filterYGroupsWithoutXPatents(
            List<List<AiSearchFinalResult>> potentialYGroups,
            List<AiSearchFinalResult> xPatents) {
        
        // 创建X类专利ID集合，用于快速查找
        Set<String> xPatentIds = xPatents.stream()
                .map(AiSearchFinalResult::getPatentId)
                .collect(Collectors.toSet());
        
        return potentialYGroups.stream()
                .filter(group -> isGroupWithoutXPatents(group, xPatentIds))
                .collect(Collectors.toList());
    }

    /**
     * 检查Y类组是否不包含任何X类专利
     * 
     * @param group Y类专利组
     * @param xPatentIds X类专利ID集合
     * @return 如果组内不包含X类专利返回true，否则返回false
     */
    private boolean isGroupWithoutXPatents(List<AiSearchFinalResult> group, Set<String> xPatentIds) {
        return group.stream()
                .noneMatch(patent -> xPatentIds.contains(patent.getPatentId()));
    }

    public String getReportType(String taskId) {
        aiTaskManager.checkPermission(taskId);
        List<LinkedHashMap<String, Object>> confirmMapList = aiTaskManager.getTaskContent(
                taskId, AiTaskContentTypeEnum.NOVELTY_SEARCH_FEATURE_CONFIRM);
        if (CollUtil.isEmpty(confirmMapList)) {
            return "";
        }
        Map<String, List<AiSearchFinalResult>> groupedResults = confirmMapList.stream()
                .map(map -> objectMapper.convertValue(map, AiSearchFinalResult.class)).toList()
                .stream()
                .collect(Collectors.groupingBy(AiSearchFinalResult::getComparativeLiteratureType));
        if (groupedResults.containsKey(NoveltySearchPatentTypeEnum.X.getValue())) {
            return NoveltySearchPatentTypeEnum.X.getValue();
        }
        if (groupedResults.containsKey(NoveltySearchPatentTypeEnum.Y.getValue())) {
            return NoveltySearchPatentTypeEnum.Y.getValue();
        }
        return NoveltySearchPatentTypeEnum.A.getValue();
    }

    /**
     * 更新检索要素
     */
    @TaskContentCache(contentType = AiTaskContentTypeEnum.NOVELTY_SEARCH_RETRIEVAL_ELEMENTS, 
        logicClass = NoveltySearchUpdateRetrievalElementsContentCacheLogic.class)
    public NoveltySearchElementResDTO updateRetrievalElements(NoveltySearchUpdateElementReqDTO request) {
        // 权限校验
        aiTaskManager.checkPermission(request.getTaskId());
        
        // 如果请求中的数据为空，则返回原始数据
        if (request.getData() == null || request.getData().isEmpty()) {
            return aiTaskManager.getTaskContent(
                    request.getTaskId(), AiTaskContentTypeEnum.NOVELTY_SEARCH_RETRIEVAL_ELEMENTS);
        }
        NoveltySearchElementResDTO elementResDTO = new NoveltySearchElementResDTO();
        elementResDTO.setData(request.getData());
        elementResDTO.setFamilyMergeEnabled(request.getFamilyMergeEnabled());
        return elementResDTO;
    }

    /**
     * 增加检索要素并扩词
     */
    public NoveltySearchRetrievalAddElementResDTO retrievalElementExtend(NoveltySearchAddElementReqDTO request) {
        // 权限校验
        aiTaskManager.checkPermission(request.getTaskId());
        // 1. 获取用户输入
        String userInput = getUserInput(request.getTaskId());
        // 2. 根据用户输入和技术特征清单，来得到技术特征和对应得分
        NoveltySearchRequestDTO noveltySearchRequest = new NoveltySearchRequestDTO()
                .setTag(NoveltySearchTagEnum.KEYWORDS_EXTEND.getValue())
                .setModel(GPT_MODEL).setText(userInput).setAddWord(request.getAddWord());
        NoveltySearchRetrievalAddElementResDTO noveltySearchData = aiNoveltySearchComputeClient.aiNoveltySearchRetrievalAddElements(noveltySearchRequest);
        
        return noveltySearchData;
    }
}


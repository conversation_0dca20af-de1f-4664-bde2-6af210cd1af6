package com.patsnap.drafting.manager.techreport.enums;

/**
 * @description 意图关键字
 */
@lombok.Getter
public enum IntentKeyEnum {
    
    TECH_DESCRIPTION("TechDescription", "技术描述"),
    TECH_PROBLEM("TechProblem", "技术问题"),
    TECH_KEYWORD("TechKeyword", "技术关键词监控"),
    COMPANY_NAME("CompanyName", "公司主体监控"),
    TECH_DOMAIN_COMPANIES("TechDomainCompanies", "技术领域公司监控"),
    INVALID_INPUT("InvalidInput", "无效或非技术类输入"),
    ;
    
    IntentKeyEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }
    
    private final String value;
    
    private final String desc;
    
}

package com.patsnap.drafting.manager.content;

import com.patsnap.drafting.enums.task.AiTaskContentTypeEnum;
import com.patsnap.drafting.exception.BizException;
import com.patsnap.drafting.exception.errorcode.ContentErrorCodeEnum;
import lombok.AccessLevel;
import lombok.Builder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;

/**
 * 任务内容构建器
 * 使用建造者模式，支持链式调用
 */
@Slf4j
@Builder
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public class TaskContentBuilder {
    
    private final String taskId;
    @Builder.Default
    private final Map<String, String> contents = new HashMap<>();
    private final TaskContentManager contentManager;
    private final Executor executor;

    /**
     * 创建构建器实例
     *
     * @param taskId 任务ID
     * @param contentManager 内容管理器
     * @return TaskContentBuilder实例
     */
    public static TaskContentBuilder create(String taskId, TaskContentManager contentManager) {
        return TaskContentBuilder.builder()
                .taskId(taskId)
                .contentManager(contentManager)
                .build();
    }

    /**
     * 添加单个内容
     *
     * @param type 内容类型
     * @param content 内容
     * @return 当前构建器实例
     */
    public TaskContentBuilder withContent(AiTaskContentTypeEnum type, String content) {
        contents.put(type.getType(), content);
        return this;
    }

    /**
     * 批量添加内容
     *
     * @param contents 内容映射
     * @return 当前构建器实例
     */
    public TaskContentBuilder withContents(Map<AiTaskContentTypeEnum, String> contents) {
        contents.forEach((type, content) -> this.contents.put(type.getType(), content));
        return this;
    }

    /**
     * 批量保存内容
     * 如果没有内容需要保存，会记录警告日志并直接返回
     */
    public void saveContents() {
        // 1. 校验内容是否为空
        if (isContentsEmpty()) {
            return;
        }
        
        // 2. 创建异步任务
        List<CompletableFuture<Void>> saveTasks = createSaveTasks();
        
        // 3. 等待所有任务完成
        waitForCompletion(saveTasks);
    }

    /**
     * 检查内容是否为空
     */
    private boolean isContentsEmpty() {
        if (MapUtils.isEmpty(contents)) {
            log.warn("没有需要保存的内容. taskId: {}", taskId);
            return true;
        }
        log.info("开始批量保存任务内容. taskId: {}, contentTypes: {}", taskId, contents.keySet());
        return false;
    }

    /**
     * 创建内容保存的异步任务列表
     */
    private List<CompletableFuture<Void>> createSaveTasks() {
        return contents.entrySet().stream()
            .map(this::createSingleSaveTask)
            .toList();
    }

    /**
     * 创建单个内容保存的异步任务
     */
    private CompletableFuture<Void> createSingleSaveTask(Entry<String, String> entry) {
        return CompletableFuture.runAsync(() -> saveContentSafely(entry), executor);
    }

    /**
     * 安全地保存单个内容
     */
    private void saveContentSafely(Entry<String, String> entry) {
        try {
            contentManager.saveTaskContent(taskId, entry.getKey(), entry.getValue());
        } catch (Exception e) {
            log.error("保存任务内容失败. taskId: {}, contentType: {}", 
                taskId, entry.getKey(), e);
            throw e;
        }
    }

    /**
     * 等待所有保存任务完成
     */
    private void waitForCompletion(List<CompletableFuture<Void>> saveTasks) {
        try {
            CompletableFuture.allOf(saveTasks.toArray(new CompletableFuture[0])).join();
            log.info("批量保存任务内容完成. taskId: {}", taskId);
        } catch (Exception e) {
            log.error("批量保存任务内容异常. taskId: {}", taskId, e);
            throw new BizException(ContentErrorCodeEnum.SAVE_CONTENT_FAILED);
        }
    }
} 
package com.patsnap.drafting.manager.aispecification.content;

import com.patsnap.core.common.copilot.streaming.model.GptResponseDTO;
import com.patsnap.drafting.config.UrlConfig;
import com.patsnap.drafting.enums.task.AiTaskContentTypeEnum;
import com.patsnap.drafting.handler.specification.SpecificationStringStreamingResponseHandler;
import com.patsnap.drafting.manager.aispecification.content.base.AbstractSpecificationCommonStreamingContent;
import com.patsnap.drafting.manager.aitask.AiTaskManager;
import com.patsnap.drafting.model.base.StreamingModelBO;
import com.patsnap.spring.boot.autoconfigure.web.entity.CommonResponse;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

/**
 * AI说明书撰写 - 实施例
 */
@Component
public class SpecificationEmbodimentContent extends AbstractSpecificationCommonStreamingContent<String> {


    protected SpecificationEmbodimentContent(AiTaskManager aiT<PERSON><PERSON>anager, UrlConfig urlConfig) {
        super(aiTaskManager, urlConfig);
    }

    @Override
    public String getContentType() {
        return AiTaskContentTypeEnum.SPECIFICATION_EMBODIMENT.getType();
    }

    @Override
    public String getFunction() {
        return "generate_embodiment";
    }

    @Override
    protected Flux<CommonResponse<GptResponseDTO<String>>> getContent(StreamingModelBO streamingModelBO) {
        String prompt = streamingModelBO.getPrompt();
        return Flux.create(sink -> {
            SpecificationStringStreamingResponseHandler handler = new SpecificationStringStreamingResponseHandler(sink);
            streamingModelBO.getModel().generate(prompt, handler);
        });
    }
}

package com.patsnap.drafting.manager.aitranslation.handler;

import com.patsnap.common.request.CorrelationIdHolder;
import com.patsnap.core.common.copilot.streaming.handler.StringStreamingResponseHandler;
import com.patsnap.core.common.copilot.streaming.model.GptResponseDTO;
import com.patsnap.drafting.enums.task.AiTaskContentTypeEnum;
import com.patsnap.drafting.manager.content.TaskContentManager;
import com.patsnap.spring.boot.autoconfigure.web.entity.CommonResponse;

import java.util.LinkedHashMap;
import java.util.List;

import cn.hutool.json.JSONUtil;

import com.patsnap.core.common.copilot.streaming.chat.AssistantMessage;
import com.patsnap.core.common.copilot.streaming.response.Response;

import reactor.core.publisher.FluxSink;

/**
 * AI翻译更改原文句子后翻译的流式输出处理器
 *
 * <AUTHOR>
 * @date 2024/08/01
 */
public class AiTranslationSentenceStreamingResponseHandler extends StringStreamingResponseHandler {

    private final String taskId;
    /**
     * 更改后原文句子在原文中的索引
     */
    private final Integer originalIndex;
    /**
     * 更改后原文句子
     */
    private final String originalText;
    private final TaskContentManager taskContentManager;
    private final String correlationId;

    public AiTranslationSentenceStreamingResponseHandler(FluxSink<CommonResponse<GptResponseDTO<String>>> sink,
            String input, String taskId, Integer originalIndex,
            String originalText, TaskContentManager taskContentManager) {
        super(sink);
        this.taskId = taskId;
        this.taskContentManager = taskContentManager;
        this.correlationId = CorrelationIdHolder.get();
        this.originalIndex = originalIndex;
        this.originalText = originalText;
    }

    @Override
    public void onError(Throwable error) {
        CorrelationIdHolder.set(correlationId);
        super.onError(error);
    }

    @Override
    public void onComplete(Response<AssistantMessage> response) {
        CorrelationIdHolder.set(correlationId);
        super.onComplete(response);
    }

    @SuppressWarnings("unchecked")
    @Override
    protected void doAfterComplete(String content) {
        List<LinkedHashMap<String, Object>> translations =
                taskContentManager.getTaskContent(taskId, AiTaskContentTypeEnum.TRANSLATION_RESULT);
        LinkedHashMap<String, Object> replace = translations.get(originalIndex);
        replace.put("translated_text", content);
        taskContentManager.saveTaskContent(taskId,
                AiTaskContentTypeEnum.TRANSLATION_RESULT.getType(),
                JSONUtil.toJsonStr(translations));
    }
}

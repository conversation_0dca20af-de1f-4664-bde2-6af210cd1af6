package com.patsnap.drafting.manager;

import com.patsnap.spring.boot.autoconfigure.resttemplate.message.MessageRestTemplate;
import com.patsnap.spring.boot.autoconfigure.resttemplate.message.apiobjects.NotificationMessageRequest;
import com.patsnap.spring.boot.autoconfigure.resttemplate.message.apiobjects.NotificationMessageResponse;
import com.patsnap.spring.boot.autoconfigure.resttemplate.message.apiobjects.NotificationReceiver;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;

/**
 * 通用的消息通知处理类
 *
 * <AUTHOR>
 * @date 2024/08/06
 */
@Component
@Slf4j
public class NoticeManager {

    // 消息通知中心占位符最大长度
    private static final int MAX_NOTIFICATION_LENGTH = 450;

    private final MessageRestTemplate messageRestTemplate;

    public NoticeManager(MessageRestTemplate messageRestTemplate) {
        this.messageRestTemplate = messageRestTemplate;
    }

    /**
     * 发送消息通知
     *
     * @param taskName  任务名称
     * @param userId    接收消息用户ID
     * @param noticeKey 消息通知场景Key
     * @param link      消息通知链接
     */
    public void sendNotice(String taskName, String userId, String noticeKey, String link) {
        try {
            List<NotificationMessageResponse> results = messageRestTemplate.sendNotification(
                    buildMessageRequest(userId, noticeKey, link, subString(taskName)));
            if (CollectionUtils.isNotEmpty(results)) {
                log.info("send tenant user {} notification message {}", userId,
                        results.stream().map(NotificationMessageResponse::getNotificationMessageId)
                                .collect(Collectors.joining(",")));
            }
        } catch (Exception e) {
            log.warn("send notification fails, error info {}", e.getMessage(), e);
        }
    }

    private String subString(String q) {
        if (q.length() > MAX_NOTIFICATION_LENGTH) {
            q = q.substring(0, MAX_NOTIFICATION_LENGTH) + "......";
        }
        return q;
    }

    /**
     * 构建消息通知请求
     *
     * @param userId    接收消息用户ID
     * @param noticeKey 消息通知场景Key
     * @param link      消息通知链接
     * @return
     */
    private static @NotNull NotificationMessageRequest buildMessageRequest(String userId, String noticeKey, String link,
            String taskName) {
        NotificationMessageRequest notificationMessageRequest = new NotificationMessageRequest();
        notificationMessageRequest.setSceneKey(noticeKey);
        notificationMessageRequest.setSender(userId);
        NotificationReceiver notificationReceiver = new NotificationReceiver();
        notificationReceiver.setReceiver(userId);
        notificationReceiver.setLink(link);
        Map<String, Object> params = new HashMap<>();
        params.put("task", Optional.ofNullable(taskName).orElse(""));
        notificationReceiver.setContentModel(params);
        notificationMessageRequest.setNotificationReceivers(Collections.singleton(notificationReceiver));
        return notificationMessageRequest;
    }
}

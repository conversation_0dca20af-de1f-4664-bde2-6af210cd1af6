package com.patsnap.drafting.manager;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@RequiredArgsConstructor
public class JsonMapperManager {
    
    private final ObjectMapper objectMapper;
    
    /**
     * 将结果对象转换为字符串
     */
    public String convertToString(Object result) {
        if (result == null) {
            return null;
        }
        
        if (result instanceof String) {
            return (String) result;
        }
        
        try {
            return objectMapper.writeValueAsString(result);
        } catch (Exception e) {
            log.error("Failed to convert result to string. result: {}", result, e);
            return null;
        }
    }
    
    /**
     * 将结果对象转换为字符串,去掉换行与缩进
     */
    public String convertToCompactString(Object result) {
        if (result == null) {
            return null;
        }
        
        if (result instanceof String) {
            return (String) result;
        }
        
        try {
            return objectMapper.disable(com.fasterxml.jackson.databind.SerializationFeature.INDENT_OUTPUT).writeValueAsString(result);
        } catch (Exception e) {
            log.error("Failed to convert result to string. result: {}", result, e);
            return null;
        }
    }
    
    /**
     * 将缓存的字符串内容转换为目标类型
     */
    public <T> T convertStringToTargetType(String content, Class<T> returnType) {
        if (StringUtils.isBlank(content)) {
            return null;
        }
        try {
            // 如果返回类型是String，直接返回
            if (String.class.equals(returnType)) {
                return (T) content;
            }
            return objectMapper.readValue(content, returnType);
        } catch (Exception e) {
            log.error("Failed to convert content to target type. content: {}", content, e);
            return null;
        }
    }
    
    public <T> T convertValue(Object content, Class<T> returnType) {
        return objectMapper.convertValue(content, returnType);
    }

}

package com.patsnap.drafting.manager.aispecification.content;

import com.patsnap.drafting.client.OpenAiClient;
import com.patsnap.drafting.config.UrlConfig;
import com.patsnap.drafting.enums.common.OperateTypeEnum;
import com.patsnap.drafting.manager.aispecification.content.base.AbstractStreamingContentOperation;
import com.patsnap.drafting.manager.aitask.AiTaskManager;
import com.patsnap.drafting.manager.credit.CreditManager;
import org.springframework.stereotype.Service;

import static com.patsnap.drafting.enums.prompt.SpecificationPromptKeyEnum.SHORTER_CONTENT;

/**
 * AI说明书撰写 - 内容简写
 */
@Service
public class ShorterContentOperation extends AbstractStreamingContentOperation {


    protected ShorterContentOperation(AiTaskManager aiTaskManager, UrlConfig urlConfig, OpenAiClient openAiClient,
            CreditManager creditManager) {
        super(aiTaskManager, urlConfig, openAiClient, creditManager);
    }

    @Override
    public OperateTypeEnum getOperationType() {
        return OperateTypeEnum.SIMPLIFY;
    }

    @Override
    protected String getPromptKey(String contenType) {
        return SHORTER_CONTENT.getValue();
    }
}

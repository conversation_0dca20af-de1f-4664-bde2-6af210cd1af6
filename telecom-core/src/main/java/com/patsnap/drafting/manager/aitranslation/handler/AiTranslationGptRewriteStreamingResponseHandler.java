package com.patsnap.drafting.manager.aitranslation.handler;

import com.patsnap.common.request.CorrelationIdHolder;
import com.patsnap.core.common.copilot.streaming.handler.SplitListJsonOutputParser;
import com.patsnap.core.common.copilot.streaming.handler.SplitListJsonStreamingResponseHandler;
import com.patsnap.core.common.copilot.streaming.model.GptResponseDTO;
import com.patsnap.drafting.constants.Constant;
import com.patsnap.drafting.enums.task.AiTaskContentTypeEnum;
import com.patsnap.drafting.manager.aitranslation.operate.TermContextUtil;
import com.patsnap.drafting.manager.aitranslation.operate.TranslationConstant;
import com.patsnap.drafting.manager.content.TaskContentManager;
import com.patsnap.drafting.model.aitranslation.TranslationBO;
import com.patsnap.drafting.util.ParagraphUtils;
import com.patsnap.spring.boot.autoconfigure.web.entity.CommonResponse;

import static com.patsnap.drafting.manager.aitranslation.operate.TranslationConstant.FIELD_SEPARATOR_REGEX;
import static com.patsnap.drafting.manager.aitranslation.operate.TranslationConstant.LINE_SEPARATOR_REGEX;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import com.fasterxml.jackson.core.type.TypeReference;

import cn.hutool.json.JSONUtil;

import com.patsnap.core.common.copilot.streaming.chat.AssistantMessage;
import com.patsnap.core.common.copilot.streaming.response.Response;

import reactor.core.publisher.FluxSink;

/**
 * 自研模型AI单句重新翻译的流式输出处理器
 *
 * <AUTHOR>
 */
public class AiTranslationGptRewriteStreamingResponseHandler extends
        SplitListJsonStreamingResponseHandler<String> {

    private final Map<String, String> terms;
    private final String targetLang;
    private final String sourceLang;
    private final int outputIndex;
    private final String taskId;
    private final int actualLength;
    private final TaskContentManager taskContentManager;
    private final String correlationId;

    public AiTranslationGptRewriteStreamingResponseHandler(
            FluxSink<CommonResponse<GptResponseDTO<String>>> sink,
            String input, Map<String, String> terms, String targetLang, String sourceLang, int outputIndex,
            String taskId, int actualLength, TaskContentManager taskContentManager) {
        super(sink, LINE_SEPARATOR_REGEX, FIELD_SEPARATOR_REGEX, new TypeReference<>() {
        });
        this.terms = terms;
        this.targetLang = targetLang;
        this.sourceLang = sourceLang;
        this.outputIndex = outputIndex;
        this.taskId = taskId;
        this.actualLength = actualLength;
        this.taskContentManager = taskContentManager;
        this.correlationId = CorrelationIdHolder.get();
    }

    @Override
    public void onError(Throwable error) {
        CorrelationIdHolder.set(correlationId);
        super.onError(error);
    }

    @Override
    public void onComplete(Response<AssistantMessage> response) {
        CorrelationIdHolder.set(correlationId);
        super.onComplete(response);
    }

    @Override
    protected boolean outputValidate(String content) {
        return TermContextUtil.areTagsBalanced(content);
    }

    @Override
    protected String convertContent(String content) {
        TranslationBO translationBO = covertContent(content);
        if (translationBO == null) {
            return "";
        }
        return translationBO.getTranslatedText();
    }

    public TranslationBO covertContent(String content) {
        List<TranslationBO> sentenceTrans = new SplitListJsonOutputParser<List<TranslationBO>>(LINE_SEPARATOR_REGEX,
                FIELD_SEPARATOR_REGEX).convertContent(content, new TypeReference<>() {
        });
        if (CollectionUtils.isEmpty(sentenceTrans)) {
            return null;
        }
        if (sentenceTrans.size() < actualLength) {
            return null;
        }
        TranslationBO translationBO = sentenceTrans.get(actualLength - 1);
        String sentence = translationBO.getTranslatedText();
        String srcText = translationBO.getSrcText();
        sentence = handleText(sentence, targetLang);
        srcText = handleText(srcText, sourceLang);
        translationBO.setTranslatedText(sentence);
        translationBO.setSrcText(srcText);
//        List<TranslationBO> usefulSentences = sentenceTrans.subList(actualLength - 1, sentenceTrans.size() - actualLength + 1);
//        if (CollectionUtils.isEmpty(usefulSentences)) {
//            return null;
//        }
//        StringBuilder sentenceBuilder = new StringBuilder();
//        StringBuilder srcBuilder = new StringBuilder();
//        for (TranslationBO translationBO : usefulSentences) {
//            String sentence = translationBO.getTranslatedText();
//            String srcText = translationBO.getSrcText();
//            sentence = handleText(sentence, targetLang);
//            srcText = handleText(srcText, sourceLang);
//            sentenceBuilder.append(sentence).append("\n");
//            srcBuilder.append(srcText).append("\n");
//        }
//        TranslationBO translationBO = sentenceTrans.get(actualLength - 1);
//        translationBO.setTranslatedText(sentenceBuilder.toString());
//        translationBO.setSrcText(srcBuilder.toString());
        return translationBO;
    }

    private String handleText(String text, String lang) {
        if (StringUtils.isBlank(text)) {
            return null;
        }
        text = text.replace(ParagraphUtils.PARAGRAPH_PREFIX, "");
        if (text.startsWith("{¥¥¥")) {
            text = text.substring(4);
        } else if (text.startsWith("{¥¥")) {
            text = text.substring(3);
        } else if (text.startsWith("{¥")) {
            text = text.substring(2);
        } else if (text.startsWith("{")) {
            text = text.substring(1);
        }
        for (Map.Entry<String, String> entry : terms.entrySet()) {
            text = TermContextUtil.getTermReplace(text, lang, entry);
        }
        return text;
    }
}

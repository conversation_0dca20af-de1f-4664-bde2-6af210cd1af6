package com.patsnap.drafting.manager.techreport.impl;

import com.patsnap.core.common.copilot.gpt.enums.GPTModelEnum;
import com.patsnap.drafting.client.OpenAiClient;
import com.patsnap.drafting.response.techreport.TechReportTechRecommendResDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.HashMap;

import static com.patsnap.drafting.enums.prompt.PromptKeyEnum.SIMPLE_WORD_SEGMENTATION;
import static com.patsnap.drafting.enums.prompt.ScenarioEnum.TECH_REPORT_SCENARIO;

@Slf4j
@Component
@RequiredArgsConstructor
public class PhraseSegment {
    private final OpenAiClient openAiClient;

    /**
     * 运行短语分段处理
     *
     * @param keywords 关键词列表
     * @param industry 行业领域
     * @return 分段结果
     */
    public Map<String, Map<String, List<String>>> run(List<String> keywords, String industry) {
        Map<String, Map<String, List<String>>> result = new HashMap<>();
        
        // 确保参数不为null，避免Map.of()抛出NullPointerException
        String safeKeywords = keywords != null && !keywords.isEmpty() ? String.join(",", keywords) : "";
        String safeIndustry = industry != null ? industry : "";
        
        // 这里需要根据实际的OpenAI API调用实现具体逻辑：SIMPLE_WORD_SEGMENTATION
        Map<String, String> params = Map.of("phrases", safeKeywords, "industry", safeIndustry);
        String prompt = openAiClient.buildPromptByPlatform(SIMPLE_WORD_SEGMENTATION.getValue(), params);
        int retryCount = 0;
        while(retryCount < 3) {
            result = openAiClient.callGptByPrompt(GPTModelEnum.GPT_MODEL_4_O, prompt, TECH_REPORT_SCENARIO, Map.class);
            if (result != null) {
                break;
            }
            retryCount++;
            log.error("调用AI处理短语分段失败, 正在重试... 重试次数: {}", retryCount);
        }

        log.info("llm结果‘SIMPLE_WORD_SEGMENTATION’:" + result);
        return result;
    }
} 
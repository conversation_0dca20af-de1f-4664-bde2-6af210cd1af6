package com.patsnap.drafting.manager;

import com.patsnap.common.utils.RetryRunner;
import com.patsnap.drafting.client.StorageClient;
import com.patsnap.drafting.model.storage.SignedUrlResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPut;
import org.apache.http.entity.ByteArrayEntity;
import org.apache.http.entity.ContentType;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;

/**
 * 文件操作的相关方法
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class FileManager {
    
    public static final int EXPIRE = 24 * 3600;
    
    private final StorageClient storageClient;
    
    private final HttpClientBuilder httpClientBuilder;
    
    private final RequestConfig requestConfig = RequestConfig.custom().setConnectTimeout(1000)
            .setConnectionRequestTimeout(3000).setSocketTimeout(6000).build();
    
    public String uploadFile2AmazonS3(byte[] bytes, String fileFullName) {
        return uploadFile2AmazonS3(bytes, fileFullName, ContentType.IMAGE_PNG);
    }
    
    public String uploadFile2AmazonS3(byte[] bytes, String fileFullName, ContentType contentType) {
        uploadFile(bytes, fileFullName, contentType);
        return signFile(fileFullName);
    }
    
    private void uploadFile(byte[] bytes, String fileFullName, ContentType contentType) {
        SignedUrlResponse signUploadResponse = storageClient.createSignedUrl(StorageClient.HTTP_PUT, fileFullName,
                EXPIRE,
                true);
        String url;
        if (CollectionUtils.isNotEmpty(signUploadResponse.getSignedUrls())) {
            url = signUploadResponse.getSignedUrls().get(0).getUrl();
            HttpPut httpPut = new HttpPut(url);
            httpPut.setConfig(requestConfig);
            httpPut.setEntity(new ByteArrayEntity(bytes));
            httpPut.setHeader("Content-Encoding", "UTF-8");
            httpPut.setHeader("Content-Type", contentType.getMimeType());
            try (CloseableHttpClient httpClient = httpClientBuilder.build()) {
                RetryRunner.run(new RetryRunner.RetryCommand<Void>() {
                    @Override
                    public Void execute() throws Throwable {
                        try (CloseableHttpResponse response = httpClient.execute(httpPut)) {
                            int statusCode = response.getStatusLine().getStatusCode();
                            if (statusCode < 200 || statusCode > 299) {
                                throw new RestClientException("Upload file to S3 failed, response code " + statusCode);
                            }
                            log.info("Upload file to S3 success");
                            return null;
                        }
                    }
                    
                    @Override
                    public void isRetriable(Throwable e) {
                        log.warn("Upload file to S3 url {} failed, will retry ...", url, e);
                    }
                }, 3, 50);
            } catch (Exception e) {
                log.error("Upload file to amazon S3 failed", e);
            }
        }
    }
    
    /**
     * 文件签名
     * @param fileKey  文件的 key
     * @return  签名后的 url
     */
    public String signFile(String fileKey) {
        if (StringUtils.isBlank(fileKey)) {
            log.warn("file key is empty");
            return StringUtils.EMPTY;
        }
        SignedUrlResponse response = storageClient.createSignedUrl(StorageClient.HTTP_GET, fileKey, EXPIRE, true);
        if (CollectionUtils.isNotEmpty(response.getSignedUrls())) {
            log.info("Sign file {} success", fileKey);
            return response.getSignedUrls().get(0).getUrl();
        }
        return StringUtils.EMPTY;
    }
    
    /**
     *  从签名的 URL 中 抽取出 key
     * @param url 签名的 url
     * @return S3 key
     */
    public static String extractFileKey(String url) {
        // 查找"export/" 或 “agent/”的起始位置
        // 定义支持的路径前缀
        String[] supportedPrefixes = {"export/", "agent/"};
        
        int startIndex = -1;
        for (String prefix : supportedPrefixes) {
            startIndex = url.indexOf(prefix);
            if (startIndex != -1) {
                break;
            }
        }
        
        // 如果没有找到任何支持的前缀，返回空字符串而不是中文错误信息
        if (startIndex == -1) {
            log.warn("No supported prefix found in URL: {}", url);
            return StringUtils.EMPTY;
        }
        
        // 查找"?sign="的位置作为结束
        int endIndex = url.indexOf("?");
        if (endIndex == -1) {
            // 如果没有找到"?"，则取整个后续字符串
            return url.substring(startIndex);
        }
        
        // 提取路径部分
        return url.substring(startIndex, endIndex);
    }
    
}

package com.patsnap.drafting.manager.aitranslation.factory;

import com.patsnap.core.common.copilot.streaming.model.GptResponseDTO;
import com.patsnap.drafting.annotation.TaskContentCache;
import com.patsnap.drafting.config.AiTranslationConfig;
import com.patsnap.drafting.enums.task.AiTaskContentTypeEnum;
import com.patsnap.drafting.manager.aitranslation.operate.TranslationFullTextService;
import com.patsnap.drafting.manager.content.logic.TranslationFullTextContentCacheLogic;
import com.patsnap.drafting.model.aitranslation.AiTransContextBo;
import com.patsnap.drafting.model.aitranslation.TranslationBO;
import com.patsnap.spring.boot.autoconfigure.web.entity.CommonResponse;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

@Component
public class TranslationFullTextFactory {

    @Autowired
    private AiTranslationConfig aiTranslationConfig;

    @Autowired
    private List<TranslationFullTextService> translationTermServices;

    @TaskContentCache(contentType = AiTaskContentTypeEnum.TRANSLATION_RESULT,
            logicClass = TranslationFullTextContentCacheLogic.class)
    public Flux<CommonResponse<GptResponseDTO<List<TranslationBO>>>> generate(
            AiTransContextBo contextBo) {
        TranslationFullTextService service = null;
        for (TranslationFullTextService translationFullTextService : translationTermServices) {
            if (!aiTranslationConfig.getFullTextModel().equals(translationFullTextService.model())) {
                continue;
            }
            service = translationFullTextService;
        }
        return service.generate(contextBo);
    }

}

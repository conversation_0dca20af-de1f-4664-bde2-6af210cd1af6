package com.patsnap.drafting.manager.content.logic;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.patsnap.drafting.enums.task.AiTaskContentTypeEnum;
import com.patsnap.drafting.manager.aitask.AiTaskManager;
import com.patsnap.drafting.request.ainoveltysearch.AiSearchResultDTO;
import com.patsnap.drafting.request.ainoveltysearch.AiSearchResultReqDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.util.EnumMap;
import java.util.Map;

/**
 * FTO查新专利查询结果content处理逻辑
 */
@Component
public class FtoSearchResultContentCacheLogic extends ContentCacheLogic {

    private static final class ProcessDictStatus {
        static final String SUCCESS = "success";
        static final String FAILED = "failed";
        static final String RUNNING = "running";
        static final String CANCELED = "canceled";
    }

    @Autowired
    private AiTaskManager aiTaskManager;

    @Override
    public void updateContent(String taskId, AiTaskContentTypeEnum contentType, Object result, Object[] args) {
        if (!(result instanceof AiSearchResultDTO)) {
            return;
        }

        AiSearchResultDTO resultDTO = (AiSearchResultDTO) result;
        AiSearchResultReqDTO resultReq = (AiSearchResultReqDTO) args[0];

        if (!needToUpdate(resultReq, resultDTO)) {
            return;
        }

        Map<AiTaskContentTypeEnum, Object> contentMap = new EnumMap<>(AiTaskContentTypeEnum.class);
        fillFinalResult(contentMap, resultDTO);
        aiTaskManager.batchUpdateTaskContent(taskId, contentMap);
    }

    private void fillFinalResult(Map<AiTaskContentTypeEnum, Object> contentMap,
            AiSearchResultDTO resultDTO) {
        contentMap.put(AiTaskContentTypeEnum.FTO_SEARCH_AGENT_RESULT, resultDTO);
        if (CollUtil.isNotEmpty(resultDTO.getFinalResult())) {
            int maxLength = Math.min(3, resultDTO.getFinalResult().size());
            contentMap.put(AiTaskContentTypeEnum.FTO_SEARCH_FEATURE_CONFIRM,
                    resultDTO.getFinalResult().subList(0, maxLength));
        }
    }

    private boolean needToUpdate(AiSearchResultReqDTO resultReq, AiSearchResultDTO resultDTO) {
        if (ObjectUtil.isEmpty(resultDTO.getProcessDict())) {
            return resultReq.getIsFinished();
        }
        return !ProcessDictStatus.RUNNING.equals(resultDTO.getTaskStatus());
    }
}
package com.patsnap.drafting.manager;

import com.patsnap.core.common.identity.AccountInfo;
import com.patsnap.core.common.identity.AccountUtils;
import com.patsnap.core.common.identity.IdentityServiceClient;
import com.patsnap.core.common.identity.UserInfo;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

import static com.patsnap.drafting.util.RedisKeyUtil.buildRedisKey;


/**
 * <AUTHOR>
 * @date 2018/8/14.
 */
@Service
@Slf4j
public class IdentityAccountManager {

    private static final Logger LOGGER = LoggerFactory.getLogger(IdentityAccountManager.class);
    private static final String KEY_PREFIX_USER_CONFIG_VALUE = "SUPPORT:IDENTITY:USERS:";
    private static final String KEY_PREFIX_USER_CONFIG_VALUE_WITH_ROLE = "SUPPORT:IDENTITY:USER_WITH_ROLE:";

    @Autowired
    private RedissonClient redisson;

    @Autowired
    private IdentityServiceClient identityServiceClient;
    
    public String getShowValueByUserId(String userId) {
        AccountInfo accountInfo = getAccountInfoByUserId(userId, false);
        if (accountInfo == null) {
            log.warn("get account info is null");
            return null;
        }
        UserInfo userInfo = AccountUtils.getUserInfo(accountInfo);
        return AccountUtils.getShowValue(userInfo);
    }

    public AccountInfo getAccountInfoByUserId(String userId) {
        return getAccountInfoByUserId(userId, false);
    }

    public AccountInfo getAccountInfoByUserId(String userId, Boolean withRole) {
        RBucket<AccountInfo> bucket = null;
        AccountInfo accountInfo = null;
        try {
            if (!withRole) {
                bucket = redisson.getBucket(buildRedisKey(KEY_PREFIX_USER_CONFIG_VALUE, userId));
            } else {
                bucket = redisson.getBucket(buildRedisKey(KEY_PREFIX_USER_CONFIG_VALUE_WITH_ROLE, userId));
            }
            accountInfo = bucket.get();
        } catch (Exception e) {
            LOGGER.warn("get cache from redis is failed", e);
        }
        if (accountInfo == null) {
            try {
                accountInfo = identityServiceClient.getAccountInfoByUserId(userId,
                        new String[]{"roles_valid", "attach_roles", "includePermissions", "include_company_logo"},
                        new boolean[]{withRole, withRole, false, true});
            } catch (Exception e) {
                LOGGER.warn("Exception occurred while get user {} info {}", userId, e);
            }
            if (bucket != null && accountInfo != null) {
                bucket.set(accountInfo, 5, TimeUnit.MINUTES);
            }
        }
        return accountInfo;
    }
}

package com.patsnap.drafting.manager.aitranslation.operate;

import com.patsnap.drafting.constants.Constant;

import java.util.List;
import java.util.Map;
import java.util.Stack;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 术语文本操作工具
 */
public class TermContextUtil {


    private final static String KEY_WORD_FORMAT = "<zh>%s</zh><en>%s</en>";

    /**
     * 将patent gpt返回的术语原文信息移除:
     * patent gpt返回的术语翻译格式为: <zh>智慧芽</zh><en>Patsnap</en>,
     * 最终呈现的翻译只需要显示: Patsnap
     *
     * @param translation 翻译结果
     * @return
     */
    public static String removeKeywordsFromTranslation(String translation,
            Map<String, String> translationKeywordByOriginal,
            String sourceLang, String targetLang) {
        String[] translationLines = translation.split(TranslationConstant.LINE_SEPARATOR_REGEX);
        for (int i = 0; i < translationLines.length; i++) {
            String[] fields = translationLines[i].split(TranslationConstant.FIELD_SEPARATOR_REGEX);
            if (fields.length != 2) {
                continue;
            }
            for (Map.Entry<String, String> entry : translationKeywordByOriginal.entrySet()) {
                fields[0] = getTermReplace(fields[0], sourceLang, entry);
                fields[1] = getTermReplace(fields[1], targetLang, entry);
            }
            translationLines[i] = String.join(TranslationConstant.FIELD_SEPARATOR, fields);
        }
        return String.join(TranslationConstant.LINE_SEPARATOR, translationLines);
    }

    public static String getTermReplace(String text, String lang, Map.Entry<String, String> entry) {
        if (Constant.CN.equals(lang)) {
            text = text.replace(String.format(TranslationConstant.KEY_WORD_FORMAT, entry.getKey(), entry.getValue()),
                    entry.getKey());
        }else if (Constant.EN.equals(lang)) {
            text = text.replace(String.format(TranslationConstant.KEY_WORD_FORMAT, entry.getKey(), entry.getValue()),
                    entry.getValue());
        }
        return text;
    }

    /**
     * 将术语信息添加到用户输入原文中
     *
     * @param input                        用户输入原文
     * @param translationKeywordByOriginal 术语信息
     * @return
     */
    public static String addKeywordsToInput(String input, String sourceLang,
            Map<String, String> translationKeywordByOriginal) {
        if (translationKeywordByOriginal == null || translationKeywordByOriginal.isEmpty()) {
            return input;
        }
        List<Map.Entry<String, String>> entryList = translationKeywordByOriginal.entrySet().stream()
                // 对字符串进行排序, 长度长的在前面, 同长度的包含的在被包含的前面
                .sorted(TermContextUtil::sortByKey).toList();
        for (Map.Entry<String, String> entry : entryList) {
            if (Constant.CN.equals(sourceLang)) {
                input = replaceNotAroundWithTag(input, "zh", entry.getKey(),
                        String.format(KEY_WORD_FORMAT, entry.getKey(), entry.getValue()));
            }else {
                input = replaceNotAroundWithTag(input, "en", entry.getValue(),
                        String.format(KEY_WORD_FORMAT, entry.getKey(), entry.getValue()));
            }
        }
        return input;
    }

    /**
     * 替换input中没有被<{tag}></{tag}>包裹的词
     *
     * @param tag    tag标签, 比如: zh, en
     * @param input  待翻译的文本
     * @param before 待替换的词
     * @param after  替换后的词
     * @return
     */
    public static String replaceNotAroundWithTag(String input, String tag, String before, String after) {
        String regex = "(<"+tag+">.*?</"+tag+">)|(" + Pattern.quote(before) + ")";
        Pattern pattern = Pattern.compile(regex, Pattern.DOTALL);
        Matcher matcher = pattern.matcher(input);

        StringBuilder result = new StringBuilder();
        while (matcher.find()) {
            if (matcher.group(1) != null) {
                matcher.appendReplacement(result, Matcher.quoteReplacement(matcher.group(1)));
            } else {
                matcher.appendReplacement(result, Matcher.quoteReplacement(after));
            }
        }
        matcher.appendTail(result);
        return result.toString();
    }

    /**
     * 对字符串进行排序, 长度长的在前面, 同长度的包含的在被包含的前面
     *
     * @param e1
     * @param e2
     * @return
     */
    public static int sortByKey(Map.Entry<String, String> e1, Map.Entry<String, String> e2) {
        String s1 = e1.getKey();
        String s2 = e2.getKey();
        if (s1.contains(s2) && s1.length() > s2.length()) {
            return -1;
        } else if (s2.contains(s1) && s2.length() > s1.length()) {
            return 1;
        } else {
            return s2.length() - s1.length();
        }
    }

    public static boolean areTagsBalanced(String input) {
        Stack<String> stack = new Stack<>();
        Pattern pattern = Pattern.compile("<(zh|en)>|</(zh|en)>");
        Matcher matcher = pattern.matcher(input);
        while (matcher.find()) {
            String tag = matcher.group();
            if (tag.equals("<zh>") || tag.equals("<en>")) {
                stack.push(tag);
            } else if (tag.equals("</zh>") || tag.equals("</en>")) {
                if (stack.isEmpty() || !matchesOpeningTag(stack.pop(), tag)) {
                    return false; // 闭合标记没有匹配的开标记
                }
            }
        }
        return stack.isEmpty();
    }

    private static boolean matchesOpeningTag(String openingTag, String closingTag) {
        return (openingTag.equals("<zh>") && closingTag.equals("</zh>")) ||
                (openingTag.equals("<en>") && closingTag.equals("</en>"));
    }

}

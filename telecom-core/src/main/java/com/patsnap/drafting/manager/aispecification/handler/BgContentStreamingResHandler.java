package com.patsnap.drafting.manager.aispecification.handler;

import com.patsnap.core.common.copilot.streaming.handler.StringStreamingResponseHandler;
import com.patsnap.core.common.copilot.streaming.model.GptResponseDTO;
import com.patsnap.drafting.util.ReferenceReplacer;
import com.patsnap.spring.boot.autoconfigure.web.entity.CommonResponse;
import org.apache.commons.lang3.StringUtils;
import reactor.core.publisher.FluxSink;


public class BgContentStreamingResHandler extends StringStreamingResponseHandler {

    private ReferenceReplacer refReplacer;


    public BgContentStreamingResHandler(FluxSink<CommonResponse<GptResponseDTO<String>>> sink,
            int combineTimes) {
        super(sink, combineTimes);
    }


    public BgContentStreamingResHandler(FluxSink<CommonResponse<GptResponseDTO<String>>> sink,
            int combineTimes, ReferenceReplacer refReplacer) {
        super(sink, combineTimes);
        this.refReplacer = refReplacer;
    }


    @Override
    protected String convertContent(String content) {
        String processed = refReplacer.doProcess(content);
        return StringUtils.isBlank(processed) ? processed : processed.replaceAll("#", "");
    }
}

package com.patsnap.drafting.manager.techreport.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * 查询结果
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QueryResult {
    /**
     * 主题查询
     */
    private String mainSubjectQuery;

    /**
     * 显示的主题查询，可能会比主题查询多IPC,CPC字段
     */
    private String displayQuery;

    /**
     * 子领域约束查询
     */
    private String subfildeConstraintQuery;

    /**
     * 子公司查询
     */
    private Map<String, String> subCompanyQueries;

    /**
     * 子技术领域查询
     */
    private Map<String, String> subTechsQueries;
} 
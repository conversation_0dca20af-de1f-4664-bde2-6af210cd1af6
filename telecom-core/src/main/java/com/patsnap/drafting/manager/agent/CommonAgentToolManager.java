package com.patsnap.drafting.manager.agent;

import com.patsnap.drafting.manager.agent.model.AgentToolResponseDTO;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletResponse;

@Component
public class CommonAgentToolManager {

    public void handleHeaderKeyResponse(AgentToolResponseDTO response) {
        // 设置响应头
        HttpServletResponse httpResponse = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getResponse();
        if (httpResponse != null && response.getToolMessageKey() != null) {
            httpResponse.setHeader("X-User-Data-Key", response.getToolMessageKey());
        }
    }
}

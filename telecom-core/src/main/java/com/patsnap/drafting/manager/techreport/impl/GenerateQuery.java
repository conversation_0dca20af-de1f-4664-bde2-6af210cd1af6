package com.patsnap.drafting.manager.techreport.impl;

import com.patsnap.core.common.copilot.gpt.enums.GPTModelEnum;
import com.patsnap.drafting.client.OpenAiClient;
import com.patsnap.drafting.manager.techreport.model.QueryResult;
import com.patsnap.drafting.manager.techreport.model.TechReportConstraintDTO;
import com.patsnap.drafting.manager.techreport.model.TechReportExcludedKeywordDTO;
import com.patsnap.drafting.response.techreport.TechReportExtractTechFeature;
import com.patsnap.drafting.util.RedissonUtils;
import jodd.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.patsnap.drafting.enums.prompt.PromptKeyEnum.EXPAND_KEYWORDS;
import static com.patsnap.drafting.enums.prompt.ScenarioEnum.TECH_REPORT_SCENARIO;

@Slf4j
@Component
@RequiredArgsConstructor
public class GenerateQuery {
    private final PhraseSegment phraseSegment;
    private final RedissonUtils redissonUtils;
    private final RedissonClient redissonClient;
    private final OpenAiClient openAiClient;

    /**
     * 生成检索查询
     *
     * @param intent 查询意图
     * @param industry 行业领域
     * @param techReportExtractTechFeature 主题特征
     * @param constraint 约束条件
     * @param extractCompanies 需要提取的公司
     * @param recommendTechSubfields 需要提取的技术领域
     * @param excludedKeyword 排除关键词对象
     * @param agentJobId 任务ID，用于Redis缓存
     * @return 查询结果
     */
    public QueryResult run(String intent, String industry, TechReportExtractTechFeature techReportExtractTechFeature,
                           TechReportConstraintDTO constraint, List<String> extractCompanies,
                           List<String> recommendTechSubfields, TechReportExcludedKeywordDTO excludedKeyword, String agentJobId) {
        Map<String, List<String>> expandKeywords = new HashMap<>();
        Map<String, Map<String, List<String>>> phraseSegments = new HashMap<>();

        String mainSubjectQuery = "";
        String displayQuery = "";
        String subfildeConstraintQuery = "";

        // 提前将所有keywords准备好，用于expandKeywords和phraseSegments的生成
        List<String> keywords = new ArrayList<>();
        Map<String, Object> techFeatureMap = new HashMap<>();
        if (intent.equals("TechKeyword") || intent.equals("TechDomainCompanies")) {
            techFeatureMap = convertFeatureToMap(techReportExtractTechFeature);
            keywords = buildTechKeywords(techFeatureMap);
        }
        keywords.addAll(buildConstraintKeywords(constraint));
        keywords.addAll(buildExcludeKeywords(excludedKeyword));
        keywords.addAll(buildRecommendTechSubfieldsKeywords(recommendTechSubfields));
        keywords = keywords.stream().distinct().toList();

        buildExpandKeywordsAndPhraseSegments(expandKeywords, phraseSegments, keywords, industry);

        // 根据意图生成主查询
        switch (intent) {
            case "TechKeyword":
                // 1、技术关键词查询：((ALL_TECH:(主关键词 OR 子术语)) OR (ALL_TECH:(上位词 OR 独特技术特征)))
                mainSubjectQuery = generateTechKeywordQuery(techFeatureMap, recommendTechSubfields, expandKeywords, phraseSegments, industry, agentJobId);
                String applicationQuery = generateApplicationSubclass(techFeatureMap);
                if (applicationQuery != null && !applicationQuery.isEmpty()) {
                    displayQuery = String.format("(%s AND %s)", mainSubjectQuery, applicationQuery);
                }
                break;
                
            case "CompanyName":
                // 2、公司名称查询：ALL_AN:(TREE@"公司名1") OR ALL_AN:(TREE@"公司名2")
                Map<String, String> companyQueries = generateSubcompanyQuery(convertToCompanyMap(extractCompanies), expandKeywords, agentJobId, recommendTechSubfields);
                if (companyQueries != null && !companyQueries.isEmpty()) {
                    mainSubjectQuery = String.format("(%s)", String.join(" OR ", companyQueries.values()));
                }
                extractCompanies = null; // 清空，避免在子查询中重复处理
                if (constraint != null) {
                    constraint.setOnlyMonitorCompanies(null); // 清空约束中的公司，避免重复限制
                }
                break;
                
            case "TechDomainCompanies":
                // 3、技术+公司组合查询：技术查询 AND 公司查询
                String techQuery = generateTechKeywordQuery(techFeatureMap, recommendTechSubfields, expandKeywords, phraseSegments, industry, agentJobId);
                Map<String, String> companyQueriesForTech = generateSubcompanyQuery(convertToCompanyMap(extractCompanies), expandKeywords, null, null);
                
                if (techQuery != null && !techQuery.isEmpty() && companyQueriesForTech != null && !companyQueriesForTech.isEmpty()) {
                    // 技术查询和公司查询都存在时，用AND连接
                    String companyQueryStr = String.join(" OR ", companyQueriesForTech.values());
                    mainSubjectQuery = String.format("(%s) AND (%s)", techQuery, companyQueryStr);
                }
                extractCompanies = null; // 清空，避免在子查询中重复处理
                if (constraint != null) {
                    constraint.setOnlyMonitorCompanies(null); // 清空约束中的公司，避免重复限制
                }
                break;
                
            default:
                // 4、其他类型查询：直接返回空结果
                return QueryResult.builder()
                        .mainSubjectQuery("")
                        .displayQuery("")
                        .subfildeConstraintQuery("")
                        .subCompanyQueries(new HashMap<>())
                        .subTechsQueries(new HashMap<>())
                        .build();
        }

        // 处理约束条件
        Map<String, Object> constraintMap = convertConstraintToMap(constraint);
        String constraintQuery = generateConstraint(constraintMap, expandKeywords, phraseSegments, industry);
        if (constraintQuery != null && !constraintQuery.isEmpty()) {
            if (mainSubjectQuery != null && !mainSubjectQuery.isEmpty()) {
                mainSubjectQuery = String.format("(%s) AND (%s)", mainSubjectQuery, constraintQuery);
                if (StringUtil.isNotBlank(displayQuery)) {
                    displayQuery = String.format("(%s) AND (%s)", displayQuery, constraintQuery);
                }
            } else {
                subfildeConstraintQuery = String.format("(%s) AND (%s)", subfildeConstraintQuery, constraintQuery);
            }
        }

        // 处理排除关键词
        String excludeQuery = generateExcludeQueryFromDTO(excludedKeyword, expandKeywords, phraseSegments, industry);
        if (excludeQuery != null && !excludeQuery.isEmpty()) {
            if (mainSubjectQuery != null && !mainSubjectQuery.isEmpty()) {
                mainSubjectQuery = String.format("(%s) NOT (%s)", mainSubjectQuery, excludeQuery);
                if (StringUtil.isNotBlank(displayQuery)) {
                    displayQuery = String.format("(%s) NOT (%s)", displayQuery, excludeQuery);
                }
            } else {
                subfildeConstraintQuery = String.format("(%s) NOT (%s)", subfildeConstraintQuery, excludeQuery);
            }
        }

        // 生成子查询：公司查询和技术领域查询
        Map<String, String> subCompanyQueries = generateSubcompanyQuery(convertToCompanyMap(extractCompanies), expandKeywords, null, null);
        Map<String, String> subTechsQueries = generateSubtechQuery(intent, recommendTechSubfields, expandKeywords, phraseSegments, industry, techReportExtractTechFeature);
        
        // 构建并返回最终查询结果
        return QueryResult.builder()
                .mainSubjectQuery(mainSubjectQuery)
                .displayQuery(displayQuery)
                .subfildeConstraintQuery(subfildeConstraintQuery)
                .subCompanyQueries(subCompanyQueries)
                .subTechsQueries(subTechsQueries)
                .build();
    }

    /**
     * 构建扩展关键词和短语片段。
     *
     * @param expandKeywords 扩展关键词映射，键为关键词，值为对应的扩展词列表
     * @param phraseSegments 短语片段映射，键为关键词，值为二级映射，二级映射的键为短语片段，值为对应的词列表
     * @param keywords 关键词列表
     * @param industry 行业
     */
    private void buildExpandKeywordsAndPhraseSegments(Map<String, List<String>> expandKeywords,
                                                      Map<String, Map<String, List<String>>> phraseSegments,
                                                      List<String> keywords,
                                                      String industry) {
        checkMissPhrase(keywords, expandKeywords, phraseSegments, industry);
        checkMissExpand(keywords, expandKeywords, industry);

        addKeyToValueIfAbsent(expandKeywords);

        phraseSegments.values().stream()
                .flatMap(map -> map.entrySet().stream())
                .forEach(entry -> addKeyToValueIfAbsent(Collections.singletonMap(entry.getKey(), entry.getValue())));
    }

    private void addKeyToValueIfAbsent(Map<String, List<String>> map) {
        if (map == null) return;

        map.forEach((key, valueList) -> {
            if (valueList != null && !valueList.contains(key)) {
                valueList.add(key);
            }
        });
    }

    private List<String> buildTechKeywords(Map<String, Object> techFeatureMap) {
        List<String> keywords = new ArrayList<>();
        if (techFeatureMap != null) {
            String mainKeyword = (String) techFeatureMap.get("keyword");
            if (mainKeyword != null) {
                keywords.add(mainKeyword);
            }

            // 安全地添加 child_term
            Object childTermObj = techFeatureMap.get("child_term");
            if (childTermObj instanceof List<?>) {
                List<?> childTerms = (List<?>) childTermObj;
                for (Object term : childTerms) {
                    if (term instanceof String) {
                        keywords.add((String) term);
                    }
                }
            }
            Object uniqueTechFeaturesObj = techFeatureMap.get("uniqueTechFeatures");
            boolean hasUniqueTechFeatures = uniqueTechFeaturesObj instanceof List<?> && !((List<?>)uniqueTechFeaturesObj).isEmpty();
            if (hasUniqueTechFeatures && childTermObj instanceof List<?> && ((List<?>)childTermObj).size() <= 6) {
                // 安全地添加 parent_keywords
                Object parentKeywordsObj = techFeatureMap.get("parent_keywords");
                if (parentKeywordsObj instanceof List<?>) {
                    List<?> parentKeywords = (List<?>) parentKeywordsObj;
                    for (Object kw : parentKeywords) {
                        if (kw instanceof String) {
                            keywords.add((String) kw);
                        }
                    }
                }

                // 安全地添加 uniqueTechFeatures
                List<?> uniqueFeatures = (List<?>) uniqueTechFeaturesObj;
                for (Object feature : uniqueFeatures) {
                    if (feature instanceof String) {
                        keywords.add((String) feature);
                    }
                }
            }
        }
        return keywords;
    }

    private List<String> buildConstraintKeywords(TechReportConstraintDTO constraint) {
        List<String> keywords = new ArrayList<>();
        // 处理约束条件
        Map<String, Object> constraintMap = convertConstraintToMap(constraint);
        // 处理技术
        if (constraintMap != null) {
            List<String> techs = (List<String>) constraintMap.get("techs");
            if (techs != null && !techs.isEmpty()) {
                keywords.addAll(techs);
            }
        }
        return keywords;
    }

    private List<String> buildExcludeKeywords(TechReportExcludedKeywordDTO excludedKeyword) {
        List<String> keywords = new ArrayList<>();
        // 收集排除的技术关键词
        if (excludedKeyword != null && excludedKeyword.getExcludeTechs() != null) {
            keywords.addAll(excludedKeyword.getExcludeTechs());
        }
        return keywords;
    }

    private List<String> buildRecommendTechSubfieldsKeywords(List<String> recommendTechSubfields) {
        List<String> keywords = new ArrayList<>();
        // 子技术领域查询
        if (CollectionUtils.isNotEmpty(recommendTechSubfields)) {
            keywords.addAll(recommendTechSubfields);
        }
        return keywords;
    }

    /**
     * 将公司列表转换为Map格式
     * key为公司名，value为包含该公司名的单元素列表
     */
    private Map<String, List<String>> convertToCompanyMap(List<String> companies) {
        if (companies == null || companies.isEmpty()) {
            return null;
        }
        Map<String, List<String>> result = new HashMap<>();
        for (String company : companies) {
            result.put(company, Collections.singletonList(company));
        }
        return result;
    }

    /**
     * 将约束条件DTO转换为Map格式
     * 包含技术效果、技术问题、国家、监控公司、技术和开始日期等字段
     */
    private Map<String, Object> convertConstraintToMap(TechReportConstraintDTO constraint) {
        if (constraint == null) {
            return null;
        }
        Map<String, Object> result = new HashMap<>();
        
        // 转换技术效果
        if (constraint.getTechEffect() != null) {
            result.put("tech_effect", constraint.getTechEffect());
        }
        
        // 转换技术问题
        if (constraint.getTechProblem() != null) {
            result.put("tech_problem", constraint.getTechProblem());
        }
        
        // 转换国家
        if (constraint.getCountry() != null) {
            result.put("country", constraint.getCountry());
        }
        
        // 转换监控公司
        if (constraint.getOnlyMonitorCompanies() != null) {
            result.put("only_monitor_companies", constraint.getOnlyMonitorCompanies());
        }
        
        // 转换技术
        if (constraint.getOnlyMonitorTechs() != null) {
            result.put("techs", constraint.getOnlyMonitorTechs());
        }
        
        // 转换开始日期
        if (constraint.getStartDate() != null) {
            result.put("start_date", constraint.getStartDate());
        }
        
        return result;
    }

    /**
     * 生成排除关键词的查询语句
     * 在标题摘要和说明书B部分中排除指定关键词
     */
    private String generateExcludeQuery(List<String> excludeKeywords, Map<String, List<String>> expandKeywords, Map<String, Map<String, List<String>>> phraseSegments, String industry) {
        if (excludeKeywords == null || excludeKeywords.isEmpty()) {
            return null;
        }
        String query = generateQueryWithkeywords(excludeKeywords, expandKeywords, phraseSegments, industry, null, null);
        return "(" + String.format("ALL_TECH:(%s)", query) + ")";
    }

    private String generateQueryWithkeywords(List<String> keywords, Map<String, List<String>> expandKeywords, Map<String, Map<String, List<String>>> phraseSegments, String industry, String mainKeyword, String agentJobId) {
        return generateQueryWithkeywords(keywords, expandKeywords, phraseSegments, industry, mainKeyword, agentJobId, null);
    }

    /**
     * 根据关键词列表生成查询语句 和 新闻关键词
     * 处理关键词扩展和短语分段，生成合适的查询条件
     */
    private String generateQueryWithkeywords(List<String> keywords, Map<String, List<String>> expandKeywords, Map<String, Map<String, List<String>>> phraseSegments, String industry, String mainKeyword, String agentJobId, List<String> recommendTechSubfields) {
        if (keywords == null || keywords.isEmpty()) {
            return null;
        }
        checkMissExpand(keywords, expandKeywords, industry);

        List<String> queryArr = new ArrayList<>();

        // 创建关键词列表的副本，避免修改原始列表
        List<String> searchKeywords = new ArrayList<>();
        
        List<String> keywordInExpand = keywords.stream()
                .filter(kw -> expandKeywords.containsKey(kw))
                .toList();
        
        for (String keyword : keywordInExpand) {
            searchKeywords.addAll(expandKeywords.get(keyword));
        }

        checkMissPhrase(keywords, expandKeywords, phraseSegments, industry);
        for (String keyword : keywords) {  // 使用副本进行遍历
            if (phraseSegments.containsKey(keyword)) {
                Map<String, List<String>> segments = phraseSegments.get(keyword);
                String query = getSegmentsQuery(segments);
                if (query != null && !query.isEmpty()) {
                    queryArr.add(query);
                }
            } else {
                searchKeywords.add(keyword);
            }
        }

        searchKeywords = removeContainedValues(searchKeywords);

        if (!searchKeywords.isEmpty()) {
            searchKeywords = searchKeywords.stream().distinct().toList();
            queryArr.add("(\"" + String.join("\" OR \"", searchKeywords) + "\")");
            // 将keywords缓存，用于组成新闻检索式的关键词列表
            if (agentJobId != null && !StringUtil.isEmpty(mainKeyword)) {
                cacheNewsKeywords(agentJobId, searchKeywords);
            }
        } else {
            if (agentJobId != null && !StringUtil.isEmpty(mainKeyword)) {
                generateHotNewsKeywords(agentJobId, recommendTechSubfields, expandKeywords);
            }
        }

        if (queryArr.isEmpty()) {
            return null;
        }

        return "(" + String.join(" OR ", queryArr) + ")";
    }

    /**
     * 缓存新闻关键词
     *
     * @param agentJobId 任务ID
     * @param keywords 新闻关键词列表
     */
    private void cacheNewsKeywords(String agentJobId, List<String> keywords) {
        if (StringUtil.isNotBlank(agentJobId) && CollectionUtils.isNotEmpty(keywords)) {
            // 将searchKeywords中全是英文大写字母的元素移除，如：IBM
            keywords = keywords.stream().distinct().filter(word -> !word.matches("^[A-Z]+$")).collect(Collectors.toList());
            redissonUtils.set(redissonClient, ("tech_report_"+agentJobId) + ":news_keywords", keywords, 24, TimeUnit.HOURS);
            log.info("Cached news keywords for agentJobId: {}, keywords: {}", agentJobId, keywords);
        }
    }

    /**
     * 根据分段信息生成查询语句
     * 将每个分段的关键词组合成OR查询，多个分段之间用AND连接
     */
    private String getSegmentsQuery(Map<String, List<String>> segments) {
        if (segments == null || segments.isEmpty()) {
            return "";
        }
        List<String> segmentQuery = new ArrayList<>();
        for (Map.Entry<String, List<String>> entry : segments.entrySet()) {
            String segment = entry.getKey();
            List<String> searchKeywords = new ArrayList<>(entry.getValue());
            searchKeywords.add(segment);
            searchKeywords = removeContainedValues(searchKeywords);
            if (!searchKeywords.isEmpty()) {
                String queryStr = String.join("\" OR \"", searchKeywords);
                segmentQuery.add(String.format("(\"%s\")", queryStr));
            }
        }
        return "(" + String.join(" AND ", segmentQuery) + ")";
    }

    /**
     * 生成约束条件查询语句
     * 处理技术效果、技术问题、国家、监控公司、技术和日期等约束条件
     */
    private String generateConstraint(Map<String, Object> constraint, Map<String, List<String>> expandKeywords, Map<String, Map<String, List<String>>> phraseSegments, String industry) {
        if (constraint == null) {
            return null;
        }

        List<String> constraintQueries = new ArrayList<>();

        // 处理技术效果
        List<String> techEffect = (List<String>) constraint.get("tech_effect");
        if (techEffect != null) {
            for (String keyword : techEffect) {
                if (expandKeywords.get(keyword) == null) {
                    continue;
                }
                Map<String, List<String>> segments = phraseSegments.get(keyword);
                String searchKeywords = getSegmentsQuery(segments);
                if (!searchKeywords.isEmpty()) {
                    String query = String.format("ALL_TECH:(%s)", searchKeywords);
                    constraintQueries.add(query);
                }
            }
        }

        // 处理技术问题
        List<String> techProblem = (List<String>) constraint.get("tech_problem");
        if (techProblem != null) {
            for (String keyword : techProblem) {
                if (expandKeywords.get(keyword) == null) {
                    continue;
                }
                Map<String, List<String>> segments = phraseSegments.get(keyword);
                String searchKeywords = getSegmentsQuery(segments);
                if (!searchKeywords.isEmpty()) {
                    String query = String.format("ALL_TECH:(%s)", searchKeywords);
                    constraintQueries.add(String.format("(%s)", query));
                }
            }
        }

        // 处理国家
        List<String> country = (List<String>) constraint.get("country");
        if (country != null && !country.isEmpty()) {
            String countryQuery = "\"" + String.join("\" OR \"", country) + "\"";
            countryQuery = String.format("AUTHORITY:(%s)", countryQuery);
            constraintQueries.add(String.format("(%s)", countryQuery));
        }

        // 处理监控公司
        List<String> onlyMonitorCompanies = (List<String>) constraint.get("only_monitor_companies");
        if (onlyMonitorCompanies != null && !onlyMonitorCompanies.isEmpty()) {
            // 直接使用公司名称列表，移除包含关系的公司名
            List<String> companies = removeContainedValues(new ArrayList<>(onlyMonitorCompanies));
            constraintQueries.add(String.format("ALL_AN:(TREE@\"%s\")", String.join("\" OR \"", companies)));
        }

        // 处理技术
        List<String> techs = (List<String>) constraint.get("techs");
        if (techs != null && !techs.isEmpty()) {
            String searchKeywords = generateQueryWithkeywords(techs, expandKeywords, phraseSegments, industry, null, null);
            String query = String.format("(ALL_TECH:(%s))", searchKeywords);
            constraintQueries.add(query);
        }

        // 处理日期
        Map<String, Object> startDate = (Map<String, Object>) constraint.get("start_date");
        if (startDate != null) {
            String dateStr = calculateDateFromJson(startDate);
            if (dateStr != null) {
                String dateQuery = String.format("PBD:[%s TO *]", dateStr);
                constraintQueries.add(String.format("(%s)", dateQuery));
            }
        }

        if (!constraintQueries.isEmpty()) {
            return String.join(" AND ", constraintQueries);
        }
        return null;
    }

    /**
     * 根据时间信息计算目标日期
     * 支持按天、周、月、季度、年进行日期计算
     */
    private String calculateDateFromJson(Map<String, Object> timeInfo) {
        if (timeInfo == null) {
            return null;
        }

        String deltaType = (String) timeInfo.get("type");
        Integer detail = (Integer) timeInfo.get("detail");
        if (detail == null) {
            detail = 0;
        }

        LocalDate now = LocalDate.now();
        LocalDate targetDate;

        switch (deltaType) {
            case "day":
                targetDate = now.plusDays(detail);
                break;
            case "week":
                targetDate = now.plusWeeks(detail);
                break;
            case "month":
                targetDate = now.plusMonths(detail);
                break;
            case "quarter":
                targetDate = now.plusMonths(detail * 3);
                break;
            case "year":
                targetDate = now.plusYears(detail);
                break;
            default:
                throw new IllegalArgumentException("不支持的类型: " + deltaType);
        }

        return targetDate.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
    }

    /**
     * 生成公司子查询语句
     * 为每个公司生成专利权人查询条件
     */
    private Map<String, String> generateSubcompanyQuery(Map<String, List<String>> extractCompanies, Map<String, List<String>> expandKeywords, String agentJobId, List<String> recommendTechSubfields) {
        if (extractCompanies == null || extractCompanies.isEmpty()) {
            return null;
        }
        Map<String, String> subcompanyQueries = new HashMap<>();
        for (Map.Entry<String, List<String>> entry : extractCompanies.entrySet()) {
            subcompanyQueries.put(entry.getKey(), 
                String.format("ALL_AN:(TREE@\"%s\")", String.join("\" OR \"", entry.getValue())));
        }
        generateHotNewsKeywords(agentJobId, recommendTechSubfields, expandKeywords);
        return subcompanyQueries;
    }

    /**
     * 生成热点新闻关键词
     *
     * @param agentJobId job ID
     * @param recommendTechSubfields 推荐技术子领域列表
     * @param expandKeywords 扩展关键词映射表，键为扩展关键词的键，值为对应的关键词列表
     */
    private void generateHotNewsKeywords(String agentJobId, List<String> recommendTechSubfields, Map<String, List<String>> expandKeywords) {
        List<String> newsKeywords = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(recommendTechSubfields)) {
            newsKeywords.addAll(recommendTechSubfields);
        }
        for (String key : expandKeywords.keySet()) {
            newsKeywords.addAll(expandKeywords.get(key));
        }
        cacheNewsKeywords(agentJobId, newsKeywords);
    }

    /**
     * 生成技术领域子查询语句
     * 根据不同意图生成相应的技术领域查询条件
     */
    private Map<String, String> generateSubtechQuery(String intent, List<String> recommendTechSubfields, Map<String, List<String>> expandKeywords, Map<String, Map<String, List<String>>> phraseSegments, String industry,TechReportExtractTechFeature techReportExtractTechFeature) {
        if (recommendTechSubfields == null || recommendTechSubfields.isEmpty()) {
            return new HashMap<>();
        }
        Map<String, String> subFieldQuery = new HashMap<>();
        String applicationQuery = generateApplicationSubclass(convertFeatureToMap(techReportExtractTechFeature));
        if (!Arrays.asList("TechDescription", "TechProblem").contains(intent) && !recommendTechSubfields.isEmpty()) {
            for (String fieldName : recommendTechSubfields) {
                String searchKeywords = generateQueryWithkeywords(Collections.singletonList(fieldName), expandKeywords, phraseSegments, industry, null, null);
                if (applicationQuery != null && !applicationQuery.isEmpty()) {
                    searchKeywords = String.format("ALL_TECH:(%s) AND %s", searchKeywords, applicationQuery);
                }
                subFieldQuery.put(fieldName, searchKeywords);
            }
        } else if (Arrays.asList("TechDescription", "TechProblem").contains(intent) && !recommendTechSubfields.isEmpty()) {
            List<String> keywords = new ArrayList<>();
            for (String subField : recommendTechSubfields) {
                keywords.add(subField);
                // 注意：由于参数类型改变，这里无法再获取keyFeatures，如果需要可以考虑其他方式
            }
            checkMissPhrase(keywords, expandKeywords, phraseSegments, industry);
            for (String subField : recommendTechSubfields) {
                List<String> queries = new ArrayList<>();

                if (subField != null && !subField.isEmpty()) {
                    String searchKeywords = generateQueryWithkeywords(Collections.singletonList(subField), expandKeywords, phraseSegments, industry, null, null);
                    queries.add(String.format("(ALL_TECH:(%s))", searchKeywords));
                }

                String query = "(" + String.join(" AND ", queries) + ")";
                if (applicationQuery != null && !applicationQuery.isEmpty()) {
                    query = String.format("%s AND %s", query, applicationQuery);
                }
                subFieldQuery.put(subField, query);
            }
        }

        return subFieldQuery;
    }

    /**
     * 生成应用子类查询语句 - 公共接口
     * 接受TechReportExtractTechFeature参数
     */
    public String generateApplicationSubclassFromFeature(TechReportExtractTechFeature techReportExtractTechFeature) {
        return generateApplicationSubclass(convertFeatureToMap(techReportExtractTechFeature));
    }

    /**
     * 生成应用子类查询语句
     * 处理IPC/CPC分类号查询
     */
    public String generateApplicationSubclass(Map<String, Object> features) {
        // 当features为null时，直接返回null，不阻止程序继续执行
        if (features == null) {
            log.debug("Features map is null in generateApplicationSubclass, skipping subclass query generation");
            return null;
        }
        
        List<String> queries = new ArrayList<>();

        // 处理子类
        Map<String, Object> subclass = (Map<String, Object>) features.get("Subclass");
        if (subclass != null && !subclass.isEmpty()) {
            String subclassStr = "\"" + String.join("\" OR \"", subclass.keySet()) + "\"";
            String subclassQuery = String.format("IPC:(%s) OR CPC:(%s)",
                subclassStr, subclassStr);
            queries.add(subclassQuery);
        }

        if (!queries.isEmpty()) {
            return "(" + String.join(" OR ", queries) + ")";
        }
        return null;
    }

    /**
     * 检查并处理缺失的短语
     * 对未处理的关键词进行短语分段和扩展
     */
    private void checkMissPhrase(List<String> keywords, Map<String, List<String>> expandKeywords, Map<String, Map<String, List<String>>> phraseSegments, String industry) {
        if (keywords == null || keywords.isEmpty()) {
            return;
        }

        List<String> missKeywords = keywords.stream()
                .filter(kw -> !phraseSegments.containsKey(kw))
                .toList();

        if (!missKeywords.isEmpty()) {
            Map<String, Map<String, List<String>>> phraseSegmentsResult = 
                phraseSegment.run(missKeywords, industry);
            
            if (phraseSegmentsResult != null) {
                for (Map.Entry<String, Map<String, List<String>>> entry : phraseSegmentsResult.entrySet()) {
                    String phrase = entry.getKey();
                    Map<String, List<String>> keywords2 = entry.getValue();

                    phraseSegments.put(phrase, keywords2);
                    if (keywords2.size() == 1 && keywords2.containsKey(phrase)) {
                        expandKeywords.put(phrase, keywords2.get(phrase));
                    }
                }
            }
        }
    }

    /**
     * 移除包含了其他内容的项
     * 如果一个较长的字符串包含了另一个较短的字符串，则移除较长的字符串
     * 分别处理英文和亚洲文字（中文、韩文、日文）
     */
    private List<String> removeContainedValues(List<String> arr) {
        if (arr == null || arr.isEmpty()) {
            return arr;
        }

        // 先去重，转换为 LinkedHashSet 保持顺序
        Set<String> uniqueSet = new LinkedHashSet<>(arr);
        
        // 分别存储英文和亚洲文字
        List<String> englishWords = new ArrayList<>();
        List<String> asianWords = new ArrayList<>();
        
        // 分类处理
        for (String word : uniqueSet) {
            if (isEnglish(word)) {
                englishWords.add(word);
            } else if (isAsian(word)) {
                asianWords.add(word);
            }
        }

        // 处理英文词
        Set<String> toRemoveEnglish = new HashSet<>();
        for (int i = 0; i < englishWords.size(); i++) {
            String current = englishWords.get(i);
            for (int j = 0; j < englishWords.size(); j++) {
                if (i != j) {
                    String other = englishWords.get(j);
                    if (other.contains(current.trim()) && other.length() > current.length()) {
                        toRemoveEnglish.add(other);
                    }
                }
            }
        }

        // 处理亚洲文字
        Set<String> toRemoveAsian = new HashSet<>();
        for (int i = 0; i < asianWords.size(); i++) {
            String current = asianWords.get(i);
            for (int j = 0; j < asianWords.size(); j++) {
                if (i != j) {
                    String other = asianWords.get(j);
                    if (other.contains(current) && other.length() > current.length()) {
                        toRemoveAsian.add(other);
                    }
                }
            }
        }

        // 合并结果
        uniqueSet.removeAll(toRemoveEnglish);
        uniqueSet.removeAll(toRemoveAsian);
        
        return new ArrayList<>(uniqueSet);
    }

    /**
     * 判断字符串是否是纯英文或英文短语
     */
    private boolean isEnglish(String s) {
        return s != null && s.matches("[a-zA-Z\\s]+");
    }

    /**
     * 判断字符串是否是纯中文、韩文或日文
     */
    private boolean isAsian(String s) {
        return s != null && s.matches("[\\u4e00-\\u9fa5\\uac00-\\ud7af\\u3040-\\u30ff]+");
    }

    /**
     * 将技术特征对象转换为Map格式
     * 包含关键词、类别、子术语、父关键词等信息
     */
    private Map<String, Object> convertFeatureToMap(TechReportExtractTechFeature feature) {
        if (feature == null) {
            return null;
        }
        Map<String, Object> result = new HashMap<>();
        result.put("keyword", feature.getKeyword());
        result.put("keyword_category", feature.getKeywordCategory());
        result.put("child_term", feature.getSubtypes());
        result.put("parent_keywords", feature.getParentKeywords());
        result.put("uniqueTechFeatures", feature.getUniqueTechFeatures());
        result.put("Subclass", feature.getSubclass());
        return result;
    }

    /**
     * 生成排除关键词的查询语句
     * 根据TechReportExcludedKeywordDTO生成查询语句
     */
    private String generateExcludeQueryFromDTO(TechReportExcludedKeywordDTO excludedKeyword, Map<String, List<String>> expandKeywords, Map<String, Map<String, List<String>>> phraseSegments, String industry) {
        if (excludedKeyword == null) {
            return null;
        }
        
        List<String> allExcludeKeywords = new ArrayList<>();
        
        // 收集排除的技术关键词
        if (excludedKeyword.getExcludeTechs() != null) {
            allExcludeKeywords.addAll(excludedKeyword.getExcludeTechs());
        }
        
        // 收集排除的国家（这里仅处理技术关键词，国家排除在其他地方处理）
        // 收集排除的公司（这里仅处理技术关键词，公司排除在其他地方处理）
        
        if (allExcludeKeywords.isEmpty()) {
            return null;
        }
        
        return generateExcludeQuery(allExcludeKeywords, expandKeywords, phraseSegments, industry);
    }

    /**
     * 生成纯技术关键词查询语句
     * 只包含主关键词、子术语、父关键词和独特技术特征，不包含应用场景和分类号
     */
    private String generateTechKeywordQuery(Map<String, Object> features, List<String> recommendTechSubfields, Map<String, List<String>> expandKeywords, Map<String, Map<String, List<String>>> phraseSegments, String industry, String agentJobId) {
        if (features == null) {
            log.warn("Features map is null");
            return "";
        }

        try {
            List<String> keywords = new ArrayList<>();
            String mainKeyword = (String) features.get("keyword");
            if (mainKeyword != null) {
                keywords.add(mainKeyword);
            }

            // 安全地添加 child_term
            Object childTermObj = features.get("child_term");
            if (childTermObj instanceof List<?>) {
                List<?> childTerms = (List<?>) childTermObj;
                for (Object term : childTerms) {
                    if (term instanceof String) {
                        keywords.add((String) term);
                    }
                }
            }

            String searchKeywordStr = generateQueryWithkeywords(keywords, expandKeywords, phraseSegments, industry, mainKeyword, agentJobId);
            String topicQuery = String.format("(ALL_TECH:(%s))", searchKeywordStr);

            // 先获取uniqueTechFeatures，判断是否存在
            Object uniqueTechFeaturesObj = features.get("uniqueTechFeatures");
            boolean hasUniqueTechFeatures = uniqueTechFeaturesObj instanceof List<?> && !((List<?>)uniqueTechFeaturesObj).isEmpty();
            
            // 判断hasUniqueTechFeatures为true时
            if (hasUniqueTechFeatures && childTermObj != null && childTermObj instanceof List<?> && ((List<?>)childTermObj).size() <= 6) {
                // 安全地添加 parent_keywords
                Object parentKeywordsObj = features.get("parent_keywords");
                List<String> parentKeywordsList = new ArrayList<>();
                if (parentKeywordsObj instanceof List<?>) {
                    List<?> parentKeywords = (List<?>) parentKeywordsObj;
                    for (Object kw : parentKeywords) {
                        if (kw instanceof String) {
                            parentKeywordsList.add((String) kw);
                        }
                    }
                }
                
                // 安全地添加 uniqueTechFeatures
                List<String> uniqueFeaturesList = new ArrayList<>();
                if (uniqueTechFeaturesObj instanceof List<?>) {
                    List<?> uniqueFeatures = (List<?>) uniqueTechFeaturesObj;
                    for (Object feature : uniqueFeatures) {
                        if (feature instanceof String) {
                            uniqueFeaturesList.add((String) feature);
                        }
                    }
                }
                
                // 生成string A（parentKeywords查询）
                String parentKeywordsString = "";
                if (!parentKeywordsList.isEmpty()) {
                    parentKeywordsString = generateQueryWithkeywords(parentKeywordsList, expandKeywords, phraseSegments, industry, null, null);
                }
                
                // 生成string B（uniqueFeatures查询）
                String uniqueFeaturesString = "";
                if (!uniqueFeaturesList.isEmpty()) {
                    uniqueFeaturesString = generateQueryWithkeywords(uniqueFeaturesList, expandKeywords, phraseSegments, industry, null, null);
                }
                
                // 最终topicQuery赋值为：topicQuery OR (parentKeywordsString AND uniqueFeaturesString)
                if (!parentKeywordsString.isEmpty() && !uniqueFeaturesString.isEmpty()) {
                    String combinedQuery = String.format("(ALL_TECH:(%s) AND ALL_TECH:(%s))", parentKeywordsString, uniqueFeaturesString);
                    topicQuery = String.format("(%s OR %s)", topicQuery, combinedQuery);
                } else if (!parentKeywordsString.isEmpty()) {
                    // 如果只有A，则添加A
                    String queryA = String.format("(ALL_TECH:(%s))", parentKeywordsString);
                    topicQuery = String.format("(%s OR %s)", topicQuery, queryA);
                } else if (!uniqueFeaturesString.isEmpty()) {
                    // 如果只有B，则添加B
                    String queryB = String.format("(ALL_TECH:(%s))", uniqueFeaturesString);
                    topicQuery = String.format("(%s OR %s)", topicQuery, queryB);
                }
            }
            return topicQuery;
        } catch (Exception e) {
            log.error("Error in generateTechKeywordQuery with features: {}", features, e);
            return "";
        }
    }

    /**
     * 检查并扩展缺失的关键词
     * 对于不在expandKeywords中的关键词，调用ExpandKeywords服务进行扩展
     * 使用prompt key: EXPAND_KEYWORDS
     *
     * @param keywords 需要检查的关键词列表
     * @param expandKeywords 已扩展的关键词映射
     * @param industry 行业领域
     */
    private void checkMissExpand(List<String> keywords, Map<String, List<String>> expandKeywords, String industry) {
        if (keywords == null || keywords.isEmpty()) {
            return;
        }

        // 找出不在expandKeywords中的关键词
        List<String> missKeywords = keywords.stream()
                .filter(kw -> !expandKeywords.containsKey(kw))
                .toList();

        if (!missKeywords.isEmpty()) {
            try {
                // 调用ExpandKeywords服务进行关键词扩展
                // 构建参数Map
                Map<String, String> params = new HashMap<>();
                params.put("industry", industry != null ? industry : "");
                params.put("keywords", String.join(",", missKeywords));
                
                // 构建prompt并调用AI服务
                String prompt = openAiClient.buildPromptByPlatform(EXPAND_KEYWORDS.getValue(), params);
                int retryCount = 0;
                Map<String, List<String>> expandedKeywords = new HashMap<>();
                while (retryCount < 3) {
                    expandedKeywords = openAiClient.callGptByPrompt(
                            GPTModelEnum.GPT_MODEL_4_O,
                            prompt,
                            TECH_REPORT_SCENARIO,
                            Map.class);
                    if (expandedKeywords != null) {
                        break;
                    }
                    retryCount++;
                    log.error("调用AI服务进行关键词扩展失败，正在重试... 重试次数: {}", retryCount);
                }

                log.info("llm结果 ‘EXPAND_KEYWORDS’：{}", expandedKeywords);

                // 如果扩展结果不为空，更新expandKeywords映射
                if (expandedKeywords != null && !expandedKeywords.isEmpty()) {
                    expandKeywords.putAll(expandedKeywords);
                    log.info("成功扩展关键词，原始关键词数量: {}, 扩展后关键词数量: {}", 
                            missKeywords.size(), expandedKeywords.size());
                }
            } catch (Exception e) {
                log.error("扩展关键词时发生错误，关键词: {}, 行业: {}", missKeywords, industry, e);
            }
        }
    }


} 
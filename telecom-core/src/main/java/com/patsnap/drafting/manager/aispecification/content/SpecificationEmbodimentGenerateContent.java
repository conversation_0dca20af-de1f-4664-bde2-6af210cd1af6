package com.patsnap.drafting.manager.aispecification.content;

import com.patsnap.core.common.copilot.constant.GPTStatus;
import com.patsnap.core.common.copilot.gpt.enums.GPTModelEnum;
import com.patsnap.core.common.copilot.streaming.handling.StreamingChatLanguageModel;
import com.patsnap.core.common.copilot.streaming.model.GptResponseDTO;
import com.patsnap.drafting.client.ComputeClient;
import com.patsnap.drafting.client.model.AiSpecificationComputeData;
import com.patsnap.drafting.client.model.AiSpecificationComputeReqDTO;
import com.patsnap.drafting.config.UrlConfig;
import com.patsnap.drafting.enums.aispecification.JurisdictionEnum;
import com.patsnap.drafting.enums.common.OperateTypeEnum;
import com.patsnap.drafting.enums.prompt.ScenarioEnum;
import com.patsnap.drafting.enums.task.AiTaskContentTypeEnum;
import com.patsnap.drafting.handler.specification.SpecificationEmbodimentStringStreamingResponseHandler;
import com.patsnap.drafting.manager.AbstractGenerateStreamingContent;
import com.patsnap.drafting.manager.aitask.AiTaskManager;
import com.patsnap.drafting.model.aispecification.SpecificationDisclosureExtractEmbodimentBO;
import com.patsnap.drafting.model.aispecification.SpecificationEmbodimentGenerateBO;
import com.patsnap.drafting.model.aispecification.SpecificationEmbodimentOutlineBO;
import com.patsnap.drafting.model.aispecification.SpecificationInitializationBO;
import com.patsnap.drafting.model.base.StreamingModelBO;
import com.patsnap.drafting.request.GenerateContentRequestDTO;
import com.patsnap.drafting.request.aispecification.DisclosureEmbodimentItem;
import com.patsnap.drafting.request.aispecification.EmbodimentGenerateItem;
import com.patsnap.drafting.request.aispecification.EmbodimentGenerateReqDTO;
import com.patsnap.drafting.request.aispecification.EmbodimentOutlineItem;
import com.patsnap.drafting.response.aispecification.SpecificationRdResDTO;
import com.patsnap.drafting.util.ExportUtils;
import com.patsnap.spring.boot.autoconfigure.web.entity.CommonResponse;
import jodd.util.StringUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import static com.patsnap.drafting.enums.task.AiTaskContentTypeEnum.FIGURES_PRE_INFO;

@Component
public class SpecificationEmbodimentGenerateContent extends AbstractGenerateStreamingContent<String> {

    @Autowired
    private ComputeClient computeClient;

    // 算法接口的功能函数key
    // https://confluence.zhihuiya.com/pages/viewpage.action?pageId=230993512
    private static final String GENERATE_FUNCTION = "generate_embodiment_from_outline";

    protected SpecificationEmbodimentGenerateContent(AiTaskManager aiTaskManager, UrlConfig urlConfig) {
        super(aiTaskManager, urlConfig);
    }

    @Override
    public String getContentType() {
        return AiTaskContentTypeEnum.SPECIFICATION_EMBODIMENT_CONTENT.getType();
    }

    @Override
    protected StreamingModelBO getStreamingModelBO(GenerateContentRequestDTO generateContentRequestDTO) {
        // 组装生成单个实施例所需要的参数
        EmbodimentGenerateReqDTO request = (EmbodimentGenerateReqDTO) generateContentRequestDTO;
        String taskId = generateContentRequestDTO.getTaskId();
        Integer embodimentNumber = request.getEmbodimentNumber();
        // 如果是generate,就从数据库捞数据，如果是regenerate,就从调用算法接口得到结果
        String operateType = generateContentRequestDTO.getOperateType();
        StreamingModelBO streamingModelBO = new StreamingModelBO();

        if (OperateTypeEnum.REGENERATE.name().equalsIgnoreCase(operateType)) {
            String claimText;
            String type;
            Integer referenceEmbodimentNumber;

            // 查询实施例大纲数据，一些字段来自大纲,如果取不到对应的大纲，那使用前端传递的值
            SpecificationEmbodimentOutlineBO outlineBO = aiTaskManager.getTaskContent(taskId, AiTaskContentTypeEnum.SPECIFICATION_EMBODIMENT_OUTLINE);
            EmbodimentOutlineItem outlineItem = outlineBO.getOutlines().stream()
                    .filter(item -> Objects.equals(item.getEmbodimentNumber(), embodimentNumber))
                    .findFirst().orElse(null);
            if (Objects.nonNull(outlineItem)) {
                claimText = outlineItem.getClaimText();
                type = outlineItem.getType();
                referenceEmbodimentNumber = outlineItem.getReferenceEmbodimentNumber();
            } else {
                claimText = request.getClaimText();
                type = request.getType();
                referenceEmbodimentNumber = request.getReferenceEmbodimentNumber();
            }
            SpecificationInitializationBO initializationBO = aiTaskManager.getTaskContent(generateContentRequestDTO.getTaskId(), AiTaskContentTypeEnum.INITIALIZATION);
            String jurisdiction = initializationBO.getJurisdiction();
            String lang = StringUtil.toLowerCase(JurisdictionEnum.fromName(jurisdiction).getValue());
            AiSpecificationComputeData computeData = new AiSpecificationComputeData();
            computeData.setClaimText(claimText);
            computeData.setPatentType(type);
            // 算法接口已经不需要我们传递了
            // computeData.setOutline(outlineItem.getOutline());
            computeData.setEmbodimentNumber(embodimentNumber);
            String figuresPreInfo = aiTaskManager.getTaskContent(taskId, FIGURES_PRE_INFO);
            computeData.setDrawingNarrative(Optional.ofNullable(figuresPreInfo).orElseGet(() -> Strings.EMPTY));
            // 需要根据大纲里面的reference_embodiment_number来找到之前已经生成好的实施例的text字段信息，作为本实施例的参考信息
            if (Objects.nonNull(referenceEmbodimentNumber)) {
                // 只有CNIPA需要在调用算法接口生成实施例时，把被引用的实施例的标题也加上去
                String referenceEmbodimentTitle = org.apache.commons.lang3.StringUtils.equals(JurisdictionEnum.CNIPA.name(), jurisdiction) ? "实施例" + ExportUtils.convertNumberToChinese(referenceEmbodimentNumber) + "\n\n" : "";
                SpecificationEmbodimentGenerateBO embodimentGenerateBO = aiTaskManager.getTaskContent(taskId, AiTaskContentTypeEnum.SPECIFICATION_EMBODIMENT_CONTENT);
                Integer finalReferenceEmbodimentNumber = referenceEmbodimentNumber;
                Optional<EmbodimentGenerateItem> referenceEmbodiment = embodimentGenerateBO.getEmbodimentGenerateItems().stream()
                        .filter(item -> Objects.equals(item.getEmbodimentNumber(), finalReferenceEmbodimentNumber))
                        .findFirst();
                referenceEmbodiment.ifPresent(embodimentGenerateItem -> computeData.setReferenceEmbodimentText(referenceEmbodimentTitle + embodimentGenerateItem.getText()));
            }
            // 查新和设置交底书实施例数据(用户选中的)，传递给算法接口，参与报告生成
            SpecificationDisclosureExtractEmbodimentBO disclosureEmbodimentBO = aiTaskManager.getTaskContent(taskId, AiTaskContentTypeEnum.SPECIFICATION_EMBODIMENT_EXTRACT);
            if (Objects.nonNull(disclosureEmbodimentBO) && CollectionUtils.isNotEmpty(disclosureEmbodimentBO.getEmbodiments())) {
                List<DisclosureEmbodimentItem> disclosureEmbodiments = disclosureEmbodimentBO.getEmbodiments().stream().filter(DisclosureEmbodimentItem::getSelected).toList();
                if (CollectionUtils.isNotEmpty(disclosureEmbodiments)) {
                    // 内部的number字段还需要设置一下,循环遍历设置，就取集合下标即可
                    for (int i = 0; i < disclosureEmbodiments.size(); i++) {
                        disclosureEmbodiments.get(i).setNumber(i + 1);
                    }
                    computeData.setDisclosureEmbodiments(disclosureEmbodiments);
                }
            }

            //get prompt
            AiSpecificationComputeReqDTO aiSpecificationComputeReqDTO = AiSpecificationComputeReqDTO.builder().function(GENERATE_FUNCTION).data(computeData).lang(lang).build();
            SpecificationRdResDTO specificationPrompt = computeClient.getSpecificationPrompt(aiSpecificationComputeReqDTO);
            streamingModelBO.setPrompt(specificationPrompt.getPrompt());
            StreamingChatLanguageModel model = streamingModelBuilder.build(
                    GPTModelEnum.getGptModelEnum(specificationPrompt.getModelName()),
                    ScenarioEnum.AI_SPECIFICATION.getValue(), 0.6, 16000);
            streamingModelBO.setModel(model);
        } else {
            streamingModelBO.setPrompt("");
            Map<String, Object> extraData = new HashMap<>();
            extraData.put("task_id", taskId);
            extraData.put("embodiment_number", embodimentNumber);
            streamingModelBO.setExtraData(extraData);
        }

        return streamingModelBO;
    }

    @Override
    protected Flux<CommonResponse<GptResponseDTO<String>>> getContent(
            StreamingModelBO streamingModelBO) {
        // 如果prompt为空，则代表需要从库里捞取数据
        String prompt = streamingModelBO.getPrompt();
        if (StringUtils.isNotBlank(prompt)) {
            return Flux.create(sink -> {
                SpecificationEmbodimentStringStreamingResponseHandler handler = new SpecificationEmbodimentStringStreamingResponseHandler(sink);
                streamingModelBO.getModel().generate(prompt, handler);
            });
        } else {
            Map<String, Object> extraData = streamingModelBO.getExtraData();
            String taskId = (String) extraData.get("task_id");
            Integer embodimentNumber = (Integer) extraData.get("embodiment_number");

            SpecificationEmbodimentGenerateBO embodimentGenerateBO = aiTaskManager.getTaskContent(taskId, AiTaskContentTypeEnum.SPECIFICATION_EMBODIMENT_CONTENT);
            if (Objects.isNull(embodimentGenerateBO) || CollectionUtils.isEmpty(embodimentGenerateBO.getEmbodimentGenerateItems())) {
                return Flux.empty();
            }

            Optional<EmbodimentGenerateItem> existingItemOpt = embodimentGenerateBO.getEmbodimentGenerateItems().stream()
                    .filter(item -> Objects.equals(item.getEmbodimentNumber(), embodimentNumber))
                    .findFirst();
            if (existingItemOpt.isPresent()) {
                EmbodimentGenerateItem existingItem = existingItemOpt.get();
                GptResponseDTO<String> gptResponse = GptResponseDTO.<String>builder()
                        .content(existingItem.getText())
                        .status(GPTStatus.FINISH)
                        .build();
                CommonResponse<GptResponseDTO<String>> response = CommonResponse.<GptResponseDTO<String>>builder()
                        .withData(gptResponse)
                        .withStatus(true)
                        .build();
                return Flux.just(response);
            } else {
                return Flux.empty();
            }
        }
    }

    @Override
    public Flux<CommonResponse<GptResponseDTO<String>>> doGenerate(GenerateContentRequestDTO request) {
        return getContent(getStreamingModelBO(request));
    }
}

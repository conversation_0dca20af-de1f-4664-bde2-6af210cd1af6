package com.patsnap.drafting.manager.aitranslation.operate.impl;


import com.patsnap.core.common.copilot.streaming.StreamingModelBuilder;
import com.patsnap.drafting.client.OpenAiClient;
import com.patsnap.drafting.config.AiTranslationConfig;
import com.patsnap.drafting.config.UrlConfig;

import org.springframework.stereotype.Component;

@Component
public class TranslationBasicImpl {

    protected OpenAiClient openAiClient;

    protected AiTranslationConfig aiTranslationConfig;

    protected UrlConfig urlConfig;

    protected StreamingModelBuilder streamingModelBuilder;

    public TranslationBasicImpl(UrlConfig urlConfig, AiTranslationConfig aiTranslationConfig,
            OpenAiClient openAiClient) {
        this.urlConfig = urlConfig;
        this.aiTranslationConfig = aiTranslationConfig;
        this.openAiClient = openAiClient;
        this.streamingModelBuilder = new StreamingModelBuilder(urlConfig.getGptBaseUrl(),
                urlConfig.getGptApiKey());
    }

}

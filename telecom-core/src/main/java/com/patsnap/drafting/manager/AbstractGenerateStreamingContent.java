package com.patsnap.drafting.manager;

import com.patsnap.core.common.copilot.streaming.StreamingModelBuilder;
import com.patsnap.core.common.copilot.streaming.model.GptResponseDTO;
import com.patsnap.drafting.annotation.TaskContentCache;
import com.patsnap.drafting.config.UrlConfig;
import com.patsnap.drafting.enums.common.OperateTypeEnum;
import com.patsnap.drafting.manager.aitask.AiTaskManager;
import com.patsnap.drafting.model.base.StreamingModelBO;
import com.patsnap.drafting.request.GenerateContentRequestDTO;
import com.patsnap.spring.boot.autoconfigure.web.entity.CommonResponse;


import com.google.common.collect.Lists;

import reactor.core.publisher.Flux;

/**
 * 流式输出处理
 * @param <T>
 */
public abstract class AbstractGenerateStreamingContent<T> {

    protected final AiTaskManager aiTaskManager;
    protected StreamingModelBuilder streamingModelBuilder;

    protected AbstractGenerateStreamingContent(AiTaskManager aiTaskManager, UrlConfig urlConfig) {
        this.aiTaskManager = aiTaskManager;
        this.streamingModelBuilder = new StreamingModelBuilder(urlConfig.getGptBaseUrl(),
                urlConfig.getGptApiKey());

    }


    protected abstract StreamingModelBO getStreamingModelBO(GenerateContentRequestDTO generateContentRequestDTO);


    protected abstract Flux<CommonResponse<GptResponseDTO<T>>> getContent(StreamingModelBO streamingModelBO);


    @TaskContentCache(contentTypeExpression = "#request.contentType")
    public Flux<CommonResponse<GptResponseDTO<T>>> doGenerate(GenerateContentRequestDTO request) {
        beforeGetContent(request);
        StreamingModelBO streamingModelBO = getStreamingModelBO(request);
        return getContent(streamingModelBO);
    }

    /**
     * 获取操作的内容类型
     * @return
     */
    public abstract String getContentType();

    protected void beforeGetContent(GenerateContentRequestDTO request) {
        //如果是重新生成的内容需要清空当前的内容 - 部分场景下可能清除整个step的内容，由子类去实现
        if (OperateTypeEnum.REGENERATE.getValue().equals(request.getOperateType())) {
            aiTaskManager.deleteTaskContentByContentType(request.getTaskId(),
                    Lists.newArrayList(request.getContentType()));
        }
    }


}

package com.patsnap.drafting.manager.aitranslation.handler;

import com.patsnap.common.request.CorrelationIdHolder;
import com.patsnap.core.common.copilot.constant.GPTStatus;
import com.patsnap.core.common.copilot.streaming.handler.SplitListJsonOutputParser;
import com.patsnap.core.common.copilot.streaming.handler.SplitListJsonStreamingResponseHandler;
import com.patsnap.core.common.copilot.streaming.model.GptResponseDTO;
import com.patsnap.drafting.constants.Constant;
import com.patsnap.drafting.manager.aitranslation.operate.TermContextUtil;
import com.patsnap.drafting.manager.aitranslation.operate.TranslationConstant;
import com.patsnap.drafting.model.aitranslation.TranslationBO;
import com.patsnap.drafting.util.ParagraphUtils;
import com.patsnap.spring.boot.autoconfigure.web.entity.CommonResponse;

import static com.patsnap.drafting.manager.aitranslation.operate.TranslationConstant.FIELD_SEPARATOR_REGEX;
import static com.patsnap.drafting.manager.aitranslation.operate.TranslationConstant.LINE_SEPARATOR_REGEX;

import java.util.List;
import java.util.Map;
import java.util.Stack;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;

import com.fasterxml.jackson.core.type.TypeReference;

import cn.hutool.core.util.StrUtil;

import com.patsnap.core.common.copilot.streaming.chat.AssistantMessage;
import com.patsnap.core.common.copilot.streaming.response.Response;

import reactor.core.publisher.FluxSink;

/**
 * 自研模型AI全文翻译的流式输出处理器
 *
 * <AUTHOR>
 */
public class AiTranslationGptStreamingResponseHandler extends
        SplitListJsonStreamingResponseHandler<List<TranslationBO>> {

    private final Map<String, String> terms;
    private final String targetLang;
    private final String sourceLang;
    private final String correlationId;
    private final List<Integer> paragraphIndexes;

    public AiTranslationGptStreamingResponseHandler(
            FluxSink<CommonResponse<GptResponseDTO<List<TranslationBO>>>> sink,
            String input, Map<String, String> terms, String targetLang, String sourceLang,
            List<Integer> paragraphIndexes) {
        super(sink, LINE_SEPARATOR_REGEX, FIELD_SEPARATOR_REGEX, new TypeReference<>() {
        });
        this.terms = terms;
        this.targetLang = targetLang;
        this.sourceLang = sourceLang;
        this.paragraphIndexes = paragraphIndexes;
        this.correlationId = CorrelationIdHolder.get();
    }

    @Override
    public void onError(Throwable error) {
        CorrelationIdHolder.set(correlationId);
        super.onError(error);
    }

    @Override
    public void onComplete(Response<AssistantMessage> response) {
        CorrelationIdHolder.set(correlationId);
        super.onComplete(response);
    }

    @Override
    protected void doAfterComplete(String content) {
    }

    @Override
    protected List<TranslationBO> convertContent(String content) {
        return covertContent(content);
    }

    @Override
    protected boolean outputValidate(String content) {
        return TermContextUtil.areTagsBalanced(content);
    }

    public @Nullable List<TranslationBO> covertContent(String content) {
        List<TranslationBO> sentenceTrans = new SplitListJsonOutputParser<List<TranslationBO>>(LINE_SEPARATOR_REGEX,
                FIELD_SEPARATOR_REGEX).convertContent(content, new TypeReference<>() {
        });
        // 根据标识符计算每个句子所属的段落index
        ParagraphUtils.addParagraphIndexPlus(sentenceTrans, paragraphIndexes);
        if (CollectionUtils.isEmpty(sentenceTrans)) {
            return sentenceTrans;
        }
        // 移除翻译结果中的段落标识符
        sentenceTrans.forEach(t -> {
            String text = handleText(t.getTranslatedText(), targetLang);
            String srcText = handleText(t.getSrcText(), sourceLang);
            t.setSrcText(srcText);
            t.setTranslatedText(text);
        });

        return sentenceTrans;
    }

    private String handleText(String text, String lang) {
        if (StrUtil.isEmpty(text)) {
            return text;
        }
        for (Map.Entry<String, String> entry : terms.entrySet()) {
            text = TermContextUtil.getTermReplace(text, lang, entry);
        }
        text = text.replace("|||", "");
        return text;
    }
}

package com.patsnap.drafting.manager.aitranslation.operate;

import com.patsnap.core.common.copilot.streaming.model.GptResponseDTO;
import com.patsnap.drafting.model.aitranslation.AiTransContextBo;
import com.patsnap.drafting.model.aitranslation.TranslationBO;
import com.patsnap.drafting.request.aitask.AiTaskReqDTO;
import com.patsnap.spring.boot.autoconfigure.web.entity.CommonResponse;

import java.util.List;
import java.util.Map;

import reactor.core.publisher.Flux;

public interface TranslationRewriteService {

    String model();

    Flux<CommonResponse<GptResponseDTO<String>>> generate(AiTransContextBo contextBo, int index,
            String original);
}

package com.patsnap.drafting.manager.aitask;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.patsnap.analytics.infrastructure.utils.WordCountUtil;
import com.patsnap.common.request.RequestFromHolder;
import com.patsnap.common.request.UserIdHolder;
import com.patsnap.drafting.CommonPage;
import com.patsnap.drafting.client.ComputeClient;
import com.patsnap.drafting.client.EurekaClient;
import com.patsnap.drafting.client.model.HistoryRequestDTO;
import com.patsnap.drafting.client.model.RdSensitiveWordsResDTO;
import com.patsnap.drafting.enums.task.AiTaskContentTypeEnum;
import com.patsnap.drafting.enums.task.AiTaskTypeEnum;
import com.patsnap.drafting.enums.task.AsyncTaskStatusEnum;
import com.patsnap.drafting.enums.task.TaskContentDirectionEnum;
import com.patsnap.drafting.enums.task.TaskStep;
import com.patsnap.drafting.enums.task.TaskTypeEnum;
import com.patsnap.drafting.exception.BizException;
import com.patsnap.drafting.exception.errorcode.ContentErrorCodeEnum;
import com.patsnap.drafting.exception.errorcode.TaskErrorCodeEnum;
import com.patsnap.drafting.manager.JsonMapperManager;
import com.patsnap.drafting.manager.acl.LimitCheckManager;
import com.patsnap.drafting.manager.content.TaskContentBuilder;
import com.patsnap.drafting.manager.content.TaskContentManager;
import com.patsnap.drafting.manager.credit.CreditManager;
import com.patsnap.drafting.repository.aitask.dao.AnalyticsAiTaskHistoryService;
import com.patsnap.drafting.repository.aitask.entity.AnalyticsAiTaskContentPO;
import com.patsnap.drafting.repository.aitask.entity.AnalyticsAiTaskHistoryPO;
import com.patsnap.drafting.repository.share.entity.AiTaskShareLinkPO;
import com.patsnap.drafting.repository.share.service.AiTaskShareLinkService;
import com.patsnap.drafting.request.aitask.AiHistoryPageReqDTO;
import com.patsnap.drafting.request.aitask.AiTaskCreateReqDTO;
import com.patsnap.drafting.response.aihistory.AiHistoryDTO;
import com.patsnap.drafting.response.aihistory.AiHistoryDetailDTO;
import com.patsnap.drafting.response.aihistory.AiHistoryStepDTO;
import com.patsnap.drafting.response.aihistory.ContentInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

import static com.patsnap.drafting.constants.Constant.AI_TASK_TYPE_LIMIT_MAP;
import static com.patsnap.drafting.constants.Constant.X_PATSNAP_FROM_EUREKA;
import static com.patsnap.drafting.enums.share.UserRoleEnum.EDITOR;
import static com.patsnap.drafting.enums.task.AiTaskTypeEnum.AI_SPECIFICATION;
import static com.patsnap.drafting.enums.task.AiTaskTypeEnum.AI_TRANSLATION;

@Slf4j
@Service
public class AiTaskManager {
    
    public static final String TELECOM_ADMIN = "telecom_admin";
    
    @Value("${configs.com.patsnap.drafting.fto.history-date}")
    private long ftoHistoryDate;

    public static final String WAITING = "WAITING";

    private final AnalyticsAiTaskHistoryService aiTaskHistoryService;
    private final TaskContentManager taskContentManager;
    private final JsonMapperManager jsonMapperManager;
    private final ComputeClient computeClient;
    private final EurekaClient eurekaClient;
    private final Executor executor;
    private final AiTaskShareLinkService aiTaskShareLinkService;
    private final LimitCheckManager limitCheckManager;

    public AiTaskManager(
            AnalyticsAiTaskHistoryService aiTaskHistoryService,
            TaskContentManager taskContentManager,
            ComputeClient computeClient,
            CreditManager creditManager,
            JsonMapperManager jsonMapperManager,
            EurekaClient eurekaClient,
            @Qualifier("updateContentExecutor") Executor executor,
            AiTaskShareLinkService aiTaskShareLinkService,
            LimitCheckManager limitCheckManager) {
        this.aiTaskHistoryService = aiTaskHistoryService;
        this.taskContentManager = taskContentManager;
        this.computeClient = computeClient;
        this.jsonMapperManager = jsonMapperManager;
        this.eurekaClient = eurekaClient;
        this.executor = executor;
        this.aiTaskShareLinkService = aiTaskShareLinkService;
        this.limitCheckManager = limitCheckManager;
    }


    @Transactional
    public String checkSensitiveWordsAndCreateTask(AiTaskCreateReqDTO reqDTO) {
        checkSensitiveWords(reqDTO.getContent().values().stream().map(Object::toString).toList());
        return createTask(reqDTO);
    }

    /**
     * 1.检测用户输入文本的语言
     *
     * @param doc 文本
     * @return 语言
     */
    public String langDetect(String doc) {
        if (StrUtil.isEmpty(doc)) {
            return doc;
        }
        return computeClient.getCheckLang(doc);
    }

    public String createTask(AiTaskCreateReqDTO reqDTO) {
        checkTaskExist(reqDTO.getTaskId());
        // 弃用积分值校验，使用次数校验和字数校验
        AiTaskTypeEnum taskType = AiTaskTypeEnum.fromType(reqDTO.getType());
        if(!AI_SPECIFICATION.equals(taskType)) {
            // 专利说明书由于需要区分CN，US，因此次数校验在SpecificationManager中处理
            // AI翻译是按照字数(中文汉字数，英文单词数)来校验
            // 专利交底书，专利查新，FTO查新，是按照次数来校验
            int increaseCount = AI_TRANSLATION.equals(taskType) ? WordCountUtil.wordsCount(String.valueOf(reqDTO.getContent().get(AiTaskContentTypeEnum.USER_INPUT.getType()))) : 1;
            limitCheckManager.checkLimit(UserIdHolder.get(), increaseCount, AI_TASK_TYPE_LIMIT_MAP.get(taskType));
        }
        // 校验通过后，再创建任务
        saveTaskHistory(reqDTO);
        saveTaskContent(reqDTO.getTaskId(), getAiTaskContentTypeEnumObjectMap(reqDTO.getContent()));
        return reqDTO.getTaskId();
    }

    private Map<AiTaskContentTypeEnum, Object> getAiTaskContentTypeEnumObjectMap(
            Map<String, Object> content) {
        return content.entrySet().stream()
                .collect(Collectors.toMap(entry ->
                        AiTaskContentTypeEnum.fromType(entry.getKey()), Map.Entry::getValue));
    }

    public void checkSensitiveWords(List<String> contentList) {
        contentList.forEach(content -> {
            RdSensitiveWordsResDTO.RdData rdData = checkSensitiveWords(content);
            if (rdData != null && rdData.hasSensitiveWords()) {
                throw new BizException(ContentErrorCodeEnum.SENSITIVE_WORDS_EXIST);
            }
        });
    }

    private void saveTaskHistory(AiTaskCreateReqDTO reqDTO) {
        AnalyticsAiTaskHistoryPO task = new AnalyticsAiTaskHistoryPO();
        AiTaskTypeEnum taskType = AiTaskTypeEnum.fromType(reqDTO.getType());
        task.setType(taskType.getType());
        task.setAsyncStatus(AsyncTaskStatusEnum.Running.getValue());
        task.setDataVersion(taskType.getDataVersion());
        task.setId(reqDTO.getTaskId());
        task.setDataFrom(getFrom());
        aiTaskHistoryService.save(task);
    }

    private void saveTaskContent(String taskId, Map<AiTaskContentTypeEnum, Object> contentMap) {
        if (org.apache.commons.collections.MapUtils.isEmpty(contentMap)) {
            log.warn("No user input or specification input, taskId: {}", taskId);
            return;
        }

        TaskContentBuilder builder = TaskContentBuilder.builder()
                .taskId(taskId)
                .contentManager(taskContentManager)
                .executor(executor)
                .build();
        for (Map.Entry<AiTaskContentTypeEnum, Object> entry : contentMap.entrySet()) {
            builder.withContent(entry.getKey(), jsonMapperManager.convertToString(entry.getValue()));
        }
        builder.saveContents();
    }

    private void checkTaskExist(String taskId) {
        AnalyticsAiTaskHistoryPO taskHistory = aiTaskHistoryService.getTaskByTaskId(taskId);
        if (taskHistory != null) {
            log.warn("Task already exist, taskId: {}", taskId);
            throw new BizException(TaskErrorCodeEnum.TASK_ALREADY_EXIST);
        }
    }

    /**
     * 更新任务内容
     *
     * @param content 待更新任务信息
     */
    public void updateTaskContent(String taskId, AiTaskContentTypeEnum contentType, Object content) {
        // 更新 任务内容表信息
        saveTaskContent(taskId, Map.of(contentType, content));
        // 更新任务更新时间
        updateTaskUpdateTime(taskId);
    }

    /**
     * 更新任务内容
     *
     * @param content 待更新任务信息
     */
    public void updateTaskContentOnly(String taskId, AiTaskContentTypeEnum contentType, Object content) {
        // 更新 任务内容表信息
        saveTaskContent(taskId, Map.of(contentType, content));
    }

    /**
     * 更新任务内容
     *
     * @param contentMap 待更新任务信息
     */
    public void batchUpdateTaskContent(String taskId, Map<AiTaskContentTypeEnum, Object> contentMap) {
        // 更新 任务内容表信息
        saveTaskContent(taskId, contentMap);
        // 更新任务更新时间
        updateTaskUpdateTime(taskId);
    }

    /**
     * 更新任务内容
     *
     * @param contentMap 待更新任务信息
     */
    public void batchUpdateTaskContentOnly(String taskId, Map<AiTaskContentTypeEnum, Object> contentMap) {
        // 更新 任务内容表信息
        saveTaskContent(taskId, contentMap);
    }

    public void checkAndUpdateTaskContent(String taskId, AiTaskContentTypeEnum contentType, Object content) {
        checkEditPermission(taskId);
        if (content instanceof String contentStr) {
            checkSensitiveWords(List.of(contentStr));
        }
        updateTaskContent(taskId, contentType, content);
    }

    public <T> T getTaskContent(String taskId, AiTaskContentTypeEnum contentType) {
        checkPermission(taskId);
        return taskContentManager.getTaskContent(taskId, contentType);
    }

    // FTO历史数据刷新，需要获取所有任务的内容数据，不能有权限校验
    public <T> T getTaskContentForHistoryHandle(String taskId, AiTaskContentTypeEnum contentType) {
        return taskContentManager.getTaskContent(taskId, contentType);
    }
    
    public Map<AiTaskContentTypeEnum, Object> getTaskContent(String taskId, List<AiTaskContentTypeEnum> contentTypeList) {
        checkPermission(taskId);
        List<AnalyticsAiTaskContentPO> allContents = taskContentManager.getAllContentsByTaskId(taskId);
        if (CollectionUtils.isEmpty(allContents)) {
            log.warn("No task content found for taskId: {}", taskId);
            return null;
        }
        Map<AiTaskContentTypeEnum, Object> contentMap = new HashMap<>();
        for (AnalyticsAiTaskContentPO contentPO : allContents) {
            AiTaskContentTypeEnum contentTypeEnum = AiTaskContentTypeEnum.fromType(contentPO.getContentType());
            if (contentTypeList.contains(contentTypeEnum)) {
                contentMap.put(contentTypeEnum, jsonMapperManager.convertStringToTargetType(contentPO.getContent(),
                        contentTypeEnum.getContentClass()));
            }
        }
        return contentMap;
    }

    public void updateTaskUpdateTime(String taskId) {
        try {
            LambdaUpdateWrapper<AnalyticsAiTaskHistoryPO> updateWrapper = new LambdaUpdateWrapper<AnalyticsAiTaskHistoryPO>()
                    .eq(AnalyticsAiTaskHistoryPO::getId, taskId)
                    .set(AnalyticsAiTaskHistoryPO::getUpdatedAt, DateTime.now().getMillis());
            updateTask(updateWrapper);
        } catch (Exception e) {
            log.warn("Failed to update task update time. taskId: {}", taskId, e);
        }
    }

    public void updateTaskTitle(String taskId, String title) {
        checkEditPermission(taskId);
        try {
            LambdaUpdateWrapper<AnalyticsAiTaskHistoryPO> updateWrapper = new LambdaUpdateWrapper<AnalyticsAiTaskHistoryPO>()
                    .eq(AnalyticsAiTaskHistoryPO::getId, taskId)
                    .set(AnalyticsAiTaskHistoryPO::getTitle, title);
            updateTask(updateWrapper);
            saveTaskToEurekaHistory(taskId, title);
        } catch (Exception e) {
            log.warn("Failed to update task title. taskId: {}", taskId, e);
        }
    }
    
    private void saveTaskToEurekaHistory(String taskId, String title) {
        try {
            String taskType = AiTaskTypeEnum.fromType(getTaskById(taskId).getType()).getType();
            HistoryRequestDTO requestDTO = HistoryRequestDTO.builder()
                    .taskId(taskId)
                    .title(title)
                    .taskType(taskType)
                    .build();
            eurekaClient.saveHistory(requestDTO);
        } catch (Exception e) {
            log.warn("Failed to save task to eureka history. taskId: {}, title: {}", taskId, title, e);
        }
    }
    
    public void hideTaskContent(String taskId, AiTaskContentTypeEnum contentType, Boolean hide) {
        checkEditPermission(taskId);
        try {
            taskContentManager.hideTaskContent(taskId, contentType, hide);
            updateTaskUpdateTime(taskId);
        } catch (Exception e) {
            log.warn("Failed to hide task content. taskId: {}", taskId, e);
        }
    }

    /**
     * 检查权限
     *
     * @param taskId 任务ID
     */
    public AnalyticsAiTaskHistoryPO checkPermission(String taskId) {
        AnalyticsAiTaskHistoryPO task = aiTaskHistoryService.getById(taskId);
        // 如果任务是公开分享属性，那就没必要校验当前用户是否是任务创建人
        AiTaskShareLinkPO shareLink = aiTaskShareLinkService.getShareLinkByTaskId(taskId);
        if (task == null) {
            throw new BizException(TaskErrorCodeEnum.TASK_NOT_EXIST);
        } else if (((Objects.nonNull(shareLink) && !shareLink.getPublicShare()) || Objects.isNull(shareLink)) && !Objects.equals(task.getCreatedBy(), UserIdHolder.get())) {
            throw new BizException(TaskErrorCodeEnum.TASK_NO_PERMISSION_ACCESS);
        }
        return task;
    }
    
    /**
     * 检查编辑权限
     *
     * @param taskId 任务ID
     */
    public AnalyticsAiTaskHistoryPO checkEditPermission(String taskId) {
        AnalyticsAiTaskHistoryPO task = aiTaskHistoryService.getById(taskId);
        if (task == null) {
            throw new BizException(TaskErrorCodeEnum.TASK_NOT_EXIST);
        }
        if (task.getCreatedBy() != null && task.getCreatedBy().equals(UserIdHolder.get())) {
            log.info("Task is created by current user, taskId: {}", taskId);
            return task;
        }
        // 检查分享链接权限
        AiTaskShareLinkPO shareLink = aiTaskShareLinkService.getShareLinkByTaskId(taskId);
        if (Objects.isNull(shareLink)) {
            throw new BizException(TaskErrorCodeEnum.TASK_NO_PERMISSION_ACCESS);
        } else if (!EDITOR.getValue().equals(shareLink.getRole())) {
            throw new BizException(TaskErrorCodeEnum.TASK_NO_PERMISSION_ACCESS);
        }
        return task;
    }

    /**
     * 更新任务状态
     *
     * @param taskId 任务ID
     * @param status 任务状态
     */
    public void updateTaskStatus(String taskId, AsyncTaskStatusEnum status) {
        LambdaUpdateWrapper<AnalyticsAiTaskHistoryPO> updateWrapper = new LambdaUpdateWrapper<AnalyticsAiTaskHistoryPO>()
                .eq(AnalyticsAiTaskHistoryPO::getId, taskId)
                .set(AnalyticsAiTaskHistoryPO::getAsyncStatus, status.getValue());
        updateTask(updateWrapper);
        log.info("Update task status success. taskId: {}, status: {}", taskId, status);
    }

    /**
     * 重新生成任务
     *
     * @param task            任务
     * @param contentTypeList 内容类型列表
     * @return 是否成功
     */
    public Boolean regenerate(AnalyticsAiTaskHistoryPO task, List<String> contentTypeList) {
        updateTaskStatus(task.getId(), AsyncTaskStatusEnum.Running);
        deleteTaskContentByContentType(task.getId(), contentTypeList);
        return true;
    }

    /**
     * 更新任务
     *
     * @param updateWrapper 待更新信息
     */
    public void updateTask(LambdaUpdateWrapper<AnalyticsAiTaskHistoryPO> updateWrapper) {
        aiTaskHistoryService.update(updateWrapper);
    }

    /**
     * 获取任务详情
     *
     * @param taskId 任务ID
     * @return 任务详情
     */
    public AnalyticsAiTaskHistoryPO getTaskById(String taskId) {
        if (StringUtils.isBlank(taskId)) {
            log.warn("Task ID is blank");
            return null;
        }
        return aiTaskHistoryService.getById(taskId);
    }

    public Map<AiTaskContentTypeEnum, Object> getTaskDetail(String taskId) {
        Map<AiTaskContentTypeEnum, Object> taskDetail = new HashMap<>();
        List<AnalyticsAiTaskContentPO> taskContentList = taskContentManager.getAllContentsByTaskId(taskId);
        taskContentList.forEach(content -> {
            AiTaskContentTypeEnum contentTypeEnum = AiTaskContentTypeEnum.fromType(content.getContentType());
            taskDetail.put(contentTypeEnum, jsonMapperManager.convertStringToTargetType(content.getContent(),
                    contentTypeEnum.getContentClass()));
        });
        return taskDetail;
    }

    /**
     * 获取任务详细信息
     *
     * @param taskId 任务ID
     * @return 任务详细信息
     */
    public AiHistoryStepDTO getTaskInfo(String taskId) {
        AnalyticsAiTaskHistoryPO task = aiTaskHistoryService.getById(taskId);
        //没有任务记录，返回默认值
        if (task == null) {
            return getDefaultHistoryStepDTO(taskId);
        }
        checkPermission(taskId);
        checkDataVersion(task);
        updateReadStatus(task);

        List<AnalyticsAiTaskContentPO> taskContentList = taskContentManager.getAllContentsByTaskId(taskId);
        AiTaskTypeEnum aiTaskTypeEnum = AiTaskTypeEnum.fromType(task.getType());
        String currentStep = getCurrentStep(aiTaskTypeEnum, taskContentList, task);
        Map<String, List<ContentInfo>> stepDetailMap = getStepDetailMap(aiTaskTypeEnum, taskContentList, currentStep);
        return getAiHistoryStepDTO(task, currentStep, stepDetailMap, taskContentList);
    }

    private void checkDataVersion(AnalyticsAiTaskHistoryPO task) {
        if (task.getType().equals(AiTaskTypeEnum.AI_NOVELTY_SEARCH.getType())) {
            if (!task.getDataVersion().equals(AiTaskTypeEnum.AI_NOVELTY_SEARCH.getDataVersion())) {
                throw new BizException(TaskErrorCodeEnum.TASK_DATA_VERSION_HAS_EXPIRED);
            }
        }
    }

    private @Nullable String getInputLang(AnalyticsAiTaskHistoryPO task, List<AnalyticsAiTaskContentPO> taskContentList) {
        String inputLang = null;
        if (AiTaskTypeEnum.AI_NOVELTY_SEARCH.getType().equals(task.getType())) {
            Object inputLangObj =  taskContentList.stream()
                    .filter(it -> AiTaskContentTypeEnum.NOVELTY_SEARCH_INPUT_LANG.getType()
                            .equals(it.getContentType()))
                    .findFirst()
                    .map(it -> jsonMapperManager.convertStringToTargetType(it.getContent(),
                            AiTaskContentTypeEnum.fromType(it.getContentType()).getContentClass())).orElse(null);
            return String.valueOf(inputLangObj);
        }

        if (AiTaskTypeEnum.AI_FTO_SEARCH.getType().equals(task.getType())) {
            Object inputLangObj =  taskContentList.stream()
                    .filter(it -> AiTaskContentTypeEnum.FTO_SEARCH_INPUT_LANG.getType()
                            .equals(it.getContentType()))
                    .findFirst()
                    .map(it -> jsonMapperManager.convertStringToTargetType(it.getContent(),
                            AiTaskContentTypeEnum.fromType(it.getContentType()).getContentClass())).orElse(null);
            return String.valueOf(inputLangObj);
        }
        
        return inputLang;
    }
    
    private String getCurrentStep(AiTaskTypeEnum aiTaskTypeEnum, List<AnalyticsAiTaskContentPO> taskContentList,
            AnalyticsAiTaskHistoryPO task) {
        // 如果是异步任务，且状态为Running 或者 Ready，则返回 step 为 WAITING，前端显示等待中
        if (TaskTypeEnum.ASYNC.getValue().equals(task.getTaskType())
                && (AsyncTaskStatusEnum.Running.getValue().equals(task.getAsyncStatus())
                || AsyncTaskStatusEnum.Ready.getValue().equals(task.getAsyncStatus()))) {
            return WAITING;
        }
        //如果任务是完成状态，则直接获取最后一步
        if (AsyncTaskStatusEnum.Complete.getValue().equals(task.getAsyncStatus())
                || AsyncTaskStatusEnum.Interrupt.getValue().equals(task.getAsyncStatus())) {
            return aiTaskTypeEnum.getSteps()[aiTaskTypeEnum.getSteps().length - 1].getStep();
        }
        return getCurrentStepByContentList(aiTaskTypeEnum, taskContentList);
    }

    /**
     * 从任务内容列表中获取用户输入
     *
     * @param taskContentList 任务内容列表，用于查找用户输入
     * @return 用户输入内容的Object对象，如果没有找到则返回null
     */
    private @Nullable Object getUserInput(List<AnalyticsAiTaskContentPO> taskContentList) {
        Optional<AnalyticsAiTaskContentPO> inputTaskContentOpt = taskContentList.stream()
                .filter(it -> TaskContentDirectionEnum.INPUT.getValue().equals(it.getDirection()))
                .findFirst();
        return inputTaskContentOpt.map(it -> {
            AiTaskContentTypeEnum contentType = AiTaskContentTypeEnum.fromType(it.getContentType());
            return jsonMapperManager.convertStringToTargetType(it.getContent(), contentType.getContentClass());
        }).orElse(null);
    }

    private AiHistoryStepDTO getDefaultHistoryStepDTO(String taskId) {
        AiHistoryStepDTO aiHistoryStepDTO = new AiHistoryStepDTO();
        aiHistoryStepDTO.setTaskId(taskId);
        aiHistoryStepDTO.setTaskStatus(AsyncTaskStatusEnum.Ready.getValue());
        return aiHistoryStepDTO;
    }

    /**
     * 根据任务内容列表获取当前步骤 该方法用于分析任务内容，确定任务当前所处的步骤 主要通过匹配任务内容类型与预定义的步骤详细映射来确定当前步骤
     *
     * @param taskTypeEnum    记录的任务类型
     * @param taskContentList 任务内容列表，包含多个任务内容对象
     * @return 当前步骤的标识字符串
     */
    private String getCurrentStepByContentList(final AiTaskTypeEnum taskTypeEnum,
            final List<AnalyticsAiTaskContentPO> taskContentList) {
        Map<String, List<String>> stepDetailMap = taskTypeEnum.getStepDetailMap();
        Set<String> contentTypes = taskContentList.stream()
                .map(AnalyticsAiTaskContentPO::getContentType)
                .collect(Collectors.toSet());
        List<Map.Entry<String, List<String>>> entryList = new ArrayList<>(stepDetailMap.entrySet());
        Collections.reverse(entryList);

        return entryList.stream()
                .filter(entry -> entry.getValue().stream().anyMatch(contentTypes::contains))
                .map(Map.Entry::getKey)
                .findFirst()
                .orElse("");
    }

    private AiHistoryStepDTO getAiHistoryStepDTO(AnalyticsAiTaskHistoryPO task, String contentStep,
            Map<String, List<ContentInfo>> stepDetailMap, List<AnalyticsAiTaskContentPO> taskContentList) {
        AiHistoryStepDTO stepDTO = new AiHistoryStepDTO();
        stepDTO.setTaskId(task.getId());
        stepDTO.setTitle(task.getTitle());
        stepDTO.setTaskStatus(task.getAsyncStatus());
        stepDTO.setTaskExecuteType(task.getTaskType());
        stepDTO.setCurrentStep(contentStep);
        stepDTO.setStepDetail(stepDetailMap);
        stepDTO.setUserInput(getUserInput(taskContentList));
        stepDTO.setInputLang(getInputLang(task, taskContentList));
        stepDTO.setCreatedAt(task.getCreatedAt().getMillis());
        stepDTO.setUpdatedAt(task.getUpdatedAt().getMillis());

        // 创建时间小于某个日期的数据，都算历史数据（FTO模块需要）
        if (AiTaskTypeEnum.AI_FTO_SEARCH.getType().equalsIgnoreCase(task.getType())) {
            boolean isHistoryData = task.getCreatedAt().getMillis() < ftoHistoryDate;
            stepDTO.setHistoryData(isHistoryData);
        }
        return stepDTO;
    }

    private Map<String, List<ContentInfo>> getStepDetailMap(AiTaskTypeEnum taskType,
            List<AnalyticsAiTaskContentPO> taskContentList, String currentStep) {
        Map<String, List<ContentInfo>> stepDetailMap = new LinkedHashMap<>();
        for (Map.Entry<String, List<String>> entry : taskType.getStepDetailMap().entrySet()) {
            String step = entry.getKey();
            List<String> subStepList = entry.getValue();
            List<ContentInfo> contentInfoList = getContentInfos(taskContentList, subStepList);
            if (step.equals(currentStep)) {
                stepDetailMap.put(step, contentInfoList);
                break;
            } else {
                stepDetailMap.put(step, contentInfoList);
            }
        }
        return stepDetailMap;
    }

    /**
     * 根据任务内容列表和子步骤列表获取内容信息列表 该方法通过筛选和排序任务内容列表中的项目，根据子步骤列表的顺序， 创建并返回一个新的内容信息列表
     *
     * @param taskContentList 任务内容列表，包含多个任务内容对象
     * @param subStepList     子步骤列表，定义了内容类型的顺序
     * @return 返回一个ContentInfo对象的列表，每个对象包含内容类型和是否隐藏的信息
     */
    private List<ContentInfo> getContentInfos(List<AnalyticsAiTaskContentPO> taskContentList,
            List<String> subStepList) {
        // 使用流处理来筛选、排序和转换任务内容列表
        return taskContentList.stream()
                // 筛选条件：内容类型包含在子步骤列表中
                .filter(it -> subStepList.contains(it.getContentType()))
                // 根据子步骤列表中的顺序进行排序
                .sorted(Comparator.comparingInt(it -> subStepList.indexOf(it.getContentType())))
                // 将每个项目转换为ContentInfo对象
                .map(it -> new ContentInfo(it.getContentType(), it.getHide())).toList();
    }

    private void updateReadStatus(AnalyticsAiTaskHistoryPO task) {
        if (BooleanUtils.isNotTrue(task.getReadStatus())) {
            task.setReadStatus(true);
            LambdaUpdateWrapper<AnalyticsAiTaskHistoryPO> updateWrapper = new LambdaUpdateWrapper<AnalyticsAiTaskHistoryPO>()
                    .eq(AnalyticsAiTaskHistoryPO::getId, task.getId())
                    .set(AnalyticsAiTaskHistoryPO::getReadStatus, 1);
            aiTaskHistoryService.update(updateWrapper);
        }
    }

    private AiHistoryDetailDTO convertHistoryDTO(AnalyticsAiTaskHistoryPO task) {
        AiHistoryDetailDTO detailDTO = new AiHistoryDetailDTO();
        detailDTO.setTaskId(task.getId());
        detailDTO.setTitle(task.getTitle());
        detailDTO.setRead(task.getReadStatus());
        detailDTO.setUpdatedAt(task.getUpdatedAt().getMillis());
        detailDTO.setCreatedAt(task.getCreatedAt().getMillis());
        detailDTO.setTaskType(task.getTaskType());
        detailDTO.setType(task.getType());
        detailDTO.setTaskStatus(task.getAsyncStatus());
        return detailDTO;
    }

    /**
     * 分页获取历史记录列表
     *
     * @param request 请求参数
     * @return 历史记录列表
     */
    public CommonPage<AiHistoryDTO> getTaskList(AiHistoryPageReqDTO request) {
        LambdaQueryWrapper<AnalyticsAiTaskHistoryPO> wrapper = getTaskHistoryQueryWrapper(request);
        Page<AnalyticsAiTaskHistoryPO> page = aiTaskHistoryService.page(request.page(), wrapper);
        return CommonPage.convertPage(page, this::convertHistoryDTO);
    }

    private LambdaQueryWrapper<AnalyticsAiTaskHistoryPO> getTaskHistoryQueryWrapper(AiHistoryPageReqDTO request) {
        LambdaQueryWrapper<AnalyticsAiTaskHistoryPO> wrapper = new LambdaQueryWrapper<AnalyticsAiTaskHistoryPO>()
                .eq(AnalyticsAiTaskHistoryPO::getCreatedBy, UserIdHolder.get())
                // 距离当前时间，最近6个月更新的任务
                .ge(AnalyticsAiTaskHistoryPO::getUpdatedAt, DateTime.now().minusMonths(6).getMillis())
                .orderByDesc(AnalyticsAiTaskHistoryPO::getUpdatedAt);

        if (StringUtils.isNotBlank(request.getType()) && !"all".equalsIgnoreCase(request.getType())) {
            AiTaskTypeEnum taskTypeEnum = AiTaskTypeEnum.fromType(request.getType());
            // 指定任务类型查询
            wrapper.eq(AnalyticsAiTaskHistoryPO::getType, request.getType())
                    .in(AnalyticsAiTaskHistoryPO::getDataVersion, taskTypeEnum.getHistoryVersions());
        } else {
            // 所有任务类型查询
            wrapper.and(AiTaskManager::addDataVersionCondition);
        }
        addDataFromCondition(wrapper);
        return wrapper;
    }

    /**
     * 添加数据来源的查询条件。原analytics数据通过is null判断
     */
    private void addDataFromCondition(LambdaQueryWrapper<AnalyticsAiTaskHistoryPO> wrapper) {
        if (getFrom() == null) {
            wrapper.isNull(AnalyticsAiTaskHistoryPO::getDataFrom);
        } else {
            wrapper.eq(AnalyticsAiTaskHistoryPO::getDataFrom, getFrom());
        }
    }

    private static void addDataVersionCondition(LambdaQueryWrapper<AnalyticsAiTaskHistoryPO> wrapperInside) {
        for (AiTaskTypeEnum aiTaskType : AiTaskTypeEnum.values()) {
            wrapperInside.or(w -> w.eq(AnalyticsAiTaskHistoryPO::getType, aiTaskType.getType())
                    .in(AnalyticsAiTaskHistoryPO::getDataVersion, aiTaskType.getHistoryVersions()));
        }
    }

    /**
     * 标记历史记录已读
     *
     * @param taskId 任务ID
     */
    public void readTask(String taskId) {
        AnalyticsAiTaskHistoryPO task = checkPermission(taskId);
        if (BooleanUtils.isTrue(task.getReadStatus())) {
            return;
        }
        LambdaUpdateWrapper<AnalyticsAiTaskHistoryPO> updateWrapper = new LambdaUpdateWrapper<AnalyticsAiTaskHistoryPO>()
                .eq(AnalyticsAiTaskHistoryPO::getId, taskId)
                .set(AnalyticsAiTaskHistoryPO::getReadStatus, 1);
        aiTaskHistoryService.update(updateWrapper);
    }

    /**
     * 批量获取异步任务执行状态
     *
     * @param taskIds 任务ID列表
     * @return 异步任务执行状态列表
     */
    public List<AiHistoryDTO> getAsyncStatus(List<String> taskIds) {
        List<AnalyticsAiTaskHistoryPO> list = aiTaskHistoryService.list(
                new LambdaQueryWrapper<AnalyticsAiTaskHistoryPO>()
                        .eq(AnalyticsAiTaskHistoryPO::getCreatedBy, UserIdHolder.get())
                        .in(AnalyticsAiTaskHistoryPO::getId, taskIds));
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.stream().map(history -> (AiHistoryDTO) convertHistoryDTO(history)).toList();
    }

    /**
     * 通过请求头判断数据来源
     *
     * @return s-core-eureka数据，原analytics数据返回null
     */
    private String getFrom() {
        String from = RequestFromHolder.get();
        if (StringUtils.isNotBlank(from) && from.contains(X_PATSNAP_FROM_EUREKA)) {
            log.info("请求来源为eureka服务。from：{}", from);
            return X_PATSNAP_FROM_EUREKA;
        }
        return null;
    }

    /**
     * 检查敏感词
     *
     * @param description 技术描述内容
     * @return 敏感词信息
     */
    public RdSensitiveWordsResDTO.RdData checkSensitiveWords(String description) {
        if (StringUtils.isBlank(description)) {
            return null;
        }
        return computeClient.checkInputHasSensitiveWords(description);
    }

    /**
     * 删除某个step下所有的content内容
     *
     * @param taskId          任务编号
     * @param contentTypeList contentType列表
     */
    public void deleteTaskContentByContentType(String taskId, List<String> contentTypeList) {
        taskContentManager.deleteTaskContentList(taskId, contentTypeList);
    }

    /**
     * 删除某个step下所有的content内容
     *
     * @param taskId          任务编号
     */
    public void deleteTaskContent(String taskId) {
        taskContentManager.deleteAllTaskContent(taskId);
    }

    /**
     * 删除某个step下所有的content内容
     *
     * @param taskId         任务编号
     * @param aiTaskStepEnum 任务step的枚举
     */
    public void deleteTaskContentByStep(String taskId, TaskStep aiTaskStepEnum) {
        taskContentManager.deleteTaskContentList(taskId, aiTaskStepEnum.getSubStepList());
    }
    
    /**
     * 获取任务所有内容列表
     *
     * @param taskId 任务ID
     * @return 任务内容列表
     */
    public List<AnalyticsAiTaskContentPO> getAllTaskContents(String taskId, String contentTypes) {
        // 只有管理员可以获取所有任务内容
        if (!TELECOM_ADMIN.equals(UserIdHolder.get())) {
            log.warn("No permission to get all task contents for taskId: {}", taskId);
            return Collections.emptyList();
        }
        List<AnalyticsAiTaskContentPO> allContents = taskContentManager.getAllContentsByTaskId(taskId);
        if (CollectionUtils.isEmpty(allContents)) {
            log.warn("No task content found for taskId: {}", taskId);
            return Collections.emptyList();
        }
        if (StringUtils.isNotBlank(contentTypes)) {
            allContents = allContents.stream()
                    .filter(content -> contentTypes.contains(content.getContentType()))
                    .toList();
        }
        
        return allContents;
    }
    
    /**
     * 根据任务类型获取所有任务
     *
     * @param type 任务类型
     * @return 任务列表
     */
    public List<AnalyticsAiTaskHistoryPO> getTasksByType(String type) {
        if (StringUtils.isBlank(type)) {
            log.warn("Task type is blank");
            return Collections.emptyList();
        }
        return aiTaskHistoryService.getTasksByType(type);
    }
}

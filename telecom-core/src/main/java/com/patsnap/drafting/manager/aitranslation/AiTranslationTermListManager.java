package com.patsnap.drafting.manager.aitranslation;

import com.patsnap.common.request.UserIdHolder;
import com.patsnap.drafting.client.ComputeClient;
import com.patsnap.drafting.client.model.RdSensitiveWordsResDTO;
import com.patsnap.drafting.config.AiTranslationConfig;
import com.patsnap.drafting.exception.BizException;
import com.patsnap.drafting.exception.errorcode.ContentErrorCodeEnum;
import com.patsnap.drafting.repository.aitask.dao.AnalyticsAiTranslationTermListService;
import com.patsnap.drafting.repository.aitask.entity.AnalyticsAiTranslationTermListPO;
import com.patsnap.drafting.request.aitranslation.TranslationTermDto;
import com.patsnap.drafting.request.aitranslation.TranslationTermPairDto;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.springframework.web.multipart.MultipartFile;

@Slf4j
@Service
public class AiTranslationTermListManager {

    private final AnalyticsAiTranslationTermListService translationTermListService;
    private final ComputeClient computeClient;
    private final AiTranslationConfig config;
    private static final int MAX_TERM_LENGTH = 120;

    public AiTranslationTermListManager(
            AnalyticsAiTranslationTermListService translationTermListService,
            ComputeClient computeClient, AiTranslationConfig config) {
        this.translationTermListService = translationTermListService;
        this.computeClient = computeClient;
        this.config = config;
    }

    public void submitTerm(TranslationTermDto termDto) {
        String userId = UserIdHolder.get();
        if (StrUtil.isEmpty(userId)) {
            return;
        }

        AnalyticsAiTranslationTermListPO term =
                translationTermListService.getTermByUser(userId).stream()
                        .findFirst()
                        .orElseGet(() -> {
                            AnalyticsAiTranslationTermListPO newTerm = new AnalyticsAiTranslationTermListPO();
                            newTerm.setScope("private");
                            newTerm.setTitle("default");
                            return newTerm;
                        });

        term.setOwner(userId);
        List<TranslationTermPairDto> termPair = termDto.getTermPair();
        if (termPair.size() > config.getTermListMaxLength()) {
            throw new BizException(ContentErrorCodeEnum.TERM_INPUT_VALUE_EXCEEDS_LIMIT);
        }
        for (int i = 0; i < termPair.size(); i++) {
            termPair.get(i).setOrder(i + 1);
        }
        term.setData(JSONUtil.toJsonStr(termPair));
        RdSensitiveWordsResDTO.RdData rdData = checkSensitiveWords(term.getData());
        if (rdData != null && rdData.hasSensitiveWords()) {
            throw new BizException(ContentErrorCodeEnum.SENSITIVE_WORDS_EXIST);
        }
        translationTermListService.saveOrUpdate(term);
    }

    public TranslationTermDto getTerm(String termId) {
        String userId = UserIdHolder.get();
        if (StrUtil.isEmpty(userId)) {
            return new TranslationTermDto();
        }

        List<AnalyticsAiTranslationTermListPO> terms = translationTermListService.getTermByUser(userId);
        if (terms.isEmpty()) {
            return new TranslationTermDto();
        }

        TranslationTermDto term = new TranslationTermDto();
        BeanUtil.copyProperties(terms.get(0), term);
        term.setTermPair(JSONUtil.toList(terms.get(0).getData(), TranslationTermPairDto.class));
        return term;
    }

    public int importTerm(MultipartFile file) {
        int importedCount = 0;
        String extension = FilenameUtils.getExtension(file.getOriginalFilename());
        
        try (Workbook workbook = createWorkbook(file, extension)) {
            Sheet sheet = workbook.getSheetAt(0);
            validateExcelFormat(sheet);

            TranslationTermDto translationTerm = getOrCreateTranslationTerm();
            List<TranslationTermPairDto> existsTerms = translationTerm.getTermPair();
            int currentTermCount = existsTerms.size();

            checkTermLimit(currentTermCount);

            Pair<Integer, Boolean> processResult = processExcelRows(sheet, existsTerms, currentTermCount);
            importedCount = processResult.getLeft();
            if (importedCount > 0) {
                translationTerm.setTermPair(existsTerms);
                submitTerm(translationTerm);
            }
            handleImportResult(processResult, importedCount);
        } catch (BizException e) {
            throw e;
        } catch (Exception e) {
            log.error("Failed to import terms from file", e);
            throw new BizException(ContentErrorCodeEnum.TERM_IMPORT_FAILED);
        }

        return importedCount;
    }

    private Workbook createWorkbook(MultipartFile file, String extension) throws IOException {
        if ("xls".equalsIgnoreCase(extension)) {
            return new HSSFWorkbook(file.getInputStream());
        } else if ("xlsx".equalsIgnoreCase(extension)) {
            return new XSSFWorkbook(file.getInputStream());
        } else {
            throw new BizException(ContentErrorCodeEnum.TERM_INVALID_EXCEL_FORMAT);
        }
    }

    private void validateExcelFormat(Sheet sheet) {
        if (!isValidExcelFormat(sheet)) {
            throw new BizException(ContentErrorCodeEnum.TERM_INVALID_EXCEL_FORMAT);
        }
    }

    private TranslationTermDto getOrCreateTranslationTerm() {
        TranslationTermDto translationTerm = getTerm(null);
        return translationTerm != null ? translationTerm : new TranslationTermDto();
    }

    private void checkTermLimit(int currentTermCount) {
        if (currentTermCount >= config.getTermListMaxLength()) {
            log.warn("Term list has reached the maximum limit of " + config.getTermListMaxLength());
            throw new BizException(ContentErrorCodeEnum.TERM_INPUT_VALUE_EXCEEDS_LIMIT, createImportResult(0));
        }
    }

    private void handleImportResult(Pair<Integer, Boolean> processResult, int importedCount) {
        if (!processResult.getRight()) {
            throw new BizException(ContentErrorCodeEnum.TERM_INPUT_VALUE_EXCEEDS_LIMIT, createImportResult(importedCount));
        }
    }

    private Map<String, Object> createImportResult(int importedCount) {
        Map<String, Object> importResult = new HashMap<>();
        importResult.put("imported_count", importedCount);
        return importResult;
    }

    private boolean isValidExcelFormat(Sheet sheet) {
        Row headerRow = sheet.getRow(0);
        return headerRow != null && headerRow.getPhysicalNumberOfCells() >= 2;
    }

    private Pair<Integer, Boolean> processExcelRows(Sheet sheet, List<TranslationTermPairDto> existsTerms,
            int currentTermCount) {
        int importedCount = 0;
        boolean allImported = true;
        for (Row row : sheet) {
            if (row.getRowNum() == 0 || !isValidRow(row)) {
                continue;
            }

            if (currentTermCount + importedCount >= config.getTermListMaxLength()) {
                log.warn("Import stopped: reached maximum limit of "+ config.getTermListMaxLength()
                        + " terms. Imported {} terms", importedCount);
                allImported = false;
                break;
            }

            Cell chineseCell = row.getCell(0);
            Cell englishCell = row.getCell(1);

            if (!isValidTermPair(chineseCell, englishCell)) {
                continue;
            }

            TranslationTermPairDto termPair = createTermPair(
                    chineseCell,
                    englishCell,
                    currentTermCount + importedCount + 1
            );
            existsTerms.add(termPair);
            importedCount++;
        }

        return Pair.of(importedCount, allImported);
    }

    private boolean isValidRow(Row row) {
        return row.getPhysicalNumberOfCells() >= 2;
    }

    private boolean isValidTermPair(Cell chineseCell, Cell englishCell) {
        return chineseCell != null &&
                englishCell != null &&
                !StringUtils.isBlank(getCellValue(chineseCell)) &&
                !StringUtils.isBlank(getCellValue(englishCell));
    }

    private TranslationTermPairDto createTermPair(Cell chineseCell, Cell englishCell, int order) {
        TranslationTermPairDto termPair = new TranslationTermPairDto();
        termPair.setOrder(order);

        // 获取并截取中文术语
        String chineseTerm = getCellValue(chineseCell);
        if (chineseTerm.length() > MAX_TERM_LENGTH) {
            chineseTerm = chineseTerm.substring(0, MAX_TERM_LENGTH);
            log.warn("Chinese term truncated to {} characters", MAX_TERM_LENGTH);
        }

        // 获取并截取英文术语
        String englishTerm = getCellValue(englishCell);
        if (englishTerm.length() > MAX_TERM_LENGTH) {
            englishTerm = englishTerm.substring(0, MAX_TERM_LENGTH);
            log.warn("English term truncated to {} characters", MAX_TERM_LENGTH);
        }

        termPair.setChineseTerm(chineseTerm);
        termPair.setEnglishTerm(englishTerm);
        return termPair;
    }

    private String getCellValue(Cell cell) {
        if (cell == null) {
            return "";
        }

        switch (cell.getCellType()) {
            case STRING:
                return StringUtils.trimToEmpty(cell.getStringCellValue());
            case NUMERIC:
                return String.valueOf(cell.getNumericCellValue());
            default:
                return "";
        }
    }

    /**
     * 检查敏感词
     *
     * @param description 文本内容
     * @return 敏感词信息
     */
    public RdSensitiveWordsResDTO.RdData checkSensitiveWords(String description) {
        if (StringUtils.isBlank(description)) {
            return null;
        }
        return computeClient.checkInputHasSensitiveWords(description);
    }

}

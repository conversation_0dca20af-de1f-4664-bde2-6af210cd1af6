package com.patsnap.drafting.manager.aispecification.content.base;

import com.patsnap.core.common.copilot.streaming.model.GptResponseDTO;
import com.patsnap.spring.boot.autoconfigure.web.entity.CommonResponse;
import com.patsnap.drafting.enums.common.OperateTypeEnum;
import com.patsnap.drafting.model.base.StreamingModelBO;
import com.patsnap.drafting.request.GenerateContentRequestDTO;


import reactor.core.publisher.Flux;

public interface ContentOperation {

    OperateTypeEnum getOperationType();

    StreamingModelBO getModelBO(GenerateContentRequestDTO generateContentRequestDTO);

    Flux<CommonResponse<GptResponseDTO<String>>> getContent(GenerateContentRequestDTO generateContentRequestDTO);


}

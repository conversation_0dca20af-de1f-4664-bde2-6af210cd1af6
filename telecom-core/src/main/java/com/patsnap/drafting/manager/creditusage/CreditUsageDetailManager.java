package com.patsnap.drafting.manager.creditusage;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.patsnap.common.request.UserIdHolder;
import com.patsnap.core.common.identity.AccountInfo;
import com.patsnap.core.common.identity.AccountUtils;
import com.patsnap.core.common.identity.UserInfo;
import com.patsnap.drafting.CommonPage;
import com.patsnap.drafting.manager.IdentityAccountManager;
import com.patsnap.drafting.repository.aitask.dao.AnalyticsAiTaskHistoryService;
import com.patsnap.drafting.repository.aitask.entity.AnalyticsAiTaskHistoryPO;
import com.patsnap.drafting.repository.creditusage.entity.AnalyticsCreditUsageDetailPO;
import com.patsnap.drafting.repository.creditusage.service.AnalyticsCreditUsageDetailService;
import com.patsnap.drafting.request.creditusage.CreditUsageDetailPageReqDTO;
import com.patsnap.drafting.response.creditusage.CreditUsageDetailDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class CreditUsageDetailManager {

    private final AnalyticsCreditUsageDetailService creditUsageDetailService;
    private final IdentityAccountManager identityAccountManager;
    private final AnalyticsAiTaskHistoryService aiTaskHistoryService;

    /**
     * 分页获取历史记录列表
     *
     * @param request 请求参数
     * @return 历史记录列表
     */
    public CommonPage<CreditUsageDetailDTO> getCreditUsageDetailList(CreditUsageDetailPageReqDTO request) {
        LambdaQueryWrapper<AnalyticsCreditUsageDetailPO> wrapper = getCreditUsageDetailQueryWrapper(request);
        Page<AnalyticsCreditUsageDetailPO> page = creditUsageDetailService.page(request.page(), wrapper);
        return CommonPage.convertPage(page, this::convertCreditUsageDetailDTO);
    }

    private LambdaQueryWrapper<AnalyticsCreditUsageDetailPO> getCreditUsageDetailQueryWrapper(CreditUsageDetailPageReqDTO request) {
        LambdaQueryWrapper<AnalyticsCreditUsageDetailPO> wrapper = new LambdaQueryWrapper<AnalyticsCreditUsageDetailPO>()
                .eq(AnalyticsCreditUsageDetailPO::getUserId, UserIdHolder.get())
                // 距离当前时间，最近6个月更新的任务
                .ge(AnalyticsCreditUsageDetailPO::getCreatedAt, DateTime.now().minusMonths(6).getMillis())
                .orderByDesc(AnalyticsCreditUsageDetailPO::getCreatedAt);
        return wrapper;
    }

    private CreditUsageDetailDTO convertCreditUsageDetailDTO(AnalyticsCreditUsageDetailPO data) {
        CreditUsageDetailDTO detailDTO = new CreditUsageDetailDTO();
        detailDTO.setUserId(data.getUserId());
        AccountInfo accountInfo = identityAccountManager.getAccountInfoByUserId(data.getUserId());
        UserInfo userInfo = AccountUtils.getUserInfo(accountInfo);
        detailDTO.setUserName(AccountUtils.getShowValue(userInfo));
        detailDTO.setTaskId(data.getTaskId());
        AnalyticsAiTaskHistoryPO taskInfo = aiTaskHistoryService.getById(data.getTaskId());
        detailDTO.setTaskName(taskInfo.getTitle());
        detailDTO.setTaskType(data.getTaskType());
        detailDTO.setCreatedAt(data.getCreatedAt().getMillis());
        detailDTO.setCreditValue(data.getCreditValue());
        detailDTO.setCreditType(data.getCreditType());
        return detailDTO;
    }
}

package com.patsnap.drafting.manager.aitranslation.operate.impl;

import com.patsnap.drafting.annotation.TaskContentCache;
import com.patsnap.drafting.client.OpenAiClient;
import com.patsnap.drafting.config.AiTranslationConfig;
import com.patsnap.drafting.config.UrlConfig;
import com.patsnap.drafting.constants.Constant;
import com.patsnap.drafting.enums.task.AiTaskContentTypeEnum;
import com.patsnap.drafting.manager.aitranslation.AiTranslationTermListManager;
import com.patsnap.drafting.manager.aitranslation.operate.TranslationTermService;
import com.patsnap.drafting.model.aitranslation.AiTransContextBo;
import com.patsnap.drafting.request.aitranslation.TranslationTermDto;
import com.patsnap.drafting.response.aitranslation.TranslationKeywordResDTO;
import com.patsnap.drafting.response.aitranslation.TranslationTermDTO;

import static com.patsnap.drafting.manager.aitranslation.operate.TranslationConstant.CUSTOM_TERM;
import java.util.ArrayList;
import java.util.List;
import org.springframework.stereotype.Component;

@Component
public class TermCustomImpl extends TranslationBasicImpl implements TranslationTermService {

    private final AiTranslationTermListManager termListManager;

    public TermCustomImpl(UrlConfig urlConfig,
            AiTranslationConfig aiTranslationConfig, OpenAiClient openAiClient,
            AiTranslationTermListManager aiTranslationTermListManager) {
        super(urlConfig, aiTranslationConfig, openAiClient);
        this.termListManager = aiTranslationTermListManager;
    }

    @Override
    public String model() {
        return CUSTOM_TERM;
    }

    @Override
    @TaskContentCache(contentType = AiTaskContentTypeEnum.TRANSLATION_CUSTOM_TERM)
    public TranslationTermDTO generate(AiTransContextBo context) {
        TranslationTermDTO termList = new TranslationTermDTO();
        List<TranslationKeywordResDTO> results = new ArrayList<>();
        termList.setTerms(results);
        TranslationTermDto customerTermDto = termListManager.getTerm(context.getTermListId());
        if (customerTermDto == null) {
            return termList;
        }
        customerTermDto.getTermPair().forEach(termPair -> {
            String original = Constant.CN.equals(context.getSourceLang()) ?
                    termPair.getChineseTerm() : termPair.getEnglishTerm();
            if (!context.getInput().contains(original)) {
                return;
            }
            TranslationKeywordResDTO res = new TranslationKeywordResDTO();
            res.setAIRecommend(false);
            res.setOriginal(List.of(termPair.getChineseTerm()));
            res.setTranslation(termPair.getEnglishTerm());
            results.add(res);
        });
        return termList;
    }
}

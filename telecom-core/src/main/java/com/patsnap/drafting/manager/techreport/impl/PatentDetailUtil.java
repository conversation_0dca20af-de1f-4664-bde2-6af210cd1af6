package com.patsnap.drafting.manager.techreport.impl;

import com.alibaba.fastjson.JSONObject;
import com.patsnap.core.common.request.SiteLangHolder;
import com.patsnap.core.spring.boot.autoconfigure.web.entity.CommonResponse;
import com.patsnap.drafting.client.PatentApiClient;
import com.patsnap.drafting.constants.PatentDetailFieldConstants;
import com.patsnap.drafting.manager.patent.PatentInfoManager;
import com.patsnap.drafting.request.patentinfo.PatentTranslateRequestDTO;
import com.patsnap.drafting.response.techreport.TechReportPreviewResDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 专利详情工具类
 * 负责获取专利详情信息的相关操作
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class PatentDetailUtil {

    private final PatentApiClient patentApiClient;
    private final PatentInfoManager patentInfoManager;

    /**
     * 批量获取专利详细信息
     *
     * @param patentIds 专利ID列表
     * @param fields 需要获取的字段列表
     * @return 专利信息对象列表
     */
    public List<TechReportPreviewResDTO.PatentInfo> getPatentDetail(List<String> patentIds, List<String> fields) {
        log.info("开始批量获取专利详细信息, patentIds: {}", patentIds);
        
        if (patentIds == null || patentIds.isEmpty()) {
            log.error("专利ID列表为空");
            return Collections.emptyList();
        }
        
        try {
            // 如果字段列表为空，使用默认字段列表
            if (fields == null || fields.isEmpty()) {
                fields = Arrays.asList(
                        PatentDetailFieldConstants.TITLE,
                        PatentDetailFieldConstants.ABST,
                        PatentDetailFieldConstants.ANCS, 
                        PatentDetailFieldConstants.PN, 
                        PatentDetailFieldConstants.TTC, 
                        PatentDetailFieldConstants.PRO_13B_SUMMARY, 
                        PatentDetailFieldConstants.PATENT_INNO_SUMMARY, 
                        PatentDetailFieldConstants.EFFECT_13B_SUMMARY,
                        PatentDetailFieldConstants.PRO_13B_SUMMARY_LANG,
                        PatentDetailFieldConstants.PATENT_INNO_SUMMARY_LANG,
                        PatentDetailFieldConstants.EFFECT_13B_SUMMARY_LANG,
                        PatentDetailFieldConstants.PRO_13B_SUMMARY_TRAN,
                        PatentDetailFieldConstants.PATENT_INNO_SUMMARY_TRAN,
                        PatentDetailFieldConstants.EFFECT_13B_SUMMARY_TRAN,
                        PatentDetailFieldConstants.TECHNICAL_TITLE,
                        PatentDetailFieldConstants.TECHNICAL_TITLE_LANG,
                        PatentDetailFieldConstants.TECHNICAL_TITLE_TRAN,
                        PatentDetailFieldConstants.THUMB
                );
            }
            
            // 调用PatentApiClient获取专利详细信息
            Map<String, Map<String, Object>> patentDetailsMap = patentApiClient.getPatentFieldsMap(patentIds, fields);
            
            if (patentDetailsMap == null || patentDetailsMap.isEmpty()) {
                log.error("获取专利详细信息失败, patentIds: {}", patentIds);
                return Collections.emptyList();
            }
            
            List<TechReportPreviewResDTO.PatentInfo> patentInfoList = new ArrayList<>();
            
            // 处理每个专利的详细信息
            for (String patentId : patentIds) {
                if (!patentDetailsMap.containsKey(patentId)) {
                    log.warn("未获取到专利详细信息, patentId: {}", patentId);
                    continue;
                }
                
                // 获取专利详细信息
                Map<String, Object> patentDetail = patentDetailsMap.get(patentId);
                
                // 创建专利信息对象
                TechReportPreviewResDTO.PatentInfo patentInfo = new TechReportPreviewResDTO.PatentInfo();
                
                // 设置专利ID和基本信息
                patentInfo.setPatentId(patentId);
                patentInfo.setTitle((String) patentDetail.get(PatentDetailFieldConstants.TITLE));
                patentInfo.setAbst((String) patentDetail.get(PatentDetailFieldConstants.ABST));
                patentInfo.setPn((String) patentDetail.get(PatentDetailFieldConstants.PN));
                
                // 获取当前网站语言
                String currentLang = SiteLangHolder.get();
                
                // 处理技术问题字段（PRO_13B_SUMMARY）
                String problem = (String) patentDetail.get(PatentDetailFieldConstants.PRO_13B_SUMMARY);
                String problemLang = (String) patentDetail.get(PatentDetailFieldConstants.PRO_13B_SUMMARY_LANG);
                if (StringUtils.isNotBlank(problem) && StringUtils.isNotBlank(problemLang) && 
                    !currentLang.equalsIgnoreCase(problemLang)) {
                    String translatedProblem = (String) patentDetail.get(PatentDetailFieldConstants.PRO_13B_SUMMARY_TRAN);
                    if (StringUtils.isNotBlank(translatedProblem)) {
                        problem = translatedProblem;
                    }
                }
                patentInfo.setProblem(problem);
                
                // 处理技术效果字段（EFFECT_13B_SUMMARY）
                String benefits = (String) patentDetail.get(PatentDetailFieldConstants.EFFECT_13B_SUMMARY);
                String benefitsLang = (String) patentDetail.get(PatentDetailFieldConstants.EFFECT_13B_SUMMARY_LANG);
                if (StringUtils.isNotBlank(benefits) && StringUtils.isNotBlank(benefitsLang) && 
                    !currentLang.equalsIgnoreCase(benefitsLang)) {
                    String translatedBenefits = (String) patentDetail.get(PatentDetailFieldConstants.EFFECT_13B_SUMMARY_TRAN);
                    if (StringUtils.isNotBlank(translatedBenefits)) {
                        benefits = translatedBenefits;
                    }
                }
                patentInfo.setBenefits(benefits);
                
                // 处理技术标题字段（TECHNICAL_TITLE）
                String technicalTitle = (String) patentDetail.get(PatentDetailFieldConstants.TECHNICAL_TITLE);
                String technicalTitleLang = (String) patentDetail.get(PatentDetailFieldConstants.TECHNICAL_TITLE_LANG);
                if (StringUtils.isNotBlank(technicalTitle) && StringUtils.isNotBlank(technicalTitleLang) && 
                    !currentLang.equalsIgnoreCase(technicalTitleLang)) {
                    String translatedTechnicalTitle = (String) patentDetail.get(PatentDetailFieldConstants.TECHNICAL_TITLE_TRAN);
                    if (StringUtils.isNotBlank(translatedTechnicalTitle)) {
                        technicalTitle = translatedTechnicalTitle;
                    }
                }
                patentInfo.setTechnicalTitle(technicalTitle);

                // 处理发明点列表（PATENT_INNO_SUMMARY）
                Object innoSummary = patentDetail.get(PatentDetailFieldConstants.PATENT_INNO_SUMMARY);
                String innoSummaryLang = (String) patentDetail.get(PatentDetailFieldConstants.PATENT_INNO_SUMMARY_LANG);
                if (innoSummary != null && StringUtils.isNotBlank(innoSummaryLang) && 
                    !currentLang.equalsIgnoreCase(innoSummaryLang)) {
                    Object translatedInnoSummary = patentDetail.get(PatentDetailFieldConstants.PATENT_INNO_SUMMARY_TRAN);
                    if (translatedInnoSummary != null) {
                        innoSummary = translatedInnoSummary;
                    }
                }
                
                if (innoSummary instanceof List) {
                    patentInfo.setInventions((List<String>) innoSummary);
                } else if (innoSummary instanceof String) {
                    patentInfo.setInventions(Collections.singletonList((String) innoSummary));
                }
                
                // 处理技术主题列表
                Object ttcObject = patentDetail.get(PatentDetailFieldConstants.TTC);
                List<String> techTopicsList = new ArrayList<>();
                
                if (ttcObject instanceof List) {
                    List<Object> ttcList = (List<Object>) ttcObject;
                    for (Object ttc : ttcList) {
                        // 提取description字段
                        extractTechTopic(ttc, techTopicsList);
                    }
                } else if (ttcObject instanceof Map) {
                    // 如果是单个Map对象
                    extractTechTopic(ttcObject, techTopicsList);
                } else if (ttcObject instanceof String) {
                    techTopicsList.add((String) ttcObject);
                } else if (ttcObject != null) {
                    techTopicsList.add(ttcObject.toString());
                }
                
                patentInfo.setTechTopics(techTopicsList);
                
                // 处理申请人信息
                Object ancsObject = patentDetail.get(PatentDetailFieldConstants.ANCS);
                if (ancsObject != null) {
                    TechReportPreviewResDTO.PatentInfo.ANCS ancs = new TechReportPreviewResDTO.PatentInfo.ANCS();
                    
                    if (ancsObject instanceof List && !((List<?>) ancsObject).isEmpty()) {
                        Object firstItem = ((List<?>) ancsObject).get(0);
                        if (firstItem instanceof Map) {
                            // 如果是Map对象，尝试获取详细信息
                            Map<?, ?> ancsMap = (Map<?, ?>) firstItem;
                            if (ancsMap.containsKey("name")) {
                                ancs.setAnsName(ancsMap.get("name").toString());
                            }
                            if (ancsMap.containsKey("entity_id")) {
                                ancs.setEntityId(ancsMap.get("entity_id").toString());
                            }
                            if (ancsMap.containsKey("entity_type")) {
                                ancs.setEntityType(ancsMap.get("entity_type").toString());
                            }
                            if (ancsMap.containsKey("logo")) {
                                ancs.setLogo(ancsMap.get("logo").toString());
                            }
                        } else {
                            // 如果不是Map对象，直接作为申请人名称处理
                            ancs.setAnsName(firstItem.toString());
                        }
                    } else if (ancsObject instanceof String) {
                        ancs.setAnsName((String) ancsObject);
                    } else if (ancsObject instanceof Map) {
                        Map<?, ?> ancsMap = (Map<?, ?>) ancsObject;
                        if (ancsMap.containsKey("name")) {
                            ancs.setAnsName(ancsMap.get("name").toString());
                        }
                        if (ancsMap.containsKey("entity_id")) {
                            ancs.setEntityId(ancsMap.get("entity_id").toString());
                        }
                        if (ancsMap.containsKey("entity_type")) {
                            ancs.setEntityType(ancsMap.get("entity_type").toString());
                        }
                        if (ancsMap.containsKey("logo")) {
                            ancs.setLogo(ancsMap.get("logo").toString());
                        }
                    }
                    
                    patentInfo.setAncs(ancs);
                }
                
                patentInfoList.add(patentInfo);
            }
            
            return patentInfoList;
        } catch (Exception e) {
            log.error("批量获取专利详细信息失败, patentIds: {}", patentIds, e);
            return Collections.emptyList();
        }
    }


    /**
     * 批量获取专利详情并映射到预览响应DTO
     *
     * @param patentIds 专利ID列表
     * @return 技术报告预览响应DTO列表
     */
    public List<TechReportPreviewResDTO> getPatentDetailPreview(List<String> patentIds) {
        log.info("开始批量获取专利详细信息并映射到预览响应DTO, patentIds: {}", patentIds);
        
        try {
            // 获取专利详细信息
            List<TechReportPreviewResDTO.PatentInfo> patentInfoList = getPatentDetail(patentIds, null);
            
            if (patentInfoList.isEmpty()) {
                return Collections.emptyList();
            }
            
            List<TechReportPreviewResDTO> previewResDTOList = new ArrayList<>();
            
            // 为每个专利信息创建预览响应DTO
            for (TechReportPreviewResDTO.PatentInfo patentInfo : patentInfoList) {
                // 创建预览结果DTO
                TechReportPreviewResDTO previewResDTO = new TechReportPreviewResDTO();
                
                // 设置专利信息到预览响应DTO
                previewResDTO.setPatentInfo(patentInfo);
                previewResDTO.setItemId(patentInfo.getPatentId());
                previewResDTO.setItemType("patent");
                
                previewResDTOList.add(previewResDTO);
            }
            
            return previewResDTOList;
        } catch (Exception e) {
            log.error("批量获取专利详细信息并映射到预览响应DTO失败, patentIds: {}", patentIds, e);
            return Collections.emptyList();
        }
    }



    /**
     * 从技术主题对象中提取描述信息
     * 
     * @param ttcObject 技术主题对象
     * @param techTopicsList 技术主题列表
     */
    private void extractTechTopic(Object ttcObject, List<String> techTopicsList) {
        if (ttcObject instanceof Map) {
            Map<String, Object> ttcMap = (Map<String, Object>) ttcObject;
            if (ttcMap.containsKey("description") && ttcMap.get("description") != null) {
                techTopicsList.add(ttcMap.get("description").toString());
            }
        } else if (ttcObject != null) {
            techTopicsList.add(ttcObject.toString());
        }
    }
}
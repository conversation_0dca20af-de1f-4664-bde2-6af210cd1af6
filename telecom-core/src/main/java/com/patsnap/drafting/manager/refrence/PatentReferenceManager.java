package com.patsnap.drafting.manager.refrence;

import com.patsnap.drafting.client.PatentApiClient;
import com.patsnap.drafting.response.aidisclosure.ReferencesReqDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
@RequiredArgsConstructor
public class PatentReferenceManager {
    
    private final PatentApiClient patentApiClient;
    
    public Collection<Map<String, Object>> getReference(ReferencesReqDTO request) {
        List<String> pns = request.getPns();
        List<String> patentIds = patentApiClient.getPatentIdByPatentNos(StringUtils.join(pns, ","));
        Map<String, Map<String, Object>> patentDetailMap = patentApiClient.getPatentDetails(patentIds);
        return patentDetailMap.values();
    }
    
}

package com.patsnap.drafting.manager.aidisclosure;

import com.patsnap.core.common.request.SiteLangHolder;
import com.patsnap.drafting.enums.common.Lang;
import com.patsnap.drafting.request.aidisclosure.DisclosureExportReqDTO;
import com.patsnap.drafting.util.DateUtils;
import com.patsnap.drafting.util.LocaleUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xwpf.usermodel.ParagraphAlignment;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;
import org.apache.poi.xwpf.usermodel.XWPFTable;
import org.apache.poi.xwpf.usermodel.XWPFTableCell;
import org.apache.poi.xwpf.usermodel.XWPFTableRow;
import org.joda.time.DateTime;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTHMerge;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTTcPr;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.STMerge;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Component;

import java.io.ByteArrayOutputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

@Component
public class PatentDisclosureWordGenerator {

    @Autowired
    private MessageSource messageSource;

    public byte[] generateWord(DisclosureExportReqDTO data) {

        try (XWPFDocument document = new XWPFDocument()) {

            // 创建标题
            XWPFParagraph titleParagraph = document.createParagraph();
            titleParagraph.setAlignment(ParagraphAlignment.CENTER);
            XWPFRun titleRun = titleParagraph.createRun();
            setText(titleRun, data.getTitle(), 16, true);

            // 添加表格
            addTable(document, data);

            // 在表格后添加一个空行
            document.createParagraph();

            Map<String, List<DisclosureExportReqDTO.Content>> groupedData = new LinkedHashMap<>();
            for (DisclosureExportReqDTO.Content item : data.getData()) {
                String parentTitle = item.getParentTitle();
                groupedData.computeIfAbsent(parentTitle, k -> new ArrayList<>()).add(item);
            }

            // 添加数据到文档
            for (Map.Entry<String, List<DisclosureExportReqDTO.Content>> entry : groupedData.entrySet()) {
                String parentTitle = entry.getKey();
                List<DisclosureExportReqDTO.Content> items = entry.getValue();

                // 添加父标题
                XWPFParagraph parentTitleParagraph = document.createParagraph();
                XWPFRun parentTitleRun = parentTitleParagraph.createRun();
                setText(parentTitleRun, parentTitle, 14, true);

                // 添加子项
                for (DisclosureExportReqDTO.Content item : items) {
                    String itemTitle = item.getTitle();
                    String content = item.getContent();

                    if (!itemTitle.equals(parentTitle)) {
                        XWPFParagraph itemTitlePara = document.createParagraph();
                        XWPFRun itemTitleRun = itemTitlePara.createRun();
                        setText(itemTitleRun, itemTitle, 12, true);
                    }

                    if (StringUtils.isBlank(content)){
                        continue;
                    }
                    Arrays.stream(content.split("\n")).forEach(line -> {
                        XWPFParagraph contentPara = document.createParagraph();
                        XWPFRun contentRun = contentPara.createRun();
                        setText(contentRun, line, 12, false);
                    });
                }
            }

            // 保存文档
            try (ByteArrayOutputStream out = new ByteArrayOutputStream()) {
                document.write(out);
                return out.toByteArray();
            }
        } catch (Exception e) {
            throw new RuntimeException("生成word文档失败", e);
        }
    }

    /**
     * 添加表格
     */
    private void addTable(XWPFDocument document, DisclosureExportReqDTO data) {
        // 创建头部表格
        XWPFTable table = document.createTable(4, 4);
        table.setWidth("100%");

        // 设置行高
        for (int i = 0; i < 4; i++) {
            // 设置行高
            table.getRow(i).setHeight(600);
        }

        // 设置表格边框
        table.setInsideHBorder(XWPFTable.XWPFBorderType.SINGLE, 4, 0, "000000");
        table.setInsideVBorder(XWPFTable.XWPFBorderType.SINGLE, 4, 0, "000000");
        table.setTopBorder(XWPFTable.XWPFBorderType.SINGLE, 4, 0, "000000");
        table.setBottomBorder(XWPFTable.XWPFBorderType.SINGLE, 4, 0, "000000");
        table.setLeftBorder(XWPFTable.XWPFBorderType.SINGLE, 4, 0, "000000");
        table.setRightBorder(XWPFTable.XWPFBorderType.SINGLE, 4, 0, "000000");

        // 填充表格内容并合并单元格
        Locale locale = LocaleUtils.getLocale(getLang());
        setCellText(table, 0, 0, messageSource.getMessage("patent.disclosure.title", null, locale), true,
                ParagraphAlignment.CENTER);
        mergeCellsHorizontal(table, 0);
        setCellText(table, 0, 1, data.getTitle(), false, ParagraphAlignment.LEFT);

        setCellText(table, 1, 0, messageSource.getMessage("patent.disclosure.author", null, locale), true,
                ParagraphAlignment.CENTER);
        setCellText(table, 1, 1, data.getAuthor(), false, ParagraphAlignment.LEFT);
        setCellText(table, 1, 2, messageSource.getMessage("patent.disclosure.phone", null, locale), true,
                ParagraphAlignment.CENTER);
        setCellText(table, 1, 3, data.getPhone(), false, ParagraphAlignment.LEFT);

        setCellText(table, 2, 0, messageSource.getMessage("patent.disclosure.email", null, locale), true,
                ParagraphAlignment.CENTER);
        mergeCellsHorizontal(table, 2);
        setCellText(table, 2, 1, data.getEmail(), false, ParagraphAlignment.LEFT);

        setCellText(table, 3, 0, messageSource.getMessage("patent.disclosure.inventor", null, locale), true,
                ParagraphAlignment.CENTER);
        setCellText(table, 3, 1, data.getInventor(), false, ParagraphAlignment.LEFT);
        setCellText(table, 3, 2, messageSource.getMessage("patent.disclosure.time", null, locale), true,
                ParagraphAlignment.CENTER);

        setCellText(table, 3, 3, DateUtils.formatDateTimeByLang(DateTime.now(), Lang.parseLang(SiteLangHolder.get())),
                false, ParagraphAlignment.LEFT);

        // 调整单元格宽度
        setColumnWidth(table, 0, 20);
        setColumnWidth(table, 1, 30);
        setColumnWidth(table, 2, 10);
        setColumnWidth(table, 3, 40);
    }

    /**
     * 设置单元格文本
     */
    private static void setCellText(XWPFTable table, int row, int col, String text, boolean isBold,
            ParagraphAlignment alignment) {
        XWPFTableCell cell = table.getRow(row).getCell(col);
        // 设置垂直对齐
        cell.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);

        XWPFParagraph paragraph = cell.getParagraphs().get(0);
        paragraph.setAlignment(ParagraphAlignment.CENTER);
        // 设置水平对齐
        paragraph.setAlignment(alignment);
        paragraph.setSpacingBefore(0);
        paragraph.setSpacingAfter(0);
        XWPFRun run = paragraph.createRun();
        setText(run, text, 12, isBold);
    }

    /**
     * 设置文本样式
     */
    private static void setText(XWPFRun run, String text, Integer fontSize, boolean isBold) {
        run.setText(text);
        run.setBold(isBold);
        run.setFontSize(fontSize);
        run.setFontFamily("宋体");
    }

    /**
     * 水平合并单元格
     */
    private static void mergeCellsHorizontal(XWPFTable table, int row) {
        for (int colIndex = 1; colIndex <= 3; colIndex++) {
            XWPFTableCell cell = table.getRow(row).getCell(colIndex);
            CTTcPr tcPr = cell.getCTTc().getTcPr();
            if (tcPr == null) {
                tcPr = cell.getCTTc().addNewTcPr();
            }
            CTHMerge merge = tcPr.addNewHMerge();
            if (colIndex == 1) {
                merge.setVal(STMerge.RESTART);
            } else {
                merge.setVal(STMerge.CONTINUE);
            }
        }
    }

    /**
     * 设置列宽
     */
    private static void setColumnWidth(XWPFTable table, int col, int width) {
        for (XWPFTableRow row : table.getRows()) {
            row.getCell(col).setWidth(width + "%");
        }
    }

    private Lang getLang() {
        if ("CN".equalsIgnoreCase(SiteLangHolder.get())) {
            return Lang.CN;
        } else if ("TW".equalsIgnoreCase(SiteLangHolder.get())) {
            return Lang.TW;
        } else if ("JP".equalsIgnoreCase(SiteLangHolder.get())) {
            return Lang.JP;
        } else if ("DE".equalsIgnoreCase(SiteLangHolder.get())) {
            return Lang.DE;
        } else {
            return Lang.EN;
        }
    }
}
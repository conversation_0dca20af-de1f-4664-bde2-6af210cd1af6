package com.patsnap.drafting.manager.aispecification.handler;

import com.fasterxml.jackson.core.type.TypeReference;
import com.patsnap.core.common.copilot.streaming.handler.SimpleJsonStreamingResponseHandler;
import com.patsnap.core.common.copilot.streaming.model.GptResponseDTO;
import com.patsnap.drafting.model.aispecification.SpecificationDisclosureExtractEmbodimentBO;
import com.patsnap.drafting.request.aispecification.DisclosureEmbodimentItem;
import com.patsnap.spring.boot.autoconfigure.web.entity.CommonResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import reactor.core.publisher.FluxSink;

import java.util.Arrays;
import java.util.List;

/**
 * desc
 *
 * <AUTHOR>
 * @Date 2025/4/29 19:11
 */
@Slf4j
public class DisclosureExtractEmbodimentHandler extends SimpleJsonStreamingResponseHandler<SpecificationDisclosureExtractEmbodimentBO> {

    public static final String FIELD_SEPARATOR_REGEX = "\\$\\$\\$";

    public DisclosureExtractEmbodimentHandler(FluxSink<CommonResponse<GptResponseDTO<SpecificationDisclosureExtractEmbodimentBO>>> sink, String input) {
        this(input, sink, 5);
    }

    public DisclosureExtractEmbodimentHandler(String input, FluxSink<CommonResponse<GptResponseDTO<SpecificationDisclosureExtractEmbodimentBO>>> sink, int combineTimes) {
        super(sink, combineTimes, input, new TypeReference<>() {
        }, new SpecificationDisclosureExtractEmbodimentBO());
    }

    public SpecificationDisclosureExtractEmbodimentBO convertContent(String content) {
        SpecificationDisclosureExtractEmbodimentBO result = new SpecificationDisclosureExtractEmbodimentBO();
        // 如果content为空或者不包含$$$，则直接返回
        if (StringUtils.isBlank(content) || !content.contains("$$$")) {
            return result;
        }
        List<String> splitList = Arrays.asList(content.split(FIELD_SEPARATOR_REGEX));
        // 过滤掉空格或者换行符等特殊字符的无效数据
        List<DisclosureEmbodimentItem> embodiments = splitList.stream()
                .map(item -> item.replaceAll("[\\r\\n]", ""))
                .filter(item -> StringUtils.isNotBlank(item.trim()))
                .map(item -> {
            DisclosureEmbodimentItem embodimentItem = new DisclosureEmbodimentItem();
            embodimentItem.setSelected(false);
            embodimentItem.setText(item);
            return embodimentItem;
        }).toList();
        if (CollectionUtils.isNotEmpty(embodiments)) {
            result.setEmbodiments(embodiments);
        }
        return result;
    }

    protected void doAfterComplete(String content) {
        System.out.println("final-extract-embodiment-content = " + content);
    }

    protected boolean outputValidate(String fullContent) {
        return true;
    }
}

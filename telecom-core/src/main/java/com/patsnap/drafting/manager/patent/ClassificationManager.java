package com.patsnap.drafting.manager.patent;

import com.patsnap.drafting.client.PatentApiClient;
import com.patsnap.drafting.constants.Constant;
import com.patsnap.drafting.model.patent.CpcClassification;
import com.patsnap.drafting.model.patent.IpcClassification;
import com.patsnap.drafting.model.patent.PatentClassification;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
public class ClassificationManager {

    @Autowired
    private PatentApiClient patentClient;


    public List<String> getClassificationDescriptions(String type, List<String> ids, String lang) {
        try {
            PatentClassification classification = patentClient.getClassificationExplain(type, ids, lang);
            if (classification == null) {
                return Collections.emptyList();
            }

            // 使用模式匹配和增强型switch表达式创建映射
            Map<String, String> idToDescMap = switch (type.toLowerCase()) {
                case Constant.CLASSIFICATION_TYPE_CPC ->
                        classification.getClassificationCpc() != null ? classification.getClassificationCpc().stream()
                                .collect(Collectors.toMap(CpcClassification::getCpc,
                                        CpcClassification::getDescriptionText)) : Map.of();
                case Constant.CLASSIFICATION_TYPE_IPC ->
                        classification.getClassificationIpc() != null ? classification.getClassificationIpc().stream()
                                .collect(Collectors.toMap(IpcClassification::getIpc,
                                        IpcClassification::getDescriptionText)) : Map.of();
                default -> Map.of();
            };

            // 使用Stream API和方法引用按原始顺序返回描述
            return ids.stream().map(id -> idToDescMap.getOrDefault(id, "")).toList();
        } catch (Exception e) {
            log.error("获取分类号描述失败", e);
            return Collections.emptyList();
        }
    }


}


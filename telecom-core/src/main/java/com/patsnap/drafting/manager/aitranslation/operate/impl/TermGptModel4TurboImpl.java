package com.patsnap.drafting.manager.aitranslation.operate.impl;

import com.patsnap.core.common.copilot.gpt.enums.GPTModelEnum;
import com.patsnap.drafting.annotation.TaskContentCache;
import com.patsnap.drafting.client.OpenAiClient;
import com.patsnap.drafting.config.AiTranslationConfig;
import com.patsnap.drafting.config.UrlConfig;
import com.patsnap.drafting.enums.prompt.PromptKeyEnum;
import com.patsnap.drafting.enums.prompt.ScenarioEnum;
import com.patsnap.drafting.enums.task.AiTaskContentTypeEnum;
import com.patsnap.drafting.manager.aitranslation.operate.TranslationTermService;
import com.patsnap.drafting.model.aitranslation.AiTransContextBo;
import com.patsnap.drafting.response.aitranslation.TranslationKeywordResDTO;
import com.patsnap.drafting.response.aitranslation.TranslationTermDTO;

import static com.patsnap.drafting.manager.aitranslation.operate.TranslationConstant.*;
import java.util.List;
import org.springframework.stereotype.Component;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.ImmutableMap;

@Component
public class TermGptModel4TurboImpl extends TranslationBasicImpl implements TranslationTermService {

    private static GPTModelEnum GPT_MODEL = GPTModelEnum.GPT_MODEL_4_TURBO;

    public TermGptModel4TurboImpl(UrlConfig urlConfig,
            AiTranslationConfig aiTranslationConfig, OpenAiClient openAiClient) {
        super(urlConfig, aiTranslationConfig, openAiClient);
    }

    @Override
    public String model() {
        return GPT_MODEL.getModelName();
    }

    @Override
    @TaskContentCache(contentType = AiTaskContentTypeEnum.TRANSLATION_SUGGESTED_TERM)
    public TranslationTermDTO generate(AiTransContextBo contextBo) {
        String sourceInput = contextBo.getInput();
        sourceInput = sourceInput.substring(0, Math.min(sourceInput.length(), 500));
        List<TranslationKeywordResDTO> suggestKeywords = openAiClient.chatCompletions(
                PromptKeyEnum.TERMINOLOGY_EXTRACTION.getValue(), GPT_MODEL, ScenarioEnum.AI_TRANSLATION,
                new TypeReference<>() {
                }, ImmutableMap.of(TOPIC, contextBo.getTechTopic(), SOURCE_LANG,
                        contextBo.getSourceLang(),
                        TARGET_LANG, contextBo.getTargetLang(), INPUT, sourceInput));
        TranslationTermDTO translationTermDTO = new TranslationTermDTO();
        translationTermDTO.setTerms(suggestKeywords);
        return translationTermDTO;
    }
}

package com.patsnap.drafting.manager.content.logic;

import com.patsnap.drafting.enums.task.AiTaskContentTypeEnum;
import com.patsnap.drafting.manager.aitask.AiTaskManager;
import com.patsnap.drafting.request.ainoveltysearch.NoveltySearchUpdateElementReqDTO;
import com.patsnap.drafting.request.ainoveltysearch.NoveltySearchElementResDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 更新检索要素缓存逻辑
 */
@Component
public class NoveltySearchUpdateRetrievalElementsContentCacheLogic extends ContentCacheLogic {

    @Autowired
    private AiTaskManager aiTaskManager;

    @Override
    public void updateContent(String taskId, AiTaskContentTypeEnum contentType, Object result,
            Object[] args) {
        if (!(result instanceof NoveltySearchElementResDTO)) {
            return;
        }

        // 更新缓存
        aiTaskManager.updateTaskContent(taskId, contentType, result);

        // 清除可能受影响的缓存内容
        List<String> stepList = new ArrayList<>();
        stepList.add(AiTaskContentTypeEnum.NOVELTY_SEARCH_AGENT_RESULT.getType());
        stepList.add(AiTaskContentTypeEnum.NOVELTY_SEARCH_TASK_ID.getType());
        stepList.add(AiTaskContentTypeEnum.NOVELTY_SEARCH_TECH_FEATURE_COMPARISON.getType());
        stepList.add(AiTaskContentTypeEnum.NOVELTY_SEARCH_PATENT_CONFIRM_FEATURE.getType());
        stepList.add(AiTaskContentTypeEnum.NOVELTY_SEARCH_FEATURE_CONFIRM.getType());
        stepList.add(AiTaskContentTypeEnum.NOVELTY_SEARCH_REPORT_COMPARATIVE_LITERATURE.getType());
        stepList.add(AiTaskContentTypeEnum.NOVELTY_SEARCH_REPORT_TITLE.getType());
        stepList.add(AiTaskContentTypeEnum.NOVELTY_SEARCH_REPORT_REVIEW_OF_NOVELTY.getType());
        stepList.add(AiTaskContentTypeEnum.NOVELTY_SEARCH_REPORT_REVIEW_OF_CREATIVE.getType());
        aiTaskManager.deleteTaskContentByContentType(taskId, stepList);
    }
} 
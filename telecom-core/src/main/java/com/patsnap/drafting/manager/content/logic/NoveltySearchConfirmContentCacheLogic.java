package com.patsnap.drafting.manager.content.logic;

import cn.hutool.core.util.StrUtil;
import com.patsnap.core.spring.boot.autoconfigure.web.entity.CommonResponse;
import com.patsnap.drafting.client.model.ainovelty.AiNoveltySearchResponse;
import com.patsnap.drafting.enums.task.AiTaskContentTypeEnum;
import com.patsnap.drafting.enums.task.AsyncTaskStatusEnum;
import com.patsnap.drafting.manager.ainoveltysearch.NoveltySearchManager;
import com.patsnap.drafting.manager.ainoveltysearch.NoveltySearchReportManager;
import com.patsnap.drafting.manager.aitask.AiTaskManager;
import com.patsnap.drafting.request.ainoveltysearch.AiSearchFinalResult;
import com.patsnap.drafting.request.ainoveltysearch.AiSearchResultDTO;
import static com.patsnap.drafting.enums.task.AiTaskContentTypeEnum.NOVELTY_SEARCH_AGENT_RESULT;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.patsnap.drafting.request.aitask.AiTaskReqDTO;
import com.patsnap.drafting.response.ainoveltysearch.NoveltySearchComparativeLiteratureResDTO;
import com.patsnap.drafting.response.ainoveltysearch.NoveltySearchReportTitleResponseDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import cn.hutool.core.collection.CollUtil;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

/**
 * AI查新确认相似特征
 */
@Component
public class NoveltySearchConfirmContentCacheLogic extends ContentCacheLogic {

    @Autowired
    private AiTaskManager aiTaskManager;

    @Autowired
    private NoveltySearchManager noveltySearchManager;
    @Override
    public void updateContent(String taskId, AiTaskContentTypeEnum contentType, Object result,
            Object[] args) {
        if (!(result instanceof List)) {
            return;
        }

        List<AiSearchFinalResult> resultList = (List<AiSearchFinalResult>) result;
        if (CollUtil.isEmpty(resultList)) {
            return;
        }
        AiNoveltySearchResponse aiSearchResult =  aiTaskManager.getTaskContent(taskId,
                NOVELTY_SEARCH_AGENT_RESULT);
        List<AiSearchFinalResult> finalResults = aiSearchResult.getFinalResult();
        if (CollUtil.isEmpty(finalResults)) {
            return;
        }
        Map<String, AiSearchFinalResult> updateMap = resultList.stream()
                .collect(Collectors.toMap(AiSearchFinalResult::getPatentId, Function.identity()));
        for (int i = 0; i < finalResults.size(); i++) {
            AiSearchFinalResult finalResult = finalResults.get(i);
            if (updateMap.containsKey(finalResult.getPatentId())) {
                finalResults.set(i, updateMap.get(finalResult.getPatentId()));
            }else{
                finalResult.setMostSimilar(false);
                finalResult.setSelected(false);
            }
        }
        aiSearchResult.setFinalResult(noveltySearchManager.updateFinalResultType(finalResults, taskId));
        Map<AiTaskContentTypeEnum, Object> contentMap = new EnumMap<>(AiTaskContentTypeEnum.class);
        contentMap.put(contentType, aiSearchResult.getFinalResult().stream().filter(AiSearchFinalResult::isSelected).toList());
        contentMap.put(AiTaskContentTypeEnum.NOVELTY_SEARCH_AGENT_RESULT, aiSearchResult);
        aiTaskManager.batchUpdateTaskContent(taskId, contentMap);
        aiTaskManager.updateTaskStatus(taskId, AsyncTaskStatusEnum.Running);
        List<String> stepList = new ArrayList<>();
        stepList.add(AiTaskContentTypeEnum.NOVELTY_SEARCH_REPORT_TITLE.getType());
        stepList.add(AiTaskContentTypeEnum.NOVELTY_SEARCH_REPORT_COMPARATIVE_LITERATURE.getType());
        stepList.add(AiTaskContentTypeEnum.NOVELTY_SEARCH_REPORT_REVIEW_OF_NOVELTY.getType());
        stepList.add(AiTaskContentTypeEnum.NOVELTY_SEARCH_REPORT_REVIEW_OF_CREATIVE.getType());
        aiTaskManager.deleteTaskContentByContentType(taskId, stepList);
    }
}
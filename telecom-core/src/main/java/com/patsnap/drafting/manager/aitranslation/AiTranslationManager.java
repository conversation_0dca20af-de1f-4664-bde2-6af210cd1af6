package com.patsnap.drafting.manager.aitranslation;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.amazonaws.services.sqs.AmazonSQS;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.patsnap.common.exception.InternalServerErrorException;
import com.patsnap.common.request.CorrelationIdHolder;
import com.patsnap.common.request.RoleIdsHolder;
import com.patsnap.common.request.SessionIdHolder;
import com.patsnap.common.request.UserIdHolder;
import com.patsnap.core.common.copilot.streaming.model.GptResponseDTO;
import com.patsnap.core.common.copilot.util.SimpleJsonMapper;
import com.patsnap.drafting.client.ComputeClient;
import com.patsnap.drafting.client.model.RdSensitiveWordsResDTO;
import com.patsnap.drafting.config.AiTranslationConfig;
import com.patsnap.drafting.config.WebHostConfig;
import com.patsnap.drafting.constants.Constant;
import com.patsnap.drafting.enums.common.OperateTypeEnum;
import com.patsnap.drafting.enums.task.AiTaskContentTypeEnum;
import com.patsnap.drafting.enums.task.AiTaskTypeEnum;
import com.patsnap.drafting.enums.task.AsyncTaskStatusEnum;
import com.patsnap.drafting.enums.task.TaskTypeEnum;
import com.patsnap.drafting.exception.BizException;
import com.patsnap.drafting.exception.errorcode.ContentErrorCodeEnum;
import com.patsnap.drafting.manager.FileManager;
import com.patsnap.drafting.manager.IdentityAccountManager;
import com.patsnap.drafting.manager.NoticeManager;
import com.patsnap.drafting.manager.aitask.AiTaskManager;
import com.patsnap.drafting.manager.aitranslation.factory.TranslationFullTextFactory;
import com.patsnap.drafting.manager.aitranslation.factory.TranslationParagraphFactory;
import com.patsnap.drafting.manager.aitranslation.factory.TranslationRewriteFactory;
import com.patsnap.drafting.manager.aitranslation.factory.TranslationTechTopicFactory;
import com.patsnap.drafting.manager.aitranslation.factory.TranslationTermFactory;
import com.patsnap.drafting.manager.aitranslation.operate.TranslationConstant;
import com.patsnap.drafting.manager.content.TaskContentManager;
import com.patsnap.drafting.model.aitask.AiTaskBO;
import com.patsnap.drafting.model.aitranslation.AiTransContextBo;
import com.patsnap.drafting.model.aitranslation.TranslationBO;
import com.patsnap.drafting.repository.aitask.entity.AnalyticsAiTaskContentPO;
import com.patsnap.drafting.repository.aitask.entity.AnalyticsAiTaskHistoryPO;
import com.patsnap.drafting.request.aitask.AiTaskCreateReqDTO;
import com.patsnap.drafting.request.aitask.AiTaskReqDTO;
import com.patsnap.drafting.request.aitranslation.AiTransCreateReqDTO;
import com.patsnap.drafting.request.aitranslation.AiTransExportReqDTO;
import com.patsnap.drafting.request.aitranslation.AiTransTaskCreateReqDTO;
import com.patsnap.drafting.request.aitranslation.AiTransTaskTestReqDTO;
import com.patsnap.drafting.request.aitranslation.ChangeTransReqDTO;
import com.patsnap.drafting.request.aitranslation.OriginalTransReqDTO;
import com.patsnap.drafting.request.aitranslation.TranslationTaskAsyncReqDTO;
import com.patsnap.drafting.response.aitranslation.AiTransResultResDTO;
import com.patsnap.drafting.response.aitranslation.TranslationKeywordResDTO;
import com.patsnap.drafting.response.aitranslation.TranslationTermDTO;
import com.patsnap.drafting.util.WordUtils;
import com.patsnap.spring.boot.autoconfigure.web.entity.CommonResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.entity.ContentType;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import reactor.core.publisher.Flux;

import java.io.ByteArrayOutputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2024/07/11
 */
@Slf4j
@Service
public class AiTranslationManager {
    
    private static final Set<String> LANGS = Set.of(Constant.CN, Constant.EN);
    public static final int CREDIT_COST = 50;

    private final AiTaskManager aiTaskManager;
    private final AiTranslationConfig aiTranslationConfig;
    private final AmazonSQS sqsClient;
    private final FileManager fileManager;
    private final NoticeManager noticeManager;
    private final WebHostConfig webHostConfig;
    private final TaskContentManager taskContentManager;
    private final TranslationTechTopicFactory translationTechTopicFactory;
    private final TranslationTermFactory translationTermFactory;
    private final TranslationFullTextFactory translationFullTextFactory;
    private final TranslationParagraphFactory translationParagraphFactory;
    private final TranslationRewriteFactory translationRewriteFactory;
    private final ComputeClient computeClient;
    private final IdentityAccountManager identityAccountManager;

    public AiTranslationManager(AiTaskManager aiTaskManager,
            AiTranslationConfig aiTranslationConfig, @Qualifier("aiTaskSqsClient") AmazonSQS sqsClient,
            FileManager fileManager, NoticeManager noticeManager, WebHostConfig webHostConfig,
            TaskContentManager taskContentManager,
            TranslationTechTopicFactory translationTechTopicFactory,
            TranslationTermFactory translationTermFactory,
            TranslationFullTextFactory translationFullTextFactory,
            TranslationParagraphFactory translationParagraphFactory,
            TranslationRewriteFactory translationRewriteFactory, ComputeClient computeClient,
            IdentityAccountManager identityAccountManager) {
        this.aiTaskManager = aiTaskManager;
        this.aiTranslationConfig = aiTranslationConfig;
        this.sqsClient = sqsClient;
        this.fileManager = fileManager;
        this.noticeManager = noticeManager;
        this.webHostConfig = webHostConfig;
        this.taskContentManager = taskContentManager;
        this.translationTechTopicFactory = translationTechTopicFactory;
        this.translationTermFactory = translationTermFactory;
        this.translationFullTextFactory = translationFullTextFactory;
        this.translationParagraphFactory = translationParagraphFactory;
        this.translationRewriteFactory = translationRewriteFactory;
        this.computeClient = computeClient;
        this.identityAccountManager = identityAccountManager;
    }

    /**
     * 1.检测用户输入文本的语言
     *
     * @param doc 文本
     * @return 语言
     */
    public String langDetect(String doc) {
        if (StrUtil.isEmpty(doc)) {
            return doc;
        }
        return computeClient.getCheckLang(doc);
    }

    public int getCreditNeedToCost(String input) {
        if (StrUtil.isEmpty(input)) {
            return CREDIT_COST;
        }
        double cost = Double.valueOf(input.length()) / Double.valueOf(aiTranslationConfig.getParagraphMaxLength());
        int size = (int) Math.ceil(cost);
        return size * CREDIT_COST;
    }

    @Transactional
    public String createTask(AiTransTaskCreateReqDTO reqDTO) {
        if (reqDTO == null ){
            return null;
        }
        AiTransCreateReqDTO createReqDTO = reqDTO.getContent();
        if (createReqDTO == null) {
            return reqDTO.getTaskId();
        }
        if (StrUtil.isNotEmpty(createReqDTO.getUserInput()) &&
                createReqDTO.getUserInput().length() > aiTranslationConfig.getTextMaxLength()) {
            throw new BizException(ContentErrorCodeEnum.INPUT_TOO_LONG);
        }
        if (!LANGS.contains(createReqDTO.getSourceLang()) || !LANGS.contains(createReqDTO.getTargetLang())) {
            return reqDTO.getTaskId();
        }
        AiTaskCreateReqDTO taskReq = getAiTaskCreateReqDTO(reqDTO,
                createReqDTO);
        return aiTaskManager.checkSensitiveWordsAndCreateTask(taskReq);
    }

    private AiTaskCreateReqDTO getAiTaskCreateReqDTO(AiTransTaskCreateReqDTO reqDTO,
            AiTransCreateReqDTO createReqDTO) {
        AiTaskCreateReqDTO taskReq = new AiTaskCreateReqDTO();
        taskReq.setType(AiTaskTypeEnum.AI_TRANSLATION.getType());
        taskReq.setTaskId(reqDTO.getTaskId());
        Map<String, Object> content = new HashMap<>();
        content.put(AiTaskContentTypeEnum.TRANSLATION_SOURCE_LANG.getType(), createReqDTO.getSourceLang());
        content.put(AiTaskContentTypeEnum.TRANSLATION_TARGET_LANG.getType(), createReqDTO.getTargetLang());
        content.put(AiTaskContentTypeEnum.USER_INPUT.getType(), createReqDTO.getUserInput());
        taskReq.setContent(content);
        taskReq.setCredit(getCreditNeedToCost(createReqDTO.getUserInput()));
        return taskReq;
    }


    public Flux<CommonResponse<GptResponseDTO<List<TranslationBO>>>> directTranslation(
            AiTaskReqDTO request) {
        AnalyticsAiTaskHistoryPO task = aiTaskManager.checkPermission(request.getTaskId());
        if (task == null) {
            return Flux.just(
                    CommonResponse.<GptResponseDTO<List<TranslationBO>>>builder().withStatus(false)
                            .withErrorMsg("task params not complete").build());
        }
        AiTransContextBo aiTransContext = getTaskDetail(task.getId());
        translationTechTopicFactory.generate(aiTransContext);
        translationTermFactory.generate(aiTransContext, null);
        return translationFullTextFactory.generate(aiTransContext);
    }

    /**
     * 提交异步翻译任务
     *
     * @param taskReq 任务信息
     */
    public void submitTranslationTask(TranslationTaskAsyncReqDTO taskReq) {
        aiTaskManager.checkPermission(taskReq.getTaskId());
        LambdaUpdateWrapper<AnalyticsAiTaskHistoryPO> updateWrapper = new LambdaUpdateWrapper<AnalyticsAiTaskHistoryPO>()
                .eq(AnalyticsAiTaskHistoryPO::getId, taskReq.getTaskId())
                .set(AnalyticsAiTaskHistoryPO::getAsyncStatus, AsyncTaskStatusEnum.Ready.getValue())
                .set(AnalyticsAiTaskHistoryPO::getTaskType, TaskTypeEnum.ASYNC.getValue());
        aiTaskManager.updateTask(updateWrapper);
        String json = SimpleJsonMapper.writeValue(
                new AiTaskBO(taskReq.getTaskId(), UserIdHolder.get(), SessionIdHolder.get(),
                        CollUtil.join(RoleIdsHolder.get(), ","), CorrelationIdHolder.get(), 0));
        sqsClient.sendMessage(aiTranslationConfig.getAiTaskSqsName(), json);
    }

    /**
     * 异步翻译
     *
     * @param taskId    任务id
     */
    public void translationAsync(String taskId) {
        AnalyticsAiTaskHistoryPO task = aiTaskManager.getTaskById(taskId);
        try {
            translationAsync(task);
        } catch (Exception e) {
            log.warn("AI异步翻译任务处理出现异常", e);
            LambdaUpdateWrapper<AnalyticsAiTaskHistoryPO> updateWrapper = new LambdaUpdateWrapper<AnalyticsAiTaskHistoryPO>()
                    .eq(AnalyticsAiTaskHistoryPO::getId, taskId)
                    .set(AnalyticsAiTaskHistoryPO::getAsyncStatus,
                            AsyncTaskStatusEnum.Failed.getValue());
            aiTaskManager.updateTask(updateWrapper);
        }
    }

    /**
     * 异步翻译
     *
     * @param task      翻译任务信息
     */
    public void translationAsync(AnalyticsAiTaskHistoryPO task) {
        // 校验任务参数是否完整
        if (checkTaskParamsInvalidUpdateStatus(task)) {
            return;
        }
        AiTransContextBo aiTransContext = getTaskDetail(task.getId());
        translationTechTopicFactory.generate(aiTransContext);
        translationTermFactory.generate(aiTransContext, null);
        List<TranslationBO> translations = translationParagraphFactory.generate(aiTransContext);
        updateTaskStatusBasedOnType(task, AsyncTaskStatusEnum.Complete);
        // 推送消息通知
        sendNotice(task.getTitle(), task.getId(), task.getCreatedBy());
    }

    public Boolean regenerate(AiTaskReqDTO request) {
        AnalyticsAiTaskHistoryPO task = aiTaskManager.checkPermission(request.getTaskId());
        List<String> subStepList = new ArrayList<>();
        subStepList.add(AiTaskContentTypeEnum.TRANSLATION_TECH_TOPIC.getType());
        subStepList.add(AiTaskContentTypeEnum.TRANSLATION_SUGGESTED_TERM.getType());
        subStepList.add(AiTaskContentTypeEnum.TRANSLATION_CUSTOM_TERM.getType());
        subStepList.add(AiTaskContentTypeEnum.TRANSLATION_RESULT.getType());
        return aiTaskManager.regenerate(task, subStepList);
    }

    public AiTransContextBo testModelTask(AiTransTaskTestReqDTO reqDTO){
        if (reqDTO == null ){
            return null;
        }
        AiTransTaskCreateReqDTO taskReq = new AiTransTaskCreateReqDTO();
        taskReq.setTaskId(UUID.randomUUID().toString());
        taskReq.setType(AiTaskTypeEnum.AI_TRANSLATION.getType());
        AiTransCreateReqDTO createReqDTO = new AiTransCreateReqDTO();
        createReqDTO.setSourceLang(reqDTO.getSourceLang());
        createReqDTO.setTargetLang(reqDTO.getTargetLang());
        createReqDTO.setUserInput(reqDTO.getUserInput());
        taskReq.setContent(createReqDTO);
        if (StrUtil.isNotEmpty(createReqDTO.getUserInput()) &&
                createReqDTO.getUserInput().length() > aiTranslationConfig.getTextMaxLength()) {
            throw new BizException(ContentErrorCodeEnum.INPUT_TOO_LONG);
        }
        AiTaskCreateReqDTO taskCreateReq = getAiTaskCreateReqDTO(taskReq,
                createReqDTO);
        taskCreateReq.setCredit(0);
        String taskId = aiTaskManager.checkSensitiveWordsAndCreateTask(taskCreateReq);
        AnalyticsAiTaskHistoryPO task = aiTaskManager.getTaskById(taskId);
        AiTransContextBo aiTransContext = getTaskDetail(task.getId());
        translationTermFactory.generate(aiTransContext, TranslationConstant.CUSTOM_TERM);
        translationParagraphFactory.generate(aiTransContext);
        return getTaskDetail(taskId);
    }

    private AiTransContextBo getTaskDetail(String taskId) {
        AiTransContextBo aiTransContext = new AiTransContextBo();
        aiTransContext.setTaskId(taskId);
        List<AnalyticsAiTaskContentPO> taskContentList = taskContentManager.getAllContentsByTaskId(
                taskId);

        taskContentList.forEach(content -> {
            switch (content.getContentType()) {
                case TranslationConstant.USER_INPUT:
                    aiTransContext.setInput(content.getContent());
                    break;
                case TranslationConstant.SOURCE_LANG:
                    aiTransContext.setSourceLang(content.getContent());
                    break;
                case TranslationConstant.TARGET_LANG:
                    aiTransContext.setTargetLang(content.getContent());
                    break;
                case TranslationConstant.TECH_TOPIC:
                    aiTransContext.setTechTopic(content.getContent());
                    break;
                case TranslationConstant.SUGGESTED_TERM:
                    aiTransContext.setSuggestedTerms(getTranslationKeyword(content.getContent()));
                    break;
                case TranslationConstant.CUSTOM_TERM:
                    aiTransContext.setCustomTerms(getTranslationKeyword(content.getContent()));
                    break;
                case TranslationConstant.TRANSLATION_RESULT:
                    aiTransContext.setResults(getTranslationResult(content.getContent()));
                    break;
                default:
                    break;
            }
        });
        return aiTransContext;
    }

    private List<TranslationBO> getTranslationResult(String content) {
        List<TranslationBO> translations = new ArrayList<>();
        if (StrUtil.isEmpty(content)) {
            return translations;
        }
        return JSONUtil.toList(content, TranslationBO.class);
    }

    private List<TranslationKeywordResDTO> getTranslationKeyword(String content) {
        List<TranslationKeywordResDTO> translations = new ArrayList<>();
        if (StrUtil.isEmpty(content)) {
            return translations;
        }
        TranslationTermDTO termDTO = JSONUtil.toBean(content, TranslationTermDTO.class);
        return termDTO.getTerms();
    }

    /**
     * 推送通知
     *
     * @param title  任务名称
     * @param taskId 任务id
     * @param userId 用户id
     */
    private void sendNotice(String title, String taskId, String userId) {
        String websiteHost = webHostConfig.getWebsiteHost();
        noticeManager.sendNotice(title, userId, "analytics_ai_translation_task_finish",
                websiteHost + aiTranslationConfig.getNoticeLink() + taskId);
    }

    /**
     * 获取翻译任务当前状态
     *
     * @param taskId 任务id
     * @return
     */
    @SuppressWarnings("unchecked")
    public AiTransResultResDTO getTranslationTaskStatus(String taskId) {
        AnalyticsAiTaskHistoryPO task = aiTaskManager.checkPermission(taskId);
        AiTransResultResDTO aiTransResultResDTO = new AiTransResultResDTO(task.getId(),
                task.getAsyncStatus());
        return aiTransResultResDTO;
    }

    /**
     * 获取翻译使用的术语表
     *
     * @param taskId 任务id
     * @return
     */
    public List<TranslationKeywordResDTO> getTranslationTaskKeywords(String taskId) {
        AiTransContextBo aiTransContext = getTaskDetail(taskId);
        translationTechTopicFactory.generate(aiTransContext);
        return translationTermFactory.generate(aiTransContext, null);
    }


    /**
     * 更改原文后流式输出翻译
     *
     * @param request 原文的index信息
     * @return
     */
    public Flux<CommonResponse<GptResponseDTO<String>>> streamTranslationWithOriginal(
            OriginalTransReqDTO request) {
        RdSensitiveWordsResDTO.RdData rdData = checkSensitiveWords(request.getOriginalText());
        if (rdData != null && rdData.hasSensitiveWords()) {
            throw new BizException(ContentErrorCodeEnum.SENSITIVE_WORDS_EXIST);
        }
        AnalyticsAiTaskHistoryPO task = aiTaskManager.checkPermission(request.getTaskId());
        AiTransContextBo aiTransContext = getTaskDetail(task.getId());
        aiTransContext.setOperateType(OperateTypeEnum.REGENERATE.getValue());
        translationTermFactory.generate(aiTransContext, TranslationConstant.CUSTOM_TERM);
        String originalText = request.getOriginalText();
        return translationRewriteFactory.generate(aiTransContext, request.getOriginalIndex(),
                originalText.replace("\n", ""));
    }

    /**
     * 更改单句翻译内容
     *
     * @param request 待更改的翻译结果信息
     */
    public void updateTranslation(ChangeTransReqDTO request) {
        AnalyticsAiTaskHistoryPO task = aiTaskManager.checkPermission(request.getTaskId());
        List<LinkedHashMap<String, Object>> translations = taskContentManager.getTaskContent(
                task.getId(), AiTaskContentTypeEnum.TRANSLATION_RESULT);
        LinkedHashMap<String, Object> replace = translations.get(request.getOriginalIndex());
        replace.put("translated_text", request.getTranslationText());
        aiTaskManager.updateTaskContent(task.getId(), AiTaskContentTypeEnum.TRANSLATION_RESULT,translations);
    }

    /**
     * 检查任务参数是否完整 若为异步任务, 若参数不完整, 则更新任务状态为Complete
     *
     * @param task 任务信息
     */
    private boolean checkTaskParamsInvalidUpdateStatus(AnalyticsAiTaskHistoryPO task) {
        if (task == null) {
            return true;
        }
        List<TranslationBO> translations = taskContentManager.getTaskContent(task.getId(),
                AiTaskContentTypeEnum.TRANSLATION_RESULT);
        if (translations != null) {
            updateTaskStatusBasedOnType(task, AsyncTaskStatusEnum.Complete);
        } else {
            updateTaskStatusBasedOnType(task, AsyncTaskStatusEnum.Running);
        }
        return translations != null;
    }

    private void updateTaskStatusBasedOnType(AnalyticsAiTaskHistoryPO task,
            AsyncTaskStatusEnum status) {
        LambdaUpdateWrapper<AnalyticsAiTaskHistoryPO> updateWrapper = new LambdaUpdateWrapper<AnalyticsAiTaskHistoryPO>()
                .eq(AnalyticsAiTaskHistoryPO::getId, task.getId())
                .set(AnalyticsAiTaskHistoryPO::getAsyncStatus, status.getValue());
        aiTaskManager.updateTask(updateWrapper);
    }

    /**
     * 检查敏感词
     *
     * @param description 文本内容
     * @return 敏感词信息
     */
    public RdSensitiveWordsResDTO.RdData checkSensitiveWords(String description) {
        if (StringUtils.isBlank(description)) {
            return null;
        }
        return computeClient.checkInputHasSensitiveWords(description);
    }


    /**
     * 导出AI翻译结果
     *
     * @param request 导出内容
     * @return
     */
    public String exportAiTranslation(AiTransExportReqDTO request) {
        // 创建新的Word文档
        try (XWPFDocument doc = new XWPFDocument();
             ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
            // 添加标题
            WordUtils.addTitle(doc, request.getTitle());
            // 添加翻译正文
            WordUtils.addText(doc, request.getTranslation());
            // 添加标题
            WordUtils.addTitle(doc, request.getTitle());
            // 添加原文正文
            WordUtils.addText(doc, request.getOriginal());
            // 将Word文档内容写入 ByteArrayOutputStream
            doc.write(baos);
            String s3key = "ai_lab/" + UserIdHolder.get() + "/" + System.currentTimeMillis() + "/"
                    + request.getTitle()
                    + ".docx";
            return fileManager.uploadFile2AmazonS3(baos.toByteArray(), s3key,
                    ContentType.MULTIPART_FORM_DATA);
        } catch (Exception e) {
            log.error("export ai translation error", e);
            throw new InternalServerErrorException();
        }
    }
}

package com.patsnap.drafting.manager.aispecification.content.strategy;

import com.patsnap.drafting.enums.common.OperateTypeEnum;
import com.patsnap.drafting.manager.AbstractGenerateStreamingContent;
import com.patsnap.drafting.manager.aispecification.content.base.ContentOperation;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class ContentGenerateHandlerFactory {

    private ApplicationContext applicationContext;
    private final Map<String, Class<? extends AbstractGenerateStreamingContent>> handlers;
    private final Map<String, Class<? extends ContentOperation>> operationTypeHandlers;


    @Autowired
    public ContentGenerateHandlerFactory(ApplicationContext applicationContext,
            Map<String, Class<? extends AbstractGenerateStreamingContent>> handlers,
            Map<String, Class<? extends ContentOperation>> operationTypeHandlers) {
        this.applicationContext = applicationContext;
        this.handlers = handlers;
        this.operationTypeHandlers = operationTypeHandlers;
    }

    public AbstractGenerateStreamingContent getHandler(String contentType) {
        Class<? extends AbstractGenerateStreamingContent> handlerClass = handlers.get(contentType);
        if (handlerClass != null) {
            return applicationContext.getBean(handlerClass);
        }
        return null;
    }

    public ContentOperation getHandler(OperateTypeEnum operateType) {
        Class<? extends ContentOperation> handlerClass = operationTypeHandlers.get(operateType.getValue());
        if (handlerClass != null) {
            return applicationContext.getBean(handlerClass);
        }
        return null;
    }
}
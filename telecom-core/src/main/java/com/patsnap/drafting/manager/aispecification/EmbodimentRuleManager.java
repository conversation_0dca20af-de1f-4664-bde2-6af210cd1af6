package com.patsnap.drafting.manager.aispecification;

import com.patsnap.drafting.constants.Constant;
import com.patsnap.drafting.request.aispecification.EmbodimentReplaceRule;

import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Service
public class EmbodimentRuleManager {

    private static final String RULE_MSG_CN_TEMPLATE = "{规则：权利要求{{num}}用关键词方向{{expandType}}，产生的关键词{{concept}}替换原关键词{{replacement}}}";
    private static final String RULE_MSG_EN_TEMPLATE = "{规则：权利要求{{num}}用关键词方向{{expandType}}，产生的关键词{{concept}}替换原关键词{{replacement}}}";
    private static final String PLACEHOLDER_NUM = "{num}";
    private static final String PLACEHOLDER_EXPAND_TYPE = "{expandType}";
    private static final String PLACEHOLDER_CONCEPT = "{concept}";
    private static final String PLACEHOLDER_REPLACEMENT = "{replacement}";

    /**
     * 获取规则信息
     *
     * @param rule 规则列表
     * @return 拼接后的规则字符串
     */
    public String fillInRules(List<EmbodimentReplaceRule> rule, String lang) {
        return rule.stream().map(r -> StringUtils.equalsIgnoreCase(lang, Constant.CN) ? RULE_MSG_CN_TEMPLATE
                : RULE_MSG_EN_TEMPLATE.replace(PLACEHOLDER_NUM, r.getNum())
                        .replace(PLACEHOLDER_EXPAND_TYPE, r.getExpandType())
                        .replace(PLACEHOLDER_CONCEPT, r.getConcept())
                        .replace(PLACEHOLDER_REPLACEMENT, r.getReplacement())).collect(Collectors.joining(","));
    }
}
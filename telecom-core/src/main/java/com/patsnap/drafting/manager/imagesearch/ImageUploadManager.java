package com.patsnap.drafting.manager.imagesearch;

import com.patsnap.common.request.UserIdHolder;
import com.patsnap.drafting.exception.BizException;
import com.patsnap.drafting.exception.errorcode.ImageUploadErrorCodeEnum;
import com.patsnap.drafting.manager.FileManager;
import com.patsnap.drafting.request.imagesearch.FileSignRequestDTO;
import com.patsnap.drafting.request.imagesearch.ImageUploadRequestDTO;
import com.patsnap.drafting.response.imagesearch.FileSignResponseDTO;
import com.patsnap.drafting.response.imagesearch.ImageUploadResponseDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.entity.ContentType;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.UUID;

import static com.patsnap.drafting.exception.errorcode.ImageUploadErrorCodeEnum.UPLOAD_FAILED;

/**
 * 图片上传管理器
 *
 * <AUTHOR> Assistant
 * @date 2024/12/19
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ImageUploadManager {
    
    private final FileManager fileManager;
    
    /**
     * 图片存储路径
     */
    public static final String DRAFTING_IMAGES = "ai_drafting/images";
    /**
     * 支持的图片文件类型
     */
    private static final Set<String> SUPPORTED_IMAGE_TYPES = Set.of(
            "jpg", "jpeg", "png"
    );
    /**
     * 最大文件大小：1MB
     */
    private static final long MAX_FILE_SIZE = 1024 * 1024;
    /**
     * 最大文件数量
     */
    private static final int MAX_FILE_COUNT = 20;

    /**
     * 上传多个图片文件
     *
     * @param files   文件列表
     * @param request 上传请求参数
     * @return 上传结果
     */
    public ImageUploadResponseDTO uploadMultipleImages(MultipartFile[] files, ImageUploadRequestDTO request) {
        log.info("开始上传多个图片文件，文件数量: {}, 用户ID: {}", files.length, UserIdHolder.get());

        // 参数校验
        validateUploadRequest(files);

        List<ImageUploadResponseDTO.UploadedFileInfo> uploadedFiles = new ArrayList<>();
        List<ImageUploadResponseDTO.FailedFileInfo> failedFiles = new ArrayList<>();

        // 处理每个文件
        for (MultipartFile file : files) {
            try {
                // 验证单个文件
                validateSingleFile(file);

                // 上传文件
                ImageUploadResponseDTO.UploadedFileInfo uploadedFile = uploadSingleImage(file, request);
                uploadedFiles.add(uploadedFile);

                log.info("文件上传成功: {}", file.getOriginalFilename());

            } catch (BizException e) {
                log.error("文件上传失败: {}, 业务异常: {}", file.getOriginalFilename(), e.getMessage());
                
                ImageUploadResponseDTO.FailedFileInfo failedFile = ImageUploadResponseDTO.FailedFileInfo.builder()
                        .originalName(file.getOriginalFilename())
                        .errorMessage(e.getMessage())
                        .errorCode(e.getNumericErrorCode())
                        .fileSize(file.getSize())
                        .build();
                failedFiles.add(failedFile);
                
            } catch (Exception e) {
                log.error("文件上传失败: {}, 系统异常", file.getOriginalFilename(), e);

                ImageUploadResponseDTO.FailedFileInfo failedFile = ImageUploadResponseDTO.FailedFileInfo.builder()
                        .originalName(file.getOriginalFilename())
                        .errorMessage("文件上传失败: " + e.getMessage())
                        .errorCode(UPLOAD_FAILED.getNumericErrCode())
                        .fileSize(file.getSize())
                        .build();
                failedFiles.add(failedFile);
            }
        }

        // 构建响应结果
        ImageUploadResponseDTO result = ImageUploadResponseDTO.builder()
                .uploadedFiles(uploadedFiles)
                .failedFiles(failedFiles)
                .totalCount(files.length)
                .successCount(uploadedFiles.size())
                .failedCount(failedFiles.size())
                .build();

        log.info("批量图片上传完成，总数: {}, 成功: {}, 失败: {}", 
                result.getTotalCount(), result.getSuccessCount(), result.getFailedCount());

        return result;
    }

    /**
     * 上传单个图片文件
     *
     * @param file    文件
     * @param request 请求参数
     * @return 上传成功的文件信息
     * @throws IOException 文件处理异常
     */
    private ImageUploadResponseDTO.UploadedFileInfo uploadSingleImage(MultipartFile file, ImageUploadRequestDTO request) throws IOException {
        try {
            // 获取文件字节数组
            byte[] fileBytes = file.getBytes();

            // 生成存储文件名和路径
            String originalFilename = file.getOriginalFilename();
            String fileExtension = FilenameUtils.getExtension(originalFilename);
            String storedFileName = generateStoredFileName(originalFilename, request);
            String s3Key = generateS3Key(storedFileName, request);

            // 确定文件内容类型
            ContentType contentType = determineContentType(fileExtension);

            // 调用 FileManager 上传到 S3
            String fileUrl = fileManager.uploadFile2AmazonS3(fileBytes, s3Key, contentType);

            if (StringUtils.isBlank(fileUrl)) {
                throw new BizException(UPLOAD_FAILED);
            }

            log.info("文件上传到S3成功，原始文件名: {}, S3键: {}, 访问URL: {}", originalFilename, s3Key, fileUrl);

            return ImageUploadResponseDTO.UploadedFileInfo.builder()
                    .fileUrl(fileUrl)
                    .fileSize(file.getSize())
                    .contentType(file.getContentType())
                    .s3Key(s3Key)
                    .uniqueId(UUID.randomUUID().toString())
                    .build();
                    
        } catch (IOException e) {
            log.error("读取文件内容失败: {}", file.getOriginalFilename(), e);
            throw new BizException(ImageUploadErrorCodeEnum.FILE_CORRUPTED);
        } catch (Exception e) {
            log.error("上传文件到S3失败: {}", file.getOriginalFilename(), e);
            throw new BizException(ImageUploadErrorCodeEnum.STORAGE_UNAVAILABLE);
        }
    }

    /**
     * 验证上传请求
     *
     * @param files   文件数组
     */
    private void validateUploadRequest(MultipartFile[] files) {
        if (files == null || files.length == 0) {
            throw new BizException(ImageUploadErrorCodeEnum.FILE_EMPTY);
        }

        if (files.length > MAX_FILE_COUNT) {
            throw new BizException(ImageUploadErrorCodeEnum.FILE_COUNT_EXCEEDED);
        }
    }

    /**
     * 验证单个文件
     *
     * @param file 文件
     */
    private void validateSingleFile(MultipartFile file) {
        if (file.isEmpty()) {
            throw new BizException(ImageUploadErrorCodeEnum.FILE_EMPTY);
        }

        if (file.getSize() > MAX_FILE_SIZE) {
            throw new BizException(ImageUploadErrorCodeEnum.FILE_TOO_LARGE);
        }

        String originalFilename = file.getOriginalFilename();
        if (StringUtils.isBlank(originalFilename)) {
            throw new BizException(ImageUploadErrorCodeEnum.FILE_NAME_INVALID);
        }

        String fileExtension = FilenameUtils.getExtension(originalFilename).toLowerCase();
        if (!SUPPORTED_IMAGE_TYPES.contains(fileExtension)) {
            throw new BizException(ImageUploadErrorCodeEnum.FILE_TYPE_NOT_SUPPORTED);
        }
    }

    /**
     * 生成存储文件名
     *
     * @param originalFilename 原始文件名
     * @param request          请求参数
     * @return 存储文件名
     */
    private String generateStoredFileName(String originalFilename, ImageUploadRequestDTO request) {
        String fileExtension = FilenameUtils.getExtension(originalFilename);
        String baseName = FilenameUtils.getBaseName(originalFilename);

        if (Boolean.TRUE.equals(request.getGenerateUniqueName())) {
            // 生成唯一文件名：前缀_时间戳_原始文件名.扩展名
            String timestamp = String.valueOf(System.currentTimeMillis());
            return String.format("%s_%s_%s.%s", 
                    StringUtils.defaultIfBlank(request.getFilePrefix(), "image"),
                    timestamp,
                    baseName,
                    fileExtension);
        } else {
            // 使用原始文件名
            return originalFilename;
        }
    }

    /**
     * 生成 S3 存储键
     *
     * @param storedFileName 存储文件名
     * @param request        请求参数
     * @return S3 键
     */
    private String generateS3Key(String storedFileName, ImageUploadRequestDTO request) {
        String userId = UserIdHolder.get();
        String folderPath = StringUtils.defaultIfBlank(request.getFolderPath(), DRAFTING_IMAGES);
        
        // 确保文件夹路径不以 / 开头和结尾
        folderPath = folderPath.replaceAll("^/+|/+$", "");
        
        return String.format("%s/%s/%s", folderPath, userId, storedFileName);
    }

    /**
     * 根据文件扩展名确定内容类型
     *
     * @param fileExtension 文件扩展名
     * @return 内容类型
     */
    private ContentType determineContentType(String fileExtension) {
        if (StringUtils.isBlank(fileExtension)) {
            return ContentType.IMAGE_PNG;
        }

        switch (fileExtension.toLowerCase()) {
            case "jpg":
            case "jpeg":
                return ContentType.IMAGE_JPEG;
            case "png":
                return ContentType.IMAGE_PNG;
            case "gif":
                return ContentType.create("image/gif");
            case "bmp":
                return ContentType.create("image/bmp");
            case "webp":
                return ContentType.create("image/webp");
            case "svg":
                return ContentType.create("image/svg+xml");
            default:
                return ContentType.IMAGE_PNG;
        }
    }

    /**
     * 批量签名文件
     *
     * @param request 签名请求参数
     * @return 签名结果
     */
    public FileSignResponseDTO signMultipleFiles(FileSignRequestDTO request) {
        log.info("开始批量签名文件，文件数量: {}, 用户ID: {}", request.getS3Keys().size(), UserIdHolder.get());

        List<FileSignResponseDTO.SignedFileInfo> signedFiles = new ArrayList<>();
        List<FileSignResponseDTO.FailedFileInfo> failedFiles = new ArrayList<>();

        // 处理每个S3Key
        for (String s3Key : request.getS3Keys()) {
            try {
                // 验证S3Key
                validateS3Key(s3Key);

                // 调用FileManager进行签名
                String signedUrl = fileManager.signFile(s3Key);

                if (StringUtils.isBlank(signedUrl)) {
                    // 签名失败
                    FileSignResponseDTO.FailedFileInfo failedFile = FileSignResponseDTO.FailedFileInfo.builder()
                            .s3Key(s3Key)
                            .errorMessage("文件签名失败")
                            .build();
                    failedFiles.add(failedFile);
                    
                    log.warn("文件签名失败: {}", s3Key);
                } else {
                    // 签名成功
                    FileSignResponseDTO.SignedFileInfo signedFile = FileSignResponseDTO.SignedFileInfo.builder()
                            .s3Key(s3Key)
                            .signedUrl(signedUrl)
                            .expireSeconds(FileManager.EXPIRE) // 使用FileManager中定义的过期时间
                            .build();
                    signedFiles.add(signedFile);
                    
                    log.info("文件签名成功: {}", s3Key);
                }

            } catch (BizException e) {
                log.error("文件签名失败: {}, 业务异常: {}", s3Key, e.getMessage());
                
                FileSignResponseDTO.FailedFileInfo failedFile = FileSignResponseDTO.FailedFileInfo.builder()
                        .s3Key(s3Key)
                        .errorMessage(e.getMessage())
                        .build();
                failedFiles.add(failedFile);
                
            } catch (Exception e) {
                log.error("文件签名失败: {}, 系统异常", s3Key, e);

                FileSignResponseDTO.FailedFileInfo failedFile = FileSignResponseDTO.FailedFileInfo.builder()
                        .s3Key(s3Key)
                        .errorMessage("文件签名失败: " + e.getMessage())
                        .build();
                failedFiles.add(failedFile);
            }
        }

        // 构建响应结果
        FileSignResponseDTO result = FileSignResponseDTO.builder()
                .signedFiles(signedFiles)
                .failedFiles(failedFiles)
                .totalCount(request.getS3Keys().size())
                .successCount(signedFiles.size())
                .failedCount(failedFiles.size())
                .build();

        log.info("批量文件签名完成，总数: {}, 成功: {}, 失败: {}", 
                result.getTotalCount(), result.getSuccessCount(), result.getFailedCount());

        return result;
    }

    /**
     * 验证S3Key格式
     *
     * @param s3Key S3文件键
     */
    private void validateS3Key(String s3Key) {
        if (StringUtils.isBlank(s3Key)) {
            throw new BizException(ImageUploadErrorCodeEnum.FILE_NAME_INVALID);
        }

        // 基本格式验证：不能包含特殊字符，不能以/开头
        if (s3Key.startsWith("/") || s3Key.contains("..") || s3Key.contains("//")) {
            throw new BizException(ImageUploadErrorCodeEnum.FILE_NAME_INVALID);
        }
    }
} 
package com.patsnap.drafting.manager.content.logic;

import com.patsnap.drafting.client.model.ainovelty.AiNoveltySearchResponse;
import com.patsnap.drafting.enums.task.AiTaskContentTypeEnum;
import com.patsnap.drafting.manager.ainoveltysearch.NoveltySearchManager;
import com.patsnap.drafting.manager.aitask.AiTaskManager;
import com.patsnap.drafting.request.ainoveltysearch.*;
import com.patsnap.drafting.response.ainoveltysearch.FeatureComparisonFinalResItem;
import com.patsnap.drafting.response.ainoveltysearch.FeatureComparisonResponseDTO;
import static com.patsnap.drafting.enums.task.AiTaskContentTypeEnum.NOVELTY_SEARCH_AGENT_RESULT;
import java.util.ArrayList;
import java.util.EnumMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;

/**
 * AI查新用户添加专利content处理逻辑
 */
@Component
public class NoveltySearchUserAddContentCacheLogic extends ContentCacheLogic {

    @Autowired
    private AiTaskManager aiTaskManager;

    @Autowired
    private NoveltySearchManager noveltySearchManager;

    @Override
    public void updateContent(String taskId, AiTaskContentTypeEnum contentType, Object result, Object[] args) {
        if (!(result instanceof FeatureComparisonResponseDTO)) {
            return;
        }
        FeatureComparisonRequestDTO featureComparisonRequestDTOS = (FeatureComparisonRequestDTO) args[0];
        FeatureComparisonResponseDTO resultDTO = (FeatureComparisonResponseDTO) result;
        AiNoveltySearchResponse aiSearchResult =  aiTaskManager.getTaskContent(taskId,
                NOVELTY_SEARCH_AGENT_RESULT);
        List<FeatureComparisonFinalResItem> finalRes = resultDTO.getFinalRes();
        if (CollUtil.isEmpty(finalRes)) {
            return;
        }
        List<AiSearchFinalResult> usefulResults = new ArrayList<>();
        finalRes.forEach(finalResItem -> {
            AiSearchFinalResult finalResult = new AiSearchFinalResult();
            BeanUtil.copyProperties(finalResItem, finalResult);
            finalResult.setUserAdded(true);
            if (CollUtil.isNotEmpty(finalResult.getFeatures())) {
                for (AiSearchFinalResultFeature feature : finalResult.getFeatures()) {
                    feature.setSimilar(feature.getScore() >= aiSearchResult.getCcThreshold());
                }
            }
            // 特征对比表页中新增专利时默认选中
            if (featureComparisonRequestDTOS.isAddPatentSelected()) {
                finalResult.setSelected(true);
            }
            usefulResults.add(finalResult);
        });
        usefulResults.addAll(aiSearchResult.getFinalResult());
        if (CollUtil.isEmpty(usefulResults)) {
            return;
        }

        aiSearchResult.setFinalResult(noveltySearchManager.updateFinalResultType(usefulResults, taskId));

        Map<AiTaskContentTypeEnum, Object> contentMap = new EnumMap<>(AiTaskContentTypeEnum.class);

        // 特征对比表页中新增专利, 且存在文献确认列表时则同步更新待确认列表
        if (featureComparisonRequestDTOS.isAddPatentSelected()) {
            List<LinkedHashMap<String, Object>> confirmMapList = aiTaskManager.getTaskContent(taskId, AiTaskContentTypeEnum.NOVELTY_SEARCH_FEATURE_CONFIRM);
            if (CollectionUtils.isNotEmpty(confirmMapList) && confirmMapList.size() < NoveltySearchManager.MAX_SELECTED_COUNT) {
                contentMap.put(AiTaskContentTypeEnum.NOVELTY_SEARCH_FEATURE_CONFIRM, aiSearchResult.getFinalResult().stream().filter(AiSearchFinalResult::isSelected).toList());
            }
        }
        contentMap.put(contentType, result);
        contentMap.put(AiTaskContentTypeEnum.NOVELTY_SEARCH_AGENT_RESULT, aiSearchResult);
        aiTaskManager.batchUpdateTaskContent(taskId, contentMap);
    }
}
package com.patsnap.drafting.manager.aitranslation.operate.impl;

import com.patsnap.core.common.copilot.gpt.enums.GPTModelEnum;
import com.patsnap.drafting.client.OpenAiClient;
import com.patsnap.drafting.config.AiTranslationConfig;
import com.patsnap.drafting.config.UrlConfig;
import com.patsnap.drafting.enums.prompt.PromptKeyEnum;
import com.patsnap.drafting.enums.prompt.ScenarioEnum;
import com.patsnap.drafting.manager.aitranslation.operate.TranslationConstant;
import com.patsnap.drafting.manager.aitranslation.operate.TranslationTechTopicService;
import com.patsnap.drafting.model.aitranslation.AiTransContextBo;

import org.springframework.stereotype.Component;

import com.google.common.collect.ImmutableMap;

@Component
public class TechTopicGptModel4TurboImpl extends TranslationBasicImpl implements
        TranslationTechTopicService {

    public TechTopicGptModel4TurboImpl(UrlConfig urlConfig,
            AiTranslationConfig aiTranslationConfig, OpenAiClient openAiClient) {
        super(urlConfig, aiTranslationConfig, openAiClient);
    }

    @Override
    public GPTModelEnum model() {
        return GPTModelEnum.GPT_MODEL_4_TURBO;
    }

    @Override
    public String generate(AiTransContextBo contextBo) {
        String sourceInput = contextBo.getInput();
        String sourceLang = contextBo.getSourceLang();
        sourceInput = sourceInput.substring(0, Math.min(sourceInput.length(), 500));
        return openAiClient.chatCompletions(PromptKeyEnum.TOPIC_EXTRACTION.getValue(), model(),
                ScenarioEnum.AI_TRANSLATION, String.class,
                ImmutableMap.of(TranslationConstant.LANG, sourceLang, TranslationConstant.INPUT,
                        sourceInput));
    }
}

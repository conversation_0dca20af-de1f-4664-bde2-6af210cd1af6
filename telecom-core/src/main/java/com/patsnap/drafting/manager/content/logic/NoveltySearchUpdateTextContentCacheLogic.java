package com.patsnap.drafting.manager.content.logic;

import com.patsnap.drafting.client.model.NoveltySearchSummaryResDTO;
import com.patsnap.drafting.enums.task.AiTaskContentTypeEnum;
import com.patsnap.drafting.manager.aitask.AiTaskManager;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * AI查新专利技术要点更新content处理逻辑
 */
@Component
public class NoveltySearchUpdateTextContentCacheLogic extends ContentCacheLogic {

    @Autowired
    private AiTaskManager aiTaskManager;

    @Override
    public void updateContent(String taskId, AiTaskContentTypeEnum contentType, Object result, Object[] args) {
        if (!(result instanceof String)) {
            return;
        }
        NoveltySearchSummaryResDTO.Data summary = aiTaskManager.getTaskContent(taskId, AiTaskContentTypeEnum.NOVELTY_SEARCH_INPUT_SUMMARY);
        summary.setTechSolution(result.toString());
        aiTaskManager.updateTaskContent(taskId, contentType, summary);
        aiTaskManager.deleteTaskContentByContentType(taskId,
                List.of(AiTaskContentTypeEnum.NOVELTY_SEARCH_TECH_FEATURE.getType()
                ));
    }
}
package com.patsnap.drafting.manager.aitranslation.factory;

import com.patsnap.core.common.copilot.gpt.enums.GPTModelEnum;
import com.patsnap.drafting.manager.aitranslation.operate.TranslationTermService;
import com.patsnap.drafting.model.aitranslation.AiTransContextBo;
import com.patsnap.drafting.response.aitranslation.TranslationKeywordResDTO;
import com.patsnap.drafting.response.aitranslation.TranslationTermDTO;

import static com.patsnap.drafting.manager.aitranslation.operate.TranslationConstant.CUSTOM_TERM;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.google.common.collect.Sets;

@Component
@Slf4j
public class TranslationTermFactory {

    private Set<String> models = Sets.newHashSet(
            GPTModelEnum.GPT_MODEL_4_TURBO.getModelName(),
            CUSTOM_TERM);

    @Autowired
    private List<TranslationTermService> translationTermServices;

    public List<TranslationKeywordResDTO> generate(AiTransContextBo contextBo, String onlyModel) {
        List<TranslationKeywordResDTO> result = new ArrayList<>();

        for (TranslationTermService translationTermService : translationTermServices) {
            if (isModelValid(translationTermService, onlyModel)) {
                try {
                    TranslationTermDTO res = translationTermService.generate(contextBo);
                    result.addAll(res.getTerms());
                    updateContextBo(contextBo, translationTermService, res.getTerms());
                }catch (Exception e) {
                    log.error("generate translation term error", e);
                }

            }
        }
        return result;
    }

    private boolean isModelValid(TranslationTermService translationTermService, String onlyModel) {
        return onlyModel == null
                ? models.contains(translationTermService.model())
                : translationTermService.model().equals(onlyModel);
    }

    private void updateContextBo(AiTransContextBo contextBo,
            TranslationTermService translationTermService,
            List<TranslationKeywordResDTO> terms) {
        if (translationTermService.model().equals(CUSTOM_TERM)) {
            contextBo.setCustomTerms(terms);
        } else {
            contextBo.setSuggestedTerms(terms);
        }
    }

}

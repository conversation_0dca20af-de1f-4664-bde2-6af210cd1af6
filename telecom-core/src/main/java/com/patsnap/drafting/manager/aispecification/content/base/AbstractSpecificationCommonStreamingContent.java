package com.patsnap.drafting.manager.aispecification.content.base;

import com.patsnap.core.common.copilot.gpt.enums.GPTModelEnum;
import com.patsnap.drafting.client.ComputeClient;
import com.patsnap.drafting.client.model.AiSpecificationComputeData;
import com.patsnap.drafting.client.model.AiSpecificationComputeReqDTO;
import com.patsnap.drafting.config.UrlConfig;
import com.patsnap.drafting.enums.aispecification.JurisdictionEnum;
import com.patsnap.drafting.enums.prompt.ScenarioEnum;
import com.patsnap.drafting.manager.AbstractGenerateStreamingContent;
import com.patsnap.drafting.manager.aispecification.SpecificationManager;
import com.patsnap.drafting.manager.aitask.AiTaskManager;
import com.patsnap.drafting.model.base.StreamingModelBO;
import com.patsnap.drafting.request.GenerateContentRequestDTO;
import com.patsnap.drafting.response.aispecification.SpecificationRdResDTO;

import org.springframework.beans.factory.annotation.Autowired;

import com.patsnap.core.common.copilot.streaming.handling.StreamingChatLanguageModel;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 流式返回，慢慢吐出数据
 *
 * @param <T>
 */
@Slf4j
public abstract class AbstractSpecificationCommonStreamingContent<T> extends AbstractGenerateStreamingContent<T> {

    @Autowired
    private ComputeClient computeClient;

    @Autowired
    private SpecificationManager specificationManager;

    protected AbstractSpecificationCommonStreamingContent(AiTaskManager aiTaskManager, UrlConfig urlConfig) {
        super(aiTaskManager, urlConfig);
    }

    @Override
    protected StreamingModelBO getStreamingModelBO(GenerateContentRequestDTO generateContentRequestDTO) {
        AiSpecificationComputeData computeReqData = specificationManager.getCommonComputeReqData(
                generateContentRequestDTO.getTaskId());
        AiSpecificationComputeReqDTO aiSpecificationComputeReqDTO = AiSpecificationComputeReqDTO.builder()
                .function(getFunction()).data(computeReqData).lang(StringUtil.toLowerCase(
                        JurisdictionEnum.fromName(computeReqData.getJurisdiction()).getValue())).build();
        SpecificationRdResDTO specificationPrompt = computeClient.getSpecificationPrompt(aiSpecificationComputeReqDTO);
        StreamingModelBO streamingModelBO = new StreamingModelBO();
        streamingModelBO.setPrompt(specificationPrompt.getPrompt());
        StreamingChatLanguageModel model = streamingModelBuilder.build(
                GPTModelEnum.getGptModelEnum(specificationPrompt.getModelName()),
                ScenarioEnum.AI_SPECIFICATION.getValue());
        streamingModelBO.setModel(model);
        streamingModelBO.setExtraData(specificationPrompt.getExtraData());
        return streamingModelBO;
    }

    public abstract String getFunction();


}

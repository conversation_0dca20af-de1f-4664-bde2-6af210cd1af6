package com.patsnap.drafting.manager.track;

import com.google.common.collect.Sets;
import com.patsnap.common.request.UserIdHolder;
import com.patsnap.core.common.request.RoleIdsHolder;
import com.patsnap.core.common.track.CommonTrackClient;
import com.patsnap.core.common.track.ModuleType;
import com.patsnap.core.common.track.ProductType;
import com.patsnap.core.common.track.TrackType;
import com.patsnap.drafting.enums.packageinfo.MainPackageEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.util.*;


/**
 * <AUTHOR>
 * @date 2022/4/25 14:32
 */
@Service
@Slf4j
public class TrackingManager {

    @Autowired
    @Qualifier("telecomTrackThreadPool")
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Autowired
    private CommonTrackClient commonTrackClient;

    private static final String EVENT_PROPERTIES = "event_properties";
    private static final String EVENT_TYPE = "event_type";
    private static final String EVENT_DETAIL = "event_detail";
    private static final String SOURCE_TYPE = "source_type";
    private static final String OTHER_PARAM = "other_param";

    public void addTracking(ModuleType moduleType, String eventType, String sourceType, String otherParam,
                            String eventDetailStr) {
        sendTrackingTask(moduleType, eventType, sourceType, otherParam, eventDetailStr,
                "Failed to submit competitor monitor tracking");
    }

    /**
     * @param moduleType
     * @param eventType
     * @param sourceType
     * @param otherParam
     * @param eventDetailStr
     * @param errorMessage
     */
    private void sendTrackingTask(ModuleType moduleType, String eventType, String sourceType, String otherParam,
                                  String eventDetailStr, String errorMessage) {
        try {
            threadPoolTaskExecutor.execute(() -> {
                sendTracking(moduleType, eventType, sourceType, otherParam, eventDetailStr);
            });
        } catch (Exception e) {
            log.error(errorMessage, e);
        }
    }

    /**
     * Basic method to send tracking message
     */
    private void sendTracking(ModuleType moduleType, String eventType, String sourceType, String otherParam,
                              String eventDetailStr) {
        Map<String, Object> message = new HashMap<>();
        try {
            Map<String, Object> eventProperties = new HashMap<>();
            eventProperties.put(SOURCE_TYPE, sourceType);
            if (StringUtils.isNotBlank(otherParam)) {
                eventProperties.put(OTHER_PARAM, otherParam.substring(0, Math.min(120, otherParam.length())));
            }
            try {
                if (StringUtils.isNotBlank(eventDetailStr)) {
                    Map<String, Object> eventDetail = com.alibaba.fastjson.JSON.parseObject(eventDetailStr, Map.class);
                    if (eventDetail == null) {
                        eventDetail = new HashMap<>();
                    }
                    //埋点添加用户业务使用场景
                    eventDetail.put("business_type", getUserBusinessType());
                    eventDetailStr = com.alibaba.fastjson.JSON.toJSONString(eventDetail);
                } else {
                    eventDetailStr = "";
                }
            } catch (Exception e) {
                log.error("Failed to parse event detail: {}", eventDetailStr, e);
            }
            eventProperties.put(EVENT_DETAIL, eventDetailStr.substring(0, Math.min(2000, eventDetailStr.length())));

            message.put(EVENT_TYPE, eventType);
            message.put(EVENT_PROPERTIES, eventProperties);
            String userId = StringUtils.isBlank(UserIdHolder.get()) ? "0000000000000000" : UserIdHolder.get();
            commonTrackClient.track(TrackType.EVENT, userId, message, ProductType.EUREKA, moduleType);
        } catch (Exception e) {
            log.error("Tracking error message: {}", message, e);
        }
    }

    /**
     * 获取当前用户的业务套餐类型
     *
     * @return
     */
    public static String getUserBusinessType() {
        Set<String> businessTypes = Sets.newLinkedHashSet();
        List<String> roleIds = RoleIdsHolder.get();
        if (CollectionUtils.isEmpty(roleIds)) {
            return "";
        }
        Arrays.stream(MainPackageEnum.values()).forEach(mainPackageEnum -> {
            if (roleIds.contains(mainPackageEnum.getFeatureId())) {
                businessTypes.add(mainPackageEnum.getProduct().getDisplay());
            }
        });
        return String.join(",", businessTypes);
    }

}
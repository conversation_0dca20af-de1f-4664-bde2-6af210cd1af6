package com.patsnap.drafting.manager.aitranslation.operate.impl;

import com.patsnap.core.common.copilot.gpt.enums.GPTModelEnum;
import com.patsnap.core.common.copilot.streaming.handler.SplitListJsonOutputParser;
import com.patsnap.drafting.client.OpenAiClient;
import com.patsnap.drafting.config.AiTranslationConfig;
import com.patsnap.drafting.config.UrlConfig;
import com.patsnap.drafting.constants.Constant;
import com.patsnap.drafting.enums.prompt.PromptKeyEnum;
import com.patsnap.drafting.enums.prompt.ScenarioEnum;
import com.patsnap.drafting.manager.aitranslation.operate.TermContextUtil;
import com.patsnap.drafting.manager.aitranslation.operate.TranslationConstant;
import com.patsnap.drafting.manager.aitranslation.operate.TranslationParagraphService;
import com.patsnap.drafting.model.aitranslation.AiTransContextBo;
import com.patsnap.drafting.model.aitranslation.TranslationBO;
import com.patsnap.drafting.response.aitranslation.TranslationKeywordResDTO;
import com.patsnap.drafting.util.ParagraphUtils;
import com.patsnap.drafting.util.RetryUtil;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.ImmutableMap;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class ParagraphTranslationGptImpl extends TranslationBasicImpl implements
        TranslationParagraphService {

    @Autowired
    private ParagraphGptModel4TurboImpl paragraphGptModel4Turbo;

    private static GPTModelEnum GPT_MODEL = GPTModelEnum.TRANSLATION_GPT;

    public ParagraphTranslationGptImpl(UrlConfig urlConfig, AiTranslationConfig aiTranslationConfig,
            OpenAiClient openAiClient) {
        super(urlConfig, aiTranslationConfig, openAiClient);
    }

    @Override
    public String model() {
        return GPT_MODEL.getModelName();
    }

    @Override
    public List<TranslationBO> generate(AiTransContextBo context) {
        String sourceInput = context.getInput();
        sourceInput = ParagraphUtils.addParagraphPrefix(sourceInput);
        List<String> preParagraphs = ParagraphUtils.splitText(sourceInput,
                aiTranslationConfig.getParagraphMaxLength());
        List<String> paragraphs = new ArrayList<>(preParagraphs.size());
        StringBuilder realInput = new StringBuilder();
        for (String paragraph : preParagraphs) {
            if (!paragraph.startsWith(ParagraphUtils.PARAGRAPH_PREFIX)) {
                paragraph = ParagraphUtils.PARAGRAPH_PREFIX + paragraph;
            }
            paragraphs.add(paragraph);
            realInput.append(paragraph);
        }
        List<Integer> paragraphIndexes = ParagraphUtils.getParagraphIndexes(realInput.toString());
        List<TranslationBO> translations = translateByParagraph(paragraphs, context);
        // 根据标识符计算每个句子所属的段落index
        ParagraphUtils.addParagraphIndexPlus(translations, paragraphIndexes);
        return translations;
    }

    /**
     * 段落翻译
     *
     * @param paragraphs 待翻译的段落
     * @return
     */
    private @NotNull List<TranslationBO> translateByParagraph(List<String> paragraphs,
            AiTransContextBo context) {
        List<TranslationBO> translations = new ArrayList<>();
        //需要移除去最后一段的回车
        for (String paragraph : paragraphs) {
            if (StringUtils.isBlank(paragraph)) {
                continue;
            }
            translations.addAll(getSentenceTrans(paragraph, context));
        }
        return translations;
    }

    private List<TranslationBO> getSentenceTrans(String paragraph, AiTransContextBo context) {
        String translation = null;
        try {
            translation = RetryUtil.retry(() -> getTranslationByLang(paragraph, context));
        } catch (Exception e) {
            log.error("段落翻译失败, paragraph： {}", paragraph);
        }
        if (StrUtil.isEmpty(context.getTechTopic())) {
            context.setTechTopic("");
        }
        if (StringUtils.isBlank(translation)) {
            List<TranslationBO> backSentenceTrans = paragraphGptModel4Turbo.translateByParagraph(List.of(paragraph),
                    context.getTargetLang(), context.getTechTopic(), "");
            if (CollUtil.isNotEmpty(backSentenceTrans)) {
                return backSentenceTrans;
            }
            return getEmptySentenceTrans(paragraph);
        }
        // 解析翻译结果为结构化数据
        SplitListJsonOutputParser<List<TranslationBO>> parser = new SplitListJsonOutputParser<>(
                TranslationConstant.LINE_SEPARATOR_REGEX,
                TranslationConstant.FIELD_SEPARATOR_REGEX);
        List<TranslationBO> sentenceTrans = parser.convertContent(translation,new TypeReference<>() {});
        return sentenceTrans;
    }

    private List<TranslationBO> getEmptySentenceTrans(String paragraph) {
        List<TranslationBO> sentenceTrans = new ArrayList<>();
        TranslationBO translationBO = new TranslationBO();
        paragraph = paragraph.replace(ParagraphUtils.PARAGRAPH_PREFIX, "");
        translationBO.setSrcText(paragraph);
        translationBO.setTranslatedText("");
        sentenceTrans.add(translationBO);
        return sentenceTrans;
    }

    private String getTranslationByLang(String input, AiTransContextBo context) {
        List<TranslationKeywordResDTO> customTerms = context.getCustomTerms();
        Map<String, String> customMap = new HashMap<>();
        for (TranslationKeywordResDTO term : customTerms) {
            term.getOriginal().forEach(original -> {
                customMap.put(original, term.getTranslation());
            });
        }
        input = TermContextUtil.addKeywordsToInput(input, context.getSourceLang(), customMap);
        PromptKeyEnum promptKeyEnum =
                Constant.CN.equals(context.getTargetLang()) ? PromptKeyEnum.SELF_MODEL_TRANSLATION_EN2CN
                        : PromptKeyEnum.SELF_MODEL_TRANSLATION_CN2EN;
        String translation = openAiClient.chatCompletionsWithTimeout(promptKeyEnum.getValue(),
                GPTModelEnum.TRANSLATION_GPT,
                ScenarioEnum.AI_TRANSLATION, aiTranslationConfig.getHttpRequestTimeout(),
                new TypeReference<>() {},
                ImmutableMap.of(TranslationConstant.INPUT, input));
        if (CollUtil.isNotEmpty(customMap) && StringUtils.isNotBlank(translation)) {
            translation = TermContextUtil.removeKeywordsFromTranslation(translation, customMap,
                    context.getSourceLang(), context.getTargetLang());
        }
        return translation;
    }
}

package com.patsnap.drafting.manager.aispecification.content;

import com.patsnap.core.common.copilot.gpt.enums.GPTModelEnum;
import com.patsnap.core.common.copilot.streaming.handling.StreamingChatLanguageModel;
import com.patsnap.core.common.copilot.streaming.model.GptResponseDTO;
import com.patsnap.drafting.client.ComputeClient;
import com.patsnap.drafting.client.model.AiSpecificationComputeData;
import com.patsnap.drafting.client.model.AiSpecificationComputeReqDTO;
import com.patsnap.drafting.config.UrlConfig;
import com.patsnap.drafting.enums.aispecification.JurisdictionEnum;
import com.patsnap.drafting.enums.prompt.ScenarioEnum;
import com.patsnap.drafting.enums.task.AiTaskContentTypeEnum;
import com.patsnap.drafting.manager.AbstractGenerateStreamingContent;
import com.patsnap.drafting.manager.aispecification.handler.DisclosureExtractEmbodimentHandler;
import com.patsnap.drafting.manager.aitask.AiTaskManager;
import com.patsnap.drafting.model.aispecification.SpecificationDisclosureExtractEmbodimentBO;
import com.patsnap.drafting.model.aispecification.SpecificationInitializationBO;
import com.patsnap.drafting.model.base.StreamingModelBO;
import com.patsnap.drafting.request.GenerateContentRequestDTO;
import com.patsnap.drafting.response.aispecification.SpecificationRdResDTO;
import com.patsnap.spring.boot.autoconfigure.web.entity.CommonResponse;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

/**
 * AI专利说明书-交底书提取实施例内容
 *
 * <AUTHOR>
 * @Date 2025/4/28 17:00
 */
@Component
@Slf4j
public class SpecificationDisclosureExtractEmbodimentContent extends AbstractGenerateStreamingContent<SpecificationDisclosureExtractEmbodimentBO> {

    @Autowired
    private ComputeClient computeClient;

    // 算法接口的功能函数key
    // https://confluence.zhihuiya.com/pages/viewpage.action?pageId=230993512
    private static final String GENERATE_FUNCTION = "disclosure_extract_embodiment";

    protected SpecificationDisclosureExtractEmbodimentContent(AiTaskManager aiTaskManager, UrlConfig urlConfig) {
        super(aiTaskManager, urlConfig);
    }

    @Override
    public String getContentType() {
        return AiTaskContentTypeEnum.SPECIFICATION_EMBODIMENT_EXTRACT.getType();
    }

    @Override
    protected StreamingModelBO getStreamingModelBO(GenerateContentRequestDTO generateContentRequestDTO) {
        SpecificationInitializationBO initializationBO = aiTaskManager.getTaskContent(generateContentRequestDTO.getTaskId(), AiTaskContentTypeEnum.INITIALIZATION);
        //用户输入的交底书
        String inputDisclosure = initializationBO.getDisclosure();
        String jurisdiction = initializationBO.getJurisdiction();
        String lang = StringUtil.toLowerCase(JurisdictionEnum.fromName(jurisdiction).getValue());
        AiSpecificationComputeData computeData = new AiSpecificationComputeData();
        computeData.setDisclosureText(inputDisclosure);
        //get prompt
        AiSpecificationComputeReqDTO aiSpecificationComputeReqDTO = AiSpecificationComputeReqDTO.builder().function(GENERATE_FUNCTION).data(computeData).lang(lang).build();
        SpecificationRdResDTO specificationPrompt = computeClient.getSpecificationPrompt(aiSpecificationComputeReqDTO);
        StreamingModelBO streamingModelBO = new StreamingModelBO();
        streamingModelBO.setPrompt(specificationPrompt.getPrompt());
        StreamingChatLanguageModel model = streamingModelBuilder.build(GPTModelEnum.getGptModelEnum(specificationPrompt.getModelName()), ScenarioEnum.AI_SPECIFICATION.getValue());
        streamingModelBO.setModel(model);
        return streamingModelBO;
    }

    @Override
    protected Flux<CommonResponse<GptResponseDTO<SpecificationDisclosureExtractEmbodimentBO>>> getContent(
            StreamingModelBO streamingModelBO) {
        String prompt = streamingModelBO.getPrompt();
        return Flux.create(sink -> {
            // 从LLM拿到的数据需要按照 $$$ 进行拆分，变成List<Object>，然后组装数据给到前端
            DisclosureExtractEmbodimentHandler handler = new DisclosureExtractEmbodimentHandler(sink, prompt);
            streamingModelBO.getModel().generate(prompt, handler);
        });
    }
}

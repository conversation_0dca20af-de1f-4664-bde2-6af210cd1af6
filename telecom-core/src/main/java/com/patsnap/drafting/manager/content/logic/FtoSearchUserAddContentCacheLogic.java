package com.patsnap.drafting.manager.content.logic;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.patsnap.drafting.enums.task.AiTaskContentTypeEnum;
import com.patsnap.drafting.manager.aiftosearch.FtoSearchManager;
import com.patsnap.drafting.manager.aitask.AiTaskManager;
import com.patsnap.drafting.request.ainoveltysearch.AiSearchFinalResult;
import com.patsnap.drafting.request.ainoveltysearch.AiSearchFinalResultFeature;
import com.patsnap.drafting.request.ainoveltysearch.AiSearchResultDTO;
import com.patsnap.drafting.response.ainoveltysearch.FeatureComparisonFinalResItem;
import com.patsnap.drafting.response.ainoveltysearch.FeatureComparisonResponseDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.util.ArrayList;
import java.util.EnumMap;
import java.util.List;
import java.util.Map;

import static com.patsnap.drafting.enums.task.AiTaskContentTypeEnum.FTO_SEARCH_AGENT_RESULT;

/**
 * AI查新用户添加专利content处理逻辑
 */
@Component
public class FtoSearchUserAddContentCacheLogic extends ContentCacheLogic {

    @Autowired
    private AiTaskManager aiTaskManager;

    @Autowired
    private FtoSearchManager ftoSearchManager;

    @Override
    public void updateContent(String taskId, AiTaskContentTypeEnum contentType, Object result, Object[] args) {
        if (!(result instanceof FeatureComparisonResponseDTO)) {
            return;
        }
        FeatureComparisonResponseDTO resultDTO = (FeatureComparisonResponseDTO) result;
        AiSearchResultDTO aiSearchResult =  aiTaskManager.getTaskContent(taskId,
                FTO_SEARCH_AGENT_RESULT);
        List<FeatureComparisonFinalResItem> finalRes = resultDTO.getFinalRes();
        if (CollUtil.isEmpty(finalRes)) {
            return;
        }
        List<AiSearchFinalResult> usefulResults = new ArrayList<>();
        finalRes.forEach(finalResItem -> {
            AiSearchFinalResult finalResult = new AiSearchFinalResult();
            BeanUtil.copyProperties(finalResItem, finalResult);
            finalResult.setUserAdded(true);
            if (CollUtil.isNotEmpty(finalResult.getFeatures())) {
                for (AiSearchFinalResultFeature feature : finalResult.getFeatures()) {
                    feature.setSimilar(feature.getScore() >= aiSearchResult.getCcThreshold());
                }
            }
            usefulResults.add(finalResult);
        });
        usefulResults.addAll(aiSearchResult.getFinalResult());
        if (CollUtil.isEmpty(usefulResults)) {
            return;
        }
        aiSearchResult.setFinalResult(ftoSearchManager.updateFinalResultDetail(usefulResults, taskId));
        Map<AiTaskContentTypeEnum, Object> contentMap = new EnumMap<>(AiTaskContentTypeEnum.class);
        contentMap.put(contentType, result);
        contentMap.put(AiTaskContentTypeEnum.FTO_SEARCH_AGENT_RESULT, aiSearchResult);
        aiTaskManager.batchUpdateTaskContent(taskId, contentMap);
    }
}
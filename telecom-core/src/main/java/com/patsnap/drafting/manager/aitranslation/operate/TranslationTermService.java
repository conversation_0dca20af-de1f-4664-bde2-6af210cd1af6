package com.patsnap.drafting.manager.aitranslation.operate;

import com.patsnap.drafting.model.aitranslation.AiTransContextBo;
import com.patsnap.drafting.response.aitranslation.TranslationKeywordResDTO;
import com.patsnap.drafting.response.aitranslation.TranslationTermDTO;

import java.util.List;
import java.util.Map;

public interface TranslationTermService {

    String model();

    TranslationTermDTO generate(AiTransContextBo contextBo);
}

package com.patsnap.drafting.manager.aitranslation.operate;

import com.patsnap.drafting.model.aitranslation.AiTransContextBo;
import com.patsnap.drafting.model.aitranslation.TranslationBO;
import com.patsnap.drafting.request.aitask.AiTaskReqDTO;
import java.util.List;
import java.util.Map;

import reactor.core.publisher.Flux;

public interface TranslationParagraphService {

    String model();

    List<TranslationBO> generate(AiTransContextBo contextBo);
}

package com.patsnap.drafting.manager.aispecification;

import com.patsnap.drafting.enums.task.AiTaskContentTypeEnum;
import com.patsnap.drafting.util.RedissonUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static com.patsnap.drafting.util.RedisKeyUtil.buildRedisKey;

/**
 * 算法任务状态管理器
 * 专门负责管理算法任务在Redis中的状态信息
 * 
 * <AUTHOR> Assistant
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AlgorithmTaskStatusManager {

    private final RedissonClient redissonClient;
    private final RedissonUtils redissonUtils;

    // Redis Key 前缀
    private static final String ALGORITHM_TASK_STATUS_PREFIX = "ALGORITHM:TASK:STATUS";
    private static final String ALGORITHM_TASK_RESULT_PREFIX = "ALGORITHM:TASK:RESULT";
    private static final String ALGORITHM_TASK_ERROR_PREFIX = "ALGORITHM:TASK:ERROR";
    
    public static final String STATUS_PROCESSING = "PROCESSING";
    public static final String STATUS_COMPLETED = "COMPLETED";
    public static final String STATUS_FAILED = "FAILED";
    
    // 默认过期时间：24小时
    private static final long DEFAULT_EXPIRE_TIME = 24;
    private static final TimeUnit DEFAULT_TIME_UNIT = TimeUnit.HOURS;

    /**
     * 任务状态信息
     */
    public static class TaskStatusInfo {
        private String status;
        private String contentType;
        private Long startTime;
        private Long updateTime;
        private String errorMessage;
        private Object additionalInfo;

        // Constructors
        public TaskStatusInfo() {}

        public TaskStatusInfo(String status, String contentType) {
            this.status = status;
            this.contentType = contentType;
            this.startTime = System.currentTimeMillis();
            this.updateTime = System.currentTimeMillis();
        }

        // Getters and Setters
        public String getStatus() { return status; }
        public void setStatus(String status) { 
            this.status = status; 
            this.updateTime = System.currentTimeMillis();
        }

        public String getContentType() { return contentType; }
        public void setContentType(String contentType) { this.contentType = contentType; }

        public Long getStartTime() { return startTime; }
        public void setStartTime(Long startTime) { this.startTime = startTime; }

        public Long getUpdateTime() { return updateTime; }
        public void setUpdateTime(Long updateTime) { this.updateTime = updateTime; }

        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }

        public Object getAdditionalInfo() { return additionalInfo; }
        public void setAdditionalInfo(Object additionalInfo) { this.additionalInfo = additionalInfo; }
    }

    /**
     * 更新任务状态
     * 
     * @param taskId 任务ID
     * @param contentType 内容类型
     * @param status 状态
     */
    public void updateTaskStatus(String taskId, AiTaskContentTypeEnum contentType, String status) {
        updateTaskStatus(taskId, contentType, status, null, null);
    }

    /**
     * 更新任务状态（带错误信息）
     * 
     * @param taskId 任务ID
     * @param contentType 内容类型
     * @param status 状态
     * @param errorMessage 错误信息
     */
    public void updateTaskStatus(String taskId, AiTaskContentTypeEnum contentType, String status, String errorMessage) {
        updateTaskStatus(taskId, contentType, status, errorMessage, null);
    }

    /**
     * 更新任务状态（完整版本）
     * 
     * @param taskId 任务ID
     * @param contentType 内容类型
     * @param status 状态
     * @param errorMessage 错误信息
     * @param additionalInfo 附加信息
     */
    public void updateTaskStatus(String taskId, AiTaskContentTypeEnum contentType, String status, 
                                String errorMessage, Object additionalInfo) {
        try {
            String statusKey = buildStatusKey(taskId, contentType);
            
            // 获取现有状态信息或创建新的
            TaskStatusInfo statusInfo = getTaskStatusInfo(taskId, contentType);
            if (statusInfo == null) {
                statusInfo = new TaskStatusInfo(status, contentType.getType());
            } else {
                statusInfo.setStatus(status);
            }
            
            // 设置错误信息和附加信息
            if (errorMessage != null) {
                statusInfo.setErrorMessage(errorMessage);
            }
            if (additionalInfo != null) {
                statusInfo.setAdditionalInfo(additionalInfo);
            }
            
            // 保存到Redis
            redissonUtils.set(redissonClient, statusKey, statusInfo, DEFAULT_EXPIRE_TIME, DEFAULT_TIME_UNIT);
            
            log.info("任务状态更新成功，任务ID: {}, 内容类型: {}, 状态: {}", 
                    taskId, contentType.getType(), status);
            
        } catch (Exception e) {
            log.error("更新任务状态失败，任务ID: {}, 内容类型: {}, 状态: {}", 
                    taskId, contentType.getType(), status, e);
        }
    }

    /**
     * 获取任务状态
     * 
     * @param taskId 任务ID
     * @param contentType 内容类型
     * @return 任务状态
     */
    public String getTaskStatus(String taskId, AiTaskContentTypeEnum contentType) {
        TaskStatusInfo statusInfo = getTaskStatusInfo(taskId, contentType);
        return statusInfo != null ? statusInfo.getStatus() : null;
    }

    /**
     * 获取任务状态信息
     * 
     * @param taskId 任务ID
     * @param contentType 内容类型
     * @return 任务状态信息
     */
    public TaskStatusInfo getTaskStatusInfo(String taskId, AiTaskContentTypeEnum contentType) {
        try {
            String statusKey = buildStatusKey(taskId, contentType);
            TaskStatusInfo statusInfo = redissonUtils.get(redissonClient, statusKey);
            
            if (statusInfo != null) {
                log.debug("获取任务状态成功，任务ID: {}, 内容类型: {}, 状态: {}", 
                        taskId, contentType.getType(), statusInfo.getStatus());
            }
            
            return statusInfo;
            
        } catch (Exception e) {
            log.error("获取任务状态失败，任务ID: {}, 内容类型: {}", 
                    taskId, contentType.getType(), e);
            return null;
        }
    }

    /**
     * 获取任务的所有状态信息
     * 
     * @param taskId 任务ID
     * @return 所有内容类型的状态信息
     */
    public Map<String, TaskStatusInfo> getAllTaskStatus(String taskId) {
        Map<String, TaskStatusInfo> statusMap = new HashMap<>();
        
        try {
            // 遍历所有可能的内容类型
            for (AiTaskContentTypeEnum contentType : AiTaskContentTypeEnum.values()) {
                TaskStatusInfo statusInfo = getTaskStatusInfo(taskId, contentType);
                if (statusInfo != null) {
                    statusMap.put(contentType.getType(), statusInfo);
                }
            }
            
            log.info("获取任务所有状态成功，任务ID: {}, 状态数量: {}", taskId, statusMap.size());
            
        } catch (Exception e) {
            log.error("获取任务所有状态失败，任务ID: {}", taskId, e);
        }
        
        return statusMap;
    }

    /**
     * 获取算法结果
     * 
     * @param taskId 任务ID
     * @param contentType 内容类型
     * @return 算法结果
     */
    public Object getAlgorithmResult(String taskId, AiTaskContentTypeEnum contentType) {
        try {
            String resultKey = buildResultKey(taskId, contentType);
            Object result = redissonUtils.get(redissonClient, resultKey);
            
            if (result != null) {
                log.info("获取算法结果成功，任务ID: {}, 内容类型: {}", taskId, contentType.getType());
            }
            
            return result;
            
        } catch (Exception e) {
            log.error("获取算法结果失败，任务ID: {}, 内容类型: {}", taskId, contentType.getType(), e);
            return null;
        }
    }

    /**
     * 保存错误信息
     * 
     * @param taskId 任务ID
     * @param contentType 内容类型
     * @param error 错误信息
     */
    public void saveErrorInfo(String taskId, AiTaskContentTypeEnum contentType, Exception error) {
        try {
            String errorKey = buildErrorKey(taskId, contentType);
            
            Map<String, Object> errorInfo = Map.of(
                "errorMessage", error.getMessage(),
                "errorTime", System.currentTimeMillis(),
                "contentType", contentType.getType(),
                "stackTrace", getStackTrace(error)
            );
            
            redissonUtils.set(redissonClient, errorKey, errorInfo, DEFAULT_EXPIRE_TIME, DEFAULT_TIME_UNIT);
            
            log.info("错误信息保存成功，任务ID: {}, 内容类型: {}", taskId, contentType.getType());
            
        } catch (Exception e) {
            log.error("保存错误信息失败，任务ID: {}, 内容类型: {}", taskId, contentType.getType(), e);
        }
    }

    /**
     * 获取错误信息
     * 
     * @param taskId 任务ID
     * @param contentType 内容类型
     * @return 错误信息
     */
    public Map<String, Object> getErrorInfo(String taskId, AiTaskContentTypeEnum contentType) {
        try {
            String errorKey = buildErrorKey(taskId, contentType);
            Map<String, Object> errorInfo = redissonUtils.get(redissonClient, errorKey);
            
            if (errorInfo != null) {
                log.info("获取错误信息成功，任务ID: {}, 内容类型: {}", taskId, contentType.getType());
            }
            
            return errorInfo;
            
        } catch (Exception e) {
            log.error("获取错误信息失败，任务ID: {}, 内容类型: {}", taskId, contentType.getType(), e);
            return null;
        }
    }

    /**
     * 删除任务相关的所有Redis数据
     * 
     * @param taskId 任务ID
     * @param contentType 内容类型
     */
    public void clearTaskData(String taskId, AiTaskContentTypeEnum contentType) {
        try {
            String statusKey = buildStatusKey(taskId, contentType);
            String resultKey = buildResultKey(taskId, contentType);
            String errorKey = buildErrorKey(taskId, contentType);
            
            // 删除状态、结果和错误信息
            redissonUtils.delete(redissonUtils.getBucket(redissonClient, statusKey));
            redissonUtils.delete(redissonUtils.getBucket(redissonClient, resultKey));
            redissonUtils.delete(redissonUtils.getBucket(redissonClient, errorKey));
            
            log.info("任务数据清理成功，任务ID: {}, 内容类型: {}", taskId, contentType.getType());
            
        } catch (Exception e) {
            log.error("清理任务数据失败，任务ID: {}, 内容类型: {}", taskId, contentType.getType(), e);
        }
    }

    /**
     * 检查任务是否正在处理中
     * 
     * @param taskId 任务ID
     * @param contentType 内容类型
     * @return 是否正在处理中
     */
    public boolean isTaskProcessing(String taskId, AiTaskContentTypeEnum contentType) {
        String status = getTaskStatus(taskId, contentType);
        return STATUS_PROCESSING.equals(status);
    }

    /**
     * 检查任务是否已完成
     * 
     * @param taskId 任务ID
     * @param contentType 内容类型
     * @return 是否已完成
     */
    public boolean isTaskCompleted(String taskId, AiTaskContentTypeEnum contentType) {
        String status = getTaskStatus(taskId, contentType);
        return STATUS_COMPLETED.equals(status);
    }

    /**
     * 检查任务是否失败
     * 
     * @param taskId 任务ID
     * @param contentType 内容类型
     * @return 是否失败
     */
    public boolean isTaskFailed(String taskId, AiTaskContentTypeEnum contentType) {
        String status = getTaskStatus(taskId, contentType);
        return STATUS_FAILED.equals(status);
    }

    /**
     * 构建状态Redis Key
     */
    private String buildStatusKey(String taskId, AiTaskContentTypeEnum contentType) {
        return buildRedisKey(ALGORITHM_TASK_STATUS_PREFIX, taskId, contentType.getType());
    }

    /**
     * 构建结果Redis Key
     */
    private String buildResultKey(String taskId, AiTaskContentTypeEnum contentType) {
        return buildRedisKey(ALGORITHM_TASK_RESULT_PREFIX, taskId, contentType.getType());
    }

    /**
     * 构建错误Redis Key
     */
    private String buildErrorKey(String taskId, AiTaskContentTypeEnum contentType) {
        return buildRedisKey(ALGORITHM_TASK_ERROR_PREFIX, taskId, contentType.getType());
    }

    /**
     * 获取异常堆栈信息
     */
    private String getStackTrace(Throwable throwable) {
        if (throwable == null) {
            return "";
        }
        
        try (java.io.StringWriter sw = new java.io.StringWriter();
             java.io.PrintWriter pw = new java.io.PrintWriter(sw)) {
            throwable.printStackTrace(pw);
            return sw.toString();
        } catch (Exception e) {
            return "Failed to get stack trace: " + e.getMessage();
        }
    }
} 
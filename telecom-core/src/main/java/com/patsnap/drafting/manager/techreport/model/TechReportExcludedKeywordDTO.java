package com.patsnap.drafting.manager.techreport.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.patsnap.drafting.response.techreport.CompanyInfo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Map;

/**
 * 技术报告排除关键词DTO
 * 用于表示需要排除的各种条件
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class TechReportExcludedKeywordDTO {

    /**
     * 排除的国家
     */
    @JsonProperty("exclude_country")
    private List<String> excludeCountry;

    /**
     * prompt中提取的排除的公司列表
     */
    @JsonProperty("exclude_companies")
    private List<String> excludeCompanies;

    /**
     * 经过处理的公司列表
     */
    @JsonProperty("exclude_company_list")
    private List<CompanyInfo> excludeCompanyList;

    /**
     * 排除的技术关键词
     */
    @JsonProperty("exclude_techs")
    private List<String> excludeTechs;
} 
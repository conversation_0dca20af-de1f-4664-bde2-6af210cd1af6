package com.patsnap.drafting.manager.aispecification;

import com.patsnap.drafting.enums.task.AiTaskContentTypeEnum;
import com.patsnap.drafting.manager.aitask.AiTaskManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.Map;

import static com.patsnap.drafting.manager.aispecification.AlgorithmTaskStatusManager.STATUS_COMPLETED;
import static com.patsnap.drafting.manager.aispecification.AlgorithmTaskStatusManager.STATUS_FAILED;
import static com.patsnap.drafting.manager.aispecification.AlgorithmTaskStatusManager.STATUS_PROCESSING;

/**
 * 异步算法处理器
 * 专门负责处理算法接口的异步调用和结果保存
 * 
 * <AUTHOR> Assistant
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AlgorithmAsyncProcessor {

    private final AiTaskManager aiTaskManager;
    private final AlgorithmTaskStatusManager statusManager;
    private final AlgorithmCallService algorithmCallService;

    /**
     * 异步调用算法接口处理内容
     * 
     * @param taskId 任务ID
     * @param contentType 内容类型
     */
    @Async("algorithmTaskExecutor")
    public void processAlgorithmAsync(String taskId, AiTaskContentTypeEnum contentType) {
        log.info("开始异步处理算法调用，任务ID: {}, 内容类型: {}", taskId, contentType.getType());
        
        try {
            // 更新任务状态为处理中
            statusManager.updateTaskStatus(taskId, contentType, STATUS_PROCESSING);
            
            // 调用具体的算法接口
            callAlgorithmService(taskId, contentType);
            
            // 更新任务状态为完成
            statusManager.updateTaskStatus(taskId, contentType, STATUS_COMPLETED);
            
            log.info("异步算法处理完成，任务ID: {}, 内容类型: {}", taskId, contentType.getType());
            
        } catch (Exception e) {
            log.error("异步算法处理失败，任务ID: {}, 内容类型: {}", taskId, contentType.getType(), e);
            
            // 更新任务状态为失败
            statusManager.updateTaskStatus(taskId, contentType, STATUS_FAILED, e.getMessage());
        }
    }
    
    /**
     * 调用算法服务
     *
     * @param taskId      任务ID
     * @param contentType 内容类型
     */
    private void callAlgorithmService(String taskId, AiTaskContentTypeEnum contentType) {
        log.info("调用算法服务，任务ID: {}, 内容类型: {}", taskId, contentType.getType());
        // 委托给专门的算法调用服务，该服务支持重试机制
        algorithmCallService.callAlgorithmService(taskId, contentType);
    }

    
    
    /**
     * 获取算法处理状态
     * 
     * @param taskId 任务ID
     * @return 各种内容类型的算法处理状态
     */
    public Map<String, String> getAlgorithmStatus(String taskId) {
        log.info("获取算法处理状态，任务ID: {}", taskId);
        
        try {
            // 使用状态管理器从Redis获取所有状态信息
            Map<String, AlgorithmTaskStatusManager.TaskStatusInfo> allStatus = statusManager.getAllTaskStatus(taskId);
            
            // 转换为简单的状态映射
            Map<String, String> statusMap = allStatus.entrySet().stream()
                .collect(java.util.stream.Collectors.toMap(
                    Map.Entry::getKey,
                    entry -> entry.getValue().getStatus()
                ));
            
            log.info("获取算法处理状态成功，任务ID: {}, 状态数量: {}", taskId, statusMap.size());
            return statusMap;
            
        } catch (Exception e) {
            log.error("获取算法处理状态失败，任务ID: {}", taskId, e);
            return Map.of();
        }
    }
    
    /**
     * 获取详细的算法处理状态信息
     * 
     * @param taskId 任务ID
     * @return 各种内容类型的详细状态信息
     */
    public Map<String, AlgorithmTaskStatusManager.TaskStatusInfo> getDetailedAlgorithmStatus(String taskId) {
        log.info("获取详细算法处理状态，任务ID: {}", taskId);
        
        try {
            Map<String, AlgorithmTaskStatusManager.TaskStatusInfo> allStatus = statusManager.getAllTaskStatus(taskId);
            log.info("获取详细算法处理状态成功，任务ID: {}, 状态数量: {}", taskId, allStatus.size());
            return allStatus;
            
        } catch (Exception e) {
            log.error("获取详细算法处理状态失败，任务ID: {}", taskId, e);
            return Map.of();
        }
    }
    
    /**
     * 获取特定内容类型的算法结果
     * 
     * @param taskId 任务ID
     * @param contentType 内容类型
     * @return 算法结果
     */
    public Object getAlgorithmResult(String taskId, AiTaskContentTypeEnum contentType) {
        log.info("获取算法结果，任务ID: {}, 内容类型: {}", taskId, contentType.getType());
        
        try {
            Object result = statusManager.getAlgorithmResult(taskId, contentType);
            if (result != null) {
                log.info("获取算法结果成功，任务ID: {}, 内容类型: {}", taskId, contentType.getType());
            } else {
                log.warn("算法结果为空，任务ID: {}, 内容类型: {}", taskId, contentType.getType());
            }
            return result;
            
        } catch (Exception e) {
            log.error("获取算法结果失败，任务ID: {}, 内容类型: {}", taskId, contentType.getType(), e);
            return null;
        }
    }
} 
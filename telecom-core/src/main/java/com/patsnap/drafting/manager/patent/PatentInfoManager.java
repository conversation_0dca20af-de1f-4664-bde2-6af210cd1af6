package com.patsnap.drafting.manager.patent;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.patsnap.common.request.ClientIdHolder;
import com.patsnap.common.request.CorrelationIdHolder;
import com.patsnap.common.request.RequestDeviceHolder;
import com.patsnap.common.request.UserIdHolder;
import com.patsnap.core.common.AnalyticsCommonService;
import com.patsnap.core.common.InvokeBean;
import com.patsnap.core.common.request.RoleIdsHolder;
import com.patsnap.core.common.request.SiteLangHolder;
import com.patsnap.core.common.utils.DateFormatUtils;
import com.patsnap.core.spring.boot.autoconfigure.web.entity.CommonResponse;
import com.patsnap.drafting.client.api.BasicSearchApi;
import com.patsnap.drafting.client.model.PatentLegalStatusResponse;
import com.patsnap.drafting.client.PatentApiClient;
import com.patsnap.drafting.exception.BizException;
import com.patsnap.drafting.request.patent.PatentImagesRequestDTO;
import com.patsnap.drafting.request.patentinfo.PatentBasicDetailRequestDTO;
import com.patsnap.drafting.request.patentinfo.PatentInfoRequestDTO;
import com.patsnap.drafting.request.patentinfo.PatentSearchRequestDTO;
import com.patsnap.drafting.request.patentinfo.PatentTranslateRequestDTO;
import com.patsnap.drafting.request.patentinfo.PatentViewRequestDTO;
import com.patsnap.drafting.response.patentinfo.PatentSearchResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import retrofit2.Response;

import java.io.IOException;
import java.util.*;

import static com.patsnap.drafting.exception.errorcode.ClientErrorCodeEnum.CLIENT_NOT_AVAILABLE;

/**
 * <AUTHOR> chendepeng
 * @Date 2025/3/13 13:42
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class PatentInfoManager {
    private final AnalyticsCommonService analyticsCommonService;

    @Value("${com.patsnap.analytics.service.analytics-search-url}")
    private String analyticsSearchUrl;

    @Value("${com.patsnap.analytics.service.patentdata-url}")
    private String patentDataUrl;

    private final RestTemplate restTemplate;

    private final PatentApiClient patentApiClient;

    private final BasicSearchApi basicSearchApi;

    // 查询专利desc信息
    public CommonResponse<Object> fetchPatentDesc(String patentId) {
        PatentViewRequestDTO viewRequestBody = new PatentViewRequestDTO();
        viewRequestBody.setIncludePatentImage(true);
        viewRequestBody.setPage(1);
        viewRequestBody.setSort("sdesc");

        InvokeBean<CommonResponse<Object>> invokeBean = new InvokeBean<>();
        invokeBean.setHttpMethod(HttpMethod.POST);
        invokeBean.setUrl(analyticsSearchUrl + "/patent/id/{patentId}/desc?highlight={highlight}");
        invokeBean.setUriVariables(patentId, true);
        invokeBean.setRequestBody(viewRequestBody);
        invokeBean.setResponseType(new ParameterizedTypeReference<>() {
        });
        return analyticsCommonService.invokeMethod(invokeBean);
    }

    public CommonResponse<Object> fetchClassificationDesc(String id) {
        InvokeBean<CommonResponse<Object>> invokeBean = new InvokeBean<>();
        invokeBean.setHttpMethod(HttpMethod.GET);
        invokeBean.setUrl(analyticsSearchUrl + "/classification/detail?type=ipc&cid={id}");
        invokeBean.setUriVariables(id);
        invokeBean.setResponseType(new ParameterizedTypeReference<>() {
        });
        return analyticsCommonService.invokeMethod(invokeBean);
    }

    /**
     * 获取专利权利要求及其翻译
     *
     * @param patentId 专利ID
     * @param targetLang 目标语言
     * @return 专利权利要求内容
     */
    public CommonResponse<String> fetchPatentClaimWithTranslate(String patentId, String targetLang) {
        // 1. 获取专利基本信息
        PatentBasicDetailRequestDTO request = buildPatentBasicDetailRequest(patentId);
        ResponseEntity<JSONObject> responseEntity = batchFetchPatentBasicInfo(request, targetLang);
        
        // 2. 解析专利权利要求
        JSONObject patentData = Optional.ofNullable(responseEntity.getBody())
                .map(body -> body.getJSONObject(patentId))
                .orElse(new JSONObject());
        
        Object claimsObj = patentData.get("CLMS");
        if (Objects.isNull(claimsObj)) {
            return CommonResponse.<String>builder().withData("").build();
        }

        // 3. 获取目标语言的权利要求
        JSONObject claimsJson = JSONObject.parseObject(JSONObject.toJSONString(claimsObj));
        String targetLangClaims = claimsJson.getString(targetLang);
        
        // 4. 如果目标语言内容不存在，尝试翻译
        if (StringUtils.isBlank(targetLangClaims)) {
            targetLangClaims = translatePatentField(patentId, targetLang, "CLMS");
        }
        
        // 5. 如果翻译失败，使用原始语言内容
        if (StringUtils.isBlank(targetLangClaims)) {
            String originalLang = patentData.getString("CLMS_LANG");
            targetLangClaims = claimsJson.getString(originalLang);
        }
        
        return CommonResponse.<String>builder().withData(targetLangClaims).build();
    }

    /**
     * 构建专利基本信息请求
     */
    private PatentBasicDetailRequestDTO buildPatentBasicDetailRequest(String patentId) {
        PatentBasicDetailRequestDTO request = new PatentBasicDetailRequestDTO();
        request.setPatentIds(CollUtil.newArrayList(patentId));
        request.setFields(Arrays.asList("PN", "CLMS"));
        return request;
    }

    /**
     * 翻译专利特定字段
     */
    private String translatePatentField(String patentId, String targetLang, String field) {
        PatentTranslateRequestDTO translateRequest = new PatentTranslateRequestDTO();
        translateRequest.setPatentId(patentId);
        translateRequest.setLang(targetLang.toUpperCase());
        translateRequest.setField(field);
        
        CommonResponse<Map<String, Object>> translationResponse = this.fetchPatentTranslation(translateRequest);
        
        return Optional.ofNullable(translationResponse.getData())
                .map(data -> data.get(field))
                .map(fieldData -> JSONObject.parseObject(JSONObject.toJSONString(fieldData)))
                .map(json -> json.getString(targetLang.toUpperCase()))
                .orElse("");
    }

    public CommonResponse<String> fetchPatentDescWithTranslate(String patentId, String targetLang) {
        PatentViewRequestDTO viewRequestBody = new PatentViewRequestDTO();
        viewRequestBody.setIncludePatentImage(true);
        viewRequestBody.setPage(1);
        viewRequestBody.setSort("sdesc");
        InvokeBean<CommonResponse<Object>> invokeBean = new InvokeBean<>();
        invokeBean.setHttpMethod(HttpMethod.POST);
        invokeBean.setUrl(analyticsSearchUrl + "/patent/id/{patentId}/desc?highlight={highlight}");
        invokeBean.setUriVariables(patentId, true);
        invokeBean.setRequestBody(viewRequestBody);
        invokeBean.setResponseType(new ParameterizedTypeReference<>() {
        });
        CommonResponse<Object> response = analyticsCommonService.invokeMethod(invokeBean);
        return handleDescJson(response, patentId, targetLang);
    }

    private CommonResponse<String> handleDescJson(CommonResponse<Object> response, String patentId, String targetLang) {
        JSONObject titleObjJson = JSONObject.parseObject(JSONObject.toJSONString(response.getData()));
        Object descObj = titleObjJson.get("DESC");
        if(Objects.isNull(descObj)) {
            return CommonResponse.<String>builder().withData("").build();
        }

        JSONObject descJson = JSONObject.parseObject(JSONObject.toJSONString(descObj));
        String targetLangDesc = descJson.getString(targetLang);
        if(StringUtils.isBlank(targetLangDesc)) {
            // 调用翻译接口
            PatentTranslateRequestDTO translateRequest = new PatentTranslateRequestDTO();
            translateRequest.setPatentId(patentId);
            translateRequest.setLang(targetLang.toUpperCase());
            translateRequest.setField("DESC");
            CommonResponse<Map<String, Object>> translationResponse = this.fetchPatentTranslation(translateRequest);
            if(Objects.nonNull(translationResponse.getData())) {
                targetLangDesc = JSONObject.parseObject(JSONObject.toJSONString(translationResponse.getData().get("DESC"))).getString(targetLang.toUpperCase());
            }
        }
        if(StringUtils.isBlank(targetLangDesc)) {
            targetLangDesc = descJson.getString(titleObjJson.getString("DESC_LANG"));
        }
        return CommonResponse.<String>builder().withData(targetLangDesc).build();
    }

    // 查询专利官方图片信息
    public CommonResponse<Object> fetchPatentOfficialImage(String patentId) {
        InvokeBean<CommonResponse<Object>> invokeBean = new InvokeBean<>();
        invokeBean.setHttpMethod(HttpMethod.GET);
        invokeBean.setUrl(analyticsSearchUrl + "/patent/id/{patentId}/official-image");
        invokeBean.setUriVariables(patentId);
        invokeBean.setResponseType(new ParameterizedTypeReference<>() {});
        return analyticsCommonService.invokeMethod(invokeBean);
    }

    // 查询专利权利要求树信息
    public CommonResponse<Object> fetchPatentClmsTree(String patentId) {
        InvokeBean<CommonResponse<Object>> invokeBean = new InvokeBean<>();
        invokeBean.setHttpMethod(HttpMethod.GET);
        invokeBean.setUrl(analyticsSearchUrl + "/patent/id/{patentId}/clms-tree");
        invokeBean.setUriVariables(patentId);
        invokeBean.setResponseType(new ParameterizedTypeReference<>() {});
        return analyticsCommonService.invokeMethod(invokeBean);
    }

    // 批量查询专利基本信息
    public JSONObject batchFetchPatentBasicInfo(PatentInfoRequestDTO requestDTO) {
        return batchFetchPatentInfoByField(requestDTO, Arrays.asList("PN","ANCS","PBD","APD","TITLE","SIMPLE_LEGAL_STATUS","LEGAL_STATUS", "EXDT", "FAMILY_ID", "ABST", "PDF_IMAGE"));
    }

    /**
     * 批量查询专利基本信息并指定字段
     *
     * @param requestDTO
     * @param fieldList
     * @return
     */
    public JSONObject batchFetchPatentInfoByField(PatentInfoRequestDTO requestDTO, List<String> fieldList) {
        PatentBasicDetailRequestDTO request = new PatentBasicDetailRequestDTO();
        request.setPatentIds(requestDTO.getPatentIds());
        request.setFields(fieldList);
        String lang = StrUtil.isEmpty(requestDTO.getLang()) ? SiteLangHolder.get() : requestDTO.getLang();
        ResponseEntity<JSONObject> responseEntity = batchFetchPatentBasicInfo(request, lang);
        return handleJsonObject(responseEntity, lang);
    }

    public ResponseEntity<JSONObject> batchFetchPatentBasicInfo(PatentBasicDetailRequestDTO request, String lang) {
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.put("X-API-Version", Collections.singletonList("2.0"));
            headers.put(SiteLangHolder.X_SITE_LANG, Collections.singletonList(lang));
            headers.put(UserIdHolder.X_USER_ID, Collections.singletonList(UserIdHolder.get()));
            headers.add("X-Correlation-ID", CorrelationIdHolder.get());
            headers.add("X-Client-ID", ClientIdHolder.get());
            headers.add("X-Device-ID", RequestDeviceHolder.get());
            headers.add("X-User-Roles", StringUtils.join(RoleIdsHolder.get(), ","));
            headers.set("Content-Type", "application/json;charset=UTF-8");
            // dateType决定专利公开日的时间格式(基于lang值)
            // todo 后期lang取值不能从网站取值，而应该从每个任务的语言取值
            request.setDateType(DateFormatUtils.getDateType(lang));
            return restTemplate.exchange(patentDataUrl + "/detail", HttpMethod.POST, new HttpEntity<>(request, headers), JSONObject.class);

        } catch (Exception e) {
            log.error("Failed to fetch-patent-basic-info, request: {}", request, e);
            throw new BizException(CLIENT_NOT_AVAILABLE);
        }
    }

    // 查询输入文本模糊查询专利信息
    public CommonResponse<PatentSearchResponse> searchPatentInfo(PatentSearchRequestDTO request) {
        InvokeBean<CommonResponse<PatentSearchResponse>> invokeBean = new InvokeBean<>();
        invokeBean.setHttpMethod(HttpMethod.POST);
        invokeBean.setUrl(analyticsSearchUrl + "/view/compare/v2/compare-search");
        request.setSimple(true);
        invokeBean.setRequestBody(request);
        invokeBean.setResponseType(new ParameterizedTypeReference<>() {});
        return analyticsCommonService.invokeMethod(invokeBean);
    }

    private JSONObject handleJsonObject(ResponseEntity<JSONObject> responseEntity, String lang) {
        JSONObject result = Optional.ofNullable(responseEntity.getBody()).orElse(new JSONObject());
        for (String key : result.keySet()) {
            JSONObject item = result.getJSONObject(key);
            JSONObject title = item.getJSONObject("TITLE");
            String titleLang = item.getString("TITLE_LANG");
            if (title == null) {
                item.put("TITLE", item.getString("PN"));
                continue;
            }

            // 根据TITLE_LANG的值获取对应的TITLE,如果取不到，就调用一下翻译接口，最后再取默认的TITLE
            String langTitle = title.getString(lang.toUpperCase());
            if (StringUtils.isBlank(langTitle)) {
                // 调用翻译接口
                PatentTranslateRequestDTO translateRequest = new PatentTranslateRequestDTO();
                translateRequest.setPatentId(key);
                translateRequest.setLang(lang.toUpperCase());
                translateRequest.setField("TITLE");
                CommonResponse<Map<String, Object>> translationResponse = this.fetchPatentTranslation(translateRequest);
                if(Objects.nonNull(translationResponse.getData())) {
                    Object titleObj = translationResponse.getData().get("TITLE");
                    JSONObject titleObjJson = JSONObject.parseObject(JSONObject.toJSONString(titleObj));
                    langTitle = titleObjJson.getString(lang.toUpperCase());
                }

                // 如果还是取不到，那就用专利默认语言的标题
                if (StringUtils.isBlank(langTitle)) {
                    langTitle = title.getString(titleLang);
                }
            }
            item.put("TITLE", langTitle);
        }
        return result;
    }

    // 专利ID和Lang，获取到对应的翻译结果
    public CommonResponse<Map<String, Object>> fetchPatentTranslation(PatentTranslateRequestDTO request) {
        InvokeBean<CommonResponse<Map<String, Object>>> invokeBean = new InvokeBean<>();
        invokeBean.setHttpMethod(HttpMethod.POST);
        invokeBean.setUrl(analyticsSearchUrl + "/translate/patent");
        invokeBean.setRequestBody(request);
        invokeBean.setResponseType(new ParameterizedTypeReference<>() {});
        return analyticsCommonService.invokeMethod(invokeBean);
    }

    public Response<PatentLegalStatusResponse> fetchLegalStatusConfig() {
        try {
            return basicSearchApi.fetchLegalStatusConfig().execute();
        } catch (IOException e) {
            log.error("Failed to fetch legal status config", e);
            throw new BizException(CLIENT_NOT_AVAILABLE);
        }
    }

    // 根据lang和simpleLegalStatus的值获取对应的文本
    public String fetchSimpleLegalStatusText(String lang, Integer simpleLegalStatus, Response<PatentLegalStatusResponse> response) {
        String result = "-";
        if (response.isSuccessful() && response.body() != null && response.body().getData() != null) {
            Object legalStatusObj = response.body().getData().getSimpleLegalStatus().get(simpleLegalStatus);
            if (Objects.nonNull(legalStatusObj)) {
                JSONObject legalStatusJson = JSONObject.parseObject(JSONObject.toJSONString(legalStatusObj));
                JSONObject title = legalStatusJson.getJSONObject("title");
                result = title.getString(lang.toLowerCase());
            }
            return result;
        }
        return result;
    }

    // 根据lang和simpleLegalStatus的值获取对应的文本
    public String fetchLegalEventText(String lang, List<Integer> legalStatusList, Response<PatentLegalStatusResponse> response) {
        String result = "-";
        if (response.isSuccessful() && response.body() != null && response.body().getData() != null) {
            Map<Integer, Object> legalEventObj = response.body().getData().getLegalEvents();
            for(Integer legalStatus : legalStatusList) {
                Object legalEvent = legalEventObj.get(legalStatus);
                if (Objects.nonNull(legalEvent)) {
                    JSONObject legalEventJson = JSONObject.parseObject(JSONObject.toJSONString(legalEvent));
                    JSONObject title = legalEventJson.getJSONObject("title");
                    result = title.getString(lang.toLowerCase());
                }
            }
            return result;
        }
        return result;
    }

    /**
     * 批量获取专利图片信息
     *
     * @param request 包含专利ID列表的请求对象
     * @return 专利图片信息响应对象,key 为 patentId value 为图片信息Map
     */
    public Map<String, Map<String, String>> batchFetchPatentImages(PatentImagesRequestDTO request) {
        try {
            // 调用PatentApiClient的getPatentImagesMap方法获取专利图片信息
            Map<String, Map<String, Object>> patentImagesMap = patentApiClient.getPatentImagesMap(request.getPatentIds());
            log.info("Batch fetch patent images, request: {}, result size: {}", request, patentImagesMap.size());

            // 将返回结果转换为包含url和url_120的Map格式
            Map<String, Map<String, String>> imageUrlMap = new HashMap<>();
            patentImagesMap.forEach((patentId, imageData) -> {
                Map<String, String> urls = new HashMap<>();
                // 提取URL和URL_120信息
                if (imageData.containsKey("url")) {
                    String url = imageData.get("url").toString();
                    urls.put("url", url);
                }
                if (imageData.containsKey("url_120")) {
                    String url120 = imageData.get("url_120").toString();
                    urls.put("url_120", url120);
                }
                imageUrlMap.put(patentId, urls);
            });

            return imageUrlMap;
        } catch (Exception e) {
            log.error("Failed to fetch patent images, request: {}", request, e);
            throw new BizException(CLIENT_NOT_AVAILABLE);
        }
    }
}

package com.patsnap.drafting.manager.aispecification.content;

import com.fasterxml.jackson.core.type.TypeReference;
import com.patsnap.core.common.copilot.constant.GPTStatus;
import com.patsnap.core.common.copilot.gpt.enums.GPTModelEnum;
import com.patsnap.core.common.copilot.streaming.handling.StreamingChatLanguageModel;
import com.patsnap.core.common.copilot.streaming.model.GptResponseDTO;
import com.patsnap.drafting.client.OpenAiClient;
import com.patsnap.drafting.config.UrlConfig;
import com.patsnap.drafting.enums.aispecification.JurisdictionEnum;
import com.patsnap.drafting.enums.prompt.ScenarioEnum;
import com.patsnap.drafting.enums.task.AiTaskContentTypeEnum;
import com.patsnap.drafting.manager.AbstractGenerateStreamingContent;
import com.patsnap.drafting.manager.aitask.AiTaskManager;
import com.patsnap.drafting.model.aispecification.SpecificationInitializationBO;
import com.patsnap.drafting.model.base.StreamingModelBO;
import com.patsnap.drafting.request.GenerateContentRequestDTO;
import com.patsnap.spring.boot.autoconfigure.web.entity.CommonResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

import java.util.List;
import java.util.Map;

import static com.patsnap.drafting.enums.prompt.SpecificationPromptKeyEnum.CATEGORY;

/**
 * AI说明书撰写 - 类别
 */
@Component
@Slf4j
public class SpecificationCategoryContent extends AbstractGenerateStreamingContent<String> {

    private static final List<String> CORRECT_VALUE = List.of("产品", "设备", "材料", "方法", "Composition of matter",
            "Machine", "Process", "Manufacture");


    @Autowired
    private OpenAiClient openAiClient;

    protected SpecificationCategoryContent(AiTaskManager aiTaskManager,
            UrlConfig urlConfig) {
        super(aiTaskManager, urlConfig);
    }

    @Override
    public String getContentType() {
        return AiTaskContentTypeEnum.CATEGORY.getType();
    }


    @Override
    protected StreamingModelBO getStreamingModelBO(GenerateContentRequestDTO generateContentRequestDTO) {
        SpecificationInitializationBO initializationBO = aiTaskManager.getTaskContent(
                generateContentRequestDTO.getTaskId(),
                AiTaskContentTypeEnum.INITIALIZATION);
        String inputClaim = initializationBO.getClaim();
        String jurisdiction = initializationBO.getJurisdiction();
        //get prompt

        String prompt = openAiClient.buildPromptByPlatform(CATEGORY.getValue(),
                Map.of("input", inputClaim, "authority", JurisdictionEnum.fromName(jurisdiction).getAuthority()),
                JurisdictionEnum.fromName(jurisdiction).getValue());
        StreamingModelBO streamingModelBO = new StreamingModelBO();
        streamingModelBO.setPrompt(prompt);
        StreamingChatLanguageModel model = streamingModelBuilder.build(GPTModelEnum.GPT_CLAUDE_3_7_SONNET,
                ScenarioEnum.AI_SPECIFICATION.getValue());
        streamingModelBO.setModel(model);
        return streamingModelBO;
    }


    @Override
    protected Flux<CommonResponse<GptResponseDTO<String>>> getContent(StreamingModelBO streamingModelBO) {
        boolean status = true;
        GptResponseDTO<String> data;
        try {
            //非流式的方式一次性返回
            String result = openAiClient.callGptByPrompt(GPTModelEnum.GPT_CLAUDE_3_7_SONNET,
                    streamingModelBO.getPrompt(), ScenarioEnum.AI_SPECIFICATION, new TypeReference<>() {
                    });
            if (!CORRECT_VALUE.contains(result)) {
                result = StringUtils.EMPTY;
            }
            data = GptResponseDTO.<String>builder().content(result).status(GPTStatus.FINISH).build();
        } catch (Exception e) {
            status = false;
            log.warn("[AI说明书撰写]请求AI获取主题类型报错", e);
            data = GptResponseDTO.<String>builder().status(GPTStatus.FAILED).build();
        }
        CommonResponse<GptResponseDTO<String>> response = CommonResponse.<GptResponseDTO<String>>builder()
                .withData(data).withStatus(status).build();
        return Flux.just(response);
    }
}

package com.patsnap.drafting.manager.aispecification.content;

import com.fasterxml.jackson.core.type.TypeReference;
import com.patsnap.core.common.copilot.gpt.enums.GPTModelEnum;
import com.patsnap.core.common.copilot.streaming.handler.SimpleJsonStreamingResponseHandler;
import com.patsnap.core.common.copilot.streaming.handling.StreamingChatLanguageModel;
import com.patsnap.core.common.copilot.streaming.model.GptResponseDTO;
import com.patsnap.drafting.client.OpenAiClient;
import com.patsnap.drafting.config.UrlConfig;
import com.patsnap.drafting.enums.aispecification.JurisdictionEnum;
import com.patsnap.drafting.enums.prompt.ScenarioEnum;
import com.patsnap.drafting.enums.task.AiTaskContentTypeEnum;
import com.patsnap.drafting.manager.AbstractGenerateStreamingContent;
import com.patsnap.drafting.manager.aispecification.SpecificationManager;
import com.patsnap.drafting.manager.aitask.AiTaskManager;
import com.patsnap.drafting.model.aispecification.ClaimFeatureContentBO;
import com.patsnap.drafting.model.aispecification.SpecificationInitializationBO;
import com.patsnap.drafting.model.base.StreamingModelBO;
import com.patsnap.drafting.request.GenerateContentRequestDTO;
import com.patsnap.spring.boot.autoconfigure.web.entity.CommonResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

import java.util.List;
import java.util.Map;

import static com.patsnap.drafting.enums.prompt.SpecificationPromptKeyEnum.FEATURE_TREE;

/**
 * AI说明书撰写 - 特征树
 */
@Service
@Slf4j
public class SpecificationFeatureTreeContent extends AbstractGenerateStreamingContent<ClaimFeatureContentBO> {

    @Autowired
    private OpenAiClient openAiClient;

    @Autowired
    private SpecificationManager specificationManager;

    protected SpecificationFeatureTreeContent(AiTaskManager aiTaskManager, UrlConfig urlConfig) {
        super(aiTaskManager, urlConfig);
    }

    @Override
    protected StreamingModelBO getStreamingModelBO(GenerateContentRequestDTO generateContentRequestDTO) {
        //获取类别
        String taskId = generateContentRequestDTO.getTaskId();
        String category = aiTaskManager.getTaskContent(taskId, AiTaskContentTypeEnum.CATEGORY);

        SpecificationInitializationBO initializationBO = aiTaskManager.getTaskContent(taskId,
                AiTaskContentTypeEnum.INITIALIZATION);
        //获取受理局
        String jurisdiction = initializationBO.getJurisdiction();

        //获取结构化之后的权利要求
        String claimFormatted = aiTaskManager.getTaskContent(taskId, AiTaskContentTypeEnum.CLAIM_FORMAT);
        if (StringUtils.isBlank(claimFormatted)) {
            claimFormatted = specificationManager.claimFormat(initializationBO);
        }

        //获取prompt
        String prompt = openAiClient.buildPromptByPlatform(FEATURE_TREE.getValue(),
                Map.of("inputClaim", claimFormatted, "inputCategory", category, "authority",
                        JurisdictionEnum.fromName(jurisdiction).getAuthority()),
                JurisdictionEnum.fromName(jurisdiction).getValue());

        StreamingModelBO streamingModelBO = new StreamingModelBO();
        streamingModelBO.setPrompt(prompt);
        StreamingChatLanguageModel model = streamingModelBuilder.build(GPTModelEnum.GPT_MODEL_4_O_20240806,
                ScenarioEnum.AI_SPECIFICATION.getValue());
        streamingModelBO.setModel(model);
        return streamingModelBO;
    }

    @Override
    protected Flux<CommonResponse<GptResponseDTO<ClaimFeatureContentBO>>> getContent(
            StreamingModelBO streamingModelBO) {
        String prompt = streamingModelBO.getPrompt();
        return Flux.create(sink -> {
            SimpleJsonStreamingResponseHandler<ClaimFeatureContentBO> handler = new SimpleJsonStreamingResponseHandler<>(
                    sink,
                    30,
                    prompt,
                    new TypeReference<>() {
                    }, new ClaimFeatureContentBO(List.of()));
            streamingModelBO.getModel().generate(prompt, handler);
        });
    }

    @Override
    public String getContentType() {
        return AiTaskContentTypeEnum.FEATURE_TREE.getType();
    }

}

package com.patsnap.drafting.manager.content.logic;

import com.patsnap.drafting.enums.task.AiTaskContentTypeEnum;
import com.patsnap.drafting.manager.aitask.AiTaskManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.util.List;

/**
 * AIFTO查新专利技术要点更新content处理逻辑
 */
@Component
public class FtoSearchUpdateTextContentCacheLogic extends ContentCacheLogic {

    @Autowired
    private AiTaskManager aiTaskManager;

    @Override
    public void updateContent(String taskId, AiTaskContentTypeEnum contentType, Object result, Object[] args) {
        if (!(result instanceof String)) {
            return;
        }
        aiTaskManager.updateTaskContent(taskId, contentType, result);
        aiTaskManager.deleteTaskContentByContentType(taskId,
                List.of(AiTaskContentTypeEnum.FTO_SEARCH_TECH_FEATURE.getType()));
    }
}
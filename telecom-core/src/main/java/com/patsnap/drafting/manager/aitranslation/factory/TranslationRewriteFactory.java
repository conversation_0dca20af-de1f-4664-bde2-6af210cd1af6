package com.patsnap.drafting.manager.aitranslation.factory;

import com.patsnap.core.common.copilot.streaming.model.GptResponseDTO;
import com.patsnap.drafting.annotation.TaskContentCache;
import com.patsnap.drafting.config.AiTranslationConfig;
import com.patsnap.drafting.enums.task.AiTaskContentTypeEnum;
import com.patsnap.drafting.manager.aitranslation.operate.TranslationRewriteService;
import com.patsnap.drafting.manager.content.logic.TranslationRewriteContentCacheLogic;
import com.patsnap.drafting.model.aitranslation.AiTransContextBo;
import com.patsnap.spring.boot.autoconfigure.web.entity.CommonResponse;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import reactor.core.publisher.Flux;

@Component
public class TranslationRewriteFactory {

    @Autowired
    private AiTranslationConfig aiTranslationConfig;

    @Autowired
    private List<TranslationRewriteService> translationRewriteServices;

    @TaskContentCache(contentType = AiTaskContentTypeEnum.TRANSLATION_REWRITE,
            logicClass = TranslationRewriteContentCacheLogic.class)
    public Flux<CommonResponse<GptResponseDTO<String>>> generate(AiTransContextBo contextBo, int index,
            String original) {
        TranslationRewriteService service = null;
        for (TranslationRewriteService translationRewriteService : translationRewriteServices) {
            if (!aiTranslationConfig.getRewriteModel().equals(translationRewriteService.model())) {
                continue;
            }
            service = translationRewriteService;
        }
        return service.generate(contextBo, index, original);
    }

}

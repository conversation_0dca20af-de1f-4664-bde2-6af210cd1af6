package com.patsnap.drafting.manager.aispecification.content;

import com.patsnap.core.common.copilot.constant.GPTStatus;
import com.patsnap.core.common.copilot.streaming.model.GptResponseDTO;
import com.patsnap.drafting.annotation.TaskContentCache;
import com.patsnap.drafting.config.UrlConfig;
import com.patsnap.drafting.enums.task.AiTaskContentTypeEnum;
import com.patsnap.drafting.manager.AbstractGenerateStreamingContent;
import com.patsnap.drafting.manager.FileManager;
import com.patsnap.drafting.manager.aitask.AiTaskManager;
import com.patsnap.drafting.model.aispecification.FigureContentBO;
import com.patsnap.drafting.model.base.StreamingModelBO;
import com.patsnap.drafting.request.GenerateContentRequestDTO;
import com.patsnap.drafting.request.aispecification.Figure;
import com.patsnap.spring.boot.autoconfigure.web.entity.CommonResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * AI说明书撰写 - 附图信息获取
 * 从 content 表获取附图数据并流式返回
 */
@Component
@Slf4j
public class SpecificationFiguresContent extends AbstractGenerateStreamingContent<FigureContentBO> {

    @Autowired
    private FileManager fileManager;

    protected SpecificationFiguresContent(AiTaskManager aiTaskManager, UrlConfig urlConfig) {
        super(aiTaskManager, urlConfig);
    }

    @Override
    public String getContentType() {
        return AiTaskContentTypeEnum.FIGURES.getType();
    }

    @Override
    protected StreamingModelBO getStreamingModelBO(GenerateContentRequestDTO generateContentRequestDTO) {
        // 对于附图获取，不需要调用AI模型，直接返回空的StreamingModelBO
        return new StreamingModelBO();
    }

    @Override
    protected Flux<CommonResponse<GptResponseDTO<FigureContentBO>>> getContent(StreamingModelBO streamingModelBO) {
        // 这里不使用AI生成，而是直接从content表获取数据
        return Flux.empty();
    }

    @Override
    @TaskContentCache(contentTypeExpression = "#request.contentType")
    public Flux<CommonResponse<GptResponseDTO<FigureContentBO>>> doGenerate(GenerateContentRequestDTO request) {
        log.info("开始获取附图信息，任务ID: {}", request.getTaskId());
        
        try {
            // 1. 从content表获取用户管理的附图数据
            FigureContentBO figureContentBO = aiTaskManager.getTaskContent(request.getTaskId(), AiTaskContentTypeEnum.FIGURE_CONFIG);
            
            // 2. 处理附图数据，生成签名URL
            FigureContentBO processedFigureContent = processFigureContent(figureContentBO);
            
            // 3. 构造响应数据
            GptResponseDTO<FigureContentBO> gptResponse = GptResponseDTO.<FigureContentBO>builder()
                    .content(processedFigureContent)
                    .status(GPTStatus.FINISH)
                    .build();
            
            CommonResponse<GptResponseDTO<FigureContentBO>> response = CommonResponse.<GptResponseDTO<FigureContentBO>>builder()
                    .withData(gptResponse)
                    .withStatus(true)
                    .build();
            
            log.info("附图信息获取成功，任务ID: {}, 附图数量: {}", 
                    request.getTaskId(), 
                    processedFigureContent.getFigures() != null ? processedFigureContent.getFigures().size() : 0);
            
            // 4. 返回流式响应
            return Flux.just(response);
            
        } catch (Exception e) {
            log.error("获取附图信息失败，任务ID: {}", request.getTaskId(), e);
            
            // 构造错误响应
            GptResponseDTO<FigureContentBO> errorResponse = GptResponseDTO.<FigureContentBO>builder()
                    .content(createEmptyFigureContent())
                    .status(GPTStatus.FAILED)
                    .build();
            
            CommonResponse<GptResponseDTO<FigureContentBO>> response = CommonResponse.<GptResponseDTO<FigureContentBO>>builder()
                    .withData(errorResponse)
                    .withStatus(false)
                    .withErrorMsg("获取附图信息失败: " + e.getMessage())
                    .build();
            
            return Flux.just(response);
        }
    }

    /**
     * 处理附图内容，生成签名URL
     * 
     * @param figureContentBO 原始附图内容
     * @return 处理后的附图内容
     */
    private FigureContentBO processFigureContent(FigureContentBO figureContentBO) {
        List<Figure> figures = Optional.ofNullable(figureContentBO)
                .map(FigureContentBO::getFigures)
                .orElseGet(ArrayList::new);
        
        // 为每个附图生成签名URL
        for (Figure figure : figures) {
            if (figure != null && figure.getImage() != null && StringUtils.isNotBlank(figure.getImage().getS3Key())) {
                try {
                    String signedUrl = fileManager.signFile(figure.getImage().getS3Key());
                    figure.getImage().setUrl(signedUrl);
                    log.debug("为附图生成签名URL成功，S3Key: {}, URL: {}", figure.getImage().getS3Key(), signedUrl);
                } catch (Exception e) {
                    log.warn("为附图生成签名URL失败，S3Key: {}", figure.getImage().getS3Key(), e);
                }
            }
        }
        
        return FigureContentBO.builder()
                .figures(figures)
                .build();
    }

    /**
     * 创建空的附图内容
     * 
     * @return 空的附图内容对象
     */
    private FigureContentBO createEmptyFigureContent() {
        return FigureContentBO.builder()
                .figures(new ArrayList<>())
                .build();
    }
} 
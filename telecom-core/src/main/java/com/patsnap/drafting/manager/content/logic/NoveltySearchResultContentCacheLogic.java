package com.patsnap.drafting.manager.content.logic;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.patsnap.drafting.client.model.ainovelty.AiNoveltySearchResponse;
import com.patsnap.drafting.client.model.ainovelty.KeywordExpress;
import com.patsnap.drafting.client.model.ainovelty.KeywordExtractResult;
import com.patsnap.drafting.client.model.ainovelty.KeywordInfo;
import com.patsnap.drafting.enums.task.AiTaskContentTypeEnum;
import com.patsnap.drafting.manager.aitask.AiTaskManager;
import com.patsnap.drafting.request.ainoveltysearch.*;
import com.patsnap.drafting.request.ainoveltysearch.element.BasicElement;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;

import java.util.*;

/**
 * AI查新专利查询结果content处理逻辑
 */
@Component
public class NoveltySearchResultContentCacheLogic extends ContentCacheLogic {

    private static final class ProcessDictStatus {
        static final String SUCCESS = "success";
        static final String FAILED = "failed";
        static final String RUNNING = "running";
        static final String CANCELED = "canceled";
    }
    public static final Set<String> sourceTypeSet = new HashSet<>(Arrays.asList("manual", "keywords_extract"));

    @Autowired
    private AiTaskManager aiTaskManager;

    @Override
    public void updateContent(String taskId, AiTaskContentTypeEnum contentType, Object result, Object[] args) {
        if (!(result instanceof AiNoveltySearchResponse)) {
            return;
        }

        AiNoveltySearchResponse resultDTO = (AiNoveltySearchResponse) result;
        AiSearchResultReqDTO resultReq = (AiSearchResultReqDTO) args[0];

        if (!needToUpdate(resultReq, resultDTO)) {
            return;
        }

        Map<AiTaskContentTypeEnum, Object> contentMap = new EnumMap<>(AiTaskContentTypeEnum.class);
        fillFinalResult(contentMap, resultDTO);
        fillRetrievalElements(contentMap, resultDTO, resultReq.getTaskId());
        aiTaskManager.batchUpdateTaskContent(taskId, contentMap);
    }

    private void fillRetrievalElements(Map<AiTaskContentTypeEnum, Object> contentMap,
                                       AiNoveltySearchResponse resultDTO, String taskId) {
        NoveltySearchElementResDTO resDTO = aiTaskManager.getTaskContent(taskId,
                AiTaskContentTypeEnum.NOVELTY_SEARCH_RETRIEVAL_ELEMENTS);
        if (ObjectUtil.isEmpty(resDTO)) {
            return;
        }
        if (ObjectUtil.isEmpty(resultDTO.getFeatureAnalysisResult())) {
            return;
        }
        if (CollUtil.isEmpty(resultDTO.getFeatureAnalysisResult().getFeatureExtractResult())) {
            return;
        }
        Map<String, List<KeywordInfo>> extendMap = new HashMap<>();
        Map<String, List<KeywordInfo>> ipcMap = new HashMap<>();
        for(KeywordExtractResult obj : resultDTO.getFeatureAnalysisResult().getKeywordsExtractResult()) {
            for(KeywordExpress expressItem : obj.getExpress()) {
                String key = obj.getFeatureTextOriginal() + "_" + expressItem.getWord().getKey();
                expressItem.getExtend().forEach(extendItem -> {
                    List<KeywordInfo> extendLists = extendMap.getOrDefault(key, new ArrayList<>());
                    if (!sourceTypeSet.contains(extendItem.getSource())) {
                        extendLists.add(extendItem);
                    }
                });
                expressItem.getIpc().forEach(ipcItem -> {
                    List<KeywordInfo> ipcLists = ipcMap.getOrDefault(key, new ArrayList<>());
                    if (!sourceTypeSet.contains(ipcItem.getSource())) {
                        ipcLists.add(ipcItem);
                    }
                });
            }
        }
        for (NoveltySearchElementDataDTO data : resDTO.getData()) {
            for (NoveltySearchElementDataDTO.Element element : data.getExpress()) {
                String key = data.getFeatureTextOriginal() + "_" + element.getWord().getKey();
                if (extendMap.containsKey(key)) {
                    List<KeywordInfo> extendLists = extendMap.getOrDefault(key, new ArrayList<>());
                    extendLists.forEach(extendItem -> {
                        BasicElement basicElement = new BasicElement();
                        basicElement.setSource(extendItem.getSource());
                        basicElement.setKey(extendItem.getKey());
                        element.getExtend().add(basicElement);
                    });
                }
                if (ipcMap.containsKey(key)) {
                    List<KeywordInfo> ipcLists = ipcMap.getOrDefault(key, new ArrayList<>());
                    ipcLists.forEach(ipcItem -> {
                        BasicElement basicElement = new BasicElement();
                        basicElement.setSource(ipcItem.getSource());
                        basicElement.setKey(ipcItem.getKey());
                        element.getIpc().add(basicElement);
                    });
                }
            }
        }
        contentMap.put(AiTaskContentTypeEnum.NOVELTY_SEARCH_RETRIEVAL_ELEMENTS, resDTO);

    }

    private void fillFinalResult(Map<AiTaskContentTypeEnum, Object> contentMap,
                                 AiNoveltySearchResponse resultDTO) {
        contentMap.put(AiTaskContentTypeEnum.NOVELTY_SEARCH_AGENT_RESULT, resultDTO);
        // fixme 待确认逻辑
//        if (CollUtil.isNotEmpty(resultDTO.getFinalResult())) {
//            int maxLength = Math.min(3, resultDTO.getFinalResult().size());
//            contentMap.put(AiTaskContentTypeEnum.NOVELTY_SEARCH_FEATURE_CONFIRM,
//                    resultDTO.getFinalResult().subList(0, maxLength));
//        }
    }

    private boolean needToUpdate(AiSearchResultReqDTO resultReq, AiNoveltySearchResponse resultDTO) {
        if (ObjectUtil.isEmpty(resultDTO.getProcessDict())) {
            return resultReq.getIsFinished();
        }
        return !ProcessDictStatus.RUNNING.equals(resultDTO.getTaskStatus());
    }

}
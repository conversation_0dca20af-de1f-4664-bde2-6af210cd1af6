package com.patsnap.drafting.manager.agent;

import com.patsnap.drafting.client.AgentClient;
import com.patsnap.drafting.client.model.AgentToolDataRequestDTO;
import com.patsnap.drafting.manager.JsonMapperManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class AgentToolManager {

    private final AgentClient agentClient;
    private final JsonMapperManager jsonMapperManager;

    public void saveToolData(String jobId, String toolData, String toolKey) {
        AgentToolDataRequestDTO requestDTO =
                AgentToolDataRequestDTO.builder().jobId(jobId).dataValue(toolData).dataKey(toolKey).build();
        try {
            Map<String, Object> response = agentClient.saveData(requestDTO);
            log.info("[saveToolData] save completed, jobId: {}, toolKey: {}, response: {}",
                    jobId, toolKey, response);
        } catch (Exception e) {
            log.error("[saveToolData] save error, jobId: {}, toolKey: {}", jobId, toolKey, e);
            // 不抛出异常，避免影响主流程
        }
    }

    public <T> T getToolData(String jobId, String toolKey, Class<T> clazz) {
        String commonResponse = getToolDataResponse(jobId, toolKey);
        if (commonResponse != null) {
            return jsonMapperManager.convertStringToTargetType(commonResponse, clazz);
        }
        return null;
    }
    
    private String getToolDataResponse(String jobId, String toolKey) {
        AgentToolDataRequestDTO requestDTO = AgentToolDataRequestDTO.builder()
                .jobId(jobId)
                .dataKey(toolKey)
                .build();
        try {
            Map<String, Object> response = agentClient.getData(requestDTO);
            if (response != null && response.get("data_value") != null) {
                String dataValue = (String) response.get("data_value");
                return jsonMapperManager.convertStringToTargetType(dataValue, String.class);
            }
        } catch (Exception e) {
            log.warn("[getToolDataResponse] get error", e);
        }
        return null;
    }
}

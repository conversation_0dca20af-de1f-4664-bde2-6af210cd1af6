package com.patsnap.drafting.manager.aispecification.content;

import com.patsnap.drafting.client.OpenAiClient;
import com.patsnap.drafting.config.UrlConfig;
import com.patsnap.drafting.enums.common.OperateTypeEnum;
import com.patsnap.drafting.enums.task.AiSpecificationStepEnum;
import com.patsnap.drafting.enums.task.AiTaskContentTypeEnum;
import com.patsnap.drafting.manager.aispecification.content.base.AbstractSpecificationCommonFullContent;
import com.patsnap.drafting.manager.aitask.AiTaskManager;
import com.patsnap.drafting.request.GenerateContentRequestDTO;

import org.springframework.stereotype.Component;

/**
 * AI-说明书撰写 标题
 */
@Component
public class SpecificationTitleFullContent extends AbstractSpecificationCommonFullContent {

    protected SpecificationTitleFullContent(AiTaskManager aiTaskManager,
            UrlConfig urlConfig, OpenAiClient openAiClient) {
        super(aiTask<PERSON>anager, urlConfig, openAiClient);
    }

    @Override
    public String getContentType() {
        return AiTaskContentTypeEnum.SPECIFICATION_TITLE.getType();
    }

    @Override
    public String getFunction() {
        return "generate_title";
    }

    @Override
    protected void beforeGetContent(GenerateContentRequestDTO request) {
        //title是说明书撰写正文里，第一步获取，需要在这里这个步骤下所有内容都需要清除一下
        if (OperateTypeEnum.REGENERATE.getValue().equals(request.getOperateType())) {
            aiTaskManager.deleteTaskContentByStep(request.getTaskId(), AiSpecificationStepEnum.SPECIFICATION_RESULT);
        }
    }
}

package com.patsnap.drafting.manager.content.logic;

import com.patsnap.drafting.client.model.NoveltySearchFeatureExtractResDTO;
import com.patsnap.drafting.enums.task.AiTaskContentTypeEnum;
import com.patsnap.drafting.manager.aitask.AiTaskManager;
import com.patsnap.drafting.manager.credit.CreditManager;
import com.patsnap.drafting.response.ainoveltysearch.FeatureExactionResponseDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * AI查新专利技术特征更新content处理逻辑
 */
@Component
public class NoveltySearchUpdateTechFeatureContentCacheLogic extends ContentCacheLogic {

    @Autowired
    private AiTaskManager aiTaskManager;

    @Autowired
    private CreditManager creditManager;

    @Override
    public void updateContent(String taskId, AiTaskContentTypeEnum contentType, Object result, Object[] args) {
        if (!(result instanceof NoveltySearchFeatureExtractResDTO)) {
            return;
        }
        aiTaskManager.updateTaskContent(taskId, contentType, result);
        aiTaskManager.deleteTaskContentByContentType(taskId,
                List.of(AiTaskContentTypeEnum.NOVELTY_SEARCH_TECH_FEATURE_COMPARISON.getType(),
                        AiTaskContentTypeEnum.NOVELTY_SEARCH_TASK_ID.getType(),
                        AiTaskContentTypeEnum.NOVELTY_SEARCH_AGENT_RESULT.getType(),
                        AiTaskContentTypeEnum.NOVELTY_SEARCH_PATENT_CONFIRM_FEATURE.getType(),
                        AiTaskContentTypeEnum.NOVELTY_SEARCH_FEATURE_CONFIRM.getType(),
                        AiTaskContentTypeEnum.NOVELTY_SEARCH_REPORT_TITLE.getType(),
                        AiTaskContentTypeEnum.NOVELTY_SEARCH_REPORT_COMPARATIVE_LITERATURE.getType(),
                        AiTaskContentTypeEnum.NOVELTY_SEARCH_REPORT_REVIEW_OF_NOVELTY.getType(),
                        AiTaskContentTypeEnum.NOVELTY_SEARCH_REPORT_REVIEW_OF_CREATIVE.getType()
                ));
    }
}
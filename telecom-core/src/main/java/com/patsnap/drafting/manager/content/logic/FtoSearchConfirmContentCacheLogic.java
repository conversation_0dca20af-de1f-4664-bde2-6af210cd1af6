package com.patsnap.drafting.manager.content.logic;

import cn.hutool.core.collection.CollUtil;
import com.patsnap.drafting.enums.task.AiTaskContentTypeEnum;
import com.patsnap.drafting.enums.task.AsyncTaskStatusEnum;
import com.patsnap.drafting.manager.aiftosearch.FtoSearchManager;
import com.patsnap.drafting.manager.aitask.AiTaskManager;
import com.patsnap.drafting.request.ainoveltysearch.AiSearchFinalResult;
import com.patsnap.drafting.request.ainoveltysearch.AiSearchResultDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.util.ArrayList;
import java.util.EnumMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import static com.patsnap.drafting.enums.task.AiTaskContentTypeEnum.FTO_SEARCH_AGENT_RESULT;

/**
 * FTO查新确认相似特征
 */
@Component
public class FtoSearchConfirmContentCacheLogic extends ContentCacheLogic {

    @Autowired
    private AiTaskManager aiTaskManager;

    @Autowired
    private FtoSearchManager ftoSearchManager;

    @Override
    public void updateContent(String taskId, AiTaskContentTypeEnum contentType, Object result,
            Object[] args) {
        if (!(result instanceof List)) {
            return;
        }

        List<AiSearchFinalResult> resultList = (List<AiSearchFinalResult>) result;
        if (CollUtil.isEmpty(resultList)) {
            return;
        }
        AiSearchResultDTO aiSearchResult =  aiTaskManager.getTaskContent(taskId,
                FTO_SEARCH_AGENT_RESULT);
        List<AiSearchFinalResult> finalResults = aiSearchResult.getFinalResult();
        if (CollUtil.isEmpty(finalResults)) {
            return;
        }
        // 表里的数据，finalResult里面可能存在同样的patentId不止一条数据，这边至少要保证不报错Duplicated Key
        Map<String, AiSearchFinalResult> updateMap = resultList.stream()
                .collect(Collectors.toMap(AiSearchFinalResult::getPatentId, Function.identity(), (existing, replacement) -> existing));
        for (int i = 0; i < finalResults.size(); i++) {
            AiSearchFinalResult finalResult = finalResults.get(i);
            if (updateMap.containsKey(finalResult.getPatentId())) {
                finalResults.set(i, updateMap.get(finalResult.getPatentId()));
            }else{
                finalResult.setMostSimilar(false);
                finalResult.setSelected(false);
            }
        }
        aiSearchResult.setFinalResult(ftoSearchManager.updateFinalResultDetail(finalResults, taskId));
        Map<AiTaskContentTypeEnum, Object> contentMap = new EnumMap<>(AiTaskContentTypeEnum.class);
        contentMap.put(contentType, aiSearchResult.getFinalResult().stream().filter(AiSearchFinalResult::isSelected).toList());
        contentMap.put(AiTaskContentTypeEnum.FTO_SEARCH_AGENT_RESULT, aiSearchResult);
        aiTaskManager.batchUpdateTaskContent(taskId, contentMap);
        aiTaskManager.updateTaskStatus(taskId, AsyncTaskStatusEnum.Running);
        List<String> stepList = new ArrayList<>();
        stepList.add(AiTaskContentTypeEnum.FTO_SEARCH_REPORT_TITLE.getType());
        aiTaskManager.deleteTaskContentByContentType(taskId, stepList);
    }
}
package com.patsnap.drafting.manager.aitranslation.operate.impl;

import com.patsnap.core.common.copilot.gpt.enums.GPTModelEnum;
import com.patsnap.core.common.copilot.streaming.handler.SplitListJsonStreamingResponseHandler;
import com.patsnap.core.common.copilot.streaming.model.GptResponseDTO;
import com.patsnap.drafting.client.OpenAiClient;
import com.patsnap.drafting.config.AiTranslationConfig;
import com.patsnap.drafting.config.UrlConfig;
import com.patsnap.drafting.enums.prompt.PromptKeyEnum;
import com.patsnap.drafting.enums.prompt.ScenarioEnum;
import com.patsnap.drafting.manager.aitranslation.handler.AiTranslationStreamingResponseHandler;
import com.patsnap.drafting.manager.aitranslation.operate.TranslationFullTextService;
import com.patsnap.drafting.model.aitranslation.AiTransContextBo;
import com.patsnap.drafting.model.aitranslation.TranslationBO;
import com.patsnap.drafting.response.aitranslation.TranslationKeywordResDTO;
import com.patsnap.drafting.util.ParagraphUtils;
import com.patsnap.spring.boot.autoconfigure.web.entity.CommonResponse;
import static com.patsnap.drafting.manager.aitranslation.operate.TranslationConstant.*;
import java.util.List;
import org.apache.commons.collections.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;
import com.google.common.collect.ImmutableMap;
import com.patsnap.core.common.copilot.streaming.handling.StreamingChatLanguageModel;
import reactor.core.publisher.Flux;

@Component
public class FullTextGptModel4TurboImpl extends TranslationBasicImpl implements
        TranslationFullTextService {

    private static GPTModelEnum GPT_MODEL = GPTModelEnum.GPT_MODEL_4_TURBO;

    public FullTextGptModel4TurboImpl(
            UrlConfig urlConfig,
            AiTranslationConfig aiTranslationConfig,
            OpenAiClient openAiClient) {
        super(urlConfig, aiTranslationConfig, openAiClient);
    }


    @Override
    public String model() {
        return GPT_MODEL.getModelName();
    }

    @Override
    public Flux<CommonResponse<GptResponseDTO<List<TranslationBO>>>> generate(
            AiTransContextBo contextBo) {
        String sourceInput = contextBo.getInput();
        String keywordsStr = getPromptKeywordsStr(contextBo.getTerms());
        // 给段落添加特定标识符
        sourceInput = ParagraphUtils.addParagraphPrefix(sourceInput);
        String translation = openAiClient.chatCompletions(
                PromptKeyEnum.SENTENCE_TRANSLATION.getValue(), GPTModelEnum.GPT_MODEL_4_TURBO,
                ScenarioEnum.AI_TRANSLATION, String.class,
                ImmutableMap.of(INPUT, sourceInput, TARGET_LANG, contextBo.getTargetLang(),
                        KEYWORDS,
                        keywordsStr, TECH_TOPIC, contextBo.getTechTopic()));
        // 获取校验翻译结果prompt
        String msgPrompt = openAiClient.buildPromptByPlatform(
                PromptKeyEnum.PROOFREADING_TRANSLATION.getValue(),
                ImmutableMap.of(INPUT, translation, TARGET_LANG, contextBo.getTargetLang(),
                        KEYWORDS, keywordsStr, TECH_TOPIC, contextBo.getTechTopic()));
        return createFluxFromModel(msgPrompt);
    }

    /**
     * 将术语处理成prompt中需要的格式: * 原始文本<-->翻译文本
     *
     * @param keywords
     * @return
     */
    private @NotNull String getPromptKeywordsStr(List<TranslationKeywordResDTO> keywords) {
        if (CollectionUtils.isEmpty(keywords)) {
            return "";
        }
        StringBuilder sb = new StringBuilder();
        for (TranslationKeywordResDTO keyword : keywords) {
            keyword.getOriginal()
                    .forEach(t -> sb.append(t).append(CORNER_MARK).append(keyword.getTranslation())
                            .append(CARRIER_MARK));
        }
        return sb.toString();
    }

    private Flux<CommonResponse<GptResponseDTO<List<TranslationBO>>>> createFluxFromModel(
            String prompt) {
        StreamingChatLanguageModel model = streamingModelBuilder.build(GPT_MODEL,
                ScenarioEnum.AI_TRANSLATION.getValue());
        return Flux.create(sink -> {
            SplitListJsonStreamingResponseHandler<List<TranslationBO>> handler =
                    new AiTranslationStreamingResponseHandler(
                    sink, prompt);
            model.generate(prompt, handler);
            sink.onCancel(handler::onCancel);
        });
    }
}

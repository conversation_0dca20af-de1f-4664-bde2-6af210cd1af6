package com.patsnap.drafting.manager.aispecification.content;

import com.patsnap.drafting.client.OpenAiClient;
import com.patsnap.drafting.config.UrlConfig;
import com.patsnap.drafting.enums.common.OperateTypeEnum;
import com.patsnap.drafting.manager.aispecification.content.base.AbstractStreamingContentOperation;
import com.patsnap.drafting.manager.aitask.AiTaskManager;
import com.patsnap.drafting.manager.credit.CreditManager;
import org.springframework.stereotype.Service;

import static com.patsnap.drafting.enums.prompt.SpecificationPromptKeyEnum.IMPROVE_CONTENT;

/**
 * AI说明书撰写 - 内容改写，润色
 */
@Service
public class ImproveContentOperation extends AbstractStreamingContentOperation {


    protected ImproveContentOperation(AiTaskManager aiTaskManager, UrlConfig urlConfig, OpenAiClient openAiClient,
            CreditManager creditManager) {
        super(aiTaskManager, urlConfig, openAiClient, creditManager);
    }

    @Override
    public OperateTypeEnum getOperationType() {
        return OperateTypeEnum.POLISH;
    }

    @Override
    protected String getPromptKey(String contenType) {
        return IMPROVE_CONTENT.getValue();
    }
}

package com.patsnap.drafting.manager.content.logic;

import com.patsnap.drafting.enums.task.AiTaskContentTypeEnum;
import com.patsnap.drafting.manager.aitask.AiTaskManager;
import com.patsnap.drafting.manager.content.TaskContentManager;
import java.util.LinkedHashMap;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 重新翻译content处理逻辑
 * <AUTHOR>
 */
@Component
public class TranslationRewriteContentCacheLogic extends ContentCacheLogic {

    @Autowired
    private AiTaskManager aiTaskManager;

    @Autowired
    private TaskContentManager taskContentManager;


    @Override
    public void updateContent(String taskId, AiTaskContentTypeEnum contentType, Object result,
            Object[] args) {
        int outputIndex = (int) args[1];
        String original = (String) args[2];
        List<LinkedHashMap<String, Object>> translations =
                taskContentManager.getTaskContent(taskId, AiTaskContentTypeEnum.TRANSLATION_RESULT);
        LinkedHashMap<String, Object> replace = translations.get(outputIndex);
        replace.put("translated_text", result);
        replace.put("src_text", original);
        aiTaskManager.updateTaskContent(taskId, AiTaskContentTypeEnum.TRANSLATION_RESULT, translations);
    }
}
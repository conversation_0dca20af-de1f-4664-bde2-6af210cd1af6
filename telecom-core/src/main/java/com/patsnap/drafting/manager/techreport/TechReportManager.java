package com.patsnap.drafting.manager.techreport;

import com.patsnap.common.web.entity.CommonResponse;
import com.patsnap.drafting.manager.techreport.model.TechReportConfigDTO;
import com.patsnap.drafting.request.techreport.TechReportInitReqDTO;
import com.patsnap.drafting.response.techreport.TechReportCreateFieldsResDTO;
import com.patsnap.drafting.response.techreport.TechReportExtractFeaturesResDTO;
import com.patsnap.drafting.response.techreport.TechReportPreviewResDTO;
import com.patsnap.drafting.response.techreport.TechReportRecommendResDTO;
import com.patsnap.drafting.response.techreport.TechReportPreviewWrapperDTO;

import java.util.List;

/**
 * 技术简报管理器接口
 * 负责技术简报的创建、编辑、查询等操作
 *
 * <AUTHOR>
 */
public interface TechReportManager {

    /**
     * 初始化技术简报对话
     *
     * @param agentJobId agent 任务 ID
     * @param request 初始化请求
     * @return 初始化结果
     */
    TechReportConfigDTO intentIdentity(String agentJobId, TechReportInitReqDTO request);

    /**
     * 提取技术特征
     *
     * @param agentJobId agent 任务 ID
     * @return 提取特征结果
     */
    TechReportExtractFeaturesResDTO extractFeatures(String agentJobId, TechReportInitReqDTO request);

    /**
     * 推荐公司与技术
     *
     * @param agentJobId agent 任务 ID
     * @return 推荐结果
     */
    TechReportRecommendResDTO recommend(String agentJobId);

    /**
     * 技术预览
     *
     * @param agentJobId agent 任务 ID
     * @return 预览结果
     */
    TechReportPreviewWrapperDTO preview(String agentJobId);
    
    /**
     * 组装创建简报必要的字段
     *
     * @param agentJobId agent 任务 ID
     * @return 创建简报所需的字段数据
     */
    TechReportCreateFieldsResDTO createFields(String agentJobId);
    
    /**
     * 创建技术监控报告
     *
     * @param agentJobId agent 任务 ID
     * @param sourceType 创建monitor的来源类型
     * @return 创建结果对象
     */
    String createTechMonitor(String agentJobId, String sourceType);
} 
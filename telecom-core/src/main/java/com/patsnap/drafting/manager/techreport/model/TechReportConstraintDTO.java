package com.patsnap.drafting.manager.techreport.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.annotation.Nulls;
import com.patsnap.drafting.response.techreport.CompanyInfo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 * 技术报告约束条件DTO
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class TechReportConstraintDTO {

    /**
     * 技术效果的核心关键词
     */
    @JsonProperty("tech_effect")
    private List<String> techEffect;

    /**
     * 技术问题的核心关键词
     */
    @JsonProperty("tech_problem")
    private List<String> techProblem;

    /**
     * 国家限制
     */
    @JsonProperty("country")
    private List<String> country;

    /**
     * 开始日期
     */
    @JsonProperty("start_date")
    private LocalDate startDate;

    /**
     * prompt提取的仅监控的公司列表
     */
    @JsonProperty("only_monitor_companies")
    private List<String> onlyMonitorCompanies;


    /**
     * 经过处理的公司列表
     */
    @JsonProperty("only_monitor_company_list")
    private List<CompanyInfo> onlyMonitorCompanyList;

    /**
     * 仅监控的子技术的核心关键词
     */
    @JsonProperty("only_monitor_techs")
    private List<String> onlyMonitorTechs;
} 
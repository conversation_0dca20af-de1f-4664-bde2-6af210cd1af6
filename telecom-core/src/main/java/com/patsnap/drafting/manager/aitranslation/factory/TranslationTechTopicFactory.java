package com.patsnap.drafting.manager.aitranslation.factory;

import com.patsnap.core.common.copilot.gpt.enums.GPTModelEnum;
import com.patsnap.drafting.annotation.TaskContentCache;
import com.patsnap.drafting.enums.task.AiTaskContentTypeEnum;
import com.patsnap.drafting.manager.aitranslation.operate.TranslationTechTopicService;
import com.patsnap.drafting.model.aitranslation.AiTransContextBo;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class TranslationTechTopicFactory {

    private GPTModelEnum model = GPTModelEnum.GPT_MODEL_4_TURBO;

    @Autowired
    private List<TranslationTechTopicService> translationTechTopicServices;

    @TaskContentCache(contentType = AiTaskContentTypeEnum.TRANSLATION_TECH_TOPIC)
    public String generate(AiTransContextBo contextBo) {
        TranslationTechTopicService service = null;
        for (TranslationTechTopicService translationTechTopicService : translationTechTopicServices) {
            if (translationTechTopicService.model() != model) {
                continue;
            }
            service = translationTechTopicService;
        }
        String result = service.generate(contextBo);
        contextBo.setTechTopic(result);
        return result;
    }

}

package com.patsnap.drafting.manager;

import com.patsnap.core.common.copilot.constant.GPTStatus;
import com.patsnap.core.common.copilot.gpt.enums.GPTModelEnum;
import com.patsnap.core.common.copilot.streaming.model.GptResponseDTO;
import com.patsnap.drafting.client.OpenAiClient;
import com.patsnap.drafting.config.UrlConfig;
import com.patsnap.drafting.enums.prompt.ScenarioEnum;
import com.patsnap.drafting.manager.aitask.AiTaskManager;
import com.patsnap.drafting.model.base.StreamingModelBO;
import com.patsnap.spring.boot.autoconfigure.web.entity.CommonResponse;

import com.fasterxml.jackson.core.type.TypeReference;

import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;

/**
 * 同步获取AI结果，全部返回
 *
 * @param <T>
 */
@Slf4j
public abstract class AbstractGenerateFullContent<T> extends AbstractGenerateStreamingContent<T> {

    private final OpenAiClient openAiClient;

    protected AbstractGenerateFullContent(AiTaskManager aiTaskManager, UrlConfig urlConfig, OpenAiClient openAiClient) {
        super(aiTaskManager, urlConfig);
        this.openAiClient = openAiClient;
    }

    @Override
    protected Flux<CommonResponse<GptResponseDTO<T>>> getContent(StreamingModelBO streamingModelBO) {
        boolean status = true;
        GptResponseDTO<T> data;
        try {
            //非流式的方式一次性返回
            T result = openAiClient.callGptByPrompt(GPTModelEnum.getGptModelEnum(streamingModelBO.getModelName()),
                    streamingModelBO.getPrompt(), ScenarioEnum.AI_SPECIFICATION, getTypeReference());

            data = GptResponseDTO.<T>builder().content(result).status(GPTStatus.FINISH).build();
        } catch (Exception e) {
            status = false;
            log.warn("[AI说明书撰写]请求AI说明书撰写 {}报错", getContentType(), e);
            data = GptResponseDTO.<T>builder().status(GPTStatus.FAILED).build();
        }
        CommonResponse<GptResponseDTO<T>> response = CommonResponse.<GptResponseDTO<T>>builder().withData(data)
                .withStatus(status).build();
        return Flux.just(response);
    }

    protected abstract TypeReference<T> getTypeReference();

}

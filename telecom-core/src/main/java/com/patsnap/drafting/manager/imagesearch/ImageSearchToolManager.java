package com.patsnap.drafting.manager.imagesearch;

import com.google.common.collect.Lists;
import com.patsnap.common.request.UserIdHolder;
import com.patsnap.core.common.copilot.exception.GPTErrorException;
import com.patsnap.core.common.copilot.gpt.enums.GPTModelEnum;
import com.patsnap.core.common.track.ModuleType;
import com.patsnap.drafting.client.OpenAiClient;
import com.patsnap.drafting.client.OpenApiClient;
import com.patsnap.drafting.client.PatentApiClient;
import com.patsnap.drafting.client.model.FileUploadResponse;
import com.patsnap.drafting.client.model.ModelCompletionDTO;
import com.patsnap.drafting.enums.common.RiskLevelEnum;
import com.patsnap.drafting.enums.prompt.ScenarioEnum;
import com.patsnap.drafting.exception.BizException;
import com.patsnap.drafting.exception.errorcode.ContentErrorCodeEnum;
import com.patsnap.drafting.manager.FileManager;
import com.patsnap.drafting.manager.IdentityAccountManager;
import com.patsnap.drafting.manager.JsonMapperManager;
import com.patsnap.drafting.manager.agent.AgentToolManager;
import com.patsnap.drafting.manager.agent.CommonAgentToolManager;
import com.patsnap.drafting.manager.agent.model.AgentToolResponseDTO;
import com.patsnap.drafting.manager.patent.PatentInfoManager;
import com.patsnap.drafting.manager.track.TrackingManager;
import com.patsnap.drafting.request.imagesearch.ImageLocPredictRequest;
import com.patsnap.drafting.request.imagesearch.ImageMultipleSimilarSearchRequest;
import com.patsnap.drafting.request.imagesearch.ImagePatentInfo;
import com.patsnap.drafting.request.imagesearch.ImageReorderRequest;
import com.patsnap.drafting.request.imagesearch.ImageSimilarSearchRequest;
import com.patsnap.drafting.request.patent.PatentImagesRequestDTO;
import com.patsnap.drafting.response.imagesearch.ComparisonsItem;
import com.patsnap.drafting.response.imagesearch.ImageFeatureComparisonResponse;
import com.patsnap.drafting.response.imagesearch.ImageLocPredictResponse;
import com.patsnap.drafting.response.imagesearch.ImageSearchRedisDataResponse;
import com.patsnap.drafting.response.imagesearch.ImageSimilarReportResponse;
import com.patsnap.drafting.response.imagesearch.ImageSimilarSearchData;
import com.patsnap.drafting.response.imagesearch.ImageSimilarSearchResponse;
import com.patsnap.drafting.response.imagesearch.ImageSimilarityResponse;
import com.patsnap.drafting.response.imagesearch.ImageSimilarityResult;
import com.patsnap.drafting.response.imagesearch.PatentMessagesItem;
import com.patsnap.drafting.util.ImageMergeUtil;
import com.patsnap.drafting.util.ImagePromptUtils;
import com.patsnap.drafting.util.RedissonUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Base64;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.patsnap.drafting.constants.PatentDetailFieldConstants.COUNTRY;
import static com.patsnap.drafting.constants.PatentDetailFieldConstants.SIMPLE_LEGAL_STATUS;
import static com.patsnap.drafting.constants.TrackEvent.AI_IMAGE_SEARCH_AGENT;
import static com.patsnap.drafting.enums.prompt.PromptKeyEnum.IMAGE_SIMILARITY_COMPARISON;

/**
 * 图像搜索工具管理器
 * 负责图像相似度搜索、图像特征比较与分析等相关功能
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ImageSearchToolManager {

    // 常量定义
    public static final GPTModelEnum DEFAULT_MODEL = GPTModelEnum.GPT_CLAUDE_3_7_SONNET;
    public static final int IMAGE_MAX_SIZE = 20;
    public static final long EXPIRE_TIME = 4;
    
    // 分页查询相关常量
    public static final int PAGE_SIZE = 100;  // 每页查询数量
    public static final int TOTAL_PAGES = 2;  // 总页数，查询200篇专利需要2页
    public static final int TARGET_PATENT_COUNT = 200;  // 目标专利数量

    // Redis键前缀
    public static final String SIMILAR_IMAGE_SEARCH_REDIS_KEY = "similar_image_search_";
    public static final String SIMILAR_IMAGE_INPUT_REDIS_KEY = "similar_image_input_";
    public static final String SIMILAR_IMAGE_COUNTRY_REDIS_KEY = "similar_image_country_";
    public static final String SIMILAR_IMAGE_FEATURE_COMPARISON_REDIS_KEY = "similar_image_feature_comparison_";
    public static final String SIMILAR_IMAGE_REPORT_REDIS_KEY = "similar_image_report_";
    
    // 专利详细信息查询字段
    private static final List<String> PATENT_DETAIL_FIELDS = Arrays.asList(COUNTRY, SIMPLE_LEGAL_STATUS);
    
    public static final String SEARCH_IMAGE_SINGLE = "search-image-single";
    public static final String FILE_UPLOAD_RESULT = "file_upload_result";
    
    // 依赖注入
    private final OpenAiClient openAiClient;
    private final OpenApiClient openApiClient;
    private final RedissonClient redissonClient;
    private final PatentApiClient patentApiClient;
    private final ImagePromptUtils imagePromptUtils;
    private final RedissonUtils redissonUtils;
    private final IdentityAccountManager identityAccountManager;
    private final PatentInfoManager patentInfoManager;
    private final TrackingManager trackingManager;
    private final AgentToolManager agentToolManager;
    private final JsonMapperManager jsonMapperManager;
    private final CommonAgentToolManager commonAgentToolManager;

    /**
     * 单图像相似搜索
     * 支持分页查询，最多查询200篇专利（分2次调用，每次100篇）
     * @param taskId 任务ID
     * @param request 搜索请求参数
     * @param enableLegalStatusFilter 是否启用法律状态过滤，true时设置为"1,2"，false时设置为null
     */
    public AgentToolResponseDTO searchSimilarImageBySingle(String taskId, ImageSimilarSearchRequest request, Boolean enableLegalStatusFilter) {
        log.info("开始检索相似图片, taskId={}, request={}, enableLegalStatusFilter={}", taskId, request, enableLegalStatusFilter);
        initRequest(taskId, request, enableLegalStatusFilter);

        // 执行分页查询，获取200篇专利
        ImageSimilarSearchResponse mergedResponse = executePagedSearch(request);
        
        // 处理搜索结果
        List<PatentMessagesItem> patentMessagesItems = processSearchResults(mergedResponse);
        
        // 保存搜索结果到Redis
        saveSearchResultToRedis(taskId, Strings.join(List.of(request.getUrl()), ','), request.getCountry(), mergedResponse);
        // 保存结果到 agent
        agentToolManager.saveToolData(taskId, jsonMapperManager.convertToString(mergedResponse), SIMILAR_IMAGE_SEARCH_REDIS_KEY + taskId);
        //发送埋点
        trackingManager.addTracking(ModuleType.EUREKA, AI_IMAGE_SEARCH_AGENT, SEARCH_IMAGE_SINGLE, null, null);
        // 保存结果到 agent 并返回响应
        return getAgentToolResponseDTO(taskId, jsonMapperManager.convertToString(mergedResponse),
                SIMILAR_IMAGE_FEATURE_COMPARISON_REDIS_KEY, patentMessagesItems.size());
    }

    /**
     * 执行分页搜索，获取200篇专利
     * 分2次调用API，每次查询100篇，然后合并结果
     */
    private ImageSimilarSearchResponse executePagedSearch(ImageSimilarSearchRequest request) {
        log.info("开始执行分页搜索，目标获取{}篇专利", TARGET_PATENT_COUNT);
        
        List<PatentMessagesItem> allPatentMessages = new ArrayList<>();
        ImageSimilarSearchResponse finalResponse = null;
        
        // 执行分页查询
        for (int page = 0; page < TOTAL_PAGES; page++) {
            int offset = page * PAGE_SIZE;
            log.info("执行第{}次查询，offset={}", page + 1, offset);
            
            // 创建当前页的请求参数
            ImageSimilarSearchRequest pageRequest = createPageRequest(request, offset);
            
            // 调用API执行搜索
            ImageSimilarSearchResponse pageResponse = openApiClient.searchImageBySingle(pageRequest);
            
            if (pageResponse != null && pageResponse.getData() != null) {
                List<PatentMessagesItem> pagePatentMessages = Optional.ofNullable(pageResponse.getData().getPatentMessages())
                        .orElseGet(Collections::emptyList);
                
                log.info("第{}次查询返回{}条结果", page + 1, pagePatentMessages.size());
                allPatentMessages.addAll(pagePatentMessages);
                
                // 使用第一次查询的响应作为基础响应结构
                if (finalResponse == null) {
                    finalResponse = pageResponse;
                }
                
                // 如果当前页返回的结果少于PAGE_SIZE条，说明已经没有更多数据了
                if (pagePatentMessages.size() < PAGE_SIZE) {
                    log.info("第{}次查询返回结果不足{}条，停止后续查询", page + 1, PAGE_SIZE);
                    break;
                }
            } else {
                log.warn("第{}次查询返回空结果", page + 1);
                break;
            }
        }
        
        // 合并所有结果到最终响应中
        if (finalResponse != null && finalResponse.getData() != null) {
            finalResponse.getData().setPatentMessages(allPatentMessages);
            log.info("分页搜索完成，总共获取{}条专利结果", allPatentMessages.size());
        }
        
        return finalResponse;
    }

    /**
     * 创建分页请求参数
     * 复制原始请求的所有参数，并设置分页相关参数
     */
    private ImageSimilarSearchRequest createPageRequest(ImageSimilarSearchRequest originalRequest, int offset) {
        ImageSimilarSearchRequest pageRequest = new ImageSimilarSearchRequest();
        
        // 复制原始请求的所有参数
        pageRequest.setLoc(originalRequest.getLoc());
        pageRequest.setCountry(originalRequest.getCountry());
        pageRequest.setIsHttps(originalRequest.getIsHttps());
        pageRequest.setApplyEndTime(originalRequest.getApplyEndTime());
        pageRequest.setAssignees(originalRequest.getAssignees());
        pageRequest.setUrl(originalRequest.getUrl());
        pageRequest.setFileKey(originalRequest.getFileKey());
        pageRequest.setPublicEndTime(originalRequest.getPublicEndTime());
        pageRequest.setMainField(originalRequest.getMainField());
        pageRequest.setField(originalRequest.getField());
        pageRequest.setPublicStartTime(originalRequest.getPublicStartTime());
        pageRequest.setStemming(originalRequest.getStemming());
        pageRequest.setApplyStartTime(originalRequest.getApplyStartTime());
        pageRequest.setModel(originalRequest.getModel());
        pageRequest.setPatentType(originalRequest.getPatentType());
        pageRequest.setLegalStatus(originalRequest.getLegalStatus());
        pageRequest.setLang(originalRequest.getLang());
        pageRequest.setSimpleLegalStatus(originalRequest.getSimpleLegalStatus());
        pageRequest.setPreFilter(originalRequest.getPreFilter());
        pageRequest.setOrder(originalRequest.getOrder());
        
        // 设置分页参数
        pageRequest.setLimit(PAGE_SIZE);  // 每次查询100条
        pageRequest.setOffset(offset);  // 设置偏移量
        
        return pageRequest;
    }

    /**
     * 处理搜索结果，设置风险等级并清理敏感信息
     */
    private List<PatentMessagesItem> processSearchResults(ImageSimilarSearchResponse response) {
        List<PatentMessagesItem> patentMessagesItems = Optional.ofNullable(response)
                .map(ImageSimilarSearchResponse::getData)
                .map(ImageSimilarSearchData::getPatentMessages)
                .orElseGet(Collections::emptyList);
        
        for (PatentMessagesItem patentMessagesItem : patentMessagesItems) {
            // 设置风险等级
            Double score = patentMessagesItem.getScore();
            RiskLevelEnum riskLevel = getRiskLevel(score);
            patentMessagesItem.setRiskLevel(riskLevel.getValue());
            
            // 清理敏感信息
            patentMessagesItem.setCurrentAssignee(null);
            patentMessagesItem.setOriginalAssignee(null);
            patentMessagesItem.setInventor(null);
            patentMessagesItem.setApdt(null);
            patentMessagesItem.setPbdt(null);
            
            log.info("设置专利[{}]的风险等级为[{}], 相似度评分为[{}]", 
                    patentMessagesItem.getPatentId(), riskLevel.getValue(), score);
        }
        
        return patentMessagesItems;
    }
    
    /**
     * 初始化请求参数
     * @param taskId 任务 ID
     * @param request 请求对象
     * @param enableLegalStatusFilter 是否启用法律状态过滤，true时设置为"1,2"，false时设置为null
     */
    private void initRequest(String taskId, ImageSimilarSearchRequest request, Boolean enableLegalStatusFilter) {
        //优先通过 agentToolManager 获取 图片上传 的结果
        FileUploadResponse agentToolResponseDTO = agentToolManager.getToolData(taskId, FILE_UPLOAD_RESULT, FileUploadResponse.class);
        if (agentToolResponseDTO != null) {
            log.info("获取到图片上传结果: {}", agentToolResponseDTO);
            request.setUrl(agentToolResponseDTO.getFileUrl());
        }

        if (request.getModel() == 0) {
            request.setModel(1);
        }
        if (request.getPatentType() == null) {
            request.setPatentType("D");
        }
        request.setPreFilter(1);
        // 根据参数值设置法律状态过滤
        if (Boolean.TRUE.equals(enableLegalStatusFilter)) {
            request.setSimpleLegalStatus("1,2");
        } else {
            request.setSimpleLegalStatus(null);
        }
        request.setField("SCORE");
        // 注意：limit 和 offset 将在分页查询中动态设置
    }

    /**
     * 多图像相似搜索
     */
    public ImageSimilarSearchResponse searchSimilarImageByMultiple(String taskId, ImageMultipleSimilarSearchRequest request) {
        log.info("开始检索多图相似图片, taskId={}, request={}", taskId, request);

        // 调用API执行搜索
        ImageSimilarSearchResponse response = openApiClient.searchImageByMultiple(request);

        // 保存搜索结果到Redis
        saveSearchResultToRedis(taskId, Strings.join(request.getUrls(), ','), request.getCountry(), response);

        return response;
    }

    /**
     * 保存搜索结果到Redis
     */
    private void saveSearchResultToRedis(String taskId, String imageUrls, Object country, ImageSimilarSearchResponse response) {
        redissonUtils.set(redissonClient, SIMILAR_IMAGE_INPUT_REDIS_KEY + taskId, imageUrls, EXPIRE_TIME, TimeUnit.HOURS);
        redissonUtils.set(redissonClient, SIMILAR_IMAGE_COUNTRY_REDIS_KEY + taskId, country, EXPIRE_TIME, TimeUnit.HOURS);
        redissonUtils.set(redissonClient, SIMILAR_IMAGE_SEARCH_REDIS_KEY + taskId, response, EXPIRE_TIME, TimeUnit.HOURS);
    }

    /**
     * 比较图像特征
     */
    public AgentToolResponseDTO compareImageFeature(String taskId, Integer mergedCount, Integer col) {
        log.info("开始图片特征对比, taskId={}", taskId);

        // 获取请求参数
        ImageReorderRequest request = getRequest(taskId);
        
        // 将请求参数中的多个图片URL转换为合并的图片列表
        buildMergedImageInfo(mergedCount, col, request);
        
        // 获取图像比较结果
        ImageSimilarityResponse openAiResponse = getImageComparisonResponse(request);

        // 构建特征比较响应
        ImageFeatureComparisonResponse response = buildFeatureComparisonResponse(request, openAiResponse);

        // 保存结果到Redis
        redissonUtils.set(redissonClient, SIMILAR_IMAGE_FEATURE_COMPARISON_REDIS_KEY + taskId, response, EXPIRE_TIME, TimeUnit.HOURS);
        // 保存结果到 agent
        return getAgentToolResponseDTO(taskId, jsonMapperManager.convertToString(response),
                SIMILAR_IMAGE_FEATURE_COMPARISON_REDIS_KEY, response.getComparisons().size());
    }
    
    private void buildMergedImageInfo(Integer mergedCount, Integer col, ImageReorderRequest request) {
        // 过滤出有图片URL的专利信息
        List<ImagePatentInfo> validImagePatentList = request.getImagePatentList().stream()
                .filter(info -> StringUtils.isNotBlank(info.getImageUrl()))
                .toList();
        List<String> mergedImageBase64List = getMergedImageBase64ListWithAuthority(validImagePatentList, mergedCount, col);
        request.setMergedImageBase64List(mergedImageBase64List);
    }
    
    /**
     * 获取合并后的图片Base64列表（带受理局信息）
     * @param imagePatentInfoList 专利图片信息列表
     * @param mergedCount 合并的图片数量
     * @param col 每行显示的图片数量
     * @return 合并后的图片Base64编码列表
     */
    private @NotNull List<String> getMergedImageBase64ListWithAuthority(List<ImagePatentInfo> imagePatentInfoList, Integer mergedCount, Integer col) {
        List<String> mergedImageBase64List = new ArrayList<>();
        List<List<ImagePatentInfo>> imagePatentPartitionList = Lists.partition(imagePatentInfoList, mergedCount);
        // 只取前 19个分区，目前 base64 形式传递图片，Claude 模型最多支持 20 张图片
        if (imagePatentPartitionList.size() > 19) {
            imagePatentPartitionList = imagePatentPartitionList.subList(0, 19);
        }
        for (int i = 0; i < imagePatentPartitionList.size(); i++) {
            List<ImagePatentInfo> patentInfoBatch = imagePatentPartitionList.get(i);
            // 使用带受理局信息的图片合并方法
            byte[] imageBytes = ImageMergeUtil.mergeImagesFromUrlsWithAuthority(patentInfoBatch, col, i * mergedCount);
            if (imageBytes != null) {
                String base64Image = Base64.getEncoder().encodeToString(imageBytes);
                mergedImageBase64List.add(base64Image);
            } else {
                log.warn("第{}批图片合并失败，跳过该批次", i + 1);
            }
        }
        return mergedImageBase64List;
    }
    
    /**
     * 获取合并后的图片Base64列表（原方法，保持向后兼容）
     * @param patentImageUrlList 专利图片URL列表
     * @param mergedCount 合并的图片数量
     * @param col 每行显示的图片数量
     * @return 合并后的图片Base64编码列表
     */
    private @NotNull List<String> getMergedImageBase64List(List<String> patentImageUrlList, Integer mergedCount, Integer col) {
        List<String> mergedImageBase64List = new ArrayList<>();
        List<List<String>> imageUrlPartitionList = Lists.partition(patentImageUrlList, mergedCount);
        // 只取前 19个分区，目前 base64 形式传递图片，Claude 模型最多支持 20 张图片
        if (imageUrlPartitionList.size() > 19) {
            imageUrlPartitionList = imageUrlPartitionList.subList(0, 19);
        }
        for (int i = 0; i < imageUrlPartitionList.size(); i++) {
            List<String> patentImageUrls = imageUrlPartitionList.get(i);
            // 合并图片
            byte[] imageBytes = ImageMergeUtil.mergeImagesFromUrls(patentImageUrls, col, i * mergedCount);
            if (imageBytes != null) {
                String base64Image = Base64.getEncoder().encodeToString(imageBytes);
                mergedImageBase64List.add(base64Image);
            } else {
                log.warn("第{}批图片合并失败，跳过该批次", i + 1);
            }
        }
        return mergedImageBase64List;
    }
    
    /**
     * 获取图像比较响应
     * 当抛出 BizException(SYSTEM_BUSY) 异常时，重试一次
     */
    private ImageSimilarityResponse getImageComparisonResponse(ImageReorderRequest request) {
        // 构建提示词
        String prompt = openAiClient.buildPromptByPlatform(IMAGE_SIMILARITY_COMPARISON.getValue(), buildParams(request));
        ModelCompletionDTO modelCompletionDTO = imagePromptUtils.buildModelCompletion(prompt, DEFAULT_MODEL);

        // 调用AI服务获取比较结果
        ImageSimilarityResponse response = openAiClient.callGptByPrompt(
                modelCompletionDTO,
                ScenarioEnum.IMAGE_SIMILAR_SEARCH,
                ImageSimilarityResponse.class
        );

        // 验证响应
        if (response == null || CollectionUtils.isEmpty(response.getResults())) {
            log.warn("图片相似度比较结果为空,openAiResponse={}", response);
            throw new GPTErrorException(ContentErrorCodeEnum.SYSTEM_BUSY.getNumericErrCode(),
                    ContentErrorCodeEnum.SYSTEM_BUSY.getErrCode(), ContentErrorCodeEnum.SYSTEM_BUSY.getPattern());
        }

        // 确保风险等级根据相似度评分正确设置
        for (ImageSimilarityResult result : response.getResults()) {
            RiskLevelEnum riskLevel = getRiskLevel(result.getSimilarScore());
            result.setRiskLevel(riskLevel.getValue());
            String pn = getPn(result.getId(), request.getImagePatentList());
            result.setPn(pn);
            log.info("设置专利[{}]的风险等级为[{}], 相似度评分为[{}]", result.getPn(), riskLevel.getValue(), result.getSimilarScore());
        }

        return response;
    }

    /**
     * 构建特征比较响应
     */
    private ImageFeatureComparisonResponse buildFeatureComparisonResponse(
            ImageReorderRequest request, ImageSimilarityResponse openAiResponse) {
        // 构建专利信息映射
        Map<String, ImagePatentInfo> pn2InfoMap = request.getImagePatentList().stream()
                .collect(Collectors.toMap(ImagePatentInfo::getPatentPn, Function.identity()));

        // 创建响应对象
        ImageFeatureComparisonResponse response = new ImageFeatureComparisonResponse();

        // 构建比较项
        List<ComparisonsItem> comparisons = createComparisonItems(openAiResponse, pn2InfoMap);

        // 收集专利ID
        List<String> patentIds = extractPatentIds(comparisons);

        // 如果有专利ID，则查询详细信息并填充
        if (!patentIds.isEmpty()) {
            comparisons = enrichPatentDetails(patentIds, comparisons);
        }

        // comparisons 按照 similarScore 的值进行排序，从大到小
        comparisons.sort(Comparator.comparingDouble(ComparisonsItem::getSimilarScore).reversed());
        // 设置响应属性
        response.setComparisons(comparisons);
        response.setUserImage(FileManager.extractFileKey(request.getTargetImageUrls().get(0)));

        return response;
    }

    /**
     * 提取专利ID
     */
    private List<String> extractPatentIds(List<ComparisonsItem> comparisons) {
        return comparisons.stream()
                .map(ComparisonsItem::getPatentId)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
    }

    /**
     * 创建比较项
     */
    private List<ComparisonsItem> createComparisonItems(ImageSimilarityResponse openAiResponse,
            Map<String, ImagePatentInfo> pn2InfoMap) {
        List<ComparisonsItem> comparisons = new ArrayList<>();

        // 为每个结果创建比较项
        for (ImageSimilarityResult result : openAiResponse.getResults()) {
            String pn = result.getPn();
            ComparisonsItem item = new ComparisonsItem();

            // 设置基本属性
            item.setPn(pn);
            item.setTitle(Optional.ofNullable(pn2InfoMap.get(pn)).map(ImagePatentInfo::getTitle).orElse(""));
            item.setPatentId(Optional.ofNullable(pn2InfoMap.get(pn)).map(ImagePatentInfo::getPatentId).orElse(""));
            if (StringUtils.isBlank(item.getPatentId())) {
                log.warn("专利ID为空, result={}", result);
                continue;
            }

            // 设置相似度信息
            item.setSimilarScore(result.getSimilarScore());
            item.setSimilar(result.getSimilar());
            item.setRiskLevel(result.getRiskLevel());
            item.setRiskDesc(result.getRiskDesc());
            item.setDifference(result.getDifference());
            item.setSuggestion(result.getSuggestion());
            item.setConfusion(result.getConfusion());
            comparisons.add(item);
        }

        return comparisons;
    }

    /**
     * 图像位置预测
     */
    public ImageLocPredictResponse predictImageLoc(ImageLocPredictRequest request) {
        log.info("开始图像位置预测, request={}", request);
        return openApiClient.predictImageLoc(request);
    }

    /**
     * 预测图像LOC信息
     */
    private String predictLoc(String imageUrl) {
        ImageLocPredictResponse response = getImageLocPredictResponse(imageUrl);
        if (response == null) {
            return null;
        }

        // 格式化位置信息
        StringBuilder locStr = new StringBuilder();
        response.getData().getLoc().forEach(loc -> locStr.append(" OR ").append(loc.getLoc()));
        return "(" + locStr + ")";
    }

    /**
     * 获取图像位置预测响应
     */
    private ImageLocPredictResponse getImageLocPredictResponse(String imageUrl) {
        ImageLocPredictRequest request = new ImageLocPredictRequest();
        request.setUrl(imageUrl);

        ImageLocPredictResponse response = openApiClient.predictImageLoc(request);

        // 验证响应
        if (response == null || response.getData() == null || CollectionUtils.isEmpty(response.getData().getLoc())) {
            log.warn("图像LOC预测结果为空");
            return null;
        }

        return response;
    }

    /**
     * 根据专利ID列表查询专利详细信息并填充到ComparisonsItem对象中
     */
    public List<ComparisonsItem> enrichPatentDetails(List<String> patentIds, List<ComparisonsItem> comparisonsItems) {
        if (CollectionUtils.isEmpty(patentIds) || CollectionUtils.isEmpty(comparisonsItems)) {
            log.warn("专利ID列表或比较项列表为空");
            return comparisonsItems;
        }

        log.info("开始查询专利详细信息, patentIds={}", patentIds);

        // 查询专利详细信息
        Map<String, Map<String, Object>> patentDetailsMap = patentApiClient.getPatentFieldsMap(patentIds, PATENT_DETAIL_FIELDS);

        if (patentDetailsMap.isEmpty()) {
            log.warn("未查询到专利详细信息");
            return comparisonsItems;
        }
        
        // 填充专利详细信息
        for (ComparisonsItem comparisonsItem : comparisonsItems) {
            Map<String, Object> patentDetail = patentDetailsMap.get(comparisonsItem.getPatentId());
            fillPatentDetails(comparisonsItem, patentDetail);
        }

        log.info("专利详细信息查询完成，已填充到比较项中");
        return comparisonsItems;
    }

    /**
     * 填充专利详细信息到比较项
     */
    private void fillPatentDetails(ComparisonsItem item, Map<String, Object> patentDetails) {
        if (patentDetails == null) {
            return;
        }
        // 填充国家（如果原来没有）
        if (StringUtils.isBlank(item.getCountry()) && patentDetails.containsKey(COUNTRY)) {
            item.setCountry(MapUtils.getString(patentDetails, COUNTRY, ""));
        }

        // 填充法律状态
        if (patentDetails.containsKey(SIMPLE_LEGAL_STATUS)) {
            item.setStatus(MapUtils.getInteger(patentDetails, SIMPLE_LEGAL_STATUS, null));
        }
    }

    /**
     * 获取图像相似度报告
     */
    public AgentToolResponseDTO getImagesBySimilarityReport(String taskId) {
        try {
            log.info("开始生成图片相似度报告, taskId={}", taskId);

            // 获取请求和特征比较响应
            ImageReorderRequest request = getRequest(taskId);
            ImageFeatureComparisonResponse comparisonResponse = getImageFeatureComparisonResponse(taskId);

            // 创建并初始化响应对象
            ImageSimilarReportResponse response = initializeReportResponse(comparisonResponse, request);

            // 如果比较结果为空，直接返回
            if (CollectionUtils.isEmpty(comparisonResponse.getComparisons())) {
                log.warn("图片特征对比结果为空");
                return getAgentToolResponseDTO(taskId, jsonMapperManager.convertToString(response),
                        SIMILAR_IMAGE_REPORT_REDIS_KEY, response.getComparisons().size());
            }

            // 分析比较结果并更新响应
            analyzeComparisonResults(comparisonResponse.getComparisons(), response);

            // 提取并设置LOC信息
            response.setLoc(getLocDescList(request.getTargetImageUrls().get(0)));
            response.setOperateDate(System.currentTimeMillis());
            response.setOperator(identityAccountManager.getShowValueByUserId(UserIdHolder.get()));
            return getAgentToolResponseDTO(taskId, jsonMapperManager.convertToString(response),
                    SIMILAR_IMAGE_REPORT_REDIS_KEY, response.getComparisons().size());
        } catch (Exception e) {
            log.error("生成图片相似度报告失败", e);
            throw new BizException(ContentErrorCodeEnum.SYSTEM_BUSY);
        }
    }
    /**
     *  保存结果到 agent 并返回响应
     */
    private AgentToolResponseDTO getAgentToolResponseDTO(String taskId, String jsonMapperManager,
            String similarImageReportRedisKey, int responseSize) {
        // 保存结果到 agent
        agentToolManager.saveToolData(taskId, jsonMapperManager, similarImageReportRedisKey + taskId);
        AgentToolResponseDTO finalResponse = AgentToolResponseDTO.builder()
                .toolMessageKey(similarImageReportRedisKey + taskId).data(responseSize + " results found").build();
        // 设置响应头
        commonAgentToolManager.handleHeaderKeyResponse(finalResponse);
        return finalResponse;
    }
    
    /**
     * 初始化报告响应对象
     */
    private ImageSimilarReportResponse initializeReportResponse(ImageFeatureComparisonResponse comparisonResponse,
            ImageReorderRequest request) {
        ImageSimilarReportResponse response = new ImageSimilarReportResponse();
        response.setUserImage(comparisonResponse.getUserImage());
        response.setComparisons(comparisonResponse.getComparisons());
        response.setCountry(request.getCountry());
        return response;
    }

    /**
     * 根据相似性评分获取风险等级
     *
     * @param similarityScore 相似性评分
     * @return 风险等级枚举值
     */
    public RiskLevelEnum getRiskLevel(Double similarityScore) {
        if (similarityScore == null) {
            return RiskLevelEnum.LOW;
        }

        if (similarityScore >= 0.75) {
            return RiskLevelEnum.HIGH;
        } else if (similarityScore >= 0.6) {
            return RiskLevelEnum.MEDIUM;
        } else {
            return RiskLevelEnum.LOW;
        }
    }
    /**
     * 根据ID获取专利号
     *
     * @param id 专利ID
     * @param imagePatentList 专利列表
     * @return 专利号
     */
    public String getPn(Integer id, List<ImagePatentInfo> imagePatentList) {
        if (id == null || id <= 0) {
            log.warn("无效的ID: {}", id);
            return null;
        }
        if (CollectionUtils.isEmpty(imagePatentList)) {
            log.warn("专利列表为空");
            return null;
        }
        if (id > imagePatentList.size()) {
            log.warn("ID超出列表范围: id={}, size={}", id, imagePatentList.size());
            return null;
        }
        
        ImagePatentInfo patentInfo = imagePatentList.get(id - 1);
        return patentInfo != null ? patentInfo.getPatentPn() : null;
    }

    private void analyzeComparisonResults(List<ComparisonsItem> comparisons,
            ImageSimilarReportResponse response) {
        Set<String> highRiskCountries = new HashSet<>();
        Set<String> middleRiskCountries = new HashSet<>();
        Set<String> suspectedInfringementCountries = new HashSet<>();
        int suspectedInfringementCount = 0;

        // 遍历比较项，分析风险等级
        for (ComparisonsItem comparison : comparisons) {
            String riskLevel = comparison.getRiskLevel();
            String country = comparison.getCountry();

            // 根据风险等级分类
            if (RiskLevelEnum.HIGH.getValue().equals(riskLevel)) {
                highRiskCountries.add(country);
                suspectedInfringementCount++;
                suspectedInfringementCountries.add(country);
            } else if (RiskLevelEnum.MEDIUM.getValue().equals(riskLevel) && !highRiskCountries.contains(country)) {
                middleRiskCountries.add(country);
                suspectedInfringementCount++;
                suspectedInfringementCountries.add(country);
            }
        }

        // 设置响应属性
        response.setHighRiskCountry(highRiskCountries);
        response.setMiddleRiskCountry(middleRiskCountries);
        response.setSuspectedInfringementCount(suspectedInfringementCount);
        response.setSuspectedInfringementCountry(suspectedInfringementCountries);
    }

    /**
     * 根据图片URL获取LOC描述列表
     */
    private List<String> getLocDescList(String imageUrl) {
        try {
            // 获取图像位置预测响应
            ImageLocPredictResponse response = getImageLocPredictResponse(imageUrl);

            // 处理响应数据
            return Optional.ofNullable(response)
                    .map(ImageLocPredictResponse::getData)
                    .map(ImageLocPredictResponse.ImageLocPredictData::getLoc)
                    .orElseGet(Collections::emptyList)
                    .stream()
                    .filter(prediction -> prediction.getScore() > 0.6)  // 过滤置信度得分
                    .map(ImageLocPredictResponse.LocPrediction::getDesc)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取LOC描述列表失败", e);
            return Collections.emptyList();
        }
    }

    /**
     * 从Redis获取图像特征比较响应
     */
    private ImageFeatureComparisonResponse getImageFeatureComparisonResponse(String taskId) {
        ImageFeatureComparisonResponse response = redissonUtils.get(redissonClient,
                SIMILAR_IMAGE_FEATURE_COMPARISON_REDIS_KEY + taskId);

        if (response == null) {
            log.warn("从Redis获取图片特征识别分析结果为空, taskId={}", taskId);
            throw new BizException(ContentErrorCodeEnum.SYSTEM_BUSY);
        }

        return response;
    }

    /**
     * 从Redis获取请求参数
     */
    private ImageReorderRequest getRequest(String taskId) {
        // 获取输入图片URL
        String inputImageUrlStr = redissonUtils.get(redissonClient, SIMILAR_IMAGE_INPUT_REDIS_KEY + taskId);
        if (StringUtils.isBlank(inputImageUrlStr)) {
            log.warn("从Redis获取输入图片URL为空, taskId={}", taskId);
            throw new BizException(ContentErrorCodeEnum.SYSTEM_BUSY);
        }
        List<String> inputImageUrls = Arrays.asList(inputImageUrlStr.split(","));

        // 获取国家信息
        List<String> countries = redissonUtils.get(redissonClient, SIMILAR_IMAGE_COUNTRY_REDIS_KEY + taskId);
        if (CollectionUtils.isEmpty(countries)) {
            log.warn("从Redis获取出口国家结果为空, taskId={}", taskId);
            throw new BizException(ContentErrorCodeEnum.SYSTEM_BUSY);
        }

        // 获取搜索响应
        ImageSimilarSearchResponse response = redissonUtils.get(redissonClient, SIMILAR_IMAGE_SEARCH_REDIS_KEY + taskId);
        if (response == null) {
            log.warn("从Redis获取图片相似度搜索结果为空, taskId={}", taskId);
            throw new BizException(ContentErrorCodeEnum.SYSTEM_BUSY);
        }

        // 构建重排序请求
        return buildReorderRequest(response, inputImageUrls, countries);
    }

    /**
     * 构建重排序请求
     */
    private ImageReorderRequest buildReorderRequest(ImageSimilarSearchResponse response,
            List<String> inputImageUrls, List<String> countries) {
        // 创建专利图像信息列表
        List<ImagePatentInfo> imagePatentList = createImagePatentInfoList(response);

        // 创建请求对象
        ImageReorderRequest request = new ImageReorderRequest();
        request.setTargetImageUrls(inputImageUrls);
        request.setCountry(countries);
        request.setImagePatentList(imagePatentList);

        return request;
    }

    /**
     * 创建专利图像信息列表
     */
    private List<ImagePatentInfo> createImagePatentInfoList(ImageSimilarSearchResponse response) {
        List<ImagePatentInfo> imagePatentList = new ArrayList<>();

        for (PatentMessagesItem patentMessage : response.getData().getPatentMessages()) {
            ImagePatentInfo imagePatentInfo = new ImagePatentInfo();
            imagePatentInfo.setImageUrl(patentMessage.getUrl());
            imagePatentInfo.setImageUrl120(patentMessage.getUrl120());
            imagePatentInfo.setPatentId(patentMessage.getPatentId());
            imagePatentInfo.setPatentPn(patentMessage.getPatentPn());
            imagePatentInfo.setTitle(patentMessage.getTitle());
            
            // 从专利号中提取受理局信息
            String authority = extractAuthorityFromPatentPn(patentMessage.getPatentPn());
            imagePatentInfo.setAuthority(authority);
            
            imagePatentList.add(imagePatentInfo);
        }
        fillNullImage(imagePatentList);
        
        return imagePatentList;
    }
    
    /**
     * 填充没有图片的专利
     */
    private void fillNullImage(List<ImagePatentInfo> imagePatentList) {
        List<String> noImagePatentIds = imagePatentList.stream().filter(it -> StringUtils.isBlank(it.getImageUrl()))
                .map(ImagePatentInfo::getPatentId).toList();
        if (CollectionUtils.isNotEmpty(noImagePatentIds)) {
            PatentImagesRequestDTO requestDTO = new PatentImagesRequestDTO();
            requestDTO.setPatentIds(noImagePatentIds);
            Map<String, Map<String, String>> patentId2ImageUrlMap = patentInfoManager.batchFetchPatentImages(requestDTO);
            imagePatentList.forEach(it -> {
                if (StringUtils.isBlank(it.getImageUrl()) && patentId2ImageUrlMap.containsKey(it.getPatentId())) {
                    Map<String, String> urls = patentId2ImageUrlMap.get(it.getPatentId());
                    // 设置原始url到imageUrl字段
                    it.setImageUrl(urls.get("url"));
                    // 设置url_120到imageUrl120字段
                    it.setImageUrl120(urls.get("url_120"));
                }
            });
        }
    }
    
    /**
     * 从专利号中提取受理局信息
     * 
     * @param patentPn 专利号
     * @return 受理局信息
     */
    private String extractAuthorityFromPatentPn(String patentPn) {
        if (StringUtils.isBlank(patentPn)) {
            return "";
        }
        
        // 专利号通常以国家代码开头，如 CN、US、EP 等
        String countryCode = patentPn.length() >= 2 ? patentPn.substring(0, 2).toUpperCase() : "";
        
        // 根据国家代码映射到受理局名称
        switch (countryCode) {
            case "CN":
                return "CNIPA";
            case "US":
                return "USPTO";
            case "EP":
                return "EPO";
            case "JP":
                return "JPO";
            case "KR":
                return "KIPO";
            case "DE":
                return "DPMA";
            case "GB":
                return "UKIPO";
            case "FR":
                return "INPI";
            case "WO":
                return "WIPO";
            default:
                return countryCode; // 如果没有匹配的受理局，返回国家代码
        }
    }
    
    /**
     * 构建提示词参数
     */
    private Map<String, String> buildParams(ImageReorderRequest request) {
        Map<String, String> params = new HashMap<>();

        // 添加目标图片
        StringBuilder targetImageList = new StringBuilder();
        for (String imageUrl : request.getTargetImageUrls()) {
            targetImageList.append("<image>").append(imageUrl).append("</image>\n");
        }
        params.put("target_image_url", targetImageList.toString());

        // 构建候选图片列表
        StringBuilder imageList = new StringBuilder();
        List<String> mergedImageBase64List = request.getMergedImageBase64List().stream()
                .limit(Math.min(request.getMergedImageBase64List().size(), IMAGE_MAX_SIZE - request.getTargetImageUrls().size()))
                .toList();

        for (String imageBase64 : mergedImageBase64List) {
            imageList.append("<image>").append(imageBase64).append("</image>\n");
        }
        params.put("image_list", imageList.toString());

        return params;
    }

    /**
     * 从Redis获取图像搜索和特征比较数据
     */
    public ImageSearchRedisDataResponse getRedisData(String taskId) {
        log.info("开始从Redis获取图像搜索数据, taskId={}", taskId);
        
        ImageSearchRedisDataResponse response = new ImageSearchRedisDataResponse();
        
        try {
            // 获取图像相似搜索结果
            ImageSimilarSearchResponse searchResponse = redissonUtils.get(redissonClient, 
                    SIMILAR_IMAGE_SEARCH_REDIS_KEY + taskId);
            response.setSearchImageSingle(searchResponse);
            
            // 获取图像特征比较结果
            ImageFeatureComparisonResponse comparisonResponse = redissonUtils.get(redissonClient, 
                    SIMILAR_IMAGE_FEATURE_COMPARISON_REDIS_KEY + taskId);
            response.setFeatureComparison(comparisonResponse);
            
            // 获取输入图片URL
            String inputImageUrls = redissonUtils.get(redissonClient, 
                    SIMILAR_IMAGE_INPUT_REDIS_KEY + taskId);
            response.setInputImageUrls(inputImageUrls);
            
            // 获取国家信息
            List<String> countries = redissonUtils.get(redissonClient, 
                    SIMILAR_IMAGE_COUNTRY_REDIS_KEY + taskId);
            response.setCountries(countries);
            
            log.info("成功从Redis获取图像搜索数据, taskId={}", taskId);
            
        } catch (Exception e) {
            log.error("从Redis获取图像搜索数据失败, taskId={}", taskId, e);
            throw new BizException(ContentErrorCodeEnum.SYSTEM_BUSY);
        }
        
        return response;
    }
}

package com.patsnap.drafting.manager.aispecification.content;

import com.alibaba.fastjson2.JSONArray;
import com.patsnap.core.common.copilot.constant.GPTStatus;
import com.patsnap.core.common.copilot.gpt.enums.GPTModelEnum;
import com.patsnap.core.common.copilot.streaming.handling.StreamingChatLanguageModel;
import com.patsnap.core.common.copilot.streaming.model.GptResponseDTO;
import com.patsnap.drafting.annotation.TaskContentCache;
import com.patsnap.drafting.client.ComputeClient;
import com.patsnap.drafting.client.model.AiSpecificationComputeData;
import com.patsnap.drafting.client.model.AiSpecificationComputeReqDTO;
import com.patsnap.drafting.config.UrlConfig;
import com.patsnap.drafting.enums.aispecification.JurisdictionEnum;
import com.patsnap.drafting.enums.prompt.ScenarioEnum;
import com.patsnap.drafting.enums.task.AiTaskContentTypeEnum;
import com.patsnap.drafting.handler.specification.SpecificationStringStreamingResponseHandler;
import com.patsnap.drafting.manager.AbstractGenerateStreamingContent;
import com.patsnap.drafting.manager.aispecification.AlgorithmCallService;
import com.patsnap.drafting.manager.aispecification.AlgorithmTaskStatusManager;
import com.patsnap.drafting.manager.aitask.AiTaskManager;
import com.patsnap.drafting.model.aispecification.FigureContentBO;
import com.patsnap.drafting.model.aispecification.SpecificationInitializationBO;
import com.patsnap.drafting.model.base.StreamingModelBO;
import com.patsnap.drafting.request.GenerateContentRequestDTO;
import com.patsnap.drafting.response.aispecification.SpecificationRdResDTO;
import com.patsnap.spring.boot.autoconfigure.web.entity.CommonResponse;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.patsnap.drafting.enums.task.AiTaskContentTypeEnum.FIGURES_PRE_INFO;
import static com.patsnap.drafting.enums.task.AiTaskContentTypeEnum.FIGURE_CONFIG;

/**
 * AI说明书撰写 - 附图说明
 * 
 * <AUTHOR> Assistant
 */
@Component
@Slf4j
public class SpecificationFiguresDescriptionContent extends AbstractGenerateStreamingContent<String> {
    
    // 常量定义
    public static final String DEFAULT_VALUE = "-";
    
    // 算法任务超时时间：80秒
    private static final Duration ALGORITHM_TASK_TIMEOUT = Duration.ofSeconds(80);
    // 轮询间隔：5秒
    private static final Duration POLL_INTERVAL = Duration.ofSeconds(5);
    
    @Autowired
    private ComputeClient computeClient;
    @Autowired
    private AlgorithmCallService algorithmCallService;
    @Autowired
    private AlgorithmTaskStatusManager algorithmTaskStatusManager;
    
    /**
     * 构造函数
     * 
     * @param aiTaskManager AI任务管理器
     * @param urlConfig URL配置
     */
    protected SpecificationFiguresDescriptionContent(AiTaskManager aiTaskManager, UrlConfig urlConfig) {
        super(aiTaskManager, urlConfig);
    }

    /**
     * 获取内容类型
     * 
     * @return 附图说明内容类型
     */
    @Override
    public String getContentType() {
        return AiTaskContentTypeEnum.FIGURES_DESCRIPTION.getType();
    }

    /**
     * 获取功能名称
     * 
     * @return 生成附图说明的功能名称
     */
    public String getFunction() {
        return "generate_drawing_description";
    }
    
    /**
     * 获取流式模型业务对象
     * 
     * @param generateContentRequestDTO 生成内容请求DTO
     * @return 流式模型业务对象
     */
    protected StreamingModelBO getStreamingModelBO(GenerateContentRequestDTO generateContentRequestDTO) {
        AiSpecificationComputeData computeReqData = buildComputeData(generateContentRequestDTO);
        AiSpecificationComputeReqDTO computeReqDTO = buildComputeReqDTO(computeReqData);
        SpecificationRdResDTO specificationPrompt = computeClient.getSpecificationPrompt(computeReqDTO);
        
        return buildStreamingModelBO(specificationPrompt);
    }
    
    /**
     * 构建计算数据
     * 
     * @param generateContentRequestDTO 生成内容请求DTO
     * @return 计算数据
     */
    private AiSpecificationComputeData buildComputeData(GenerateContentRequestDTO generateContentRequestDTO) {
        String taskId = generateContentRequestDTO.getTaskId();
        String figuresPreInfo = aiTaskManager.getTaskContent(taskId, FIGURES_PRE_INFO);
        SpecificationInitializationBO initializationBO = aiTaskManager.getTaskContent(taskId, AiTaskContentTypeEnum.INITIALIZATION);
        
        AiSpecificationComputeData computeReqData = new AiSpecificationComputeData();
        computeReqData.setDrawingNarrative(Optional.ofNullable(figuresPreInfo).orElseGet(() -> Strings.EMPTY));
        computeReqData.setJurisdiction(initializationBO.getJurisdiction());
        return computeReqData;
    }
    
    /**
     * 构建计算请求DTO
     * 
     * @param computeReqData 计算数据
     * @return 计算请求DTO
     */
    private AiSpecificationComputeReqDTO buildComputeReqDTO(AiSpecificationComputeData computeReqData) {
        return AiSpecificationComputeReqDTO.builder()
                .function(getFunction())
                .data(computeReqData)
                .lang(StringUtil.toLowerCase(JurisdictionEnum.fromName(computeReqData.getJurisdiction()).getValue()))
                .build();
    }
    
    /**
     * 构建流式模型业务对象
     * 
     * @param specificationPrompt 规范提示响应
     * @return 流式模型业务对象
     */
    private StreamingModelBO buildStreamingModelBO(SpecificationRdResDTO specificationPrompt) {
        StreamingModelBO streamingModelBO = new StreamingModelBO();
        streamingModelBO.setPrompt(specificationPrompt.getPrompt());
        
        StreamingChatLanguageModel model = streamingModelBuilder.build(
                GPTModelEnum.getGptModelEnum(specificationPrompt.getModelName()),
                ScenarioEnum.AI_SPECIFICATION.getValue(), 0.6, 16000);
        streamingModelBO.setModel(model);
        streamingModelBO.setExtraData(specificationPrompt.getExtraData());
        
        return streamingModelBO;
    }
    
    /**
     * 获取内容
     * 
     * @param streamingModelBO 流式模型业务对象
     * @return 内容流
     */
    protected Flux<CommonResponse<GptResponseDTO<String>>> getContent(StreamingModelBO streamingModelBO) {
        // 这里不使用AI生成，而是直接从content表获取数据
        String prompt = streamingModelBO.getPrompt();
        return Flux.create(sink -> {
            // 创建专门处理字符串流式响应的处理器
            SpecificationStringStreamingResponseHandler handler = new SpecificationStringStreamingResponseHandler(sink);
            // 调用模型生成附图说明内容
            streamingModelBO.getModel().generate(prompt, handler);
        });
    }

    
    /**
     * 执行任务状态检查的核心逻辑
     * 
     * @param taskId 任务ID
     */
    private void checkTaskStatus(String taskId) {
        // 首先检查任务内容是否已存在
        Object taskContent = aiTaskManager.getTaskContent(taskId, FIGURES_PRE_INFO);
        if (taskContent != null) {
            log.info("FIGURES_PRE_INFO 已存在，{}", taskId);
            return;
        }
        // 获取算法任务状态
        AlgorithmTaskStatusManager.TaskStatusInfo statusInfo = algorithmTaskStatusManager.getTaskStatusInfo(taskId, FIGURES_PRE_INFO);
        if (statusInfo == null) {
            log.info("算法任务状态为空，启动新的算法任务，{}", taskId);
            startAlgorithmTask(taskId);
            return;
        }
        
        // 状态不为空时，根据状态进行处理
        String status = statusInfo.getStatus();
        log.info("获取到算法任务状态，任务ID: {}, 状态: {}", taskId, status);
        
        if (AlgorithmTaskStatusManager.STATUS_COMPLETED.equals(status) || 
            AlgorithmTaskStatusManager.STATUS_FAILED.equals(status)) {
            // 如果是已完成或失败状态，直接结束
            log.info("算法任务已完成或失败，任务ID: {}, 状态: {}", taskId, status);
            algorithmTaskStatusManager.clearTaskData(taskId, FIGURES_PRE_INFO);
            return;
        }
        
        if (AlgorithmTaskStatusManager.STATUS_PROCESSING.equals(status)) {
            // 如果是进行中状态，循环查询直到状态改变
            log.info("算法任务正在处理中，开始轮询状态，任务ID: {}", taskId);
            waitForTaskCompletion(taskId);
        }
    }
    
    /**
     * 启动算法任务
     * 
     * @param taskId 任务ID
     * @return 是否成功启动
     */
    private boolean startAlgorithmTask(String taskId) {
        try {
            log.info("开始启动算法任务，{}", taskId);
            
            algorithmCallService.callAlgorithmService(taskId, FIGURES_PRE_INFO);
            
            log.info("算法任务启动成功，{}", taskId);
            return true;
            
        } catch (Exception e) {
            log.error("启动算法任务失败，{}", taskId, e);
            return false;
        }
    }
    
    /**
     * 等待任务完成，轮询查询状态
     * 最多循环10次，间隔5秒
     * 
     * @param taskId 任务ID
     */
    private void waitForTaskCompletion(String taskId) {
        final int maxRetries = 10;
        final int pollingIntervalSeconds = 5;
        
        for (int i = 0; i < maxRetries; i++) {
            try {
                // 等待5秒后再查询状态
                Thread.sleep(pollingIntervalSeconds * 1000);
                
                // 获取最新的任务状态
                AlgorithmTaskStatusManager.TaskStatusInfo statusInfo = 
                    algorithmTaskStatusManager.getTaskStatusInfo(taskId, FIGURES_PRE_INFO);
                
                if (statusInfo == null) {
                    log.warn("轮询过程中状态信息变为空，任务ID: {}, 轮询次数: {}", taskId, i + 1);
                    break;
                }
                
                String currentStatus = statusInfo.getStatus();
                log.info("轮询获取任务状态，任务ID: {}, 状态: {}, 轮询次数: {}", taskId, currentStatus, i + 1);
                
                // 如果状态不再是处理中，结束轮询
                if (!AlgorithmTaskStatusManager.STATUS_PROCESSING.equals(currentStatus)) {
                    log.info("任务状态已改变，结束轮询，任务ID: {}, 最终状态: {}", taskId, currentStatus);
                    break;
                }
                
                log.info("任务仍在处理中，继续轮询，任务ID: {}, 剩余轮询次数: {}", taskId, maxRetries - i - 1);
                
            } catch (InterruptedException e) {
                log.error("轮询过程中被中断，任务ID: {}", taskId, e);
                Thread.currentThread().interrupt();
                break;
            } catch (Exception e) {
                log.error("轮询过程中发生异常，任务ID: {}, 轮询次数: {}", taskId, i + 1, e);
            }
        }
        
        log.info("轮询结束，任务ID: {}", taskId);
    }
    
    /**
     * 生成内容的主要入口方法
     * 
     * @param request 生成内容请求
     * @return 内容流
     */
    @Override
    @TaskContentCache(contentTypeExpression = "#request.contentType")
    public Flux<CommonResponse<GptResponseDTO<String>>> doGenerate(GenerateContentRequestDTO request) {
        log.info("开始获取附图说明信息，任务ID: {}", request.getTaskId());
        beforeGetContent(request);
        // 1. 若没有用户上传的附图，直接返回空内容
        if (!hasFiguresContent(request.getTaskId())) {
            log.info("无需获取附图说明，任务ID: {}，原因：未找到附图信息", request.getTaskId());
            return getEmptyCommonResponseFlux();
        }
        //2. 检查算法任务状态
        checkTaskStatus(request.getTaskId());
        //3. 获取内容
        StreamingModelBO streamingModelBO = new StreamingModelBO();
        try {
            streamingModelBO = getStreamingModelBO(request);
        } catch (Exception e) {
            log.error("获取附图说明内容失败，任务ID: {}", request.getTaskId(), e);
            return getEmptyCommonResponseFlux();
        }
        return getContent(streamingModelBO);
    }
    
    /**
     * 检查是否有附图内容且算法是否返回了非空数据的附图说明
     * 
     * @param taskId 任务ID
     * @return 是否有附图内容
     */
    private boolean hasFiguresContent(String taskId) {
        Map<AiTaskContentTypeEnum, Object> contentMap = aiTaskManager.getTaskContent(taskId,
                List.of(FIGURE_CONFIG, FIGURES_PRE_INFO));
        FigureContentBO figureContentBO = (FigureContentBO) Optional.ofNullable(contentMap)
                .map(it -> it.get(FIGURE_CONFIG)).orElseGet(() -> null);
        boolean hasFigure =
                figureContentBO != null && figureContentBO.getFigures() != null && !figureContentBO.getFigures()
                        .isEmpty();
        String figuresPreInfo = (String) Optional.ofNullable(contentMap).map(it -> it.get(FIGURES_PRE_INFO))
                .orElseGet(() -> null);
        boolean hasFiguresPreInfo = figuresPreInfo != null && !figuresPreInfo.isEmpty() && isArray(figuresPreInfo);
        return hasFigure && hasFiguresPreInfo;
    }
    
    /**
     * 是数据且数组长度大于0
     * @param figuresPreInfo 算法返回的附图说明数据
     * @return 是否是数组
     */
    private static boolean isArray(String figuresPreInfo) {
        try {
            JSONArray jsonArray = JSONArray.parse(figuresPreInfo);
            return jsonArray != null && !jsonArray.isEmpty();
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 获取空的CommonResponse流
     * @return 空的CommonResponse流
     */
    private Flux<CommonResponse<GptResponseDTO<String>>> getEmptyCommonResponseFlux() {
        GptResponseDTO<String> gptResponse = GptResponseDTO.<String>builder()
                .content(DEFAULT_VALUE)
                .status(GPTStatus.FINISH)
                .build();
        CommonResponse<GptResponseDTO<String>> response = CommonResponse.<GptResponseDTO<String>>builder()
                .withData(gptResponse)
                .withStatus(true)
                .build();
        return Flux.just(response);
    }
} 
package com.patsnap.drafting.manager.aitranslation.operate;

public class TranslationConstant {

    public static final String LINE_SEPARATOR_REGEX = "\\|\\|\\|";
    public static final String LINE_SEPARATOR = "|||";
    public static final String FIELD_SEPARATOR_REGEX = "\\$\\$\\$";
    public static final String FIELD_SEPARATOR = "$$$";
    public static final String KEY_WORD_FORMAT = "<zh>%s</zh><en>%s</en>";
    public static final int PARAGRAPH_MAX_LENGTH = 800;
    public static final String INPUT = "input";
    public static final String CORNER_MARK = "<-->";
    public static final String CARRIER_MARK = "\n";
    public static final String LANG = "lang";
    public static final String CONTEXT = "context";
    public static final String KEYWORDS = "keywords";
    public static final String USER_INPUT = "user_input";
    public static final String SOURCE_LANG = "source_lang";
    public static final String TARGET_LANG = "target_lang";
    public static final String TECH_TOPIC = "tech_topic";
    public static final String TOPIC = "topic";
    public static final String SUGGESTED_TERM = "suggested_term";
    public static final String CUSTOM_TERM = "custom_term";
    public static final String TRANSLATION_RESULT = "translation_result";

}

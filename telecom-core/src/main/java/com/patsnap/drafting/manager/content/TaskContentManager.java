package com.patsnap.drafting.manager.content;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.patsnap.drafting.annotation.DistributedLock;
import com.patsnap.drafting.enums.task.AiTaskContentTypeEnum;
import com.patsnap.drafting.exception.BizException;
import com.patsnap.drafting.exception.errorcode.ContentErrorCodeEnum;
import com.patsnap.drafting.manager.JsonMapperManager;
import com.patsnap.drafting.manager.validator.TaskContentValidator;
import com.patsnap.drafting.repository.aitask.dao.AnalyticsAiTaskContentService;
import com.patsnap.drafting.repository.aitask.entity.AnalyticsAiTaskContentPO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 任务内容管理器
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TaskContentManager {
    private final AnalyticsAiTaskContentService contentService;
    private final TaskContentValidator validator;
    private final JsonMapperManager jsonMapperManager;

    private static final String LOCK_KEY_PREFIX = "task_content:";

    /**
     * 保存任务内容
     *
     * @param taskId 任务ID
     * @param contentType 内容类型
     * @param content 内容
     */
    @DistributedLock(prefix = LOCK_KEY_PREFIX, key = "#taskId + ':' + #contentType")
    public void saveTaskContent(String taskId, String contentType, String content) {
        log.info("开始保存任务内容. taskId: {}, contentType: {}", taskId, contentType);
        // 验证
        validator.validate(taskId, AiTaskContentTypeEnum.fromType(contentType), content);
        
        try {
            // 保存内容
            AnalyticsAiTaskContentPO contentPO = new AnalyticsAiTaskContentPO();
            contentPO.setTaskId(taskId);
            contentPO.setContentType(contentType);
            contentPO.setContent(content);
            contentPO.setDirection(AiTaskContentTypeEnum.fromType(contentType).getDirection().getValue());
            
            contentService.saveOrUpdateContent(contentPO);
            log.info("成功保存任务内容. taskId: {}, contentType: {}", taskId, contentType);
            
        } catch (Exception e) {
            log.error("保存任务内容异常. taskId: {}, contentType: {}", taskId, contentType, e);
            throw new BizException(ContentErrorCodeEnum.SAVE_CONTENT_FAILED);
        }
    }
    
    /**
     * 获取任务内容并转换为指定类型
     *
     * @param taskId 任务ID
     * @param contentType 内容类型
     * @return 转换后的内容对象
     */
    public <T> T getTaskContent(String taskId, AiTaskContentTypeEnum contentType) {
        String taskContent = getTaskContentStr(taskId, contentType);
        if (taskContent == null) {
            log.warn("任务内容为空. taskId: {}, contentType: {}", taskId, contentType);
            return null;
        }
        return getTaskContent(contentType, taskContent);
    }
    
    private <T> T getTaskContent(AiTaskContentTypeEnum contentTypeEnum, String taskContent) {
        try {
            Class<T> contentClass = (Class<T>) contentTypeEnum.getContentClass();
            
            T result = jsonMapperManager.convertStringToTargetType(taskContent, contentClass);
            log.info("成功将任务内容转换为对象. contentType: {}", contentTypeEnum);
            return result;
        } catch (Exception e) {
            log.warn("任务内容转换失败. contentType: {}, content: {}", contentTypeEnum, taskContent, e);
            throw new BizException(ContentErrorCodeEnum.CONTENT_TYPE_INVALID,
                String.format("Failed to convert content to type: %s", contentTypeEnum));
        }
    }
    
    /**
     * 获取任务内容字符串
     *
     * @param taskId 任务ID
     * @param contentType 内容类型
     * @return 内容字符串
     */
    public String getTaskContentStr(String taskId, AiTaskContentTypeEnum contentType) {
        log.info("开始获取任务内容. taskId: {}, contentType: {}", taskId, contentType);
        // 验证
        validator.validate(taskId);
        
        // 获取内容
        AnalyticsAiTaskContentPO contentPO = contentService.getContent(taskId, contentType);
        if (contentPO == null) {
            log.warn("任务内容不存在. taskId: {}, contentType: {}", taskId, contentType);
            return null;
        }
        
        log.info("成功获取任务内容. taskId: {}, contentType: {}", taskId, contentType);
        return contentPO.getContent();
    }
    
    /**
     * 获取已生成的任务内容列表
     *
     * @param taskId 任务ID
     * @return 内容列表
     */
    public List<AnalyticsAiTaskContentPO> getAllContentsByTaskId(String taskId) {
        
        // 获取内容
        return Optional.ofNullable(contentService.getAllContentsByTaskId(taskId)).orElseGet(ArrayList::new);
    }
    
    /**
     * 删除已生成的任务内容列表
     *
     * @param taskId 任务ID
     * @param contentTypes 内容类型列表
     */
    public void deleteTaskContentList(String taskId, List<String> contentTypes) {
        List<AiTaskContentTypeEnum> contentTypeEnums = new ArrayList<>();
        contentTypes.forEach(contentType -> {
            AiTaskContentTypeEnum contentTypeEnum = AiTaskContentTypeEnum.fromType(contentType);
            contentTypeEnums.add(contentTypeEnum);
        });
        contentService.deleteContent(taskId, contentTypeEnums);
    }

    /**
     * 删除已生成的任务所有内容
     *
     * @param taskId 任务ID
     */
    public void deleteAllTaskContent(String taskId) {
        contentService.deleteAllContent(taskId);
    }
    
    /**
     * 隐藏任务内容
     * @param taskId 任务ID
     * @param contentType   内容类型
     * @param hide 是否隐藏
     */
    public void hideTaskContent(String taskId, AiTaskContentTypeEnum contentType, boolean hide) {
        LambdaUpdateWrapper<AnalyticsAiTaskContentPO> updateWrapper = new LambdaUpdateWrapper<AnalyticsAiTaskContentPO>()
                .eq(AnalyticsAiTaskContentPO::getTaskId, taskId)
                .eq(AnalyticsAiTaskContentPO::getContentType, contentType.getType())
                .set(AnalyticsAiTaskContentPO::getHide, hide);
        contentService.update(updateWrapper);
    }
} 
package com.patsnap.drafting.manager.content.logic;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.patsnap.drafting.client.model.ainovelty.AiNoveltySearchResponse;
import com.patsnap.drafting.enums.task.AiTaskContentTypeEnum;
import com.patsnap.drafting.manager.ainoveltysearch.NoveltySearchManager;
import com.patsnap.drafting.manager.aitask.AiTaskManager;
import com.patsnap.drafting.request.ainoveltysearch.AiSearchFinalResult;
import com.patsnap.drafting.response.ainoveltysearch.FeatureComparisonFinalResItem;
import com.patsnap.drafting.response.ainoveltysearch.FeatureComparisonResponseDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.patsnap.drafting.enums.task.AiTaskContentTypeEnum.NOVELTY_SEARCH_AGENT_RESULT;

/**
 * 异步CC处理逻辑
 */
@Component
public class NoveltySearchSyncCcCacheLogic extends ContentCacheLogic {

    @Autowired
    private AiTaskManager aiTaskManager;

    @Autowired
    private NoveltySearchManager noveltySearchManager;

    @Override
    public void updateContent(String taskId, AiTaskContentTypeEnum contentType, Object result, Object[] args) {
        if (!(result instanceof FeatureComparisonResponseDTO)) {
            return;
        }
        FeatureComparisonResponseDTO resultDTO = (FeatureComparisonResponseDTO) result;
        if (CollUtil.isEmpty(resultDTO.getFinalRes())) {
            return;
        }

        AiNoveltySearchResponse aiSearchResult = aiTaskManager.getTaskContent(taskId, NOVELTY_SEARCH_AGENT_RESULT);
        double ccThreshold = aiSearchResult.getCcThreshold();

        // 转换并构建特征比较结果映射
        Map<String, AiSearchFinalResult> ccResults = resultDTO.getFinalRes().stream()
                .collect(Collectors.toMap(
                    FeatureComparisonFinalResItem::getPatentId,
                    finalResItem -> {
                        AiSearchFinalResult finalResult = new AiSearchFinalResult();
                        BeanUtil.copyProperties(finalResItem, finalResult);
                        // 设置特征相似度标记
                        Optional.ofNullable(finalResult.getFeatures())
                                .ifPresent(features -> features.forEach(feature ->
                                    feature.setSimilar(feature.getScore() >= ccThreshold)));
                        return finalResult;
                    }
                ));

        // 合并特征数据到原始结果
        List<AiSearchFinalResult> finalResultList = aiSearchResult.getFinalResult();
        finalResultList.forEach(finalResult ->
            Optional.ofNullable(ccResults.get(finalResult.getPatentId()))
                    .ifPresent(ccResult -> BeanUtil.copyProperties(ccResult, finalResult)));

        // 更新并保存结果
        aiSearchResult.setFinalResult(noveltySearchManager.updateFinalResultType(finalResultList, taskId));
        aiTaskManager.batchUpdateTaskContent(taskId,
            Map.of(AiTaskContentTypeEnum.NOVELTY_SEARCH_AGENT_RESULT, aiSearchResult));

    }
}
package com.patsnap.drafting.manager.content.logic;

import com.patsnap.drafting.enums.task.AiTaskContentTypeEnum;
import com.patsnap.drafting.manager.aitask.AiTaskManager;
import com.patsnap.drafting.manager.aitranslation.AiTranslationManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 全文翻译content处理逻辑
 * <AUTHOR>
 */
@Component
public class TranslationFullTextContentCacheLogic extends ContentCacheLogic {

    @Autowired
    private AiTranslationManager aiTranslationManager;

    @Autowired
    private AiTaskManager aiTaskManager;

    @Override
    public void updateContent(String taskId, AiTaskContentTypeEnum contentType, Object result,
            Object[] args) {
        aiTaskManager.updateTaskContent(taskId, contentType, result);
    }

}
package com.patsnap.drafting.manager.aiftosearch;

import com.patsnap.drafting.client.model.NoveltySearchResponseDTO;
import com.patsnap.drafting.config.WebHostConfig;
import com.patsnap.drafting.enums.task.AiTaskContentTypeEnum;
import com.patsnap.drafting.enums.task.AiTaskTypeEnum;
import com.patsnap.drafting.manager.aitask.AiTaskManager;
import com.patsnap.drafting.manager.share.PublicShareManager;
import com.patsnap.drafting.repository.aitask.entity.AnalyticsAiTaskHistoryPO;
import com.patsnap.drafting.repository.share.entity.AiTaskShareLinkPO;
import com.patsnap.drafting.request.ainoveltysearch.AiSearchAgentFeature;
import com.patsnap.drafting.request.ainoveltysearch.AiSearchResultDTO;
import com.patsnap.drafting.request.ainoveltysearch.AiSearchResultReqDTO;
import com.patsnap.drafting.request.aitask.AiTaskCreateReqDTO;
import com.patsnap.drafting.request.aitask.AiTaskReqDTO;
import com.patsnap.drafting.request.share.FreeShareCreateRequest;
import com.patsnap.drafting.request.share.FreeShareInitRequest;
import com.patsnap.drafting.response.ainoveltyagent.AiSearchAgentReportResponse;
import com.patsnap.drafting.response.ainoveltyagent.AiSearchAgentStartResponse;
import com.patsnap.drafting.response.ainoveltyagent.FeatureExactionAgentResponseDTO;
import com.patsnap.drafting.response.ainoveltysearch.FeatureExactionResponseDTO;
import com.patsnap.drafting.response.share.FreeShareCreateResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.Map;

import static com.patsnap.drafting.util.TimeUtil.sleep;

@Slf4j
@RequiredArgsConstructor
@Service
public class FtoSearchAgentManager {
    public static final String AI_FTO_NOVELTY_CHECK_REPORT = "/drafting/#/fto-report?shareId=";
    
    public static final String CURRENT_STEP = "&currentStep=";
    public static final String FTO_SEARCH_TECH_FEATURE = "FTO_SEARCH_TECH_FEATURE";
    public static final String STEP_CONFIRM = "CONFIRM";
    public static final String STEP_RESULT = "RESULT";

    private final WebHostConfig webHostConfig;
    
    private final AiTaskManager aiTaskManager;

    private final FtoSearchManager ftoSearchManager;

    private final PublicShareManager publicShareManager;

    public FeatureExactionAgentResponseDTO extractTechFeature(String userInput, String taskId) {
        createTask(taskId, userInput);
        AiTaskReqDTO reqDTO = new AiTaskReqDTO();
        reqDTO.setTaskId(taskId);
        FeatureExactionResponseDTO featureExactionResponseDTO = ftoSearchManager.extractTechFeature(reqDTO);
        return getExactionAgentResponseDTO(userInput, featureExactionResponseDTO, taskId);
    }

    public AiSearchAgentStartResponse noveltySearchAgent(AiTaskReqDTO request) {
        createTask(request.getTaskId(), request.getText());
        AiSearchResultDTO result = getAiSearchResultDTO(request.getTaskId());
        String url = webHostConfig.getWebsiteHost() + AI_FTO_NOVELTY_CHECK_REPORT + getShareId(request.getTaskId()) + CURRENT_STEP + STEP_CONFIRM;
        return AiSearchAgentStartResponse.builder().searchResult(result).url(url).status(result.getTaskStatus()).build();
    }

    public AiSearchAgentReportResponse getReport(AiTaskReqDTO request) {
        createTask(request.getTaskId(), request.getText());
        String websiteHost = webHostConfig.getWebsiteHost();
        String url = websiteHost + AI_FTO_NOVELTY_CHECK_REPORT + getShareId(request.getTaskId()) + CURRENT_STEP + STEP_RESULT;
        return AiSearchAgentReportResponse.builder().report(null).url(url).build();
    }
    
    private static @NotNull AiTaskCreateReqDTO buildCreateReq(String taskId, String userInput) {
        AiTaskCreateReqDTO createReq = new AiTaskCreateReqDTO();
        createReq.setTaskId(taskId);
        createReq.setType(AiTaskTypeEnum.AI_FTO_SEARCH.getType());
        createReq.setContent(Map.of(AiTaskContentTypeEnum.USER_INPUT.getType(), userInput));
        return createReq;
    }
    
    private @NotNull FeatureExactionAgentResponseDTO getExactionAgentResponseDTO(String userInput,
            FeatureExactionResponseDTO featureExactionResponse, String taskId) {
        FeatureExactionAgentResponseDTO featureExactionResponseDTO = FeatureExactionAgentResponseDTO.builder()
                .text(userInput).features(featureExactionResponse.getFeatures())
                .threshold(featureExactionResponse.getThreshold()).taskId(taskId).build();
        String websiteHost = webHostConfig.getWebsiteHost();
        String url =
                websiteHost + AI_FTO_NOVELTY_CHECK_REPORT + getShareId(taskId) + CURRENT_STEP +
                        FTO_SEARCH_TECH_FEATURE;
        featureExactionResponseDTO.setUrl(url);
        return featureExactionResponseDTO;
    }
    
    private @NotNull FeatureExactionResponseDTO getFeatureExactionResponseDTO(
            NoveltySearchResponseDTO.Data noveltySearchData, String userInput) {
        FeatureExactionResponseDTO featureExactionResponse = new FeatureExactionResponseDTO();
        featureExactionResponse.setThreshold(noveltySearchData.getThreshold());
        featureExactionResponse.setFeatures(buildFeatures(noveltySearchData));
        featureExactionResponse.setText(userInput);
        return featureExactionResponse;
    }
    
    private List<AiSearchAgentFeature> buildFeatures(NoveltySearchResponseDTO.Data noveltySearchData) {
        List<AiSearchAgentFeature> features = noveltySearchData.getFeatures();
        for (AiSearchAgentFeature feature : features) {
            feature.setSelect(feature.getScore() > noveltySearchData.getThreshold());
        }
        return features;
    }
    
    private @NotNull AiSearchResultDTO getAiSearchResultDTO(String taskId) {
        AiTaskReqDTO aiTaskReqDTO = new AiTaskReqDTO();
        aiTaskReqDTO.setTaskId(taskId);
        ftoSearchManager.ftoSearchAgent(aiTaskReqDTO);
        int waitTime = 0;
        int maxWaitTime = 30 * 60;
        boolean unFinished = true;
        AiSearchResultDTO result = null;
        while (unFinished && waitTime < maxWaitTime) {
            log.info("waiting for novelty search result");
            sleep(3000);
            waitTime += 3;
            result = fetchNoveltySearchResult(taskId);
            unFinished = !isTaskFinished(result);
        }
        log.info("novelty search result: " + result.toString());
        return result;
    }

    private boolean isTaskFinished(AiSearchResultDTO resultDTO) {
        return !"running".equals(resultDTO.getTaskStatus());
    }
    
    private AiSearchResultDTO fetchNoveltySearchResult(String taskId) {
        AiSearchResultReqDTO aiSearchResultReqDTO = new AiSearchResultReqDTO();
        aiSearchResultReqDTO.setTaskId(taskId);
        return ftoSearchManager.fetchFtoSearchResult(aiSearchResultReqDTO);
    }

    private String getShareId(String taskId) {
        FreeShareInitRequest initRequest = new FreeShareInitRequest();
        initRequest.setTaskId(taskId);
        AiTaskShareLinkPO shareLinkPO = publicShareManager.getShareLinkPO(initRequest);
        if (shareLinkPO != null) {
            return shareLinkPO.getShareId();
        }
        FreeShareCreateRequest request = new FreeShareCreateRequest();
        request.setTaskId(taskId);
        FreeShareCreateResponse shareCreateResponse = publicShareManager.createPublicShareLink(request);
        return shareCreateResponse.getShareId();
    }

    private void createTask(String taskId, String userInput) {
        AnalyticsAiTaskHistoryPO taskHistoryPO = aiTaskManager.getTaskById(taskId);
        if (taskHistoryPO == null) {
            aiTaskManager.createTask(buildCreateReq(taskId, userInput));
            AiTaskReqDTO aiTaskReqDTO = new AiTaskReqDTO();
            aiTaskReqDTO.setTaskId(taskId);
            aiTaskReqDTO.setText(userInput);
            ftoSearchManager.getInputLang(aiTaskReqDTO);
        }
    }
}

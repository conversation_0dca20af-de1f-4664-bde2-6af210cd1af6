package com.patsnap.drafting.manager.content.logic;

import com.patsnap.drafting.enums.task.AiTaskContentTypeEnum;
import com.patsnap.drafting.enums.task.TaskContentDirectionEnum;
import com.patsnap.drafting.manager.aitask.AiTaskManager;
import com.patsnap.drafting.manager.credit.CreditManager;
import com.patsnap.drafting.request.ainoveltysearch.AiSearchResultDTO;
import com.patsnap.drafting.response.aiftosearch.FtoSearchReportTitleResponseDTO;
import com.patsnap.drafting.response.ainoveltysearch.FeatureComparisonResponseDTO;
import com.patsnap.drafting.response.ainoveltysearch.FeatureExactionResponseDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * AIFTO查新专利技术特征更新content处理逻辑
 */
@Component
public class FtoSearchUpdateTechFeatureContentCacheLogic extends ContentCacheLogic {

    @Autowired
    private AiTaskManager aiTaskManager;

    @Autowired
    private CreditManager creditManager;

    @Override
    public void updateContent(String taskId, AiTaskContentTypeEnum contentType, Object result, Object[] args) {
        if (!(result instanceof FeatureExactionResponseDTO)) {
            return;
        }
        aiTaskManager.updateTaskContent(taskId, contentType, result);
        aiTaskManager.deleteTaskContentByContentType(taskId,
                List.of(AiTaskContentTypeEnum.FTO_SEARCH_TECH_FEATURE_COMPARISON_SCORE.getType(),
                        AiTaskContentTypeEnum.FTO_SEARCH_TASK_ID.getType(),
                        AiTaskContentTypeEnum.FTO_SEARCH_AGENT_RESULT.getType(),
                        AiTaskContentTypeEnum.FTO_SEARCH_FEATURE_CONFIRM.getType(),
                        AiTaskContentTypeEnum.FTO_SEARCH_REPORT_TITLE.getType()
                ));
    }
}
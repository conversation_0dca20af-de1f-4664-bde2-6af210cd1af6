package com.patsnap.drafting.manager.aitranslation.operate;

import com.patsnap.core.common.copilot.streaming.model.GptResponseDTO;
import com.patsnap.drafting.model.aitranslation.AiTransContextBo;
import com.patsnap.drafting.model.aitranslation.TranslationBO;
import com.patsnap.spring.boot.autoconfigure.web.entity.CommonResponse;
import java.util.List;

import reactor.core.publisher.Flux;

public interface TranslationFullTextService {

    String model();

    Flux<CommonResponse<GptResponseDTO<List<TranslationBO>>>> generate(AiTransContextBo contextBo);
}

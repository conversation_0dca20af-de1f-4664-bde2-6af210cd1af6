package com.patsnap.drafting.manager.aispecification.content;

import com.patsnap.core.common.copilot.constant.GPTStatus;
import com.patsnap.core.common.copilot.streaming.model.GptResponseDTO;
import com.patsnap.drafting.annotation.TaskContentCache;
import com.patsnap.drafting.config.UrlConfig;
import com.patsnap.drafting.enums.task.AiTaskContentTypeEnum;
import com.patsnap.drafting.manager.AbstractGenerateStreamingContent;
import com.patsnap.drafting.manager.FileManager;
import com.patsnap.drafting.manager.aitask.AiTaskManager;
import com.patsnap.drafting.model.aispecification.FigureContentBO;
import com.patsnap.drafting.model.base.StreamingModelBO;
import com.patsnap.drafting.request.GenerateContentRequestDTO;
import com.patsnap.drafting.request.aispecification.Figure;
import com.patsnap.spring.boot.autoconfigure.web.entity.CommonResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * AI说明书撰写 - 摘要附图信息获取
 * 从 content 表获取附图数据并流式返回
 */
@Component
@Slf4j
public class SpecificationAbstractFiguresContent extends AbstractGenerateStreamingContent<FigureContentBO> {

    @Autowired
    private FileManager fileManager;

    protected SpecificationAbstractFiguresContent(AiTaskManager aiTaskManager, UrlConfig urlConfig) {
        super(aiTaskManager, urlConfig);
    }

    @Override
    public String getContentType() {
        return AiTaskContentTypeEnum.ABSTRACT_FIGURE.getType();
    }

    @Override
    protected StreamingModelBO getStreamingModelBO(GenerateContentRequestDTO generateContentRequestDTO) {
        // 对于附图获取，不需要调用AI模型，直接返回空的StreamingModelBO
        return new StreamingModelBO();
    }

    @Override
    protected Flux<CommonResponse<GptResponseDTO<FigureContentBO>>> getContent(StreamingModelBO streamingModelBO) {
        // 这里不使用AI生成，而是直接从content表获取数据
        return Flux.empty();
    }

    @Override
    @TaskContentCache(contentTypeExpression = "#request.contentType")
    public Flux<CommonResponse<GptResponseDTO<FigureContentBO>>> doGenerate(GenerateContentRequestDTO request) {
        log.info("开始获取摘要附图信息，任务ID: {}", request.getTaskId());
        
        try {
            // 1. 从content表获取附图数据
            FigureContentBO figureContentBO = aiTaskManager.getTaskContent(request.getTaskId(), AiTaskContentTypeEnum.FIGURE_CONFIG);

            //2. 从 figureContentBO 中获取摘要附图
            List<Figure> abstractFigureList = new ArrayList<>();
            if (figureContentBO != null && figureContentBO.getFigures() != null) {
                abstractFigureList = figureContentBO.getFigures().stream()
                        .filter(figure -> figure != null 
                                && figure.getImage() != null 
                                && Boolean.TRUE.equals(figure.getAbstractFigure()))
                        .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(abstractFigureList) && !figureContentBO.getFigures().isEmpty()) {
                    abstractFigureList.add(figureContentBO.getFigures().get(0));
                }
            }
            FigureContentBO abstractFigureContentBO = FigureContentBO.builder()
                    .figures(abstractFigureList)
                    .build();
            
            GptResponseDTO<FigureContentBO> gptResponse = GptResponseDTO.<FigureContentBO>builder()
                    .content(abstractFigureContentBO)
                    .status(GPTStatus.FINISH)
                    .build();
            
            CommonResponse<GptResponseDTO<FigureContentBO>> response = CommonResponse.<GptResponseDTO<FigureContentBO>>builder()
                    .withData(gptResponse)
                    .withStatus(true)
                    .build();
            
            log.info("摘要附图信息获取成功，任务ID: {}", request.getTaskId());
            
            // 3. 返回流式响应
            return Flux.just(response);
            
        } catch (Exception e) {
            log.error("获取附图信息失败，任务ID: {}", request.getTaskId(), e);
            
            // 构造错误响应
            GptResponseDTO<FigureContentBO> errorResponse = GptResponseDTO.<FigureContentBO>builder()
                    .content(FigureContentBO.builder().figures(new ArrayList<>()).build())
                    .status(GPTStatus.FAILED)
                    .build();
            
            CommonResponse<GptResponseDTO<FigureContentBO>> response = CommonResponse.<GptResponseDTO<FigureContentBO>>builder()
                    .withData(errorResponse)
                    .withStatus(false)
                    .withErrorMsg("获取附图信息失败: " + e.getMessage())
                    .build();
            
            return Flux.just(response);
        }
    }
    
} 
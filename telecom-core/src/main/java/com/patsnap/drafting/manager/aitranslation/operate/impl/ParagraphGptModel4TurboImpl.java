package com.patsnap.drafting.manager.aitranslation.operate.impl;

import com.patsnap.core.common.copilot.gpt.enums.GPTModelEnum;
import com.patsnap.core.common.copilot.streaming.handler.SplitListJsonOutputParser;
import com.patsnap.drafting.client.OpenAiClient;
import com.patsnap.drafting.config.AiTranslationConfig;
import com.patsnap.drafting.config.UrlConfig;
import com.patsnap.drafting.enums.prompt.PromptKeyEnum;
import com.patsnap.drafting.enums.prompt.ScenarioEnum;
import com.patsnap.drafting.manager.aitranslation.operate.TranslationConstant;
import com.patsnap.drafting.manager.aitranslation.operate.TranslationParagraphService;
import com.patsnap.drafting.model.aitranslation.AiTransContextBo;
import com.patsnap.drafting.model.aitranslation.TranslationBO;
import com.patsnap.drafting.response.aitranslation.TranslationKeywordResDTO;
import com.patsnap.drafting.util.ParagraphUtils;
import com.patsnap.drafting.util.RetryUtil;
import static com.patsnap.drafting.manager.aitranslation.operate.TranslationConstant.*;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.ImmutableMap;

@Component
public class ParagraphGptModel4TurboImpl extends TranslationBasicImpl implements TranslationParagraphService {
    private static GPTModelEnum GPT_MODEL = GPTModelEnum.GPT_MODEL_4_TURBO;

    public ParagraphGptModel4TurboImpl(UrlConfig urlConfig, AiTranslationConfig aiTranslationConfig,
            OpenAiClient openAiClient) {
        super(urlConfig, aiTranslationConfig, openAiClient);
    }

    @Override
    public String model() {
        return GPT_MODEL.getModelName();
    }

    @Override
    public List<TranslationBO> generate(AiTransContextBo contextBo) {
        String sourceInput = contextBo.getInput();
        String keywordsStr = getPromptKeywords(contextBo.getSuggestedTerms());
        // 给段落添加特定标识符
        sourceInput = ParagraphUtils.addParagraphPrefix(sourceInput);
        // 将文本进行分段
        List<String> paragraphs = ParagraphUtils.splitText(sourceInput, TranslationConstant.PARAGRAPH_MAX_LENGTH);
        // 逐段翻译
        List<TranslationBO> translations = translateByParagraph(paragraphs, contextBo.getTargetLang(),
                contextBo.getTechTopic(), keywordsStr);
        // 过滤异常数据
        translations.removeIf(t -> StringUtils.isAnyBlank(t.getTranslatedText(), t.getSrcText()));
        // 根据标识符计算每个句子所属的段落index
        ParagraphUtils.addParagraphIndex(translations);
        // 移除翻译结果中的段落标识符
        translations.forEach(
                t -> t.setTranslatedText(t.getTranslatedText().replace(ParagraphUtils.PARAGRAPH_PREFIX, "")));
        return translations;
    }

    /**
     * 段落翻译
     *
     * @param paragraphs 待翻译的段落
     * @return
     */
    public @NotNull List<TranslationBO> translateByParagraph(List<String> paragraphs,
            String targetLang, String techTopic, String keywordsStr) {
        List<TranslationBO> translations = new ArrayList<>();
        List<String> originalTranslation = new ArrayList<>();
        for (String paragraph : paragraphs) {
            String translation = RetryUtil.retry(
                    () -> getTranslationByLang(GPT_MODEL, paragraph, targetLang, techTopic, keywordsStr));
            originalTranslation.add(translation);
            // 校验翻译结果
            String paragraphTrans = RetryUtil.retry(
                    () -> openAiClient.chatCompletions(PromptKeyEnum.PROOFREADING_TRANSLATION.getValue(),
                            GPT_MODEL, ScenarioEnum.AI_TRANSLATION,
                            String.class, ImmutableMap.of(INPUT, translation, TARGET_LANG,
                                    targetLang, KEYWORDS, keywordsStr, TECH_TOPIC,
                                    techTopic)));
            // 解析翻译结果为结构化数据
            SplitListJsonOutputParser<List<TranslationBO>> parser = new SplitListJsonOutputParser<>(
                    TranslationConstant.LINE_SEPARATOR_REGEX,
                    TranslationConstant.FIELD_SEPARATOR_REGEX);
            List<TranslationBO> sentenceTrans = parser.convertContent(paragraphTrans, new TypeReference<>() {});
            translations.addAll(sentenceTrans);
        }
        return translations;
    }

    /**
     * 将术语处理成prompt中需要的格式:
     * 原始文本<-->翻译文本
     *
     * @param keywords 术语列表
     * @return
     */
    private String getPromptKeywords(List<TranslationKeywordResDTO> keywords) {
        if (CollectionUtils.isEmpty(keywords)) {
            return "";
        }
        StringBuilder sb = new StringBuilder();
        for (TranslationKeywordResDTO keyword : keywords) {
            keyword.getOriginal()
                    .forEach(t -> sb.append(t).append(CORNER_MARK).append(keyword.getTranslation()).append(CARRIER_MARK));
        }
        return sb.toString();
    }

    private String getTranslationByLang(GPTModelEnum modelEnum, String input, String targetLang,
            String techTopic, String keywordsStr) {
        return openAiClient.chatCompletions(PromptKeyEnum.SENTENCE_TRANSLATION.getValue(), modelEnum,
                ScenarioEnum.AI_TRANSLATION, String.class,
                ImmutableMap.of(INPUT, input, TARGET_LANG, targetLang, KEYWORDS,
                        keywordsStr, TECH_TOPIC, techTopic));
    }
}

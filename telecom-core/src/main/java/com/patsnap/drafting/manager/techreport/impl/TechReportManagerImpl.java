package com.patsnap.drafting.manager.techreport.impl;

import com.alibaba.fastjson.JSON;
import com.patsnap.common.exception.ForbiddenException;
import com.patsnap.common.request.CorrelationIdHolder;
import com.patsnap.common.web.entity.CommonResponse;
import com.patsnap.core.common.copilot.gpt.enums.GPTModelEnum;
import com.patsnap.core.common.searchclient.client.SearchApiClient;
import com.patsnap.core.common.searchclient.query.Query;
import com.patsnap.core.common.searchclient.query.QueryResponse;
import com.patsnap.core.common.entity.Sort;
import com.patsnap.core.common.entity.Collapse;
import com.patsnap.core.common.entity.CollapseRule;
import com.patsnap.core.common.track.ModuleType;
import com.patsnap.drafting.client.EurekaClient;
import com.patsnap.drafting.client.OpenAiClient;
import com.patsnap.drafting.client.PatentApiClient;
import com.patsnap.drafting.client.api.EurekaApi;
import com.patsnap.drafting.manager.IdentityAccountManager;
import com.patsnap.common.request.UserIdHolder;
import com.patsnap.core.common.identity.AccountInfo;
import com.patsnap.drafting.client.model.CompanyDetailsResDTO;
import com.patsnap.drafting.constants.PatentDetailFieldConstants;
import com.patsnap.drafting.exception.BizException;
import com.patsnap.drafting.exception.errorcode.TechReportErrorCodeEnum;
import com.patsnap.drafting.manager.techreport.TechReportManager;
import com.patsnap.drafting.manager.techreport.model.QueryResult;
import com.patsnap.drafting.manager.techreport.model.TechReportConfigDTO;
import com.patsnap.drafting.manager.techreport.model.TechReportConstraintDTO;
import com.patsnap.drafting.manager.techreport.model.TechReportExcludedKeywordDTO;
import com.patsnap.drafting.manager.track.TrackingManager;
import com.patsnap.drafting.request.techreport.TechReportInitReqDTO;
import com.patsnap.drafting.response.techreport.*;
import com.patsnap.drafting.util.RedissonUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.function.Supplier;
import java.text.SimpleDateFormat;
import java.util.concurrent.CompletableFuture;

import static com.patsnap.drafting.constants.TrackEvent.TECH_MONITOR_AGENT_EVENT;
import static com.patsnap.drafting.constants.TrackEvent.TECH_MONITOR_AGENT_FEATURE_ANALYSIS_SOURCE;
import static com.patsnap.drafting.constants.TrackEvent.TECH_MONITOR_AGENT_INTENT_FAILURE_SOURCE;
import static com.patsnap.drafting.constants.TrackEvent.TECH_MONITOR_AGENT_INTENT_SOURCE;
import static com.patsnap.drafting.constants.TrackEvent.TECH_MONITOR_AGENT_PREVIEW_QUERY_ERROR_SOURCE;
import static com.patsnap.drafting.constants.TrackEvent.TECH_MONITOR_AGENT_PREVIEW_SOURCE;
import static com.patsnap.drafting.constants.TrackEvent.TECH_MONITOR_AGENT_RECOMMEND_SOURCE;
import static com.patsnap.drafting.constants.TrackEvent.TECH_MONITOR_AGENT_REPORT_CONFIG_FAILURE_SOURCE;
import static com.patsnap.drafting.enums.prompt.PromptKeyEnum.*;
import static com.patsnap.drafting.enums.prompt.ScenarioEnum.TECH_REPORT_SCENARIO;

import com.patsnap.drafting.response.techreport.CompanyInfo;
import org.springframework.web.client.HttpClientErrorException;
import retrofit2.Response;
import com.patsnap.drafting.client.model.CompanyRecommendResDTO;

/**
 * 技术简报管理器实现类
 * 负责技术简报的创建、编辑、查询等操作
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TechReportManagerImpl implements TechReportManager {

    public static final String TECH_REPORT = "tech_report_";

    private static final int SEARCH_PATENT_ROWS = 10;
    private static final String PATENT_TYPE_CONDITION = " AND PATENT_TYPE:(\"A\")";
    private static final String PBD_CONDITION_FORMAT = " AND PBD:[%s TO *]";

    private final EurekaApi eurekaApi;
    
    private final EurekaClient eurekaClient;

    // 注入需要的依赖服务
    private final OpenAiClient openAiClient;
    private final SearchApiClient searchApiClient;
    private final RedissonUtils redissonUtils;
    private final RedissonClient redissonClient;
    private final GenerateQuery generateQuery;
    private final PatentApiClient patentApiClient;
    private final PatentDetailUtil patentDetailUtil;
    private final IdentityAccountManager identityAccountManager;
    private final TrackingManager trackingManager;

    private static final String REDIS_KEY_INTENT = ":intent";
    private static final String REDIS_KEY_REPORT_CONFIG = ":report_config";
    private static final String REDIS_KEY_EXTRACT_FEATURES = ":extract_features_resp";
    private static final String REDIS_KEY_EXTRACT_TECH_FEATURES = ":extract_tech_features";
    private static final String REDIS_KEY_TECH_RECOMMEND = ":tech_recommend";
    private static final String REDIS_KEY_RECOMMEND = ":recommend";
    private static final String REDIS_KEY_EXTRACT_FEATURES_RESP = ":extract_features_resp";
    private static final String REDIS_KEY_QUERY_RESULT = ":query_result";
    private static final String REDIS_KEY_NEWS_KEYWORDS = ":news_keywords";
    private static final String FORESIGHT= "foresight";

    @Autowired
    @Qualifier("commonExecutor")
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    /**
     * 获取上周开始日期
     * @return 格式化的日期字符串 (yyyyMMdd)
     */
    private String getLastWeekStartDate() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.WEEK_OF_YEAR, -1);  // 上一周
        calendar.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);  // 设置为周一
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        return sdf.format(calendar.getTime());
    }

    /**
     * 获取近一个月的开始日期
     * @return 格式化的日期字符串 (yyyyMMdd)
     */
    private String getLastMonthStartDate() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MONTH, -1);  // 一个月前
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        return sdf.format(calendar.getTime());
    }

    /**
     * 获取近一个月的开始日期
     * @return 格式化的日期字符串 (yyyyMMdd)
     */
    private String getLastYearStartDate() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.YEAR, -1);  // 一年前
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        return sdf.format(calendar.getTime());
    }

    /**
     * 从Redis获取数据,如果为空则返回Optional.empty()
     * @param agentJobId 任务ID
     * @param suffix Redis key后缀
     * @param clazz 返回对象类型
     * @return Optional包装的对象
     */
    @SuppressWarnings("unchecked")
    private <T> Optional<T> getFromRedis(String agentJobId, String suffix, Class<T> clazz) {
        try {
            Object data = redissonUtils.get(redissonClient, getRedisKey(agentJobId) + suffix);
            if (data == null) {
                return Optional.empty();
            }
            // 检查类型是否匹配
            if (clazz.isInstance(data)) {
                return Optional.of((T) data);
            } else {
                log.warn("类型不匹配: 预期 {}, 实际 {}, key: {}",
                        clazz.getName(), data.getClass().getName(), getRedisKey(agentJobId) + suffix);
                return Optional.empty();
            }
        } catch (Exception e) {
            log.error("获取Redis数据失败: {}", getRedisKey(agentJobId) + suffix, e);
            return Optional.empty();
        }
    }

    /**
     * 如果用户公司为空，则设置当前用户所在的公司
     * 
     * @param techReportIntentResDTO 技术报告意图DTO
     */
    private void setUserCompanyIfEmpty(TechReportIntentResDTO techReportIntentResDTO) {
        if (techReportIntentResDTO.getUserCompany() == null || techReportIntentResDTO.getUserCompany().isEmpty()) {
            String userId = UserIdHolder.get();
            if (userId != null && !userId.isEmpty()) {
                AccountInfo accountInfo = identityAccountManager.getAccountInfoByUserId(userId);
                if (accountInfo != null && accountInfo.getCompanyName() != null) {
                    techReportIntentResDTO.setUserCompany(accountInfo.getCompanyName());
                    log.info("已自动设置用户公司: {}, userId: {}", accountInfo.getCompanyName(), userId);
                } else {
                    log.warn("未能获取用户公司信息, userId: {}", userId);
                }
            } else {
                log.warn("未能获取用户账户信息, userId: {}", userId);
            }
        }
    }

    /**
     * 如果用户行业为空，则设置当前用户所在的行业
     * 
     * @param techReportConfigDTO 技术报告意图DTO
     */
    private void setUserIndustryIfEmpty(TechReportConfigDTO techReportConfigDTO) {
        if (techReportConfigDTO == null) {
            techReportConfigDTO = new TechReportConfigDTO();
        }
        if (techReportConfigDTO.getIndustry() == null || techReportConfigDTO.getIndustry().isEmpty()) {
            String userId = UserIdHolder.get();
            if (userId != null && !userId.isEmpty()) {
                AccountInfo accountInfo = identityAccountManager.getAccountInfoByUserId(userId);
                if (accountInfo != null && accountInfo.getIndustry() != null) {
                    techReportConfigDTO.setIndustry(accountInfo.getIndustry());
                    log.info("已自动设置用户行业: {}, userId: {}", accountInfo.getIndustry(), userId);
                } else {
                    log.warn("未能获取用户行业信息, userId: {}", userId);
                }
            } else {
                log.warn("未能获取用户账户信息, userId: {}", userId);
            }
        }
    }

    /**
     * 初始化技术简报对话
     *
     * @param agentJobId agent 任务 ID
     * @param request 初始化请求
     * @return 初始化结果
     */
    @Override
    public TechReportConfigDTO intentIdentity(String agentJobId, TechReportInitReqDTO request) {
        log.info("开始技术简报意图识别, agentJobId: {}, request: {}", agentJobId, request);
        try {
            //1. 调用AI服务识别意图
            String userInput = request.getUserInput() != null ? request.getUserInput() : "";
            Map<String, String> params = Map.of("user_input", userInput);
            String prompt = openAiClient.buildPromptByPlatform(INTENT_RECOGNITION.getValue(), params);
            int retryCount = 0;
            TechReportIntentResDTO techReportIntentResDTO = null;
            while (retryCount < 3) {
                techReportIntentResDTO = openAiClient.callGptByPrompt(GPTModelEnum.GPT_MODEL_4_O,
                        prompt, TECH_REPORT_SCENARIO, TechReportIntentResDTO.class);
                if (techReportIntentResDTO != null) {
                    break;
                }
                retryCount++;
                log.error("识别意图失败, 正在重试... agentJobId: {}, 重试次数: {}", agentJobId, retryCount);
            }
            //如果不是这三种意图：TechKeyword，CompanyName，TechDomainCompanies，返回错误信息
            String intent = techReportIntentResDTO != null ? techReportIntentResDTO.getIntent() : "";
            Map<String, String> eventData = Map.of(
                    "agent_job_id", StringUtils.isNotBlank(agentJobId) ? agentJobId : "",
                    "user_input", StringUtils.isNotBlank(userInput) ? userInput : "",
                    "intent", StringUtils.isNotBlank(intent) ? intent : "",
                    "correlation_id", StringUtils.isNotBlank(CorrelationIdHolder.get()) ? CorrelationIdHolder.get() : ""
            );
            if (!"TechKeyword".equals(intent) && !"CompanyName".equals(intent) && !"TechDomainCompanies".equals(intent)) {
                log.error("意图类型无效, agentJobId: {}, intent: {}", agentJobId, intent);
                // 意图错误，埋点
                trackingManager.addTracking(ModuleType.EUREKA, TECH_MONITOR_AGENT_EVENT,
                        TECH_MONITOR_AGENT_INTENT_FAILURE_SOURCE, intent, com.alibaba.fastjson.JSON.toJSONString(eventData));
//                throw new BizException(TechReportErrorCodeEnum.INTENT_INVALID);
            }
            
            techReportIntentResDTO.setUserInput(request.getUserInput());
            //如果userCompany为空，则取当前用户所在的公司
            setUserCompanyIfEmpty(techReportIntentResDTO);
            log.info("技术简报意图识别，jobId: {}, 结果: {}", agentJobId, techReportIntentResDTO);

            //2. 调用AI服务：要素拆解
            String promptConfig = openAiClient.buildPromptByPlatform(ELEMENT_ANALYSIS_DEFAULT.getValue(), params);
            int retryConfigCount = 0;
            TechReportConfigDTO techReportConfigDTO = null;
            while (retryConfigCount < 3) {
                techReportConfigDTO = openAiClient.callGptByPrompt(GPTModelEnum.GPT_MODEL_4_O, promptConfig,
                        TECH_REPORT_SCENARIO, TechReportConfigDTO.class);
                if (techReportConfigDTO != null) {
                    break;
                }
                retryConfigCount++;
                log.error("AI要素拆解失败, 正在重试... agentJobId: {}, 重试次数: {}", agentJobId, retryConfigCount);
            }
            log.info("技术简报要素拆解结果: {}", techReportConfigDTO);

            if (techReportConfigDTO == null) {
                // 要素拆解失败，埋点
                trackingManager.addTracking(ModuleType.EUREKA, TECH_MONITOR_AGENT_EVENT,
                        TECH_MONITOR_AGENT_REPORT_CONFIG_FAILURE_SOURCE, intent, com.alibaba.fastjson.JSON.toJSONString(eventData));
            }

            //这里重置下intent
            resetIntent(techReportIntentResDTO, techReportConfigDTO);

            //处理prompt提取出来的所有的公司
            processExtractedCompanies(techReportConfigDTO);

            //如果industry为空，则从identity中取用户所在的industry
            setUserIndustryIfEmpty(techReportConfigDTO);
            redissonUtils.set(redissonClient, getRedisKey(agentJobId) + ":intent", techReportIntentResDTO, 72, TimeUnit.HOURS);
            redissonUtils.set(redissonClient, getRedisKey(agentJobId) + ":report_config", techReportConfigDTO, 72, TimeUnit.HOURS);
            // 意图识别完成，埋点
            trackingManager.addTracking(ModuleType.EUREKA, TECH_MONITOR_AGENT_EVENT,
                    TECH_MONITOR_AGENT_INTENT_SOURCE,
                    intent,
                    com.alibaba.fastjson.JSON.toJSONString(eventData));
            return techReportConfigDTO;
        } catch (BizException e) {
            // BizException需要重新抛出，让上层处理
            log.error("技术简报意图识别业务异常, agentJobId: {}", agentJobId, e);
            throw e;
        } catch (Exception e) {
            log.error("技术简报意图识别失败, agentJobId: {}", agentJobId, e);
            return null;
        }
    }

    /**
     * 重置意图类型
     * 根据coreSubject和extractCompanies的情况重新设置intent
     * 
     * @param techReportIntentResDTO 技术报告意图DTO
     * @param techReportConfigDTO 技术报告配置DTO
     */
    private void resetIntent(TechReportIntentResDTO techReportIntentResDTO, TechReportConfigDTO techReportConfigDTO) {
        if (techReportIntentResDTO == null || techReportConfigDTO == null) {
            log.warn("无法重置intent，参数为空: intentResDTO={}, configDTO={}", 
                    techReportIntentResDTO != null, techReportConfigDTO != null);
            return;
        }
        
        String coreSubject = techReportConfigDTO.getCoreSubject();
        List<String> extractCompanies = techReportConfigDTO.getExtractCompanies();
        
        String originalIntent = techReportIntentResDTO.getIntent();
        String newIntent;
        
        // 判断coreSubject是否为空
        boolean isCoreSubjectEmpty = StringUtils.isEmpty(coreSubject);
        // 判断extractCompanies是否不为空
        boolean hasExtractCompanies = extractCompanies != null && !extractCompanies.isEmpty();
        // 判断extractCompanies是否只有一个
        boolean hasOnlyOneCompany = extractCompanies != null && extractCompanies.size() == 1;
        
        if (isCoreSubjectEmpty && hasExtractCompanies) {
            // 1. coreSubject为空，且extractCompanies不为空，intent=CompanyName
            newIntent = "CompanyName";
        } else if (!isCoreSubjectEmpty && hasExtractCompanies && hasOnlyOneCompany) {
            // 2. coreSubject不为空，且extractCompanies不为空，且extractCompanies个数只有一个，intent=TechDomainCompanies
            newIntent = "TechDomainCompanies";
        } else {
            // 3. 其他情况：均为 intent=TechKeyword
            newIntent = "TechKeyword";
        }
        
        // 更新intent
        techReportIntentResDTO.setIntent(newIntent);
        
        log.info("重置intent完成: 原intent={}, 新intent={}, coreSubject为空={}, extractCompanies数量={}", 
                originalIntent, newIntent, isCoreSubjectEmpty, 
                extractCompanies != null ? extractCompanies.size() : 0);
    }

    /**
     * 处理prompt提取出来的公司列表，将extractCompanies转化为CompanyInfo类型
     * 
     * @param techReportConfigDTO 技术报告配置DTO
     */
    private void processExtractedCompanies(TechReportConfigDTO techReportConfigDTO) {
        if (techReportConfigDTO == null) {
            log.warn("技术报告配置DTO为空，无法处理公司列表");
            return;
        }
        
        try {
            // 1. 处理extractCompanies字段 - 恢复对主要公司列表的处理
            processCompanyList(
                "主要提取的公司",
                () -> techReportConfigDTO.getExtractCompanies(),
                companyInfoList -> techReportConfigDTO.setExtractCompanyList(companyInfoList),
                () -> techReportConfigDTO.setExtractCompanyList(new ArrayList<>())
            );
            
            // 2. 处理约束条件中的公司列表
            processCompanyList(
                "约束条件中的公司",
                () -> techReportConfigDTO.getConstraint() != null ? 
                      techReportConfigDTO.getConstraint().getOnlyMonitorCompanies() : null,
                companyInfoList -> {
                    if (techReportConfigDTO.getConstraint() != null) {
                        techReportConfigDTO.getConstraint().setOnlyMonitorCompanyList(companyInfoList);
                    }
                },
                () -> {
                    if (techReportConfigDTO.getConstraint() != null) {
                        techReportConfigDTO.getConstraint().setOnlyMonitorCompanyList(new ArrayList<>());
                    }
                }
            );
            
            // 3. 处理排除关键词中的公司列表
            processCompanyList(
                "排除关键词中的公司",
                () -> techReportConfigDTO.getExcluded() != null ?
                      techReportConfigDTO.getExcluded().getExcludeCompanies() : null,
                companyInfoList -> {
                    if (techReportConfigDTO.getExcluded() != null) {
                        techReportConfigDTO.getExcluded().setExcludeCompanyList(companyInfoList);
                    }
                },
                () -> {
                    if (techReportConfigDTO.getExcluded() != null) {
                        techReportConfigDTO.getExcluded().setExcludeCompanyList(new ArrayList<>());
                    }
                }
            );
            
        } catch (Exception e) {
            log.error("处理提取的公司列表时发生异常", e);
        }
    }
    
    /**
     * 通用的公司列表处理方法
     * 
     * @param processType 处理类型描述，用于日志
     * @param companyNamesSupplier 获取公司名称列表的供应商
     * @param resultSetter 设置处理结果的消费者
     * @param defaultValueSetter 设置默认值的操作
     */
    private void processCompanyList(
            String processType,
            Supplier<List<String>> companyNamesSupplier,
            Consumer<List<CompanyInfo>> resultSetter,
            Runnable defaultValueSetter) {
        
        try {
            // 1. 获取公司名称列表
            List<String> companyNames = companyNamesSupplier.get();
            
            // 2. 空值检查
            if (companyNames == null || companyNames.isEmpty()) {
                log.info("{}列表为空，无需处理", processType);
                return;
            }
            
            log.info("开始处理{}列表，公司数量: {}", processType, companyNames.size());
            
            // 3. 调用转换方法获取公司详细信息
            List<CompanyInfo> companyInfoList = fetchRecommendedCompanies(companyNames);
            
            // 4. 处理结果
            if (companyInfoList != null && !companyInfoList.isEmpty()) {
                resultSetter.accept(companyInfoList);
                log.info("成功处理{}列表，转换后的公司数量: {}", processType, companyInfoList.size());
            } else {
                log.warn("获取{}详细信息失败或结果为空", processType);
                defaultValueSetter.run();
            }
        } catch (Exception e) {
            log.error("处理{}列表时发生异常", processType, e);
            defaultValueSetter.run();
        }
    }

    /**
     * 创建空的技术特征提取结果对象
     * 
     * @return 空的TechReportExtractFeaturesResDTO对象
     */
    private TechReportExtractFeaturesResDTO createEmptyExtractFeaturesResult() {
        TechReportExtractFeaturesResDTO emptyResult = new TechReportExtractFeaturesResDTO();
        emptyResult.setChildTerms(Collections.emptyList());
        emptyResult.setUpperTerms(Collections.emptyList());
        emptyResult.setKeyFeatures(Collections.emptyList());
        emptyResult.setClassificationNumbers(Collections.emptyList());
        emptyResult.setCoreSubject("");
        return emptyResult;
    }

    /**
     * 提取技术特征
     *
     * @param agentJobId agent 任务 ID
     * @param request 初始化请求
     * @return 提取特征结果
     */
    @Override
    public TechReportExtractFeaturesResDTO extractFeatures(String agentJobId, TechReportInitReqDTO request) {
        log.info("开始提取技术特征, agentJobId: {}", agentJobId);


        try {
            // 1. 从缓存中获取意图和要素拆解结果
            Map<String, Object> cacheData = fetchCacheData(agentJobId);
            
            // 检查行业和主题字段
            Object industryObj = cacheData.get("industry");
            Object subjectObj = cacheData.get("subject");
            String industry = industryObj != null ? industryObj.toString() : null;
            String subject = subjectObj != null ? subjectObj.toString() : null;
            
            // 当industry和subject都为空时，返回空对象
            if (subject == null || subject.trim().isEmpty()) {
                log.error("行业和主题数据都为空，无法提取技术特征, agentJobId: {}, industry: {}, subject: {}", 
                        agentJobId, industry, subject);
                return createEmptyExtractFeaturesResult();
            }
            
            TechReportConfigDTO techReportConfigDTO = (TechReportConfigDTO) cacheData.get("configDTO");
            
            // 2. 分析特征-关键词
            TechReportExtractTechFeature techReportExtractTechFeature = extractTechFeatures(
                    agentJobId, industry, subject);
            if (techReportExtractTechFeature == null) {
                log.error("提取技术特征失败, agentJobId: {}", agentJobId);
                return createEmptyExtractFeaturesResult();
            }

            // 3. 二次检查独特技术特征
            TechReportExtractKeyTechFeature techReportExtractKeyTechFeature = extractKeyTechFeatures(
                    agentJobId,
                    techReportExtractTechFeature.getUniqueTechFeatures(),
                    industry, 
                    subject);
            
            // 4. 更新独特技术特征
            updateUniqueTechFeatures(techReportExtractTechFeature, techReportExtractKeyTechFeature);
            redissonUtils.set(redissonClient, getRedisKey(agentJobId) + ":extract_tech_features", techReportExtractTechFeature, 72, TimeUnit.HOURS);
            // 5. 汇总结果
            TechReportExtractFeaturesResDTO result = assembleExtractFeaturesResult(
                    techReportExtractTechFeature, 
                    techReportExtractKeyTechFeature, 
                    techReportConfigDTO);
            
            // 缓存结果
            redissonUtils.set(redissonClient, getRedisKey(agentJobId) + ":extract_features_resp", result, 72, TimeUnit.HOURS);
            // 提取技术特征完成，埋点
            String intent = (String)cacheData.getOrDefault("intent", "");
            Map<String, String> eventData = Map.of(
                    "agent_job_id", StringUtils.isNotBlank(agentJobId) ? agentJobId : "",
                    "intent", StringUtils.isNotBlank(intent) ? intent : "",
                    "user_input", StringUtils.isNotBlank(request.getUserInput()) ? request.getUserInput() : "",
                    "correlation_id", StringUtils.isNotBlank(CorrelationIdHolder.get()) ? CorrelationIdHolder.get() : "",
                    "core_subject", StringUtils.isNotBlank(result.getCoreSubject()) ? result.getCoreSubject() : ""
            );
            trackingManager.addTracking(ModuleType.EUREKA, TECH_MONITOR_AGENT_EVENT,
                    TECH_MONITOR_AGENT_FEATURE_ANALYSIS_SOURCE,
                    intent,
                    com.alibaba.fastjson.JSON.toJSONString(eventData));
            return result;
        } catch (Exception e) {
            log.error("提取技术特征失败, agentJobId: {}", agentJobId, e);
            return createEmptyExtractFeaturesResult();
        }
    }
    
    /**
     * 分析提取技术特征
     * 
     * @param agentJobId agent任务ID
     * @param industry 行业
     * @param subject 主题
     * @return 技术特征
     */
    private TechReportExtractTechFeature extractTechFeatures(String agentJobId, String industry, String subject) {
        // 确保参数不为null，避免Map.of()抛出NullPointerException
        String safeIndustry = industry != null ? industry : "";
        String safeSubject = subject != null ? subject : "";
        
        Map<String, String> feature_params = Map.of("industry", safeIndustry, "subject", safeSubject);
        String feature_prompt = openAiClient.buildPromptByPlatform(ANALYSIS_FEATURE_KEYWORDS.getValue(), feature_params);
        log.info("技术简报jobId:{}, prompt打印: {}", agentJobId, feature_prompt);

        int retryCount = 0;
        TechReportExtractTechFeature result = null;
        while(retryCount < 3) {
            result = openAiClient.callGptByPrompt(
                    GPTModelEnum.GPT_MODEL_4_O,
                    feature_prompt,
                    TECH_REPORT_SCENARIO,
                    TechReportExtractTechFeature.class);
            if (result != null) {
                break;
            }
            retryCount++;
            log.error("技术简报特征分析失败, 正在重试... jobId: {}, 重试次数: {}", agentJobId, retryCount);
        }

        log.info("技术简报特征分析, jobId:{}, 结果: {}", agentJobId, result);

        return result;
    }
    
    /**
     * 提取关键技术特征
     * 
     * @param agentJobId agent任务ID
     * @param uniqueTechFeatures tech features
     * @param industry 行业
     * @param subject 主题
     * @return 关键技术特征
     */
    private TechReportExtractKeyTechFeature extractKeyTechFeatures(String agentJobId, List<String> uniqueTechFeatures, String industry, String subject) {
        // 确保参数不为null，避免Map.of()抛出NullPointerException
        String uniqueTechFeature = uniqueTechFeatures != null ? String.join(",", uniqueTechFeatures) : "";
        String safeIndustry = industry != null ? industry : "";
        String safeSubject = subject != null ? subject : "";
        
        Map<String, String> key_feature_params = Map.of(
                "uniqueTechFeatures", uniqueTechFeature,
                "industry", safeIndustry,
                "subject", safeSubject);
        
        String key_feature_prompt = openAiClient.buildPromptByPlatform(
                SECONDARY_CHECK_UNIQUE_TECH_FEATURES.getValue(), 
                key_feature_params);

        int retryCount = 0;
        TechReportExtractKeyTechFeature result = null;
        while(retryCount < 3) {
            result = openAiClient.callGptByPrompt(
                    GPTModelEnum.GPT_MODEL_4_O,
                    key_feature_prompt,
                    TECH_REPORT_SCENARIO,
                    TechReportExtractKeyTechFeature.class);
            if (result != null) {
                break;
            }
            retryCount++;
            log.error("技术简报独特特征分析失败, 正在重试... jobId: {}, 重试次数: {}", agentJobId, retryCount);
        }
        
        log.info("技术简报独特特征分析，jobId:{}, 结果: {}", agentJobId, result);
        return result;
    }
    
    /**
     * 更新独特技术特征
     * 
     * @param techFeatures 技术特征
     * @param keyTechFeatures 关键技术特征
     */
    private void updateUniqueTechFeatures(
            TechReportExtractTechFeature techFeatures, 
            TechReportExtractKeyTechFeature keyTechFeatures) {
        
        if (techFeatures == null) {
            log.warn("无法更新独特技术特征，techReportExtractTechFeature为空");
            return;
        }
        
        if (keyTechFeatures == null || keyTechFeatures.getFeatures() == null) {
            log.warn("二次检查独特技术特征结果为空");
            techFeatures.setUniqueTechFeatures(Collections.emptyList());
            return;
        }
        
        List<String> uniqueFeatures = keyTechFeatures.getFeatures().entrySet().stream()
                .filter(entry -> entry.getValue() != null && Boolean.TRUE.equals(entry.getValue().getBelongsToThemeUniquely()))
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
        
        techFeatures.setUniqueTechFeatures(uniqueFeatures);
        log.info("已更新独特技术特征到TechReportExtractTechFeature中，特征数量: {}", uniqueFeatures.size());
    }
    
    /**
     * 组装特征提取结果
     * 
     * @param techFeatures 技术特征
     * @param keyTechFeatures 关键技术特征
     * @param configDTO 配置DTO
     * @return 特征提取结果
     */
    private TechReportExtractFeaturesResDTO assembleExtractFeaturesResult(
            TechReportExtractTechFeature techFeatures,
            TechReportExtractKeyTechFeature keyTechFeatures,
            TechReportConfigDTO configDTO) {
        
        TechReportExtractFeaturesResDTO result = new TechReportExtractFeaturesResDTO();
        
        // 设置子术语列表
        if (techFeatures != null) {
            result.setChildTerms(techFeatures.getSubtypes());
            result.setUpperTerms(techFeatures.getParentKeywords());
        }
        
        // 设置关键特征列表
        if (keyTechFeatures != null && keyTechFeatures.getFeatures() != null) {
            List<String> keyFeatures = keyTechFeatures.getFeatures().entrySet().stream()
                    .filter(entry -> entry.getValue() != null && Boolean.TRUE.equals(entry.getValue().getBelongsToThemeUniquely()))
                    .map(Map.Entry::getKey)
                    .collect(Collectors.toList());
            result.setKeyFeatures(keyFeatures);
        } else {
            result.setKeyFeatures(Collections.emptyList());
        }
        
        // 设置分类号信息
        if (techFeatures != null && techFeatures.getSubclass() != null) {
            List<TechReportExtractFeaturesResDTO.ClassificationInfo> classificationNumbers = techFeatures.getSubclass().entrySet().stream()
                    .map(subclass -> {
                        TechReportExtractFeaturesResDTO.ClassificationInfo info = new TechReportExtractFeaturesResDTO.ClassificationInfo();
                        info.setCode(subclass.getKey());
                        info.setDescription(subclass.getValue());
                        return info;
                    })
                    .collect(Collectors.toList());
            result.setClassificationNumbers(classificationNumbers);
        }
        
        // 设置核心主题对象
        if (configDTO != null) {
            result.setCoreSubject(configDTO.getCoreSubject());
        }
        
        return result;
    }

    private static @NotNull String getRedisKey(String agentJobId) {
        return TECH_REPORT + agentJobId;
    }

    /**
     * 推荐公司与技术
     * 根据配置数据和约束条件，生成推荐的公司和技术列表
     *
     * @param agentJobId 任务ID
     * @return 推荐结果DTO
     */
    @Override
    public TechReportRecommendResDTO recommend(String agentJobId) {
        log.info("开始推荐公司与技术, agentJobId: {}", agentJobId);
        try {
            // 1. 从缓存中获取必要数据
            Map<String, Object> cacheData = fetchCacheData(agentJobId);
            TechReportConfigDTO techReportConfigDTO = (TechReportConfigDTO) cacheData.get("configDTO");
            if (techReportConfigDTO == null) {
                log.error("配置数据缺失，无法推荐公司与技术, agentJobId: {}", agentJobId);
                return null;
            }
            
            // 2. 初始化推荐结果
            TechReportRecommendResDTO techReportRecommendResDTO = new TechReportRecommendResDTO();
            
            // 3. 处理约束条件
            TechReportConstraintDTO constraintDTO = techReportConfigDTO.getConstraint();
            boolean needCompleteCompany = processCompanyConstraints(constraintDTO, techReportRecommendResDTO);
            boolean needCompleteTech = processTechConstraints(constraintDTO, techReportRecommendResDTO);
            
            // 4. 获取基础数据, 从配置数据中获取行业和公司列表，允许为null
            String industry = techReportConfigDTO.getIndustry();
            List<String> extractCompanies = techReportConfigDTO.getExtractCompanies();
            List<String> extractTechSubfields = techReportConfigDTO.getExtractTechSubfields();
            String subject = techReportConfigDTO.getCoreSubject();
            
            // 5. 获取公司和意图, 从缓存中获取techReportIntentResDTO中的userCompany和intent
            TechReportIntentResDTO techReportIntentResDTO = (TechReportIntentResDTO) cacheData.get("intentResDTO");
            String userCompany = techReportIntentResDTO != null ? techReportIntentResDTO.getUserCompany() : null;
            String intent = techReportIntentResDTO != null ? techReportIntentResDTO.getIntent() : null;
            
            // 6. 处理推荐结果
            processRecommendationResult(
                agentJobId,
                techReportRecommendResDTO,
                needCompleteCompany,
                needCompleteTech,
                industry,
                subject,
                extractCompanies,
                extractTechSubfields,
                techReportConfigDTO,
                userCompany,
                intent
            );
            
            // 7. 缓存结果
            cacheRecommendationResult(agentJobId, techReportRecommendResDTO);
            // 推荐公司与技术完成，埋点
            Map<String, Object> eventData = Map.of(
                    "agent_job_id", StringUtils.isNotBlank(agentJobId) ? agentJobId : "",
                    "intent", StringUtils.isNotBlank(intent) ? intent : "",
                    "user_input", techReportIntentResDTO != null && StringUtils.isNotBlank(techReportIntentResDTO.getUserInput()) ? techReportIntentResDTO.getUserInput() : "",
                    "subject", StringUtils.isNotBlank(subject) ? subject : "",
                    "correlation_id", StringUtils.isNotBlank(CorrelationIdHolder.get()) ? CorrelationIdHolder.get() : ""
            );
            trackingManager.addTracking(ModuleType.EUREKA, TECH_MONITOR_AGENT_EVENT,
                    TECH_MONITOR_AGENT_RECOMMEND_SOURCE,
                    intent,
                    com.alibaba.fastjson.JSON.toJSONString(eventData));
            return techReportRecommendResDTO;
        } catch (Exception e) {
            log.error("推荐公司与技术失败, agentJobId: {}", agentJobId, e);
            return null;
        }
    }

    /**
     * 处理公司约束条件
     * @return 是否需要补充公司数据
     */
    private boolean processCompanyConstraints(TechReportConstraintDTO constraintDTO, TechReportRecommendResDTO recommendResDTO) {
        List<CompanyInfo> onlyMonitorCompanyList = constraintDTO.getOnlyMonitorCompanyList();
        List<String> onlyMonitorCompanies = constraintDTO.getOnlyMonitorCompanies();
        
        if (onlyMonitorCompanyList != null && !onlyMonitorCompanyList.isEmpty()) {
            log.info("已从约束条件数据中获取推荐公司列表, 数量: {}", onlyMonitorCompanyList.size());
            recommendResDTO.setHeadCompanies(onlyMonitorCompanyList);
            return false;
        } else if (onlyMonitorCompanies != null && !onlyMonitorCompanies.isEmpty()) {
            recommendResDTO.setHeadCompanies(fetchRecommendedCompanies(onlyMonitorCompanies));
            return false;
        }
        return true;
    }

    /**
     * 处理技术约束条件
     * @return 是否需要补充技术数据
     */
    private boolean processTechConstraints(TechReportConstraintDTO constraintDTO, TechReportRecommendResDTO recommendResDTO) {
        List<String> onlyMonitorTechs = constraintDTO.getOnlyMonitorTechs();
        if (onlyMonitorTechs != null && !onlyMonitorTechs.isEmpty()) {
            log.info("已从约束条件数据中获取推荐技术列表, 数量: {}", onlyMonitorTechs.size());
            recommendResDTO.setImportantTechnologies(onlyMonitorTechs);
            return false;
        }
        return true;
    }

    /**
     * 处理推荐结果
     */
    private void processRecommendationResult(
            String agentJobId,
            TechReportRecommendResDTO recommendResDTO,
            boolean needCompleteCompany,
            boolean needCompleteTech,
            String industry,
            String subject,
            List<String> extractCompanies,
            List<String> extractTechSubfields,
            TechReportConfigDTO configDTO,
            String userCompany,
            String intent) {
            
        boolean hasEnoughCompanies = !needCompleteCompany || (extractCompanies != null && extractCompanies.size() > 5);
        boolean hasEnoughTechSubfields = !needCompleteTech || (extractTechSubfields != null && extractTechSubfields.size() > 5);
        
        if (hasEnoughCompanies && hasEnoughTechSubfields) {
            processSufficientData(recommendResDTO, needCompleteCompany, needCompleteTech, extractCompanies, extractTechSubfields);
        } else {
            processInsufficientData(
                agentJobId,
                recommendResDTO,
                needCompleteCompany,
                needCompleteTech,
                industry,
                subject,
                extractCompanies,
                extractTechSubfields,
                configDTO,
                userCompany,
                intent
            );
        }
        
        recommendResDTO.setCoreSubject(subject != null ? subject : "");
    }

    /**
     * 处理数据充足的情况
     */
    private void processSufficientData(
            TechReportRecommendResDTO recommendResDTO,
            boolean needCompleteCompany,
            boolean needCompleteTech,
            List<String> extractCompanies,
            List<String> extractTechSubfields) {
            
        log.info("提取的公司数量({})和技术子领域数量({})都大于5个，跳过AI推荐直接组装结果", 
                extractCompanies.size(), extractTechSubfields.size());
                
        if (needCompleteCompany) {
            List<CompanyInfo> companies = fetchRecommendedCompanies(extractCompanies);
            recommendResDTO.setHeadCompanies(companies);
        }
        
        if (needCompleteTech) {
            recommendResDTO.setImportantTechnologies(extractTechSubfields);
        }
    }

    /**
     * 处理数据不足的情况, 调用AI服务，获取推荐公司和子领域
     */
    private void processInsufficientData(
            String agentJobId,
            TechReportRecommendResDTO techReportRecommendResDTO,
            boolean needCompleteCompany,
            boolean needCompleteTech,
            String industry,
            String subject,
            List<String> extractCompanies,
            List<String> extractTechSubfields,
            TechReportConfigDTO techReportConfigDTO,
            String userCompany,
            String intent) {
            
        TechReportTechRecommendResDTO techReportTechRecommendResDTO = callRecommendationAI(
                agentJobId, industry, subject, extractCompanies, extractTechSubfields, techReportConfigDTO, userCompany, intent);
        // 缓存中间结果，无论是否为null
        redissonUtils.set(redissonClient, getRedisKey(agentJobId) + ":tech_recommend", techReportTechRecommendResDTO, 72, TimeUnit.HOURS);
        
        if (needCompleteCompany) {
            List<CompanyInfo> headCompanies = assembleHeadCompanies(techReportConfigDTO, techReportTechRecommendResDTO);
            techReportRecommendResDTO.setHeadCompanies(headCompanies);
        }
        
        if (needCompleteTech) {
            processTechRecommendations(techReportRecommendResDTO, extractTechSubfields, techReportTechRecommendResDTO);
        }
    }

    /**
     * 处理技术推荐
     */
    private void processTechRecommendations(
            TechReportRecommendResDTO techReportRecommendResDTO,
            List<String> extractTechSubfields,
            TechReportTechRecommendResDTO techReportTechRecommendResDTO) {

        // 组装重要技术：tech_child
        // 当extractTechSubfields大于5个时，则不拼接AI返回的推荐技术
        // 当extractTechSubfields小于等于5个时，拼接AI返回的推荐技术
        List<String> importantTechnologies = new ArrayList<>();

        // 首先添加提取的技术子领域
        if (extractTechSubfields != null && !extractTechSubfields.isEmpty()) {
            importantTechnologies.addAll(extractTechSubfields);
        }

        // 判断是否需要拼接AI推荐的技术
        if (extractTechSubfields == null || extractTechSubfields.size() < 5) {
            if (techReportTechRecommendResDTO != null && techReportTechRecommendResDTO.getRecommendTech() != null) {
                importantTechnologies.addAll(techReportTechRecommendResDTO.getRecommendTech());
                log.info("已拼接AI推荐技术，当前提取技术子领域数量: {}", extractTechSubfields != null ? extractTechSubfields.size() : 0);
            }
        } else {
            log.info("提取的技术子领域数量({})大于5个，跳过拼接AI推荐技术", extractTechSubfields.size());
        }

        techReportRecommendResDTO.setImportantTechnologies(importantTechnologies);
    }

    /**
     * 缓存推荐结果
     */
    private void cacheRecommendationResult(String agentJobId, TechReportRecommendResDTO recommendResDTO) {
        redissonUtils.set(redissonClient, getRedisKey(agentJobId) + ":recommend", recommendResDTO, 72, TimeUnit.HOURS);
    }

    /**
     * 组装头部公司列表
     * 包含从配置中提取的公司和推荐的公司
     * 
     * @param techReportConfigDTO 技术报告配置DTO
     * @param techReportTechRecommendResDTO 技术推荐结果DTO
     * @return 组装后的公司信息列表
     */
    private List<CompanyInfo> assembleHeadCompanies(TechReportConfigDTO techReportConfigDTO, 
            TechReportTechRecommendResDTO techReportTechRecommendResDTO) {
        
        // 头部公司列表：要素拆解中的extract_companies（从缓存中取）+recommend_companies
        List<String> headCompanies = new ArrayList<>();
        
        // 获取从配置中提取的公司，使用新的转化方法
        List<CompanyInfo> extractedCompanies = null;
        if (techReportConfigDTO != null) {
            extractedCompanies = convertExtractedCompaniesToCompanyInfo(techReportConfigDTO);
            headCompanies.addAll(extractedCompanies.stream()
                    .map(CompanyInfo::getCompanyName)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList()));
        }
        
        // 添加推荐的公司 - 如果extractedCompanies小于5个，则添加推荐的公司
        if (extractedCompanies == null || extractedCompanies.size() < 5) {
            if (techReportTechRecommendResDTO != null && techReportTechRecommendResDTO.getRecommendCompanies() != null) {
                headCompanies.addAll(techReportTechRecommendResDTO.getRecommendCompanies());
                log.info("已添加推荐公司，当前提取公司数量: {}", extractedCompanies != null ? extractedCompanies.size() : 0);
            }
        } else {
            log.info("提取的公司数量({})大于5个，跳过添加推荐公司", extractedCompanies.size());
        }
        
        // 获取推荐公司详细信息
        List<CompanyInfo> companies = fetchRecommendedCompanies(headCompanies);
        return companies != null ? companies : new ArrayList<>();
    }

    /**
     * 调用AI服务获取推荐结果
     * 根据intent类型选择合适的prompt进行推荐
     * 
     * @param agentJobId agent任务ID
     * @param industry 行业信息
     * @param subject 核心主题
     * @param extractCompanies 提取的公司列表
     * @param extractTechSubfields 提取的技术子领域列表
     * @param techReportConfigDTO 技术报告配置
     * @param userCompany 用户公司
     * @param intent 意图类型：CompanyName-基于公司推荐，TechKeyword-基于行业关键词推荐，TechDomainCompanies-综合推荐
     * @return AI推荐结果
     */
    private TechReportTechRecommendResDTO callRecommendationAI(String agentJobId, String industry,  String subject,
            List<String> extractCompanies, List<String> extractTechSubfields, TechReportConfigDTO techReportConfigDTO, String userCompany, String intent) {
        
        // 初始化默认结果，确保不会出现空指针异常
        TechReportTechRecommendResDTO defaultResult = new TechReportTechRecommendResDTO();
        defaultResult.setRecommendCompanies(Collections.emptyList());
        defaultResult.setRecommendTech(Collections.emptyList());

        try {
            // 从缓存中获取techReportConfigDTO中的TechReportExcludedKeywordDTO.excludeCompanies
            List<String> excludeCompanies = techReportConfigDTO != null && 
                    techReportConfigDTO.getExcluded() != null && 
                    techReportConfigDTO.getExcluded().getExcludeCompanies() != null ?
                    techReportConfigDTO.getExcluded().getExcludeCompanies() : Collections.emptyList();
            
            log.info("从缓存中获取到排除公司列表，数量: {}, intent: {}", excludeCompanies.size(), intent);

            // 构建prompt参数
            Map<String, String> params = buildRecommendParams(industry, subject, extractCompanies, extractTechSubfields, userCompany);
            
            // 根据intent选择对应的推荐策略
            if ("CompanyName".equals(intent)) {
                // 场景1: intent为CompanyName时，基于公司推荐
                return recommendByCompany(params);
                
            } else if ("TechKeyword".equals(intent)) {
                // 场景2: intent为TechKeyword时，基于行业关键词推荐
                return recommendByIndustry(params);
                
            } else if ("TechDomainCompanies".equals(intent)) {
                // 场景3: intent为TechDomainCompanies时，综合推荐
                return recommendByIndustryAndCompany(params);
                
            } else {
                // 场景4: 其他情况，基于主题进行默认推荐
                log.warn("未识别的intent类型或intent为空，将使用主题进行默认推荐, agentJobId: {}, intent: {}", agentJobId, intent);
                return recommendBySubject(agentJobId, subject);
            }
        } catch (Exception e) {
            log.error("调用AI服务获取推荐结果失败, agentJobId: {}", agentJobId, e);
            return defaultResult;
        }
    }

    /**
     * 基于公司信息进行推荐
     */
    private TechReportTechRecommendResDTO recommendByCompany(Map<String, String> params) {
        return executeRecommendation(RECOMMEND_SUBDOMAIN_COMPANY_TECH.getValue(), params, "基于公司");
    }

    /**
     * 基于行业信息进行推荐
     */
    private TechReportTechRecommendResDTO recommendByIndustry(Map<String, String> params) {
        return executeRecommendation(RECOMMEND_SUBDOMAIN_KEYWORDS.getValue(), params, "基于行业关键词");
    }

    /**
     * 基于行业和公司信息进行综合推荐
     */
    private TechReportTechRecommendResDTO recommendByIndustryAndCompany(Map<String, String> params) {
        return executeRecommendation(RECOMMEND_SUBDOMAIN_COMPANY_TECH.getValue(), params, "基于行业和公司");
    }

    /**
     * 基于主题进行默认推荐
     */
    private TechReportTechRecommendResDTO recommendBySubject(String agentJobId, String subject) {
        log.warn("行业和公司数据都缺失，将使用主题进行默认推荐, agentJobId: {}, subject: {}", agentJobId, subject);
        
        Map<String, String> params = Map.of("subject", Objects.toString(subject, ""));
        return executeRecommendation(RECOMMEND_SUBDOMAIN_KEYWORDS.getValue(), params, "仅基于主题");
    }

    /**
     * 构建推荐参数Map
     * 
     * @param industry 行业
     * @param subject 主题
     * @param extractCompanies 提取的公司列表
     * @param extractTechSubfields 提取的技术子领域列表
     * @param userCompany 用户公司
     * @return 参数Map
     */
    private Map<String, String> buildRecommendParams(String industry, String subject,
            List<String> extractCompanies, List<String> extractTechSubfields, String userCompany) {
        
        // 使用Objects.toString()进行空值安全处理
        String extractCompaniesStr = extractCompanies != null ? String.join(",", extractCompanies) : "";
        String extractTechSubfieldsStr = extractTechSubfields != null ? String.join(",", extractTechSubfields) : "";
        String industryStr = Objects.toString(industry, "");
        String subjectStr = Objects.toString(subject, "");
        String userCompanyStr = Objects.toString(userCompany, "");
        
        return Map.of(
                "industry", industryStr,
                "subject", subjectStr,
                "extract_companies", extractCompaniesStr,
                "extract_tech_subfields", extractTechSubfieldsStr,
                "user_company", userCompanyStr);
    }

    /**
     * 执行推荐调用
     * 
     * @param promptKey prompt键值
     * @param params 参数Map
     * @param logDescription 日志描述
     * @return 推荐结果
     */
    private TechReportTechRecommendResDTO executeRecommendation(String promptKey, Map<String, String> params, String logDescription) {
        String prompt = openAiClient.buildPromptByPlatform(promptKey, params);
        int retryTimes = 0;
        TechReportTechRecommendResDTO result = null;
        while(retryTimes < 3) {
            result = openAiClient.callGptByPrompt(GPTModelEnum.GPT_MODEL_4_O, prompt,
                    TECH_REPORT_SCENARIO, TechReportTechRecommendResDTO.class);
            if (result != null) {
                break;
            }
            retryTimes++;
            log.error("调用AI服务获取推荐结果失败，将进行重试 ({}), 重试次数: {}", logDescription, retryTimes);
        }

        log.info("推荐公司和子领域结果 ({}): {}", logDescription, result);
        return result;
    }

    /**
     * 获取推荐公司详细信息
     * 
     * @param headCompanies 公司名称列表
     * @return 公司信息列表
     */
    private List<CompanyInfo> fetchRecommendedCompanies(List<String> headCompanies) {
        List<CompanyInfo> companies = new ArrayList<>();
        if (headCompanies == null || headCompanies.isEmpty()) {
            log.warn("公司列表为空，无法获取推荐公司信息");
            return companies;
        }
        
        log.info("开始获取推荐公司信息，公司数量: {}", headCompanies.size());
        for (String companyName : headCompanies) {
            try {
                if (StringUtils.isEmpty(companyName)) {
                    continue;
                }

                CompanyRecommendResDTO companyRecommend = eurekaClient.recommendCompany(companyName);
                
                // 使用Optional流式API简化嵌套条件判断
                // 创建公司信息对象
                CompanyInfo company = new CompanyInfo();
                
                // 尝试获取推荐数据
                boolean hasRecommendData = Optional.ofNullable(companyRecommend)
                    .map(CompanyRecommendResDTO::getSuggest)
                    .filter(suggests -> !suggests.isEmpty())
                    .map(suggests -> {
                        // 有推荐数据时使用推荐数据
                        company.setCompanyName(suggests.get(0).getCompanyName());
                        company.setCompanyId(suggests.get(0).getCompanyId());
                        return true;
                    })
                    .orElse(false);
                
                // 无推荐数据时使用原始公司名
                if (!hasRecommendData) {
                    company.setCompanyName(companyName);
                    company.setCompanyId(null);
                    log.warn("公司推荐接口返回空数据，使用原始公司名称: {}, companyRecommend为空: {}", 
                            companyName, companyRecommend == null);
                } else {
                    log.info("成功获取公司推荐数据: 名称={}, ID={}", 
                            company.getCompanyName(), company.getCompanyId());
                }
                
                // 添加到公司列表
                companies.add(company);
            } catch (Exception e) {
                log.error("获取公司推荐数据失败: {}", companyName, e);
            }
        }
        
        log.info("完成推荐公司信息获取，成功获取: {}/{}", companies.size(), headCompanies.size());

        // 获取公司的logo
        fetchCompanyLogos(companies);
        return companies;
    }
    
    /**
     * 获取公司logo信息
     * 
     * @param companies 公司信息列表
     */
    private void fetchCompanyLogos(List<CompanyInfo> companies) {
        if (companies == null || companies.isEmpty()) {
            log.warn("公司列表为空，无法获取公司logo信息");
            return;
        }
        
        log.info("开始获取公司logo信息，公司数量: {}", companies.size());
        for (CompanyInfo company : companies) {
            try {
                if (StringUtils.isEmpty(company.getCompanyId())) {
                    log.warn("公司ID为空，无法获取logo信息: {}", company.getCompanyName());
                    continue;
                }
                
                Response<CommonResponse<CompanyDetailsResDTO>> response = eurekaClient.getCompanyDetails(company.getCompanyId());
                // 使用Optional处理可能为空的情况，简化嵌套
                Optional.ofNullable(response)
                    .map(Response::body)
                    .map(CommonResponse::getData)
                    .map(CompanyDetailsResDTO::getLogo)
                    .ifPresentOrElse(
                        // logo存在时的处理
                        logo -> {
                            company.setLogo(logo);
                            log.info("成功获取公司logo: 公司ID={}, logo={}", company.getCompanyId(), logo);
                        },
                        // logo不存在时的处理
                        () -> log.warn("获取公司信息失败或logo为空: 公司ID={}", company.getCompanyId())
                    );
            } catch (Exception e) {
                log.error("获取公司logo信息失败: 公司ID={}", company.getCompanyId(), e);
            }
        }
        
        log.info("完成公司logo信息获取");
    }

    /**
     * 技术预览
     *
     * @param agentJobId agent 任务 ID
     * @return 预览结果
     */
    @Override
    public TechReportPreviewWrapperDTO preview(String agentJobId) {
        log.info("开始生成技术预览, agentJobId: {}", agentJobId);
        String intent = "";
        // 专利默认查询时间范围为最近1个月
        String timeRange = "1";
        try {
            // 1. 从缓存中获取必要数据
            Map<String, Object> cacheData = fetchCacheData(agentJobId);
            intent = (String) cacheData.get("intent");

            // 2. 获取构建检索式必要的字段构建检索式
            QueryResult queryResult = buildQueryFromCacheData(agentJobId, cacheData);
            if (queryResult == null || queryResult.getMainSubjectQuery() == null || queryResult.getMainSubjectQuery().isEmpty()) {
                log.error("构建检索式失败，无法生成技术预览, agentJobId: {}", agentJobId);
                return new TechReportPreviewWrapperDTO("", Collections.emptyList(), intent, timeRange);
            }

            String patentQuery = intent.equals("TechKeyword") ? queryResult.getDisplayQuery() : queryResult.getMainSubjectQuery();
            // preview 检索式生成，埋点
            trackingManager.addTracking(ModuleType.EUREKA, TECH_MONITOR_AGENT_EVENT, TECH_MONITOR_AGENT_PREVIEW_SOURCE, intent, null);

            // 3. 请求search api服务，获取专利
            Map<String, Object> patentsMap = searchPatents(patentQuery, intent, cacheData);
            List<String> patentIds = (List<String>) patentsMap.getOrDefault("patentIds", Collections.emptyList());
            timeRange = (String) patentsMap.getOrDefault("timeRange", "12");
            if (patentIds == null || patentIds.isEmpty()) {
                log.warn("未找到相关专利，无法生成技术预览, agentJobId: {}", agentJobId);
                return new TechReportPreviewWrapperDTO(patentQuery, Collections.emptyList(), intent, timeRange);
            }
            
            // 4. 获取专利的基本信息
            List<TechReportPreviewResDTO.PatentInfo> patentInfoList = fetchPatentBasicInfo(patentIds);
            if (patentInfoList == null || patentInfoList.isEmpty()) {
                log.warn("未获取到专利基本信息，无法生成技术预览, agentJobId: {}", agentJobId);
                return new TechReportPreviewWrapperDTO(patentQuery, Collections.emptyList(), intent, timeRange);
            }
            
            // 5. 计算专利相关度并获取Top2
            List<TechReportPreviewResDTO.PatentInfo> topTwoPatentInfos = calculateRelevanceAndGetTopPatents(patentInfoList, 
                    (String) cacheData.get("subject"), 
                    (String) cacheData.get("industry"),
                    (String) cacheData.get("userCompany"));
            if (topTwoPatentInfos == null || topTwoPatentInfos.isEmpty()) {
                log.warn("未能计算出相关度最高的专利，无法生成技术预览, agentJobId: {}", agentJobId);
                return new TechReportPreviewWrapperDTO(patentQuery, Collections.emptyList(), intent, timeRange);
            }
            
            // 6. 获取Top2专利的详细信息并组装返回结果
            return fetchAdditionalInfoAndBuildResult(topTwoPatentInfos, patentQuery, intent, timeRange);
        } catch (Exception e) {
            log.error("生成技术预览失败, agentJobId: {}", agentJobId, e);
            return new TechReportPreviewWrapperDTO("", Collections.emptyList(), intent, timeRange);
        }
    }
    
    /**
     * 从缓存中获取必要数据
     * @param agentJobId agent任务ID
     * @return 缓存数据Map，缺失的数据会被设置为null
     */
    private Map<String, Object> fetchCacheData(String agentJobId) {
        Map<String, Object> cacheData = new HashMap<>();
        
        // 获取各类缓存数据
        TechReportIntentResDTO techReportIntentResDTO = redissonUtils.get(
                redissonClient, getRedisKey(agentJobId) + ":intent"
        );
        if (techReportIntentResDTO == null) {
            log.warn("缓存中未找到意图数据, agentJobId: {}", agentJobId);
        }
        cacheData.put("intentResDTO", techReportIntentResDTO);
        
        TechReportConfigDTO techReportConfigDTO = redissonUtils.get(
                redissonClient, getRedisKey(agentJobId) + ":report_config"
        );
        if (techReportConfigDTO == null) {
            log.warn("缓存中未找到报告配置数据, agentJobId: {}", agentJobId);
        }
        cacheData.put("configDTO", techReportConfigDTO);
        
        TechReportExtractFeaturesResDTO featuresResDTO = redissonUtils.get(
                redissonClient, getRedisKey(agentJobId) + REDIS_KEY_EXTRACT_FEATURES_RESP
        );
        cacheData.put("featuresResDTO", featuresResDTO);
        
        TechReportExtractTechFeature techReportExtractTechFeature = redissonUtils.get(
                redissonClient, getRedisKey(agentJobId) + ":extract_tech_features"
        );

        cacheData.put("techFeature", techReportExtractTechFeature);
        
        TechReportRecommendResDTO techReportRecommendResDTO = redissonUtils.get(
                redissonClient, getRedisKey(agentJobId) + ":recommend"
        );
        if (techReportRecommendResDTO == null) {
            log.warn("缓存中未找到推荐数据, agentJobId: {}", agentJobId);
        }
        cacheData.put("recommendResDTO", techReportRecommendResDTO);
        
        // 提取常用字段方便后续使用，安全地从可能为null的对象中获取数据
        cacheData.put("intent", techReportIntentResDTO != null ? techReportIntentResDTO.getIntent() : null);
        cacheData.put("industry", techReportConfigDTO != null ? techReportConfigDTO.getIndustry() : null);
        cacheData.put("userCompany", techReportIntentResDTO != null ? techReportIntentResDTO.getUserCompany() : null);
        cacheData.put("subject", techReportConfigDTO != null ? techReportConfigDTO.getCoreSubject() : null);
        cacheData.put("constraint", techReportConfigDTO != null ? techReportConfigDTO.getConstraint() : null);
        cacheData.put("excludeKeywords", techReportConfigDTO != null ? techReportConfigDTO.getExcluded() : null);
        cacheData.put("extractTechSubfields", techReportConfigDTO != null ? techReportConfigDTO.getExtractTechSubfields() : null);
        // 安全地处理公司列表，避免空指针异常
        cacheData.put("companies", techReportRecommendResDTO != null && techReportRecommendResDTO.getHeadCompanies() != null ? 
                techReportRecommendResDTO.getHeadCompanies().stream().map(CompanyInfo::getCompanyName).collect(Collectors.toList()) : null);
        cacheData.put("recommendTechSubfields", techReportRecommendResDTO != null && techReportRecommendResDTO.getImportantTechnologies() != null ?
                techReportRecommendResDTO.getImportantTechnologies() : null);

        return cacheData;
    }
    
    /**
     * 根据缓存数据构建检索式
     * @param agentJobId agent任务ID
     * @param cacheData 缓存数据
     * @return 查询结果，如果必要的参数缺失可能返回null
     */
    private QueryResult buildQueryFromCacheData(String agentJobId, Map<String, Object> cacheData) {
        try {
            String intent = (String) cacheData.get("intent");
            String industry = (String) cacheData.get("industry");
            TechReportExtractTechFeature techReportExtractTechFeature = (TechReportExtractTechFeature) cacheData.get("techFeature");
            TechReportConstraintDTO constraint = (TechReportConstraintDTO) cacheData.get("constraint");
            List<String> companies = (List<String>) cacheData.get("companies");
            List<String> recommendTechSubfields = (List<String>) cacheData.get("recommendTechSubfields");
            TechReportExcludedKeywordDTO excludeKeywords = (TechReportExcludedKeywordDTO) cacheData.get("excludeKeywords");
            
            // 为空值设置默认值
            intent = intent != null ? intent : "";
            industry = industry != null ? industry : "";
            constraint = constraint != null ? constraint : new TechReportConstraintDTO();
            companies = companies != null ? companies : Collections.emptyList();
            excludeKeywords = excludeKeywords != null ? excludeKeywords : new TechReportExcludedKeywordDTO();
            
            // 调用GenerateQuery生成检索式
            QueryResult queryResult = generateQuery.run(intent, industry, techReportExtractTechFeature, 
                    constraint, companies, recommendTechSubfields, excludeKeywords, agentJobId);
            
            if (queryResult != null) {
                // 缓存结果
                redissonUtils.set(redissonClient, getRedisKey(agentJobId) + ":query_result", queryResult, 72, TimeUnit.HOURS);
                return queryResult;
            } else {
                log.error("生成检索式失败, agentJobId: {}", agentJobId);
                return null;
            }
        } catch (Exception e) {
            log.error("构建检索式过程中发生异常, agentJobId: {}", agentJobId, e);
            return null;
        }
    }

    /**
     * 执行专利搜索，优先近一个月，无结果则自动扩大到近一年
     * @param mainQuery 主查询条件
     * @param intent 意图
     * @param cacheData 缓存数据
     * @return 专利ID列表，若无结果返回空集合
     */
    private Map<String, Object> searchPatents(String mainQuery, String intent, Map<String, Object> cacheData) {
        Map<String, Object> patentsMap = new HashMap<>();
        // 并发执行近一个月和近一年的检索
        String lastMonthStartDate = getLastMonthStartDate();
        String lastYearStartDate = getLastYearStartDate();
        String queryMonth = mainQuery + PATENT_TYPE_CONDITION + String.format(PBD_CONDITION_FORMAT, lastMonthStartDate);
        String queryYear = mainQuery + PATENT_TYPE_CONDITION + String.format(PBD_CONDITION_FORMAT, lastYearStartDate);

        CompletableFuture<List<String>> monthFuture = CompletableFuture.supplyAsync(() -> doPatentSearch(queryMonth, intent, cacheData), threadPoolTaskExecutor);
        CompletableFuture<List<String>> yearFuture = CompletableFuture.supplyAsync(() -> doPatentSearch(queryYear, intent, cacheData), threadPoolTaskExecutor);

        try {
            List<String> monthResult = monthFuture.get();
            if (monthResult != null && !monthResult.isEmpty()) {
                // 如果近一个月有结果，优先返回
                patentsMap.put("patentIds", monthResult);
                patentsMap.put("timeRange", "1");
                return patentsMap;
            } else {
                // 否则等待近一年结果
                List<String> yearResult = yearFuture.get();
                if (yearResult != null && !yearResult.isEmpty()) {
                    patentsMap.put("patentIds", yearResult);
                    patentsMap.put("timeRange", "12");
                    return patentsMap;
                }
            }
        } catch (Exception e) {
            log.error("并发检索专利时发生异常", e);
        }
        return patentsMap;
    }

    /**
     * 执行一次专利检索
     */
    private List<String> doPatentSearch(String queryStr, String intent, Map<String, Object> cacheData) {
        Collapse collapse = buildCollapse("FAMILY_ID", "PBDT_YEARMONTHDAY");
        List<Sort> sortList = buildSortList("score", Sort.Order.DESC);
        Query query = new Query.Builder()
                .withQ(queryStr)
                .withSort(sortList)
                .withCollapse(collapse)
                .withRows(SEARCH_PATENT_ROWS)
                .build();
        try {
            QueryResponse response = searchApiClient.doSearch(query);
            return Optional.ofNullable(response.getPatentIds()).orElse(Collections.emptyList());
        } catch (HttpClientErrorException hcee) {
            if (hcee.getStatusCode().is4xxClientError()) {
                // preview 生成的检索式有问题，searchapi报4XX，埋点
                Map<String, Object> eventDetail = new HashMap<>();
                TechReportIntentResDTO techReportIntentResDTO = (TechReportIntentResDTO)cacheData.get("intentResDTO");
                if (techReportIntentResDTO != null) {
                    eventDetail.put("user_input", techReportIntentResDTO.getUserInput());
                }
                trackingManager.addTracking(ModuleType.EUREKA, TECH_MONITOR_AGENT_EVENT, TECH_MONITOR_AGENT_PREVIEW_QUERY_ERROR_SOURCE, intent, JSON.toJSONString(eventDetail));
            }
            log.error("技术预览, 专利搜索失败咯: {}", hcee.getMessage());
        } catch (Exception e) {
            log.error("专利检索异常: ", e);
        }
        return Collections.emptyList();
    }
    
    /**
     * 获取专利的基本信息
     * @param patentIds 专利ID列表
     * @return 专利信息列表
     */
    private List<TechReportPreviewResDTO.PatentInfo> fetchPatentBasicInfo(List<String> patentIds) {
        List<String> fields = Arrays.asList(
            PatentDetailFieldConstants.TITLE,
            PatentDetailFieldConstants.ABST,
            PatentDetailFieldConstants.PRO_13B_SUMMARY,
            PatentDetailFieldConstants.PRO_13B_SUMMARY_LANG,
            PatentDetailFieldConstants.PRO_13B_SUMMARY_TRAN,
            PatentDetailFieldConstants.PATENT_INNO_SUMMARY,
            PatentDetailFieldConstants.PATENT_INNO_SUMMARY_TRAN,
            PatentDetailFieldConstants.PATENT_INNO_SUMMARY_LANG,
            PatentDetailFieldConstants.EFFECT_13B_SUMMARY,
            PatentDetailFieldConstants.EFFECT_13B_SUMMARY_TRAN,
            PatentDetailFieldConstants.EFFECT_13B_SUMMARY_LANG,
            PatentDetailFieldConstants.TECHNICAL_TITLE,
            PatentDetailFieldConstants.TECHNICAL_TITLE_TRAN,
            PatentDetailFieldConstants.TECHNICAL_TITLE_LANG,
            PatentDetailFieldConstants.ANCS,
            PatentDetailFieldConstants.PN,
            PatentDetailFieldConstants.TTC
        );
        
        // 调用patentDetailUtil获取专利详情
        return patentDetailUtil.getPatentDetail(patentIds, fields);
    }
    
    /**
     * 计算专利相关度并获取得分最高的专利
     * @param patentInfoList 专利信息列表
     * @param subject 主题
     * @param industry 行业
     * @return 得分最高的专利信息列表
     */
    private List<TechReportPreviewResDTO.PatentInfo> calculateRelevanceAndGetTopPatents(
            List<TechReportPreviewResDTO.PatentInfo> patentInfoList, 
            String subject, 
            String industry,
            String userCompany) {
        
        if (patentInfoList.isEmpty()) {
            return Collections.emptyList();
        }
        
        // 计算专利相关度
        List<TechReportPreviewResDTO.PatentInfo> scoredPatentInfoList = 
                calculatePatentRelevance(patentInfoList, subject, industry, userCompany);
        
        // 先过滤掉score为null的专利，然后获取得分最高的3条专利信息
        return scoredPatentInfoList.stream()
                .filter(patentInfo -> patentInfo.getRelevanceInfo() != null && patentInfo.getRelevanceInfo().getScore() != null)
                .sorted(Comparator.comparing(patentInfo -> patentInfo.getRelevanceInfo().getScore(), Comparator.reverseOrder()))
                .limit(3)
                .collect(Collectors.toList());
    }
    
    /**
     * 获取专利的详细信息并组装最终结果
     * @param topTwoPatentInfos 得分最高的专利信息列表
     * @param mainQuery 主查询语句
     * @param intent 意图
     * @return 技术预览结果包装对象
     */
    private TechReportPreviewWrapperDTO fetchAdditionalInfoAndBuildResult(
            List<TechReportPreviewResDTO.PatentInfo> topTwoPatentInfos,
            String mainQuery,
            String intent,
            String timeRange) {
        // 组装最终返回结果
        List<TechReportPreviewResDTO> previewList = topTwoPatentInfos.stream()
                .map(patentInfo -> {
                    TechReportPreviewResDTO previewDTO = new TechReportPreviewResDTO();
                    previewDTO.setPatentInfo(patentInfo);
                    previewDTO.setItemId(patentInfo.getPatentId());
                    previewDTO.setItemType("patent");
                    return previewDTO;
                })
                .collect(Collectors.toList());
        // 创建包含mainQuery的结果对象
        return new TechReportPreviewWrapperDTO(mainQuery, previewList, intent, timeRange);
    }

    /**
     * 构建折叠配置
     * @param collapseField 折叠字段
     * @param ruleField 规则字段
     * @return 折叠配置对象
     */
    private Collapse buildCollapse(String collapseField, String ruleField) {
        Collapse collapse = new Collapse();
        collapse.setField(collapseField);
        List<CollapseRule> rules = new ArrayList<>();
        CollapseRule rule = new CollapseRule();
        rule.setType("max");
        rule.setField(ruleField);
        rules.add(rule);
        collapse.setRules(rules);
        return collapse;
    }

    /**
     * 构建排序列表
     * @param field 排序字段
     * @param order 排序顺序
     * @return 排序列表
     */
    private List<Sort> buildSortList(String field, Sort.Order order) {
        List<Sort> sortList = new ArrayList<>();
        sortList.add(new Sort(field, order));
        return sortList;
    }

    /**
     * 计算专利与主题的相关度
     *
     * @param patentInfoList 专利信息列表
     * @param subject 主题
     * @param industry 行业
     * @return 更新后的专利信息列表
     */
    private List<TechReportPreviewResDTO.PatentInfo> calculatePatentRelevance(
            List<TechReportPreviewResDTO.PatentInfo> patentInfoList, String subject, String industry,String userCompany) {
        log.info("开始批量计算专利相关度, 专利数量: {}, subject: {}, industry: {}, companies:{}",
                patentInfoList.size(), subject, industry, userCompany);

        try {

            // 构建专利列表数据
            List<Map<String, String>> patentList = new ArrayList<>();
            for (TechReportPreviewResDTO.PatentInfo patentInfo : patentInfoList) {
                Map<String, String> patentData = new HashMap<>();
                patentData.put("patent_id", patentInfo.getPatentId());
                patentData.put("title", patentInfo.getTitle() != null ? patentInfo.getTitle() : "");
                patentData.put("abstract", patentInfo.getAbst() != null ? patentInfo.getAbst() : "");
                patentData.put("llm_innovation_list", patentInfo.getInventions() != null ?
                        com.alibaba.fastjson.JSON.toJSONString(patentInfo.getInventions()) : "");
                patentData.put("benefits", patentInfo.getBenefits() != null ? patentInfo.getBenefits() : "");
                patentData.put("problem", patentInfo.getProblem() != null ? patentInfo.getProblem() : "");
                patentData.put("llm_technical_title", patentInfo.getTechnicalTitle() != null ? patentInfo.getTechnicalTitle() : "");

                patentList.add(patentData);
            }

            // 构建输入参数
            Map<String, String> params = new HashMap<>();
            params.put("patent_list", com.alibaba.fastjson.JSON.toJSONString(patentList));
            params.put("subject", subject != null ? subject : "");
            params.put("industry", industry != null ? industry : "");
            params.put("user_company", userCompany != null ? String.join(",", userCompany) : "");

            // 构建prompt
            String prompt = openAiClient.buildPromptByPlatform(CORRELATION_ANALYSIS.getValue(), params);
            log.info("prompt打印: {}", prompt);

            int retryCount = 0;
            Map<String, Object> resultMap = new HashMap<>();
            // 调用AI服务
            while(retryCount < 3) {
                resultMap = openAiClient.callGptByPrompt(
                        GPTModelEnum.GPT_MODEL_4_O,
                        prompt,
                        TECH_REPORT_SCENARIO,
                        Map.class);
                if (resultMap != null && !resultMap.isEmpty()) {
                    break;
                }
                retryCount++;
                log.error("调用AI服务计算相关度失败，正在重试... 重试次数: {}", retryCount);
            }
            
            log.info("相关度计算返回原始结果: {}", resultMap);

            // 将结果转换为预期的格式
            Map<String, TechReportPreviewResDTO.PatentInfo.RelevanceInfo> relevanceResultMap = new HashMap<>();
            if (resultMap != null && !resultMap.isEmpty()) {
                resultMap.forEach((patentId, valueObj) -> {
                    TechReportPreviewResDTO.PatentInfo.RelevanceInfo relevanceInfo = new TechReportPreviewResDTO.PatentInfo.RelevanceInfo();
                    
                    try {
                        if (valueObj instanceof Map) {
                            @SuppressWarnings("unchecked")
                            Map<String, Object> valueMap = (Map<String, Object>) valueObj;
                            
                            // 处理score字段 - 确保安全转换为整数
                            Object scoreObj = valueMap.getOrDefault("score", 0);
                            if (scoreObj instanceof Number) {
                                relevanceInfo.setScore(((Number) scoreObj).intValue());
                            } else if (scoreObj instanceof String) {
                                try {
                                    relevanceInfo.setScore(Integer.parseInt(((String) scoreObj).trim()));
                                } catch (NumberFormatException e) {
                                    log.warn("专利 {} 的score值 {} 无法解析为整数，使用默认值0", patentId, scoreObj);
                                    relevanceInfo.setScore(0);
                                }
                            } else {
                                relevanceInfo.setScore(0);
                                log.warn("专利 {} 的score值类型不支持: {}, 使用默认值0", patentId,
                                        scoreObj != null ? scoreObj.getClass().getName() : "null");
                            }
                            
                            // 处理description字段
                            Object descObj = valueMap.getOrDefault("analysis", "无描述信息");
                            relevanceInfo.setDescription(String.valueOf(descObj));
                            
                            log.info("成功转换专利 {} 的相关度信息: score={}, description={}",
                                    patentId, relevanceInfo.getScore(), relevanceInfo.getDescription());
                        } else {
                            // 如果不是Map类型，设置分数为空
                            relevanceInfo.setScore(null);
                            relevanceInfo.setDescription("AI返回的格式不符合预期，设置为默认值");
                            log.warn("专利 {} 的相关度信息不是Map类型: {}", patentId, 
                                    valueObj != null ? valueObj.getClass().getName() : "null");
                        }
                    } catch (Exception e) {
                        // 捕获所有异常，确保处理过程稳定
                        relevanceInfo.setScore(null);
                        relevanceInfo.setDescription("处理过程发生异常，设置为默认值");
                        log.error("处理专利 {} 的相关度信息时发生异常: {}", patentId, e.getMessage(), e);
                    }
                    
                    relevanceResultMap.put(patentId, relevanceInfo);
                });
            } else {
                log.warn("AI返回的相关度结果为空或为null");
            }

            // 将相关度信息设置回专利信息对象
            for (TechReportPreviewResDTO.PatentInfo patentInfo : patentInfoList) {
                TechReportPreviewResDTO.PatentInfo.RelevanceInfo relevanceInfo = relevanceResultMap.get(patentInfo.getPatentId());
                if (relevanceInfo != null) {
                    patentInfo.setRelevanceInfo(relevanceInfo);
                    log.info("专利相关度计算结果: patentId: {}, score: {}, description: {}",
                            patentInfo.getPatentId(),
                            relevanceInfo.getScore(),
                            relevanceInfo.getDescription());
                } else {
                    // 设置默认相关度信息
                    TechReportPreviewResDTO.PatentInfo.RelevanceInfo defaultRelevance = new TechReportPreviewResDTO.PatentInfo.RelevanceInfo();
                    defaultRelevance.setScore(null); // 设置为空
                    defaultRelevance.setDescription("无法计算相关度，设置为默认值");
                    patentInfo.setRelevanceInfo(defaultRelevance);
                    log.warn("未找到专利相关度计算结果，使用默认值: patentId: {}", patentInfo.getPatentId());
                }
            }

            return patentInfoList;
        } catch (Exception e) {
            log.error("批量计算专利相关度失败", e);

            return patentInfoList;
        }
    }

    /**
     * 组装创建简报必要的字段
     *
     * @param agentJobId agent 任务 ID
     * @return 创建简报所需的字段数据
     */
    @Override
    public TechReportCreateFieldsResDTO createFields(String agentJobId) {
        log.info("开始组装创建简报必要的字段, agentJobId: {}", agentJobId);
        
        TechReportCreateFieldsResDTO result = new TechReportCreateFieldsResDTO();
        
        try {
            // 使用fetchCacheData方法从缓存中获取数据
            Map<String, Object> cacheData = fetchCacheData(agentJobId);
            
            // 从缓存数据中提取各个组件
            TechReportIntentResDTO intentResDTO = (TechReportIntentResDTO) cacheData.get("intentResDTO");
            TechReportConfigDTO configDTO = (TechReportConfigDTO) cacheData.get("configDTO");
            TechReportRecommendResDTO recommendResDTO = (TechReportRecommendResDTO) cacheData.get("recommendResDTO");
            TechReportExtractTechFeature techReportExtractTechFeature = (TechReportExtractTechFeature) cacheData.get("techFeature");
            
            // 直接从Redis获取QueryResult数据
            QueryResult queryResult = redissonUtils.get(redissonClient, getRedisKey(agentJobId) + REDIS_KEY_QUERY_RESULT);
            
            // 从Redis中获取新闻关键词数据
            Optional<List<String>> newsKeywordsOpt = getFromRedis(agentJobId, REDIS_KEY_NEWS_KEYWORDS, List.class).map(list -> {
                @SuppressWarnings("unchecked")
                List<String> stringList = (List<String>) list;
                return stringList;
            });
            
            // 日志记录缓存数据获取状态
            log.info("缓存数据获取状态 - 意图数据: {}, 配置数据: {}, 推荐数据: {}, 检索式数据: {}, 新闻关键词: {}", 
                    intentResDTO != null, configDTO != null, recommendResDTO != null, 
                    queryResult != null, newsKeywordsOpt.isPresent());
            
            // 1. 设置基本信息 - 从意图数据和配置数据中获取
            if (configDTO != null) {
                // 设置title：优先使用coreSubject，如果为空则使用第一个公司名称 todo
                String title = configDTO.getCoreSubject();
                if (StringUtils.isEmpty(title) && recommendResDTO.getHeadCompanies() != null ) {
                    title = recommendResDTO.getHeadCompanies().get(0).getCompanyName();
                }
                result.setTitle(title);
                result.setIndustry(configDTO.getIndustry());
                result.setSubject(configDTO.getCoreSubject());
                result.setExtractCompany(configDTO.getExtractCompanies());
                result.setRecommendCompany(recommendResDTO.getHeadCompanies().stream()
                        .map(CompanyInfo::getCompanyName)
                        .collect(Collectors.toList()));

                // 生成IPC查询语句
                if (techReportExtractTechFeature != null) {
                    String ipcQuery = generateQuery.generateApplicationSubclassFromFeature(techReportExtractTechFeature);
                    result.setIpcQuery(ipcQuery);
                }
            }
            
            if (intentResDTO != null) {
                result.setIntentType(intentResDTO.getIntent());
                result.setUserInput(intentResDTO.getUserInput());
                result.setUserCompany(intentResDTO.getUserCompany());
            }

            // 3. 设置子领域列表 - 如果检索式数据存在
            if (queryResult != null) {
                List<TechReportCreateFieldsResDTO.SubField> subFieldList = new ArrayList<>();
                
                // 处理技术子领域查询（subTechsQueries）
                if (queryResult.getSubTechsQueries() != null && !queryResult.getSubTechsQueries().isEmpty()) {
                    List<TechReportCreateFieldsResDTO.SubField> techSubFields = queryResult.getSubTechsQueries().entrySet().stream()
                            .map(entry -> {
                                TechReportCreateFieldsResDTO.SubField subField = new TechReportCreateFieldsResDTO.SubField();
                                subField.setName(entry.getKey());
                                subField.setPatentQuery(entry.getValue());
                                // 技术子领域固定设置为"tech"
                                subField.setClassificationType("tech");
                                return subField;
                            })
                            .collect(Collectors.toList());
                    subFieldList.addAll(techSubFields);
                    log.info("已处理技术子领域查询，数量: {}", techSubFields.size());
                }
                
                // 处理公司子领域查询，从TechReportRecommendResDTO中的headCompanies获取
                if (recommendResDTO != null && recommendResDTO.getHeadCompanies() != null && !recommendResDTO.getHeadCompanies().isEmpty()) {
                    List<TechReportCreateFieldsResDTO.SubField> companySubFields = recommendResDTO.getHeadCompanies().stream()
                            .filter(company -> company.getCompanyName() != null && !company.getCompanyName().isEmpty())
                            .map(company -> {
                                TechReportCreateFieldsResDTO.SubField subField = new TechReportCreateFieldsResDTO.SubField();
                                subField.setName(company.getCompanyName());
                                subField.setId(company.getCompanyId());
                                // 按照指定格式拼接patentQuery
                                subField.setPatentQuery(String.format("ALL_AN:(TREE@\"%s\")", company.getCompanyName()));
                                // 公司子领域固定设置为"company"
                                subField.setClassificationType("company");
                                return subField;
                            })
                            .collect(Collectors.toList());
                    subFieldList.addAll(companySubFields);
                    log.info("已处理公司子领域查询，数量: {}", companySubFields.size());
                } else {
                    log.warn("未获取到公司信息或公司列表为空，无法处理公司子领域");
                }
                
                if (!subFieldList.isEmpty()) {
                    result.setSubFieldList(subFieldList);
                    log.info("已设置子领域列表，总数量: {}", subFieldList.size());
                } else {
                    log.warn("未获取到有效的子领域数据，子领域列表为空");
                    result.setSubFieldList(Collections.emptyList());
                }
            } else {
                log.warn("未获取到检索式数据，子领域列表为空");
                result.setSubFieldList(Collections.emptyList());
            }

            // 4. 设置新闻检索式 - 如果新闻关键词数据存在
            newsKeywordsOpt.ifPresent(keywords -> {
                if (!keywords.isEmpty()) {
                    result.setNewsQuery(keywords);
                } else {
                    log.warn("获取到的新闻关键词列表为空");
                }
            });
            
            // 5. 设置专利检索式 - 如果检索式数据存在
            if (queryResult != null) {
                result.setPatentQuery(queryResult.getMainSubjectQuery());
            }

            log.info("组装创建简报必要的字段成功, agentJobId: {}", agentJobId);
            return result;
        } catch (Exception e) {
            log.error("组装创建简报必要的字段失败, agentJobId: {}", agentJobId, e);
            return result;
        }
    }
    
    /**
     * 创建技术监控报告
     *
     * @param agentJobId agent 任务 ID
     * @param sourceType 创建monitor的来源类型
     * @return 创建结果对象
     */
    @Override
    public String createTechMonitor(String agentJobId, String sourceType) {
        log.info("开始创建技术监控报告, agentJobId: {}, sourceType: {}", agentJobId, sourceType);
        
        try {
            // 1. 获取创建报告所需字段
            TechReportCreateFieldsResDTO fieldsResDTO = createFields(agentJobId);
            if (fieldsResDTO == null) {
                log.error("创建技术监控报告失败: 无法获取必要字段, agentJobId: {}", agentJobId);
                throw new BizException(TechReportErrorCodeEnum.REQUIRED_FIELDS_MISSING);
            }
            
            // 设置sourceType
            if (sourceType != null) {
                fieldsResDTO.setSourceType(sourceType);
                // 如果sourceType为foresight，则设置sessionId为agentJobId
                if (FORESIGHT.equals(sourceType)) {
                    fieldsResDTO.setSessionId(agentJobId);
                }
            }
            
            // 检查必要字段
            if (StringUtils.isEmpty(fieldsResDTO.getTitle())) {
                log.error("创建技术监控报告失败: 标题为空, agentJobId: {}", agentJobId);
                throw new BizException(TechReportErrorCodeEnum.REQUIRED_FIELDS_MISSING);
            }
            
            if (StringUtils.isEmpty(fieldsResDTO.getPatentQuery())) {
                log.error("创建技术监控报告失败: 专利检索式为空, agentJobId: {}", agentJobId);
                throw new BizException(TechReportErrorCodeEnum.REQUIRED_FIELDS_MISSING);
            }
            
            // 2. 调用Eureka API创建技术监控报告
            log.info("调用Eureka API创建技术监控报告, agentJobId: {}, 标题: {}", agentJobId, fieldsResDTO.getTitle());
            try {
                // 通过EurekaClient发送请求
                CommonResponse<String> response = eurekaClient.createTechMonitor(fieldsResDTO);
                
                // 处理响应
                if (response != null && response.isStatus()) {
                    String responseData = response.getData();
                    log.info("创建技术监控报告成功, agentJobId: {}, 结果: {}", agentJobId, responseData);
                    // 直接返回Object对象
                    return responseData;
                } else {
                    log.error("创建技术监控报告失败: 响应失败或数据为空, agentJobId: {}, 状态码: {}, 错误信息: {}", 
                            agentJobId, response != null ? response.getErrorCode() : "null",
                            response != null ? response.getErrorMsg() : "response is null");
                    throw new BizException(TechReportErrorCodeEnum.TECH_REPORT_CREATE_FAILED);
                }
            } catch (ForbiddenException e) {
                log.error("创建技术监控报告失败: 调用API异常, agentJobId: {}", agentJobId, e);
                throw e;
            }
        } catch (BizException e) {
            // BizException需要重新抛出，让上层处理
            log.error("创建技术监控报告业务异常, agentJobId: {}", agentJobId, e);
            throw e;
        } catch (ForbiddenException e) {
            log.error("创建技术监控报告过程中发生异常, agentJobId: {}", agentJobId, e);
            throw e;
        } catch (Exception e) {
            log.error("创建技术监控报告过程中发生未知异常, agentJobId: {}", agentJobId, e);
            throw new BizException(TechReportErrorCodeEnum.TECH_REPORT_CREATE_FAILED);
        }
    }

    /**
     * 转化prompt提取出来的公司列表为CompanyInfo类型
     * 
     * @param configDTO 技术报告配置DTO
     * @return 转化后的公司信息列表
     */
    private List<CompanyInfo> convertExtractedCompaniesToCompanyInfo(TechReportConfigDTO configDTO) {
        if (configDTO == null) {
            log.warn("技术报告配置DTO为空，返回空的公司信息列表");
            return Collections.emptyList();
        }
        
        // 优先使用已经处理过的extractCompanyList字段
        if (configDTO.getExtractCompanyList() != null && !configDTO.getExtractCompanyList().isEmpty()) {
            log.info("使用已处理的公司列表，数量: {}", configDTO.getExtractCompanyList().size());
            return configDTO.getExtractCompanyList();
        }
        
        // 如果companyList为空，则回退到原来的逻辑：从各个字段中提取公司名称
        List<String> extractedCompanyNames = new ArrayList<>();
        
        // 从TechReportConfigDTO中提取公司名称
        if (configDTO.getExtractCompanies() != null) {
            // extractCompanies字段是List<String>类型，直接添加到公司名称列表中
            extractedCompanyNames.addAll(configDTO.getExtractCompanies());
        }
        
        // 从约束条件中提取公司名称（如果存在相应字段）
        if (configDTO.getConstraint() != null && configDTO.getConstraint().getOnlyMonitorCompanies() != null) {
            extractedCompanyNames.addAll(configDTO.getConstraint().getOnlyMonitorCompanies());
        }
        
        // 去重并过滤空值
        List<String> uniqueCompanyNames = extractedCompanyNames.stream()
                .filter(Objects::nonNull)
                .filter(name -> !name.trim().isEmpty())
                .distinct()
                .collect(Collectors.toList());
        
        log.info("从配置中提取到的公司名称数量: {}", uniqueCompanyNames.size());
        
        // 调用fetchRecommendedCompanies方法转化为CompanyInfo列表
        return fetchRecommendedCompanies(uniqueCompanyNames);
    }
    

}
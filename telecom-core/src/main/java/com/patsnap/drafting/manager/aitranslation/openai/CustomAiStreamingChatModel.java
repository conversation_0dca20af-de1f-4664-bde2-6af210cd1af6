package com.patsnap.drafting.manager.aitranslation.openai;

/**
 * <AUTHOR>
 * @version Id: CustomAiStreamingChatModel, v 0.1 2025/2/24 12:11 liuxuyang Exp $
 */
//public class CustomAiStreamingChatModel implements StreamingChatLanguageModel, TokenCountEstimator {
//    private static final Logger log = LoggerFactory.getLogger(dev.langchain4j.model.openai.OpenAiStreamingChatModel.class);
//    private final OpenAiClient client;
//    private final String modelName;
//    private final Double temperature;
//    private final Double topP;
//    private final List<String> stop;
//    private final Integer maxTokens;
//    private final Double presencePenalty;
//    private final Double frequencyPenalty;
//    private final Map<String, Integer> logitBias;
//    private final String responseFormat;
//    private final Integer seed;
//    private final String user;
//    private final Tokenizer tokenizer;
//    private final boolean isOpenAiModel;
//    private final List<ChatModelListener> listeners;
//
//    public CustomAiStreamingChatModel(String baseUrl, String apiKey, String organizationId, String modelName, Double temperature, Double topP, List<String> stop, Integer maxTokens, Double presencePenalty, Double frequencyPenalty, Map<String, Integer> logitBias, String responseFormat, Integer seed, String user, Duration timeout, Proxy proxy, Boolean logRequests, Boolean logResponses, Tokenizer tokenizer, Map<String, String> customHeaders, List<ChatModelListener> listeners) {
//        timeout = (Duration) Utils.getOrDefault(timeout, Duration.ofSeconds(60L));
//        this.client = OpenAiClient.builder().baseUrl((String) Utils.getOrDefault(baseUrl, "https://api.openai.com/v1"))
//                .openAiApiKey(apiKey).organizationId(organizationId).callTimeout(timeout).connectTimeout(timeout)
//                .readTimeout(timeout).writeTimeout(timeout).proxy(proxy).logRequests(logRequests)
//                .logStreamingResponses(logResponses).userAgent("langchain4j-openai").customHeaders(customHeaders)
//                .build();
//        this.modelName = (String)Utils.getOrDefault(modelName, "gpt-3.5-turbo");
//        this.temperature = temperature;
//        this.topP = topP;
//        this.stop = stop;
//        this.maxTokens = maxTokens;
//        this.presencePenalty = presencePenalty;
//        this.frequencyPenalty = frequencyPenalty;
//        this.logitBias = logitBias;
//        this.responseFormat = responseFormat;
//        this.seed = seed;
//        this.user = user;
//        this.tokenizer = Utils.getOrDefault(tokenizer, OpenAiTokenizer::new);
//        this.isOpenAiModel = isOpenAiModel(this.modelName);
//        this.listeners = listeners == null ? emptyList() : new ArrayList<>(listeners);
//    }
//
//    static boolean isOpenAiModel(String modelName) {
//        if (modelName == null) {
//            return false;
//        }
//        for (OpenAiChatModelName openAiChatModelName : OpenAiChatModelName.values()) {
//            if (modelName.contains(openAiChatModelName.toString())) {
//                return true;
//            }
//        }
//        return false;
//    }
//
//    public String modelName() {
//        return this.modelName;
//    }
//
//    public void generate(List<ChatMessage> messages, StreamingResponseHandler<AiMessage> handler) {
//        this.generate(messages, (List)null, (ToolSpecification)null, handler);
//    }
//
//    public void generate(List<ChatMessage> messages, List<ToolSpecification> toolSpecifications, StreamingResponseHandler<AiMessage> handler) {
//        this.generate(messages, toolSpecifications, (ToolSpecification)null, handler);
//    }
//
//    public void generate(List<ChatMessage> messages, ToolSpecification toolSpecification, StreamingResponseHandler<AiMessage> handler) {
//        this.generate(messages, (List)null, toolSpecification, handler);
//    }
//
//    private void generate(List<ChatMessage> messages, List<ToolSpecification> toolSpecifications, ToolSpecification toolThatMustBeExecuted, StreamingResponseHandler<AiMessage> handler) {
//        ChatCompletionRequest.Builder requestBuilder = ChatCompletionRequest.builder().stream(true)
//                .model(this.modelName).messages(InternalOpenAiHelper.toOpenAiMessages(messages))
//                .temperature(this.temperature).topP(this.topP).stop(this.stop).maxTokens(this.maxTokens)
//                .presencePenalty(this.presencePenalty).frequencyPenalty(this.frequencyPenalty).logitBias(this.logitBias)
//                .responseFormat(this.responseFormat).seed(this.seed).user(this.user);
//        if (toolThatMustBeExecuted != null) {
//            requestBuilder.tools(InternalOpenAiHelper.toTools(Collections.singletonList(toolThatMustBeExecuted)));
//            requestBuilder.toolChoice(toolThatMustBeExecuted.name());
//        } else if (!Utils.isNullOrEmpty(toolSpecifications)) {
//            requestBuilder.tools(InternalOpenAiHelper.toTools(toolSpecifications));
//        }
//
//        ChatCompletionRequest request = requestBuilder.build();
//        ChatModelRequest modelListenerRequest = createModelListenerRequest(request, messages, toolSpecifications);
//        Map<Object, Object> attributes = new ConcurrentHashMap();
//        ChatModelRequestContext requestContext = new ChatModelRequestContext(modelListenerRequest, attributes);
//        this.listeners.forEach((listener) -> {
//            try {
//                listener.onRequest(requestContext);
//            } catch (Exception e) {
//                log.warn("Exception while calling model listener", e);
//            }
//
//        });
//        int inputTokenCount = this.countInputTokens(messages, toolSpecifications, toolThatMustBeExecuted);
//        OpenAiStreamingResponseBuilder responseBuilder = new OpenAiStreamingResponseBuilder(inputTokenCount);
//        AtomicReference<String> responseId = new AtomicReference();
//        AtomicReference<String> responseModel = new AtomicReference();
//        this.client.chatCompletion(request).onPartialResponse((partialResponse) -> {
//            responseBuilder.append(partialResponse);
//            handle(partialResponse, handler);
//            if (!Utils.isNullOrBlank(partialResponse.id())) {
//                responseId.set(partialResponse.id());
//            }
//
//            if (!Utils.isNullOrBlank(partialResponse.model())) {
//                responseModel.set(partialResponse.model());
//            }
//
//        }).onComplete(() -> {
//            Response<AiMessage> response = this.createResponse(responseBuilder, toolThatMustBeExecuted);
//            ChatModelResponse modelListenerResponse = createModelListenerResponse((String)responseId.get(), (String)responseModel.get(), response);
//            ChatModelResponseContext responseContext = new ChatModelResponseContext(modelListenerResponse, modelListenerRequest, attributes);
//            this.listeners.forEach((listener) -> {
//                try {
//                    listener.onResponse(responseContext);
//                } catch (Exception e) {
//                    log.warn("Exception while calling model listener", e);
//                }
//
//            });
//            handler.onComplete(response);
//        }).onError((error) -> {
//            Response<AiMessage> response = this.createResponse(responseBuilder, toolThatMustBeExecuted);
//            ChatModelResponse modelListenerPartialResponse = createModelListenerResponse((String)responseId.get(), (String)responseModel.get(), response);
//            ChatModelErrorContext errorContext = new ChatModelErrorContext(error, modelListenerRequest, modelListenerPartialResponse, attributes);
//            this.listeners.forEach((listener) -> {
//                try {
//                    listener.onError(errorContext);
//                } catch (Exception e) {
//                    log.warn("Exception while calling model listener", e);
//                }
//
//            });
//            handler.onError(error);
//        }).execute();
//    }
//
//    ChatModelResponse createModelListenerResponse(String responseId,
//            String responseModel,
//            Response<AiMessage> response) {
//        if (response == null) {
//            return null;
//        }
//
//        return ChatModelResponse.builder()
//                .id(responseId)
//                .model(responseModel)
//                .tokenUsage(response.tokenUsage())
//                .finishReason(response.finishReason())
//                .aiMessage(response.content())
//                .build();
//    }
//
//
//    ChatModelRequest createModelListenerRequest(ChatCompletionRequest request,
//            List<ChatMessage> messages,
//            List<ToolSpecification> toolSpecifications) {
//        return ChatModelRequest.builder()
//                .model(request.model())
//                .temperature(request.temperature())
//                .topP(request.topP())
//                .maxTokens(request.maxTokens())
//                .messages(messages)
//                .toolSpecifications(toolSpecifications)
//                .build();
//    }
//
//    private Response<AiMessage> createResponse(OpenAiStreamingResponseBuilder responseBuilder, ToolSpecification toolThatMustBeExecuted) {
//        Response<AiMessage> response = responseBuilder.build(this.tokenizer, toolThatMustBeExecuted != null);
//        return this.isOpenAiModel ? response : removeTokenUsage(response);
//    }
//
//    Response<AiMessage> removeTokenUsage(Response<AiMessage> response) {
//        return Response.from(response.content(), null, response.finishReason());
//    }
//
//    private int countInputTokens(List<ChatMessage> messages, List<ToolSpecification> toolSpecifications, ToolSpecification toolThatMustBeExecuted) {
//        int inputTokenCount = this.tokenizer.estimateTokenCountInMessages(messages);
//        if (toolThatMustBeExecuted != null) {
//            inputTokenCount += this.tokenizer.estimateTokenCountInForcefulToolSpecification(toolThatMustBeExecuted);
//        } else if (!Utils.isNullOrEmpty(toolSpecifications)) {
//            inputTokenCount += this.tokenizer.estimateTokenCountInToolSpecifications(toolSpecifications);
//        }
//
//        return inputTokenCount;
//    }
//
//    private static void handle(ChatCompletionResponse partialResponse, StreamingResponseHandler<AiMessage> handler) {
//        List<ChatCompletionChoice> choices = partialResponse.choices();
//        if (choices != null && !choices.isEmpty()) {
//            Delta delta = ((ChatCompletionChoice)choices.get(0)).delta();
//            String content = delta.content();
//            if (content != null) {
//                handler.onNext(content);
//            }
//
//        }
//    }
//
//    public int estimateTokenCount(List<ChatMessage> messages) {
//        return this.tokenizer.estimateTokenCountInMessages(messages);
//    }
//
//    public static CustomAiStreamingChatModel withApiKey(String apiKey) {
//        return builder().apiKey(apiKey).build();
//    }
//
//    public static CustomAiStreamingChatModel.CustomAiStreamingChatModelBuilder builder() {
//        return new CustomAiStreamingChatModel.CustomAiStreamingChatModelBuilder();
//    }
//
//    public static class CustomAiStreamingChatModelBuilder {
//        private String baseUrl;
//        private String apiKey;
//        private String organizationId;
//        private String modelName;
//        private Double temperature;
//        private Double topP;
//        private List<String> stop;
//        private Integer maxTokens;
//        private Double presencePenalty;
//        private Double frequencyPenalty;
//        private Map<String, Integer> logitBias;
//        private String responseFormat;
//        private Integer seed;
//        private String user;
//        private Duration timeout;
//        private Proxy proxy;
//        private Boolean logRequests;
//        private Boolean logResponses;
//        private Tokenizer tokenizer;
//        private Map<String, String> customHeaders;
//        private List<ChatModelListener> listeners;
//
//        public CustomAiStreamingChatModelBuilder() {
//        }
//
//        public CustomAiStreamingChatModel.CustomAiStreamingChatModelBuilder modelName(String modelName) {
//            this.modelName = modelName;
//            return this;
//        }
//
//        public CustomAiStreamingChatModel.CustomAiStreamingChatModelBuilder modelName(
//                OpenAiChatModelName modelName) {
//            this.modelName = modelName.toString();
//            return this;
//        }
//
//        public CustomAiStreamingChatModel.CustomAiStreamingChatModelBuilder baseUrl(String baseUrl) {
//            this.baseUrl = baseUrl;
//            return this;
//        }
//
//        public CustomAiStreamingChatModel.CustomAiStreamingChatModelBuilder apiKey(String apiKey) {
//            this.apiKey = apiKey;
//            return this;
//        }
//
//        public CustomAiStreamingChatModel.CustomAiStreamingChatModelBuilder organizationId(String organizationId) {
//            this.organizationId = organizationId;
//            return this;
//        }
//
//        public CustomAiStreamingChatModel.CustomAiStreamingChatModelBuilder temperature(Double temperature) {
//            this.temperature = temperature;
//            return this;
//        }
//
//        public CustomAiStreamingChatModel.CustomAiStreamingChatModelBuilder topP(Double topP) {
//            this.topP = topP;
//            return this;
//        }
//
//        public CustomAiStreamingChatModel.CustomAiStreamingChatModelBuilder stop(List<String> stop) {
//            this.stop = stop;
//            return this;
//        }
//
//        public CustomAiStreamingChatModel.CustomAiStreamingChatModelBuilder maxTokens(Integer maxTokens) {
//            this.maxTokens = maxTokens;
//            return this;
//        }
//
//        public CustomAiStreamingChatModel.CustomAiStreamingChatModelBuilder presencePenalty(Double presencePenalty) {
//            this.presencePenalty = presencePenalty;
//            return this;
//        }
//
//        public CustomAiStreamingChatModel.CustomAiStreamingChatModelBuilder frequencyPenalty(Double frequencyPenalty) {
//            this.frequencyPenalty = frequencyPenalty;
//            return this;
//        }
//
//        public CustomAiStreamingChatModel.CustomAiStreamingChatModelBuilder logitBias(Map<String, Integer> logitBias) {
//            this.logitBias = logitBias;
//            return this;
//        }
//
//        public CustomAiStreamingChatModel.CustomAiStreamingChatModelBuilder responseFormat(String responseFormat) {
//            this.responseFormat = responseFormat;
//            return this;
//        }
//
//        public CustomAiStreamingChatModel.CustomAiStreamingChatModelBuilder seed(Integer seed) {
//            this.seed = seed;
//            return this;
//        }
//
//        public CustomAiStreamingChatModel.CustomAiStreamingChatModelBuilder user(String user) {
//            this.user = user;
//            return this;
//        }
//
//        public CustomAiStreamingChatModel.CustomAiStreamingChatModelBuilder timeout(Duration timeout) {
//            this.timeout = timeout;
//            return this;
//        }
//
//        public CustomAiStreamingChatModel.CustomAiStreamingChatModelBuilder proxy(Proxy proxy) {
//            this.proxy = proxy;
//            return this;
//        }
//
//        public CustomAiStreamingChatModel.CustomAiStreamingChatModelBuilder logRequests(Boolean logRequests) {
//            this.logRequests = logRequests;
//            return this;
//        }
//
//        public CustomAiStreamingChatModel.CustomAiStreamingChatModelBuilder logResponses(Boolean logResponses) {
//            this.logResponses = logResponses;
//            return this;
//        }
//
//        public CustomAiStreamingChatModel.CustomAiStreamingChatModelBuilder tokenizer(Tokenizer tokenizer) {
//            this.tokenizer = tokenizer;
//            return this;
//        }
//
//        public CustomAiStreamingChatModel.CustomAiStreamingChatModelBuilder customHeaders(Map<String, String> customHeaders) {
//            this.customHeaders = customHeaders;
//            return this;
//        }
//
//        public CustomAiStreamingChatModel.CustomAiStreamingChatModelBuilder listeners(List<ChatModelListener> listeners) {
//            this.listeners = listeners;
//            return this;
//        }
//
//        public CustomAiStreamingChatModel build() {
//            return new CustomAiStreamingChatModel(this.baseUrl, this.apiKey, this.organizationId, this.modelName, this.temperature, this.topP, this.stop, this.maxTokens, this.presencePenalty, this.frequencyPenalty, this.logitBias, this.responseFormat, this.seed, this.user, this.timeout, this.proxy, this.logRequests, this.logResponses, this.tokenizer, this.customHeaders, this.listeners);
//        }
//
//        public String toString() {
//            return "OpenAiStreamingChatModel.OpenAiStreamingChatModelBuilder(baseUrl=" + this.baseUrl + ", apiKey=" + this.apiKey + ", organizationId=" + this.organizationId + ", modelName=" + this.modelName + ", temperature=" + this.temperature + ", topP=" + this.topP + ", stop=" + this.stop + ", maxTokens=" + this.maxTokens + ", presencePenalty=" + this.presencePenalty + ", frequencyPenalty=" + this.frequencyPenalty + ", logitBias=" + this.logitBias + ", responseFormat=" + this.responseFormat + ", seed=" + this.seed + ", user=" + this.user + ", timeout=" + this.timeout + ", proxy=" + this.proxy + ", logRequests=" + this.logRequests + ", logResponses=" + this.logResponses + ", tokenizer=" + this.tokenizer + ", customHeaders=" + this.customHeaders + ", listeners=" + this.listeners + ")";
//        }
//    }
//}
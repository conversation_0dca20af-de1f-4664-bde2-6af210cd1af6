package com.patsnap.drafting.manager.feedback;

import com.patsnap.common.request.TenantIdHolder;
import com.patsnap.common.request.UserIdHolder;
import com.patsnap.drafting.repository.aitask.dao.mapper.CopilotHistoryMapper;
import com.patsnap.drafting.repository.aitask.entity.CopilotHistoryPO;
import com.patsnap.drafting.request.feedback.DraftingFeedbackRequestDto;
import java.time.LocalDateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;

/**
 * 用户反馈
 * <AUTHOR>
 */
@Slf4j
@Service
public class FeedbackManager {

    @Autowired
    private CopilotHistoryMapper copilotHistoryMapper;

    public void submit(DraftingFeedbackRequestDto feedbackRequestDto) {
        try {
            CopilotHistoryPO copilotHistoryPO = new CopilotHistoryPO();
            copilotHistoryPO.setProduct(feedbackRequestDto.getProduct());
            copilotHistoryPO.setModule(feedbackRequestDto.getModule());
            copilotHistoryPO.setQuestion(feedbackRequestDto.getQuestion());
            copilotHistoryPO.setAnswer(feedbackRequestDto.getAnswer());
            copilotHistoryPO.setIntent(feedbackRequestDto.getIntent());
            copilotHistoryPO.setCompanyId(TenantIdHolder.get());
            copilotHistoryPO.setPageParam(feedbackRequestDto.getPageParams());
            copilotHistoryPO.setQuery(feedbackRequestDto.getQuery());
            copilotHistoryPO.setType(feedbackRequestDto.getType());
            copilotHistoryPO.setComment(feedbackRequestDto.getComment());
            LocalDateTime now = LocalDateTime.now();
            copilotHistoryPO.setCreatedAt(now);
            copilotHistoryPO.setUpdatedAt(now);
            copilotHistoryPO.setCreatedBy(UserIdHolder.get());
            copilotHistoryMapper.insert(copilotHistoryPO);
        } catch (Exception e) {
            log.error("FeedbackManager.submit error", e);
        }
    }

}

package com.patsnap.drafting.manager.aispecification;

import com.patsnap.drafting.client.DrawNarratorClient;
import com.patsnap.drafting.client.model.DrawingNarratorRequest;
import com.patsnap.drafting.client.model.DrawingNarratorResponse;
import com.patsnap.drafting.enums.aispecification.JurisdictionEnum;
import com.patsnap.drafting.enums.task.AiTaskContentTypeEnum;
import com.patsnap.drafting.exception.BizException;
import com.patsnap.drafting.exception.errorcode.ClientErrorCodeEnum;
import com.patsnap.drafting.manager.FileManager;
import com.patsnap.drafting.manager.aitask.AiTaskManager;
import com.patsnap.drafting.model.aispecification.FigureContentBO;
import com.patsnap.drafting.model.aispecification.SpecificationInitializationBO;
import com.patsnap.drafting.model.aispecification.TermContentBO;
import com.patsnap.drafting.model.aispecification.TermItem;
import com.patsnap.drafting.util.ImageBase64Util;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

import static com.patsnap.drafting.enums.task.AiTaskContentTypeEnum.FIGURE_CONFIG;
import static com.patsnap.drafting.enums.task.AiTaskContentTypeEnum.TERMS;

/**
 * 算法调用服务
 * 专门负责调用外部算法接口，支持重试机制
 * 
 * <AUTHOR> Assistant
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AlgorithmCallService {

    private final AiTaskManager aiTaskManager;
    private final FileManager fileManager;
    private final DrawNarratorClient drawNarratorClient;

    /**
     * 调用算法服务（支持重试）
     *
     * @param taskId      任务ID
     * @param contentType 内容类型
     */
    @Retryable(value = {BizException.class}, maxAttempts = 3, backoff = @Backoff(delay = 5000, multiplier = 2))
    public void callAlgorithmService(String taskId, AiTaskContentTypeEnum contentType) {
        log.info("调用算法服务，任务ID: {}, 内容类型: {}", taskId, contentType.getType());
        
        // 根据内容类型调用不同的算法处理逻辑
        if (contentType == AiTaskContentTypeEnum.FIGURES_PRE_INFO) {
            callFigureAlgorithm(taskId);
            return;
        }
        
        log.warn("不支持的内容类型: {}", contentType.getType());
        throw new BizException(ClientErrorCodeEnum.CLIENT_NOT_AVAILABLE);
    }
    
    /**
     * 调用附图算法处理
     *
     * @param taskId 任务ID
     */
    private void callFigureAlgorithm(String taskId) {
        log.info("调用附图算法处理，任务ID: {}", taskId);
        
        try {
            // 1. 获取附图数据
            FigureContentBO figureContent = aiTaskManager.getTaskContent(taskId, FIGURE_CONFIG);
            if (figureContent == null || figureContent.getFigures() == null || figureContent.getFigures().isEmpty()) {
                log.warn("附图数据为空，跳过算法处理，任务ID: {}", taskId);
                return;
            }
            
            // 2. 构建算法请求参数
            DrawingNarratorRequest request = buildFigureAlgorithmRequest(taskId, figureContent);
            if (request == null) {
                log.warn("构建附图算法请求参数失败，任务ID: {}", taskId);
                throw new BizException(ClientErrorCodeEnum.CLIENT_NOT_AVAILABLE);
            }
            
            // 3. 调用算法接口
            DrawingNarratorResponse response = drawNarratorClient.generateDrawingNarrator(request);
            
            // 4. 处理算法响应
            if (response != null && Boolean.TRUE.equals(response.getSuccess()) && response.getResult() != null) {
                log.info("附图算法处理成功，任务ID: {}, 返回描述: {}", taskId, response.getResult());
                // 保存数据到 content 表
                aiTaskManager.updateTaskContent(taskId, AiTaskContentTypeEnum.FIGURES_PRE_INFO, response.getResult());
                
            } else {
                log.warn("附图算法处理失败，任务ID: {}, 响应: {}", taskId, response);
                throw new BizException(ClientErrorCodeEnum.CLIENT_NOT_AVAILABLE);
            }
            
        } catch (BizException e) {
            // 重新抛出业务异常，触发重试
            throw e;
        } catch (Exception e) {
            log.error("附图算法处理异常，任务ID: {}", taskId, e);
            throw new BizException(ClientErrorCodeEnum.CLIENT_NOT_AVAILABLE);
        }
    }
    
    /**
     * 构建附图算法请求参数
     * 
     * @param taskId 任务ID
     * @param figureContent 附图内容
     * @return 算法请求参数
     */
    private DrawingNarratorRequest buildFigureAlgorithmRequest(String taskId, FigureContentBO figureContent) {
        log.info("构建附图算法请求参数，任务ID: {}", taskId);
        
        // 提取附图的base64数据
        List<String> images = figureContent.getFigures().stream()
            .map(figure -> {
                // 优先使用annotationImageBase64，如果没有则尝试从image.url获取
                String base64Data = figure.getAnnotationImageBase64();
                if (base64Data == null || base64Data.isEmpty()) {
                    // 如果没有标注图像的base64，尝试从原图URL获取
                    if (figure.getImage() != null && figure.getImage().getS3Key() != null) {
                        log.info("附图标注base64为空，尝试从URL获取，uniqueId: {}, URL: {}", 
                                figure.getUniqueId(), figure.getImage().getUrl());
                        base64Data = ImageBase64Util.getBase64FromUrl(fileManager.signFile(figure.getImage().getS3Key()));
                        if (base64Data != null) {
                            log.info("从URL成功获取附图base64，uniqueId: {}", figure.getUniqueId());
                        } else {
                            log.warn("从URL获取附图base64失败，uniqueId: {}", figure.getUniqueId());
                        }
                    } else {
                        log.warn("附图base64和URL都为空，uniqueId: {}", figure.getUniqueId());
                    }
                }
                // 清理base64数据中的数据URL前缀（如data:image/png;base64,等）
                if (base64Data != null && base64Data.startsWith("data:image/")) {
                    int commaIndex = base64Data.indexOf(',');
                    if (commaIndex != -1) {
                        base64Data = base64Data.substring(commaIndex + 1);
                        log.debug("已清理base64数据URL前缀，uniqueId: {}", figure.getUniqueId());
                    }
                }
                return base64Data;
            })
            .filter(base64 -> base64 != null && !base64.isEmpty())
            .collect(Collectors.toList());
        
        if (images.isEmpty()) {
            log.warn("没有有效的附图base64数据，任务ID: {}", taskId);
            return null;
        }
        
        // 获取权利要求文本
        SpecificationInitializationBO initializationBO = aiTaskManager.getTaskContent(taskId, AiTaskContentTypeEnum.INITIALIZATION);
        String claimText = getClaimTextFromTask(taskId, initializationBO);
        String lang = getLang(initializationBO);
        
        // 获取标签列表
        List<TermItem> itemList = getItemListFromTask(taskId);
        
        return DrawingNarratorRequest.builder()
            .images(images)
            .claimText(claimText)
            .itemList(itemList)
            .lang(lang)
            .build();
    }
    
    private String getLang(SpecificationInitializationBO initializationBO) {
        String jurisdiction = initializationBO.getJurisdiction();
        return JurisdictionEnum.fromName(jurisdiction).getValue();
    }
    
    /**
     * 从任务中获取权利要求文本
     * 
     * @param taskId 任务ID
     * @return 权利要求文本
     */
    private String getClaimTextFromTask(String taskId, SpecificationInitializationBO initializationBO) {
        if (initializationBO != null && StringUtils.isNotBlank(initializationBO.getClaim())) {
            String originalClaim = initializationBO.getClaim();
            log.info("获取到原始权利要求文本，任务ID: {}, 文本长度: {}", taskId, originalClaim.length());
            return originalClaim;
        }
        return ""; // 如果没有获取到权利要求文本，返回空字符串
    }
    
    /**
     * 从任务中获取标签列表
     * 
     * @param taskId 任务ID
     * @return 标签列表
     */
    private List<TermItem> getItemListFromTask(String taskId) {
        // 可能需要从TERMS类型的任务内容中获取
        try {
            TermContentBO termContent = aiTaskManager.getTaskContent(taskId, TERMS);
            if (termContent != null && termContent.getTerms() != null) {
                return termContent.getTerms().stream()
                        .filter(term -> StringUtils.isNotBlank(term.getNumber()) && StringUtils.isNotBlank(term.getName()))
                        .map(term -> TermItem.builder()
                                .number(term.getNumber())
                                .item(term.getName()).build())
                        .collect(Collectors.toList());
            }
            log.warn("术语数据为空，使用空列表，任务ID: {}", taskId);
            return List.of();
        } catch (Exception e) {
            log.warn("获取标签列表失败，使用空列表，任务ID: {}", taskId, e);
            return List.of();
        }
    }
} 
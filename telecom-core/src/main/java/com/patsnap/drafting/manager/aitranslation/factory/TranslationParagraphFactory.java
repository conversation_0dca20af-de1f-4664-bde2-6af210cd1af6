package com.patsnap.drafting.manager.aitranslation.factory;

import com.patsnap.drafting.annotation.TaskContentCache;
import com.patsnap.drafting.config.AiTranslationConfig;
import com.patsnap.drafting.enums.task.AiTaskContentTypeEnum;
import com.patsnap.drafting.manager.aitranslation.operate.TranslationParagraphService;
import com.patsnap.drafting.manager.content.logic.TranslationFullTextContentCacheLogic;
import com.patsnap.drafting.model.aitranslation.AiTransContextBo;
import com.patsnap.drafting.model.aitranslation.TranslationBO;
import com.patsnap.drafting.util.RetryUtil;

import java.util.ArrayList;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class TranslationParagraphFactory {

    @Autowired
    private AiTranslationConfig aiTranslationConfig;

    @Autowired
    private List<TranslationParagraphService> translationParagraphServices;

    @TaskContentCache(contentType = AiTaskContentTypeEnum.TRANSLATION_RESULT,
            logicClass = TranslationFullTextContentCacheLogic.class)
    public List<TranslationBO> generate(AiTransContextBo contextBo) {
        List<TranslationBO> result = new ArrayList<>();
        getGenerateResult(contextBo, result);
        return result;
    }

    private void getGenerateResult(AiTransContextBo contextBo, List<TranslationBO> result) {
        TranslationParagraphService service = null;
        for (TranslationParagraphService translationParagraphService : translationParagraphServices) {
            if (!aiTranslationConfig.getParagraphModel().equals(translationParagraphService.model())) {
                continue;
            }
            service = translationParagraphService;
        }
        result.addAll(service.generate(contextBo));
    }

}

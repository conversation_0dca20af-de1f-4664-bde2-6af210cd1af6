package com.patsnap.drafting.manager.aispecification.content;

import com.patsnap.core.common.copilot.gpt.enums.GPTModelEnum;
import com.patsnap.core.common.copilot.streaming.handler.StringStreamingResponseHandler;
import com.patsnap.core.common.copilot.streaming.handling.StreamingChatLanguageModel;
import com.patsnap.core.common.copilot.streaming.model.GptResponseDTO;
import com.patsnap.drafting.annotation.CreditCheckLimit;
import com.patsnap.drafting.client.OpenAiClient;
import com.patsnap.drafting.config.UrlConfig;
import com.patsnap.drafting.enums.aispecification.JurisdictionEnum;
import com.patsnap.drafting.enums.prompt.ScenarioEnum;
import com.patsnap.drafting.enums.task.AiTaskContentTypeEnum;
import com.patsnap.drafting.exception.BizException;
import com.patsnap.drafting.exception.errorcode.ContentErrorCodeEnum;
import com.patsnap.drafting.manager.AbstractGenerateStreamingContent;
import com.patsnap.drafting.manager.aispecification.EmbodimentRuleManager;
import com.patsnap.drafting.manager.aitask.AiTaskManager;
import com.patsnap.drafting.manager.credit.CreditManager;
import com.patsnap.drafting.model.aispecification.ClaimFeature;
import com.patsnap.drafting.model.aispecification.ClaimFeatureContentBO;
import com.patsnap.drafting.model.aispecification.FeatureTreeBO;
import com.patsnap.drafting.model.aispecification.SpecificationInitializationBO;
import com.patsnap.drafting.model.base.StreamingModelBO;
import com.patsnap.drafting.request.GenerateContentRequestDTO;
import com.patsnap.drafting.request.aispecification.EmbodimentAddReqDTO;
import com.patsnap.spring.boot.autoconfigure.web.entity.CommonResponse;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.patsnap.drafting.enums.aispecification.JurisdictionEnum.EPO;
import static com.patsnap.drafting.enums.prompt.SpecificationPromptKeyEnum.ADD_EMBODIMENT;
import static com.patsnap.drafting.enums.prompt.SpecificationPromptKeyEnum.ADD_EMBODIMENT_EPO;

/**
 * AI说明书撰写 - 扩展新实施例
 */
@Component
public class SpecificationAddEmbodimentContent extends AbstractGenerateStreamingContent<String> {

    @Autowired
    private OpenAiClient openAiClient;

    @Autowired
    private EmbodimentRuleManager embeddedRuleManager;

    @Autowired
    private CreditManager creditManager;

    protected SpecificationAddEmbodimentContent(AiTaskManager aiTaskManager, UrlConfig urlConfig) {
        super(aiTaskManager, urlConfig);
    }

    @Override
    protected StreamingModelBO getStreamingModelBO(GenerateContentRequestDTO generateContentRequestDTO) {
        EmbodimentAddReqDTO request = (EmbodimentAddReqDTO) generateContentRequestDTO;
        String taskId = generateContentRequestDTO.getTaskId();
        String category = aiTaskManager.getTaskContent(taskId, AiTaskContentTypeEnum.CATEGORY);
        SpecificationInitializationBO initializationBO = aiTaskManager.getTaskContent(taskId,
                AiTaskContentTypeEnum.INITIALIZATION);
        //获取受理局
        String jurisdiction = initializationBO.getJurisdiction();
        String claim = initializationBO.getClaim();
        String techField = aiTaskManager.getTaskContent(taskId, AiTaskContentTypeEnum.SPECIFICATION_TECH_FIELD);
        ClaimFeatureContentBO claimFeatureContentBO = aiTaskManager.getTaskContent(taskId,
                AiTaskContentTypeEnum.FEATURE_TREE);
        List<FeatureTreeBO> featureTreeList = claimFeatureContentBO.getFeatureTree();
        if (CollectionUtils.isEmpty(featureTreeList)) {
            throw new BizException(ContentErrorCodeEnum.CONTENT_TYPE_INVALID);
        }
        String embodiment = aiTaskManager.getTaskContent(taskId, AiTaskContentTypeEnum.SPECIFICATION_EMBODIMENT);
        String feature = featureTreeList.stream().map(FeatureTreeBO::getClaimFeatures).flatMap(Collection::stream)
                .map(ClaimFeature::getFeature).collect(Collectors.joining("\n"));
        String lang = JurisdictionEnum.fromName(jurisdiction).getValue();
        String rules = embeddedRuleManager.fillInRules(request.getRules(), lang);
        String promptKey = getPromptKey(jurisdiction);
        String prompt = openAiClient.buildPromptByPlatform(promptKey,
                Map.of("claim", claim, "category", category, "techField", techField, "feature", feature, "rules", rules,
                        "embodiment", embodiment), lang);
        StreamingModelBO streamingModelBO = new StreamingModelBO();
        streamingModelBO.setPrompt(prompt);
        StreamingChatLanguageModel model = streamingModelBuilder.build(GPTModelEnum.GPT_MODEL_4_O,
                ScenarioEnum.AI_SPECIFICATION.getValue());
        streamingModelBO.setModel(model);
        return streamingModelBO;
    }
    
    /**
     * 获取提示语
     * @param jurisdiction 受理局
     * @return 提示语
     */
    private static String getPromptKey(String jurisdiction) {
        String promptKey = ADD_EMBODIMENT.getValue();
        if (EPO ==JurisdictionEnum.fromName(jurisdiction)) {
            promptKey = ADD_EMBODIMENT_EPO.getValue();
        }
        return promptKey;
    }
    
    @Override
    protected Flux<CommonResponse<GptResponseDTO<String>>> getContent(StreamingModelBO streamingModelBO) {
        String prompt = streamingModelBO.getPrompt();
        return Flux.create(sink -> {
            StringStreamingResponseHandler handler = new StringStreamingResponseHandler(sink);
            streamingModelBO.getModel().generate(prompt, handler);
        });
    }

    @Override
    public String getContentType() {
        return AiTaskContentTypeEnum.SPECIFICATION_EMBODIMENT_ADD.getType();
    }


    @Override
    @CreditCheckLimit(contentTypeExpression = "#request.contentType")
    public Flux<CommonResponse<GptResponseDTO<String>>> doGenerate(GenerateContentRequestDTO request) {
        return getContent(getStreamingModelBO(request));
    }
}

package com.patsnap.drafting.manager.ainoveltysearch;

import cn.hutool.core.collection.CollUtil;
import com.patsnap.drafting.enums.ainoveltysearch.NoveltySearchPatentTypeEnum;
import com.patsnap.drafting.request.aitask.AiTaskReqDTO;
import com.patsnap.drafting.response.ainoveltysearch.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;

@Slf4j
@RequiredArgsConstructor
@Service
public class NoveltySearchReportManager {

    private final NoveltySearchManager noveltySearchManager;


    public List<NoveltySearchComparativeLiteratureResDTO> getReportComparativeLiterature(String taskId) {
        AiTaskReqDTO request = new AiTaskReqDTO();
        request.setTaskId(taskId);
        List<NoveltySearchComparativeLiteratureResDTO> list = new ArrayList<>();
        NoveltySearchComputeDTO reqA = noveltySearchManager.getReportCompute(request);
        NoveltySearchComputeDTO reqX = noveltySearchManager.getReportReviewOfNovelty(request);
        NoveltySearchComputeDTO reqY = noveltySearchManager.getReportReviewOfCreative(request);
        if (CollUtil.isNotEmpty(reqX.getComparativeLiteratures())) {
            list.addAll(reqX.getComparativeLiteratures());
        }
        if (CollUtil.isNotEmpty(reqY.getComparativeLiteratures())) {
            list.addAll(reqY.getComparativeLiteratures());
        }
        if (CollUtil.isNotEmpty(reqA.getComparativeLiteratures())) {
            list.addAll(reqA.getComparativeLiteratures());
        }
        return list;
    }

    public String getReportReviewReport(String taskId) {
        AiTaskReqDTO request = new AiTaskReqDTO();
        request.setTaskId(taskId);
        String result = null;
        String type = noveltySearchManager.getReportType(taskId);
        if (type.equals(NoveltySearchPatentTypeEnum.X.getValue())) {
            result = noveltySearchManager.getReportReviewOfNovelty(request).getReport();
        } else if (type.equals(NoveltySearchPatentTypeEnum.Y.getValue())) {
            result = noveltySearchManager.getReportReviewOfCreative(request).getReport();
        } else if (type.equals(NoveltySearchPatentTypeEnum.A.getValue())) {
            String lang = noveltySearchManager.getInputLang(request);
            if ("CN".equals(lang)) {
                result = "检索到的对比文件中未发现影响该技术方案新颖性和创造性的文件，该技术方案具有新颖性和创造性。";
            }else {
                result = "";
            }
        }

        return result;
    }
}


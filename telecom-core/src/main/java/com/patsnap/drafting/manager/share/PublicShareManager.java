package com.patsnap.drafting.manager.share;

import cn.hutool.core.lang.Opt;
import com.patsnap.common.request.UserIdHolder;
import com.patsnap.drafting.manager.aitask.AiTaskManager;
import com.patsnap.drafting.repository.aitask.entity.AnalyticsAiTaskHistoryPO;
import com.patsnap.drafting.repository.share.entity.AiTaskShareLinkPO;
import com.patsnap.drafting.repository.share.service.AiTaskShareLinkService;
import com.patsnap.drafting.request.share.FreeShareCreateRequest;
import com.patsnap.drafting.request.share.FreeShareInitRequest;
import com.patsnap.drafting.response.share.FreeShareCreateResponse;
import com.patsnap.drafting.response.share.FreeShareInitResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.joda.time.DateTime;
import org.springframework.stereotype.Service;

import java.util.Optional;
import java.util.UUID;

import static com.patsnap.drafting.constants.Constant.SHARE_FOREVER;
import static com.patsnap.drafting.enums.share.UserRoleEnum.VIEWER;

/**
 * 公开分享的管理类
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class PublicShareManager {
    
    private final AiTaskShareLinkService aiTaskShareLinkService;
    private final AiTaskManager aiTaskManager;
    
    public FreeShareInitResponse initPublicShare(FreeShareInitRequest request) {
        // 获取分享链接
        AiTaskShareLinkPO shareLink = getShareLinkPO(request);
        
        Boolean publicShare = Optional.ofNullable(shareLink).map(AiTaskShareLinkPO::getPublicShare).orElse(Boolean.FALSE);
        String taskId = Optional.ofNullable(shareLink).map(AiTaskShareLinkPO::getResourceId).orElse(request.getTaskId());
        aiTaskManager.checkPermission(taskId);
        AnalyticsAiTaskHistoryPO taskHistoryPO = aiTaskManager.getTaskById(taskId);
        String taskType = Optional.ofNullable(taskHistoryPO).map(AnalyticsAiTaskHistoryPO::getType).orElse(null);
        String taskOwner = Optional.ofNullable(taskHistoryPO).map(AnalyticsAiTaskHistoryPO::getCreatedBy).orElse(null);
        Boolean owner = StringUtils.isNotBlank(UserIdHolder.get()) && UserIdHolder.get().equals(taskOwner);
        return FreeShareInitResponse.builder()
                .publicShare(publicShare)
                .taskId(taskId)
                .taskType(taskType)
                .owner(owner)
                .build();
    }
    
    /**
     * 获取分享链接对象
     * 根据请求中的分享ID或任务ID来获取对应的分享链接对象
     *
     * @param request 免费分享初始化请求对象，包含分享ID或任务ID
     * @return AiTaskShareLinkPO 分享链接对象
     */
    public AiTaskShareLinkPO getShareLinkPO(FreeShareInitRequest request) {
        if (StringUtils.isNotBlank(request.getShareId())) {
            log.info("获取分享链接对象，分享ID: {}", request.getShareId());
            return aiTaskShareLinkService.getShareLinkByShareId(request.getShareId());
        } else if (StringUtils.isNotBlank(request.getTaskId())) {
            log.info("获取分享链接对象，任务ID: {}", request.getTaskId());
            return aiTaskShareLinkService.getShareLinkByTaskId(request.getTaskId());
        } else {
            throw new IllegalArgumentException("分享ID和任务ID不能同时为空");
        }
    }
    
    public FreeShareCreateResponse createPublicShareLink(FreeShareCreateRequest request) {
        // 校验权限
        String taskId = request.getTaskId();
        AnalyticsAiTaskHistoryPO taskHistoryPO = aiTaskManager.checkPermission(taskId);
        // 创建分享链接
        AiTaskShareLinkPO aiTaskShareLinkPO = buildAiTaskShareLinkPO(request, taskId, taskHistoryPO);
        AiTaskShareLinkPO shareLink = aiTaskShareLinkService.createShareLink(aiTaskShareLinkPO);
        return FreeShareCreateResponse.builder()
                .shareId(shareLink.getShareId())
                .publicShare(shareLink.getPublicShare())
                .build();
    }
    
    
    private static @NotNull AiTaskShareLinkPO buildAiTaskShareLinkPO(FreeShareCreateRequest request, String taskId,
            AnalyticsAiTaskHistoryPO taskHistoryPO) {
        AiTaskShareLinkPO aiTaskShareLinkPO = new AiTaskShareLinkPO();
        aiTaskShareLinkPO.setShareId(UUID.randomUUID().toString());
        aiTaskShareLinkPO.setResourceId(taskId);
        aiTaskShareLinkPO.setResourceType(taskHistoryPO.getType());
        aiTaskShareLinkPO.setPublicShare(true);
        aiTaskShareLinkPO.setActive(true);
        aiTaskShareLinkPO.setPassword(request.getPassword());
        aiTaskShareLinkPO.setExpiresAt(new DateTime(SHARE_FOREVER));
        aiTaskShareLinkPO.setRole(VIEWER.getValue());
        return aiTaskShareLinkPO;
    }
    
    
}

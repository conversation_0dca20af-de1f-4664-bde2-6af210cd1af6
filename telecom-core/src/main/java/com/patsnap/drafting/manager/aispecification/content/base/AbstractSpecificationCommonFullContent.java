package com.patsnap.drafting.manager.aispecification.content.base;

import com.fasterxml.jackson.core.type.TypeReference;
import com.patsnap.core.common.copilot.constant.GPTStatus;
import com.patsnap.core.common.copilot.gpt.enums.GPTModelEnum;
import com.patsnap.core.common.copilot.streaming.model.GptResponseDTO;
import com.patsnap.drafting.client.ComputeClient;
import com.patsnap.drafting.client.OpenAiClient;
import com.patsnap.drafting.client.model.AiSpecificationComputeData;
import com.patsnap.drafting.client.model.AiSpecificationComputeReqDTO;
import com.patsnap.drafting.config.UrlConfig;
import com.patsnap.drafting.enums.aispecification.JurisdictionEnum;
import com.patsnap.drafting.enums.prompt.ScenarioEnum;
import com.patsnap.drafting.manager.AbstractGenerateFullContent;
import com.patsnap.drafting.manager.aispecification.SpecificationManager;
import com.patsnap.drafting.manager.aitask.AiTaskManager;
import com.patsnap.drafting.model.base.StreamingModelBO;
import com.patsnap.drafting.request.GenerateContentRequestDTO;
import com.patsnap.drafting.response.aispecification.SpecificationRdResDTO;
import com.patsnap.spring.boot.autoconfigure.web.entity.CommonResponse;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import reactor.core.publisher.Flux;

/**
 * 一次性返回全部文本
 */
@Slf4j
public abstract class AbstractSpecificationCommonFullContent extends AbstractGenerateFullContent<String> {


    private static final TypeReference<String> DEFAULT_STRING_TYPE_REFERENCE = new TypeReference<>() {
    };

    @Autowired
    private ComputeClient computeClient;

    @Autowired
    private SpecificationManager specificationManager;

    @Autowired
    private OpenAiClient openAiClient;


    protected AbstractSpecificationCommonFullContent(AiTaskManager aiTaskManager, UrlConfig urlConfig,
            OpenAiClient openAiClient) {
        super(aiTaskManager, urlConfig, openAiClient);
    }


    @Override
    protected StreamingModelBO getStreamingModelBO(GenerateContentRequestDTO generateContentRequestDTO) {
        AiSpecificationComputeData computeReqData = specificationManager.getCommonComputeReqData(
                generateContentRequestDTO.getTaskId());
        AiSpecificationComputeReqDTO aiSpecificationComputeReqDTO = AiSpecificationComputeReqDTO.builder()
                .function(getFunction()).data(computeReqData).lang(StringUtil.toLowerCase(
                        JurisdictionEnum.fromName(computeReqData.getJurisdiction()).getValue())).build();
        SpecificationRdResDTO specificationPrompt = computeClient.getSpecificationPrompt(aiSpecificationComputeReqDTO);
        StreamingModelBO streamingModelBO = new StreamingModelBO();
        streamingModelBO.setPrompt(specificationPrompt.getPrompt());
        streamingModelBO.setModelName(specificationPrompt.getModelName());
        streamingModelBO.setExtraData(specificationPrompt.getExtraData());
        return streamingModelBO;
    }

    public abstract String getFunction();

    @Override
    protected TypeReference<String> getTypeReference() {
        return DEFAULT_STRING_TYPE_REFERENCE;
    }

    @Override
    protected Flux<CommonResponse<GptResponseDTO<String>>> getContent(StreamingModelBO streamingModelBO) {
        boolean status = true;
        GptResponseDTO<String> data;
        try {
            //非流式的方式一次性返回
            String result = openAiClient.callGptByPrompt(GPTModelEnum.getGptModelEnum(streamingModelBO.getModelName()),
                    streamingModelBO.getPrompt(), ScenarioEnum.AI_SPECIFICATION, getTypeReference());
            result = StringUtils.isBlank(result) ? result : result.replaceAll("#", "");

            data = GptResponseDTO.<String>builder().content(result).status(GPTStatus.FINISH).build();
        } catch (Exception e) {
            status = false;
            log.warn("[AI说明书撰写]请求AI说明书撰写 {}报错", getContentType(), e);
            data = GptResponseDTO.<String>builder().status(GPTStatus.FAILED).build();
        }
        CommonResponse<GptResponseDTO<String>> response = CommonResponse.<GptResponseDTO<String>>builder().withData(data)
                .withStatus(status).build();
        return Flux.just(response);
    }


}

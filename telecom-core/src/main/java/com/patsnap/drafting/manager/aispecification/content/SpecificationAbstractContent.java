package com.patsnap.drafting.manager.aispecification.content;

import com.patsnap.core.common.copilot.gpt.enums.GPTModelEnum;
import com.patsnap.core.common.copilot.streaming.handling.StreamingChatLanguageModel;
import com.patsnap.core.common.copilot.streaming.model.GptResponseDTO;
import com.patsnap.drafting.client.ComputeClient;
import com.patsnap.drafting.client.model.AiSpecificationComputeData;
import com.patsnap.drafting.client.model.AiSpecificationComputeReqDTO;
import com.patsnap.drafting.config.UrlConfig;
import com.patsnap.drafting.enums.aispecification.JurisdictionEnum;
import com.patsnap.drafting.enums.prompt.ScenarioEnum;
import com.patsnap.drafting.enums.task.AiTaskContentTypeEnum;
import com.patsnap.drafting.handler.specification.SpecificationStringStreamingResponseHandler;
import com.patsnap.drafting.manager.AbstractGenerateStreamingContent;
import com.patsnap.drafting.manager.aispecification.SpecificationManager;
import com.patsnap.drafting.manager.aitask.AiTaskManager;
import com.patsnap.drafting.model.base.StreamingModelBO;
import com.patsnap.drafting.request.GenerateContentRequestDTO;
import com.patsnap.drafting.response.aispecification.SpecificationRdResDTO;
import com.patsnap.spring.boot.autoconfigure.web.entity.CommonResponse;
import jodd.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

/**
 * AI说明书撰写 - 摘要
 */
@Component
public class SpecificationAbstractContent extends AbstractGenerateStreamingContent<String> {
    @Autowired
    private SpecificationManager specificationManager;

    @Autowired
    private ComputeClient computeClient;

    private static final String GENERATE_FUNCTION = "generate_abstract";

    protected SpecificationAbstractContent(AiTaskManager aiTaskManager, UrlConfig urlConfig) {
        super(aiTaskManager, urlConfig);
    }

    @Override
    protected StreamingModelBO getStreamingModelBO(GenerateContentRequestDTO generateContentRequestDTO) {
        AiSpecificationComputeData computeReqData = specificationManager.getAbstComputeReqData(generateContentRequestDTO.getTaskId());
        AiSpecificationComputeReqDTO aiSpecificationComputeReqDTO = AiSpecificationComputeReqDTO.builder()
                .function(GENERATE_FUNCTION).data(computeReqData).lang(StringUtil.toLowerCase(
                        JurisdictionEnum.fromName(computeReqData.getJurisdiction()).getValue())).build();
        SpecificationRdResDTO specificationPrompt = computeClient.getSpecificationPrompt(aiSpecificationComputeReqDTO);
        StreamingModelBO streamingModelBO = new StreamingModelBO();
        streamingModelBO.setPrompt(specificationPrompt.getPrompt());
        StreamingChatLanguageModel model = streamingModelBuilder.build(
                GPTModelEnum.getGptModelEnum(specificationPrompt.getModelName()),
                ScenarioEnum.AI_SPECIFICATION.getValue(), 0.6, 32000);
        streamingModelBO.setModel(model);
        return streamingModelBO;
    }

    @Override
    protected Flux<CommonResponse<GptResponseDTO<String>>> getContent(StreamingModelBO streamingModelBO) {
        String prompt = streamingModelBO.getPrompt();
        return Flux.create(sink -> {
            SpecificationStringStreamingResponseHandler handler = new SpecificationStringStreamingResponseHandler(sink);
            streamingModelBO.getModel().generate(prompt, handler);
        });
    }

    @Override
    public String getContentType() {
        return AiTaskContentTypeEnum.ABSTRACT.getType();
    }

}

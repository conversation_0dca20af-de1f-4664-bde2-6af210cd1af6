package com.patsnap.drafting.manager.aispecification.content;

import com.patsnap.core.common.copilot.streaming.model.GptResponseDTO;
import com.patsnap.core.common.utils.MapUtils;
import com.patsnap.drafting.config.UrlConfig;
import com.patsnap.drafting.enums.task.AiTaskContentTypeEnum;
import com.patsnap.drafting.manager.aispecification.content.base.AbstractSpecificationCommonStreamingContent;
import com.patsnap.drafting.manager.aispecification.handler.BgContentStreamingResHandler;
import com.patsnap.drafting.manager.aitask.AiTaskManager;
import com.patsnap.drafting.model.base.StreamingModelBO;
import com.patsnap.drafting.util.ReferenceReplacer;
import com.patsnap.spring.boot.autoconfigure.web.entity.CommonResponse;

import java.util.List;

import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;

import reactor.core.publisher.Flux;

/**
 * AI说明书撰写 - 背景技术
 */
@Component
public class SpecificationBackgroundContent extends AbstractSpecificationCommonStreamingContent<String> {


    protected SpecificationBackgroundContent(AiTaskManager aiTaskManager, UrlConfig urlConfig) {
        super(aiTaskManager, urlConfig);
    }

    @Override
    public String getContentType() {
        return AiTaskContentTypeEnum.SPECIFICATION_BACKGROUND.getType();
    }

    @Override
    public String getFunction() {
        return "generate_background";
    }

    @Override
    protected Flux<CommonResponse<GptResponseDTO<String>>> getContent(StreamingModelBO streamingModelBO) {
        String prompt = streamingModelBO.getPrompt();
        List<String> references = (List<String>) MapUtils.get(streamingModelBO.getExtraData(),
                "similar_patents.pn",
                Lists.newArrayList());
        ReferenceReplacer replacer = new ReferenceReplacer(references);
        return Flux.create(sink -> {
            BgContentStreamingResHandler handler = new BgContentStreamingResHandler(sink, 5, replacer);
            streamingModelBO.getModel().generate(prompt, handler);
        });
    }
}

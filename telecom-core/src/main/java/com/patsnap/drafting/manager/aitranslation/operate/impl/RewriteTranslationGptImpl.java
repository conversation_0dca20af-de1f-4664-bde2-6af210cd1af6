package com.patsnap.drafting.manager.aitranslation.operate.impl;

import com.patsnap.core.common.copilot.gpt.enums.GPTModelEnum;
import com.patsnap.core.common.copilot.streaming.handler.SplitListJsonStreamingResponseHandler;
import com.patsnap.core.common.copilot.streaming.model.GptResponseDTO;
import com.patsnap.drafting.client.OpenAiClient;
import com.patsnap.drafting.config.AiTranslationConfig;
import com.patsnap.drafting.config.UrlConfig;
import com.patsnap.drafting.constants.Constant;
import com.patsnap.drafting.enums.prompt.PromptKeyEnum;
import com.patsnap.drafting.enums.prompt.ScenarioEnum;
import com.patsnap.drafting.manager.aitranslation.handler.AiTranslationGptRewriteStreamingResponseHandler;
import com.patsnap.drafting.manager.aitranslation.operate.TermContextUtil;
import com.patsnap.drafting.manager.aitranslation.operate.TranslationRewriteService;
import com.patsnap.drafting.manager.content.TaskContentManager;
import com.patsnap.drafting.model.aitranslation.AiTransContextBo;
import com.patsnap.drafting.model.aitranslation.TranslationBO;
import com.patsnap.drafting.util.ParagraphUtils;
import com.patsnap.spring.boot.autoconfigure.web.entity.CommonResponse;
import static com.patsnap.drafting.manager.aitranslation.operate.TranslationConstant.INPUT;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;
import com.google.common.collect.ImmutableMap;
import com.patsnap.core.common.copilot.streaming.handling.StreamingChatLanguageModel;
import reactor.core.publisher.Flux;

@Component
public class RewriteTranslationGptImpl extends FullTextTranslationGptImpl implements
        TranslationRewriteService {

    private static GPTModelEnum GPT_MODEL = GPTModelEnum.TRANSLATION_GPT;

    private final TaskContentManager taskContentManager;

    public RewriteTranslationGptImpl(UrlConfig urlConfig, AiTranslationConfig aiTranslationConfig,
            OpenAiClient openAiClient, TaskContentManager taskContentManager) {
        super(urlConfig, aiTranslationConfig, openAiClient);
        this.taskContentManager = taskContentManager;
    }

    @Override
    public String model() {
        return GPT_MODEL.getModelName();
    }

    @Override
    public Flux<CommonResponse<GptResponseDTO<String>>> generate(AiTransContextBo contextBo,
            int index,
            String originalText) {
        Map<String, String> terms = contextBo.getMatchCustomTerms();
        Pair<String, Integer> pair = getSourceInput(contextBo, index, originalText, terms);
        PromptKeyEnum promptKeyEnum = Constant.CN.equals(contextBo.getTargetLang()) ?
                PromptKeyEnum.SELF_MODEL_TRANSLATION_EN2CN
                : PromptKeyEnum.SELF_MODEL_TRANSLATION_CN2EN;
        String translationMsg = openAiClient.buildPromptByPlatform(
                promptKeyEnum.getValue(),
                ImmutableMap.of(INPUT, pair.getLeft()));
        return createFluxFromModel(translationMsg, contextBo.getTargetLang(),
                contextBo.getSourceLang(), terms, index, pair.getRight(), contextBo.getTaskId());
    }

    private Pair<String, Integer> getSourceInput(AiTransContextBo contextBo, int index,
            String originalText, Map<String, String> terms) {

        List<TranslationBO> translationBOS = contextBo.getResults();
        int start = Math.max(0, index - aiTranslationConfig.getRewritePrefixLength());
        StringBuilder sourceInput = new StringBuilder();
        int actualLength = 0;
        for (int i = start; i < index; i++) {
            actualLength++;
            sourceInput.append(ParagraphUtils.PARAGRAPH_PREFIX)
                    .append(translationBOS.get(i).getSrcText())
                    .append("\n");
        }
        actualLength++;
        sourceInput.append(ParagraphUtils.PARAGRAPH_PREFIX)
                .append(originalText);
        String result = TermContextUtil.addKeywordsToInput(sourceInput.toString(),
                contextBo.getSourceLang(),
                terms);
        return Pair.of(result, actualLength);
    }

    private Flux<CommonResponse<GptResponseDTO<String>>> createFluxFromModel(
            String msg, String targetLang, String sourceLang, Map<String, String> terms,
            int index, int actualLength, String taskId) {
        StreamingChatLanguageModel model = build(GPT_MODEL,
                ScenarioEnum.AI_TRANSLATION.getValue(), 0.7);
        return Flux.create(sink -> {
            SplitListJsonStreamingResponseHandler<String> handler =
                    new AiTranslationGptRewriteStreamingResponseHandler(
                            sink, msg, terms, targetLang, sourceLang, index,
                            taskId, actualLength, taskContentManager);
            model.generate(msg, handler);
            sink.onCancel(handler::onCancel);
        });
    }
}

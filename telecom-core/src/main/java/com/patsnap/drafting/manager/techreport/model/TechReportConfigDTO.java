package com.patsnap.drafting.manager.techreport.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.patsnap.drafting.response.techreport.CompanyInfo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Map;

/**
 * 技术报告配置DTO
 * 用于表示技术报告的完整配置信息
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class TechReportConfigDTO {

    /**
     * 核心主题对象
     */
    @JsonProperty("core_subject")
    private String coreSubject;

    /**
     * 核心主题对象
     */
    @JsonProperty("industry")
    private String industry;


    /**
     * 约束条件
     */
    @JsonProperty("constraint")
    private TechReportConstraintDTO constraint;

    /**
     * 需要提取的技术子领域
     */
    @JsonProperty("extract_tech_subfields")
    private List<String> extractTechSubfields;

    /**
     * prompt提取的公司
     * key: 公司标识, value: 公司的所有名称
     */
    @JsonProperty("extract_companies")
    private List<String> extractCompanies;

    /**
     * 经过处理的公司列表
     */
    @JsonProperty("extract_company_list")
    private List<CompanyInfo> extractCompanyList;

    /**
     * 需要排除的关键词
     */
    @JsonProperty("excluded")
    private TechReportExcludedKeywordDTO excluded;
} 
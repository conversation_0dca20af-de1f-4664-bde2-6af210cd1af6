package com.patsnap.drafting.manager.credit;

import com.patsnap.common.request.UserIdHolder;
import com.patsnap.core.common.acl.CapacityDimension;
import com.patsnap.core.common.acl.LimitConstants;
import com.patsnap.drafting.manager.acl.LimitCheckManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * 积分管理
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class CreditManager {
    
    private final LimitCheckManager limitCheckManager;
    
    /**
     * 获取次数限制
     */
    public Integer getSearchLimit(LimitConstants constants) {
        Integer maxLimitations = limitCheckManager.getMaxLimitations(constants, CapacityDimension.DIMENSION_YEAR);
        log.info("[credit_check], getSearchLimit {} maxLimitations:{}", constants.getResource(), maxLimitations);
        return Optional.ofNullable(maxLimitations).orElse(0);
    }
    
    /**
     * 获取已使用次数
     */
    public Integer getSearchUsage(LimitConstants constants) {
        Integer usages = limitCheckManager.getUsages(UserIdHolder.get(), constants, CapacityDimension.DIMENSION_YEAR);
        log.info("[credit_check], getSearchUsage {} usages:{}", constants.getResource(), usages);
        return Optional.ofNullable(usages).orElse(0);
    }
}

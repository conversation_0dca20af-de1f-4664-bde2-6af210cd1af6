package com.patsnap.drafting.manager;

import com.patsnap.core.common.copilot.gpt.enums.GPTModelEnum;
import com.patsnap.core.common.copilot.streaming.StreamingModelBuilder;
import com.patsnap.core.common.copilot.streaming.handler.StringStreamingResponseHandler;
import com.patsnap.core.common.copilot.streaming.model.GptResponseDTO;
import com.patsnap.spring.boot.autoconfigure.web.entity.CommonResponse;
import com.patsnap.drafting.enums.prompt.ScenarioEnum;
import com.patsnap.core.common.copilot.streaming.handling.StreamingChatLanguageModel;
import reactor.core.publisher.Flux;

/**
 * <AUTHOR>
 * @date 2024/10/28
 */
public class CommonGptManager {

    private final StreamingModelBuilder streamingModelBuilder;

    public CommonGptManager(StreamingModelBuilder streamingModelBuilder) {
        this.streamingModelBuilder = streamingModelBuilder;
    }

    public Flux<CommonResponse<GptResponseDTO<String>>> streamChat(String msg, String modelName,
            ScenarioEnum scenario) {
        GPTModelEnum gptModelEnum = GPTModelEnum.getGptModelEnum(modelName);
        if (gptModelEnum == null) {
            return Flux.just(
                    CommonResponse.<GptResponseDTO<String>>builder().withStatus(false).withErrorMsg("model not found")
                            .build());
        }
        StreamingChatLanguageModel model = streamingModelBuilder.build(gptModelEnum, scenario.getValue());
        return Flux.create(sink -> {
            StringStreamingResponseHandler handler = new StringStreamingResponseHandler(sink);
            model.generate(msg, handler);
        });
    }

    public StreamingModelBuilder getStreamingModelBuilder() {
        return streamingModelBuilder;
    }
}

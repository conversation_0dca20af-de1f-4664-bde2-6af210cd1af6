package com.patsnap.drafting.manager.company;

import cn.hutool.core.util.StrUtil;
import com.patsnap.core.common.AnalyticsCommonService;
import com.patsnap.core.common.InvokeBean;
import com.patsnap.drafting.request.company.CompanyNameRequestDTO;
import com.patsnap.drafting.request.company.CompanyTreeRequestDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.Map;

/**
 * 公司树查询管理器
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CompanyTreeManager {

    private final AnalyticsCommonService analyticsCommonService;

    @Value("${com.patsnap.analytics.service.analytics-search-url}")
    private String analyticsSearchUrl;

    /**
     * 获取公司名称建议
     * 
     * @param request 公司名称搜索请求
     * @return 公司名称建议列表
     */
    public Object getCompanyNameSuggestions(CompanyNameRequestDTO request) {
        InvokeBean<Map<String, Object>> invokeBean = new InvokeBean<>();
        invokeBean.setHttpMethod(HttpMethod.POST);
        invokeBean.setUrl(analyticsSearchUrl + "/helper/corporate-tree/company-name");
        invokeBean.setRequestBody(request);
        invokeBean.setResponseType(new ParameterizedTypeReference<Map<String, Object>>() {});
        Map<String, Object> result = analyticsCommonService.invokeMethod(invokeBean);
        return result.get("data");
    }
    
    /**
     * 获取公司树信息
     * 
     * @param request 公司树查询请求
     * @return 公司树信息
     */
    public Object getCompanyTree(CompanyTreeRequestDTO request) {
        String url = analyticsSearchUrl + "/helper/corporate-tree/no-geetest";
        
        // 构建URL和查询参数
        UriComponentsBuilder builder = UriComponentsBuilder.fromUriString(url)
                .queryParam("q", request.getQ())
                .queryParam("rows", request.getRows())
                .queryParam("active", request.getActive())
                .queryParam("exact_match", request.getExactMatch());

        if (StrUtil.isNotEmpty(request.getUa())) {
            builder.queryParam("ua", request.getUa());
        }
        
        // 时间戳参数可选
        if (request.getTimestamp() != null) {
            builder.queryParam("_", request.getTimestamp());
        }
        
        // 创建调用Bean
        InvokeBean<Map<String, Object>> invokeBean = new InvokeBean<>();
        invokeBean.setHttpMethod(HttpMethod.GET);
        invokeBean.setUrl(builder.build().toUriString());
        invokeBean.setResponseType(new ParameterizedTypeReference<Map<String, Object>>() {});
        
        log.info("调用公司树查询接口: {}", builder.build().toUriString());
        Map<String, Object> result = analyticsCommonService.invokeMethod(invokeBean);
        return result.get("data");
    }
} 
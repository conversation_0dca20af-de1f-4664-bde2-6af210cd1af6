package com.patsnap.drafting.manager.aispecification.content;

import com.patsnap.core.common.copilot.streaming.model.GptResponseDTO;
import com.patsnap.drafting.config.UrlConfig;
import com.patsnap.drafting.enums.task.AiTaskContentTypeEnum;
import com.patsnap.drafting.handler.specification.SpecificationStringStreamingResponseHandler;
import com.patsnap.drafting.manager.aispecification.content.base.AbstractSpecificationCommonStreamingContent;
import com.patsnap.drafting.manager.aitask.AiTaskManager;
import com.patsnap.drafting.model.base.StreamingModelBO;
import com.patsnap.spring.boot.autoconfigure.web.entity.CommonResponse;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

/**
 * AI说明书撰写 - 技术领域
 */
@Component
public class SpecificationTechFieldContent extends AbstractSpecificationCommonStreamingContent<String> {


    protected SpecificationTechFieldContent(AiTaskManager ai<PERSON><PERSON><PERSON><PERSON>, UrlConfig urlConfig) {
        super(aiTask<PERSON>anager, urlConfig);
    }

    @Override
    public String getContentType() {
        return AiTaskContentTypeEnum.SPECIFICATION_TECH_FIELD.getType();
    }

    @Override
    public String getFunction() {
        return "generate_techfield";
    }


    @Override
    protected Flux<CommonResponse<GptResponseDTO<String>>> getContent(StreamingModelBO streamingModelBO) {
        String prompt = streamingModelBO.getPrompt();
        return Flux.create(sink -> {
            SpecificationStringStreamingResponseHandler handler = new SpecificationStringStreamingResponseHandler(sink);
            streamingModelBO.getModel().generate(prompt, handler);
        });
    }
}

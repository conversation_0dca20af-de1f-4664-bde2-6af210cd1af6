package com.patsnap.drafting.manager.aispecification;

import com.patsnap.core.common.redissonclient.RedissonClientTryCatchWrapper;
import com.patsnap.drafting.client.model.AiSpecificationComputeData;
import com.patsnap.drafting.enums.task.AiTaskContentTypeEnum;
import com.patsnap.drafting.manager.aitask.AiTaskManager;

import java.util.concurrent.TimeUnit;

import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ClaimParseManager {

    @Autowired
    private RedissonClientTryCatchWrapper redisson;

    @Autowired
    private AiTaskManager aiTaskManager;

    private static final String LINE_LABEL = "</span>";

    private static final String LABEL_REGEX = "<[^>]+>";

    private static final String INDEP_CLAIM_LABLE_START_WITH = "<span class='indep-clm'";

    private static final String CACHE_KEY = "AI-INDEPCLM:%s";

    public AiSpecificationComputeData parseSimpleFormattedClmByTaskId(String taskId) {
        // 尝试从缓存中获取数据
        String cacheKey = String.format(CACHE_KEY, taskId);
        RBucket<AiSpecificationComputeData> rBucket = redisson.getBucket(cacheKey);
        if (redisson.isExists(rBucket)) {
            return rBucket.get();
        }
        AiSpecificationComputeData data = doParseFirstIndepClmByTaskId(taskId);
        // 将数据存入缓存
        redisson.set(rBucket, data, 5, TimeUnit.MINUTES);
        return data;
    }

    private AiSpecificationComputeData doParseFirstIndepClmByTaskId(String taskId) {
        // 从任务管理器获取内容
        String claimFormatted = aiTaskManager.getTaskContent(taskId, AiTaskContentTypeEnum.CLAIM_FORMAT);

        // 将</span>替换为换行符
        String inputTmp = claimFormatted.replace(LINE_LABEL, "\n");

        // 去掉所有标签
        String cleanedInput = inputTmp.replaceAll(LABEL_REGEX, "");

        // 提取第一个class='indep-clm'包围的段落
        String firstIndepClmParagraph = extractFirstIndepClmParagraph(claimFormatted);

        // 创建数据对象并设置值
        AiSpecificationComputeData data = new AiSpecificationComputeData();
        data.setClaimText(cleanedInput);
        data.setFirstIndependClaim(firstIndepClmParagraph);
        return data;
    }


    private static String extractFirstIndepClmParagraph(String input) {
        int startIndex = input.indexOf(INDEP_CLAIM_LABLE_START_WITH);
        if (startIndex == -1) {
            return StringUtils.EMPTY;
        }
        int endIndex = input.indexOf(LINE_LABEL, startIndex);
        if (endIndex == -1) {
            return StringUtils.EMPTY;
        }
        String indepClmContent = input.substring(startIndex, endIndex);
        // 去掉标签
        return indepClmContent.replaceAll(LABEL_REGEX, StringUtils.EMPTY).trim();
    }

}

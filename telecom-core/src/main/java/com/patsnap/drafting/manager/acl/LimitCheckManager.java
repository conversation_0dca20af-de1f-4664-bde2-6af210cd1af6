package com.patsnap.drafting.manager.acl;

import com.google.common.collect.ImmutableMap;
import com.patsnap.common.request.RequestFromHolder;
import com.patsnap.common.request.UserIdHolder;
import com.patsnap.core.common.acl.*;
import com.patsnap.core.common.request.RoleIdsHolder;
import com.patsnap.core.common.request.SessionIdHolder;
import com.patsnap.drafting.enums.task.AiTaskContentTypeEnum;
import com.patsnap.drafting.exception.BizException;
import com.patsnap.drafting.exception.errorcode.CreditErrorCodeEnum;
import com.patsnap.drafting.manager.credit.CreditManager;
import com.patsnap.drafting.repository.aitask.dao.AnalyticsAiTaskHistoryService;
import com.patsnap.drafting.repository.aitask.entity.AnalyticsAiTaskHistoryPO;
import com.patsnap.drafting.repository.creditusage.service.AnalyticsCreditUsageDetailService;
import com.patsnap.drafting.response.init.FtoSearchUsage;
import com.patsnap.drafting.util.TaskIdHolder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;

import static com.patsnap.drafting.constants.Constant.*;
import static com.patsnap.drafting.constants.Constant.AI_FTO_SEARCH_LIMIT;

/**
 * 套餐权限校验
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class LimitCheckManager {
    
    private final LimitService limitService;
    private final AnalyticsCreditUsageDetailService creditUsageDetailService;
    private final AnalyticsAiTaskHistoryService aiTaskHistoryService;

    /**
     * 获取席位
     */
    public Integer getUserSeats() {
        //目前 AI 撰写 统一只有一个席位
        return 1;
    }
    
    /**
     * 获取已使用次数/积分
     */
    public Integer getUsages(String userId, LimitConstants resourceName, CapacityDimension capacityDimension) {
        Integer usage = getUsages(userId, resourceName).get(capacityDimension.getName());
        if (Objects.isNull(usage) || usage < 0) {
            return 0;
        }
        return usage;
        
    }
    
    /**
     * 获取所有纬度已使用次数
     */
    private Map<String, Integer> getUsages(String userId, LimitConstants resourceName) {
        return limitService.getUsages(userId, resourceName.getResource(), false, 1);
    }
    
    /**
     * 获取最大限制次数/积分
     */
    public Integer getMaxLimitations(LimitConstants resources, CapacityDimension capacityDimension) {
        Map<String, Integer> maxLimitations = getMaxLimitations(resources);
        if (Objects.nonNull(maxLimitations) && maxLimitations.containsKey(capacityDimension.getName())) {
            return maxLimitations.get(capacityDimension.getName());
        }
        return -1;
    }
    
    /**
     * 获取最大限制次数
     *
     * @param resources 资源
     * @return -1表示无次数。-2表示无限制
     */
    private Map<String, Integer> getMaxLimitations(LimitConstants resources) {
        String userId = UserIdHolder.get();
        List<String> roleIds = RoleIdsHolder.get();
        
        if (StringUtils.isEmpty(userId) || CollectionUtils.isEmpty(roleIds) || StringUtils.isEmpty(resources.getResource())) {
            return new ImmutableMap.Builder<String, Integer>()
                    .put(CapacityDimension.DIMENSION_MINUTE.getName(), 0)
                    .put(CapacityDimension.DIMENSION_HOUR.getName(), 0)
                    .put(CapacityDimension.DIMENSION_DAY.getName(), 0)
                    .put(CapacityDimension.DIMENSION_WEEK.getName(), 0)
                    .put(CapacityDimension.DIMENSION_MONTH.getName(), 0)
                    .put(CapacityDimension.DIMENSION_YEAR.getName(), 0)
                    .put(CapacityDimension.DIMENSION_ALL.getName(), 0)
                    .build();
        }
        
        if (limitService.isSuperAdmin(roleIds)) {
            return new ImmutableMap.Builder<String, Integer>()
                    .put(CapacityDimension.DIMENSION_MINUTE.getName(), -2)
                    .put(CapacityDimension.DIMENSION_HOUR.getName(), -2)
                    .put(CapacityDimension.DIMENSION_DAY.getName(), -2)
                    .put(CapacityDimension.DIMENSION_WEEK.getName(), -2)
                    .put(CapacityDimension.DIMENSION_MONTH.getName(), -2)
                    .put(CapacityDimension.DIMENSION_YEAR.getName(), -2)
                    .put(CapacityDimension.DIMENSION_ALL.getName(), -2)
                    .build();
        }
        int seats = getUserSeats();
        return limitService.getLimitations(userId, SessionIdHolder.get(), roleIds, resources.getResource(), seats);
    }
    
    /**
     * 检查用户的 AI 撰写积分限制。
     * 该函数用于在用户进行AI草稿操作时，检查其信用限制是否被超出。如果不需要检查限制，则直接返回。
     * 如果需要检查限制，则调用限流服务进行检查，并根据检查结果抛出相应的异常。
     *
     * @param userId 用户的唯一标识符，用于指定要检查的用户。
     * @param increaseCount 需要增加的积分数量，用于计算当前操作是否超出限制。
     * @param contentTypeEnum ai任务的任务类型
     * @throws BizException 如果积分限制被超出，则抛出此异常，错误码为CREDIT_EXCEED_LIMIT。
     */
    public void checkDraftingLimit(String userId, int increaseCount, AiTaskContentTypeEnum contentTypeEnum) {
        if (increaseCount <= 0 || !needCheckLimit()) {
            log.info("Check ai limit not need, userId:{}, increaseCount:{}", userId, increaseCount);
            return;
        }
        try {
            LimitConstants resource = LimitResourceConstants.AI_DRAFTING_CREDIT_LIMIT;
            limitService.checkLimit(userId, SessionIdHolder.get(), RoleIdsHolder.get(), resource.getResourceList(),
                    true, increaseCount, getUserSeats());
            // 生成一条积分使用明细数据
            String taskId = TaskIdHolder.get();
            AnalyticsAiTaskHistoryPO taskInfo = aiTaskHistoryService.getById(taskId);
            if(Objects.nonNull(taskInfo)) {
                // creditUsageDetailService.createCreditUsageDetail(taskId, taskInfo.getType(), userId, false, CreditTypeEnum.NUMBER.getValue(), -increaseCount, contentTypeEnum);
            }
            log.info("Check ai limit success, userId:{}, increaseCount:{}, contentType:{}", userId, increaseCount, contentTypeEnum.getType());
        } catch (Exception e) {
            log.warn("Check ai limit error:", e);
            if (e instanceof AclLimitExceededException) {
                throw new BizException(CreditErrorCodeEnum.CREDIT_EXCEED_LIMIT);
                
            }
            throw e;
        }
    }
    
    /**
     * 检查用户使用特定资源的限制
     * 此方法用于验证用户是否有足够的额度来增加指定数量的资源如果增加的数量小于等于0，
     * 或者不需要检查限制，则不执行任何操作否则，它将调用限制服务来验证用户的限制
     *
     *
     * @param userId       用户ID，用于识别用户
     * @param increaseCount    用户请求增加的资源数量
     * @param resource     资源类型，用于指定需要检查的资源
     */
    public void checkLimit(String userId, int increaseCount, LimitConstants resource) {
        if (increaseCount <= 0 || !needCheckLimit()) {
            log.info("Check {} not need, userId:{}, increaseCount:{}", resource.getResource(), userId, increaseCount);
            return;
        }
        try {
            limitService.checkLimit(userId, SessionIdHolder.get(), RoleIdsHolder.get(), resource.getResourceList(),
                    true, increaseCount, getUserSeats());
            log.info("Check {} success, userId:{}, increaseCount:{}", resource.getResource(), userId, increaseCount);
        } catch (Exception e) {
            log.warn("Check {} error:", resource.getResource(), e);
            if (e instanceof AclLimitExceededException) {
                throw new BizException(CreditErrorCodeEnum.EXCEED_LIMIT, createLimitMargin(resource));
            }
            throw e;
        }
    }
    
    /**
     * 判断当前请求是否需要扣除用量：来源eureka的请求不需要校验，来自AI平台的agent调用也不需要校验
     *
     * @return true 需要校验 false 不需要校验
     */
    private boolean needCheckLimit() {
        String from = RequestFromHolder.get();
        if (StringUtils.isNotBlank(from) && (from.contains(X_PATSNAP_FROM_EUREKA) || from.contains(X_PATSNAP_FROM_AGENT))) {
            log.info("请求来源为eureka服务，不扣除用量。from：{}", from);
            return false;
        }
        return true;
    }

    private Map<String, Object> createLimitMargin(LimitConstants resource) {
        Integer limit = this.getMaxLimitations(resource, CapacityDimension.DIMENSION_YEAR);
        Integer usage = this.getUsages(UserIdHolder.get(), resource, CapacityDimension.DIMENSION_YEAR);
        Map<String, Object> errorData = new HashMap<>();
        errorData.put("margin_count", Optional.ofNullable(limit).orElse(0) - Optional.ofNullable(usage).orElse(0));
        return errorData;
    }

}

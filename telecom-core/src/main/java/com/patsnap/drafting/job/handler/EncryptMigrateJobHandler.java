package com.patsnap.drafting.job.handler;

import com.patsnap.core.common.bizsecurity.migration.DataMigrationManager;
import com.patsnap.core.common.bizsecurity.migration.MigrationProgress;
import com.patsnap.core.common.bizsecurity.util.EncryptTableAnnotationUtil;
import com.patsnap.drafting.repository.aitask.entity.AnalyticsAiTaskContentPO;
import com.xxl.job.core.context.XxlJobHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 加密迁移任务
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class EncryptMigrateJobHandler {

    private final DataMigrationManager dataMigrationManager;

    public void migrate() {
        dataMigrationManager.migrateTableDataByAnnotation(AnalyticsAiTaskContentPO.class);
    }

    public void progress() {
        MigrationProgress migrationProgress = dataMigrationManager.getProgress(EncryptTableAnnotationUtil.getTableName(AnalyticsAiTaskContentPO.class));

        XxlJobHelper.handleSuccess(migrationProgress.toString());
    }

    public void cancel() {
        dataMigrationManager.cancelMigration(EncryptTableAnnotationUtil.getTableName(AnalyticsAiTaskContentPO.class));
    }
}

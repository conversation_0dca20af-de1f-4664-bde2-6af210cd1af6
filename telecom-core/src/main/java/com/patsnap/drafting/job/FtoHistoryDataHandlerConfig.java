package com.patsnap.drafting.job;

import com.patsnap.drafting.manager.aiftosearch.FtoSearchManager;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import java.util.Arrays;

/**
 * FTO历史数据刷新
 *
 * <AUTHOR>
 * @Date 2025/7/18 14:12
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class FtoHistoryDataHandlerConfig {
    private final FtoSearchManager ftoSearchManager;

    @XxlJob("ftoHistoryDataHandle")
    public void ftoHistoryDataHandle() {
        // 如果xxl-job配置了可选参数，那就只刷参数里的taskId
        String param = XxlJobHelper.getJobParam();
        if(StringUtils.isBlank(param)) {
            // 跑所有的历史数据
            ftoSearchManager.handleHistoryTaskContents("");
        } else {
            // 只跑参数里指定的历史数据
            if(param.contains(",")) {
                Arrays.asList(param.split(",")).forEach(ftoSearchManager::handleHistoryTaskContents);
            } else {
                ftoSearchManager.handleHistoryTaskContents(param);
            }
        }
    }
}

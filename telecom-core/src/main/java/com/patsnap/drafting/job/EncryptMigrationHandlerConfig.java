package com.patsnap.drafting.job;

import com.patsnap.drafting.job.handler.EncryptMigrateJobHandler;
import com.patsnap.drafting.job.handler.RollbackEncryptMigrateJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class EncryptMigrationHandlerConfig {

    private final EncryptMigrateJobHandler encryptMigrateJobHandler;
    private final RollbackEncryptMigrateJobHandler rollbackEncryptMigrateJobHandler;

    @XxlJob("postEncryptMigrateForHistory")
    public void encryptMigrateForHistory() {
        encryptMigrateJobHandler.migrate();
    }

    @XxlJob("getEncryptMigrateProgress")
    public void getEncryptMigrateProgress() {
        encryptMigrateJobHandler.progress();
    }

    @XxlJob("postCancelEncryptMigrate")
    public void postCancelEncryptMigrate() {
        encryptMigrateJobHandler.cancel();
    }

    @XxlJob("postRollbackMigrate")
    public void postRollbackMigrate() {
        rollbackEncryptMigrateJobHandler.rollback();
    }

    @XxlJob("getRollbackMigrateProgress")
    public void getRollbackMigrateProgress() {
        rollbackEncryptMigrateJobHandler.progress();
    }

    @XxlJob("postCancelRollbackMigrate")
    public void postCancelRollbackMigrate() {
        rollbackEncryptMigrateJobHandler.cancel();
    }
}

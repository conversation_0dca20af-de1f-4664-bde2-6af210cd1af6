package com.patsnap.drafting.job.handler;

import com.patsnap.core.common.bizsecurity.migration.DataRollbackManager;
import com.patsnap.core.common.bizsecurity.migration.MigrationProgress;
import com.patsnap.core.common.bizsecurity.util.EncryptTableAnnotationUtil;
import com.patsnap.drafting.repository.aitask.entity.AnalyticsAiTaskContentPO;

import com.xxl.job.core.context.XxlJobHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 回滚加密迁移任务(紧急情况使用)
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class RollbackEncryptMigrateJobHandler {

    private final DataRollbackManager dataRollbackManager;

    public void rollback() {
        dataRollbackManager.rollbackTableDataByAnnotation(AnalyticsAiTaskContentPO.class, null);
    }

    public void progress() {
        MigrationProgress migrationProgress = dataRollbackManager.getRollbackProgress(EncryptTableAnnotationUtil.getTableName(AnalyticsAiTaskContentPO.class));

        XxlJobHelper.handleSuccess(migrationProgress.toString());
    }

    public void cancel() {
        dataRollbackManager.cancelRollback(EncryptTableAnnotationUtil.getTableName(AnalyticsAiTaskContentPO.class));
    }
}

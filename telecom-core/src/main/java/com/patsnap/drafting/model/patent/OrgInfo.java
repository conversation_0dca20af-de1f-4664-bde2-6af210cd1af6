package com.patsnap.drafting.model.patent;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 表示组织信息的类。
 */
@Data
public class OrgInfo {
    /** 实体 ID */
    @JsonProperty("entity_id")
    private String entityId;
    
    /** 英文名称 */
    @JsonProperty("name_en")
    private String nameEn;
    
    /** 名称 */
    @JsonProperty("name")
    private String name;
    
    /** 显示名称 */
    @JsonProperty("display_name")
    private String displayName;
    
    /** 规范化名称 */
    @JsonProperty("normalized_name")
    private String normalizedName;
    
    /** 原始地址 */
    @JsonProperty("original_address")
    private Address originalAddress;
    
    /** 描述 */
    @JsonProperty("description")
    private String description;
    
    /** 国家 ID */
    @JsonProperty("country_id")
    private String countryId;
    
    /** 国家名称 */
    @JsonProperty("country_name")
    private String countryName;
    
    /** 州 ID */
    @JsonProperty("state_id")
    private String stateId;
    
    /** 州名称 */
    @JsonProperty("state_name")
    private String stateName;
    
    /** 成立日期 */
    @JsonProperty("founded_date")
    private long foundedDate;
    
    /** 网站 */
    @JsonProperty("website")
    private String website;
    
    /** logo */
    @JsonProperty("logo")
    private String logo;
    
    /** 规范化实体类型 */
    @JsonProperty("normalized_entity_type_en")
    private String normalizedEntityTypeEn;
    
    /** ID */
    @JsonProperty("id")
    private String id;
    
    /** 实体类型 */
    @JsonProperty("entity_type")
    private String entityType;
    
    /** 总部地址 */
    @JsonProperty("headquarters")
    private Address headquarters;
    
    /** 员工数量 */
    @JsonProperty("employee_number")
    private int employeeNumber;
    
    /** 员工数量区间 */
    @JsonProperty("employee_number_interval")
    private String employeeNumberInterval;
}

package com.patsnap.drafting.model.aitask;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/07/25
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
public class AiTaskBO {
    private String taskId;
    private String userId;
    private String sessionId;
    private String roleIds;
    private String correlationId;
    private int retryCount = 0;
}

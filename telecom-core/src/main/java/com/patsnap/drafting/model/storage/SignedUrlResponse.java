package com.patsnap.drafting.model.storage;

import com.patsnap.common.entity.EntityObject;

import java.util.Collections;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;

import com.fasterxml.jackson.annotation.JsonIgnore;

/**
 * Created by liyc on 2015/12/4.
 */
public class SignedUrlResponse extends EntityObject {

    public static final int SUCCESS_CODE = 200;
    public static final int INTERNAL_ERROR_CODE = 500;
    private static final long serialVersionUID = 1L;
    /**
     * region, US or CN
     */
    private String region;

    /**
     * resource name mostly will be cloudfront domain name, CNAME or bucketName
     */
    private String resource;

    /**
     * signed urls
     */
    private List<SignedUrl> signedUrls;

    /**
     * sign result code, 200 - success 400 - bad request 500 - service internal error
     */
    private int code = SUCCESS_CODE;

    public SignedUrlResponse() {
    }

    public SignedUrlResponse(String region, String resource, List<SignedUrl> signedUrls) {
        this.region = region;
        this.resource = resource;
        this.signedUrls = signedUrls;
    }

    public SignedUrlResponse(String region, String resource, int code) {
        this.region = region;
        this.code = code;
        this.resource = resource;
        this.signedUrls = Collections.emptyList();
    }

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public String getResource() {
        return resource;
    }

    public void setResource(String resource) {
        this.resource = resource;
    }

    public List<SignedUrl> getSignedUrls() {
        return signedUrls;
    }

    public void setSignedUrls(List<SignedUrl> signedUrls) {
        this.signedUrls = signedUrls;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    @JsonIgnore
    public String getFirstSignedUrl() {
        if (CollectionUtils.isEmpty(signedUrls)) {
            throw new IllegalStateException("signedUrls is empty");
        }
        return signedUrls.get(0).getUrl();
    }
}

package com.patsnap.drafting.model.aispecification;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.patsnap.drafting.request.aispecification.Figure;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@lombok.Builder
public class FigureContentBO {

    @JsonProperty("figures")
    @ApiModelProperty(value = "绘图列表")
    private List<Figure> figures;
}

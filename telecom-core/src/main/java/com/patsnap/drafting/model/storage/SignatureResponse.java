package com.patsnap.drafting.model.storage;

import com.patsnap.common.entity.EntityObject;

import java.util.List;

/**
 * @author: dongdong
 * @date: 4/5/17
 */
public class SignatureResponse extends EntityObject {

    private static final long serialVersionUID = 3982805428146944490L;

    private String resource;

    private List<SignedUrl> signedUrls;

    public String getResource() {
        return resource;
    }

    public void setResource(String resource) {
        this.resource = resource;
    }

    public List<SignedUrl> getSignedUrls() {
        return signedUrls;
    }

    public void setSignedUrls(List<SignedUrl> signedUrls) {
        this.signedUrls = signedUrls;
    }
}

package com.patsnap.drafting.model.aidisclosure;

import lombok.Data;

/**
 * 交底书用户输入的结构化数据
 *
 * <AUTHOR>
 * @date 2024/08/22
 */
@Data
public class StructUserInputBO {

    /**
     * 改进主体
     */
    private String inventionSubject;
    /**
     * 应用领域
     */
    private String applicationField;
    /**
     * 技术手段
     */
    private String techMeans;
    /**
     * 技术效果
     */
    private String techEffect;
    /**
     * 技术背景
     */
    private String techBackground;
    /**
     * 技术问题
     */
    private String techProblem;
    /**
     * 实施例
     */
    private String embodiment;
}

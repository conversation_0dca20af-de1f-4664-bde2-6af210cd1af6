package com.patsnap.drafting.model.aispecification;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.patsnap.drafting.request.aispecification.DisclosureEmbodimentItem;
import lombok.Data;

import java.util.List;

/**
 * 交底书抽取实施例
 *
 * <AUTHOR>
 * @Date 2025/4/28 17:04
 */
@Data
public class SpecificationDisclosureExtractEmbodimentBO {

    // 交底书抽取实施例
    @JsonProperty("embodiments")
    private List<DisclosureEmbodimentItem> embodiments;
}

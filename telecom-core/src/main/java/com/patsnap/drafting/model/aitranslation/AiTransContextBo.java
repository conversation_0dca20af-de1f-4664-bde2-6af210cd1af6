package com.patsnap.drafting.model.aitranslation;
import com.patsnap.drafting.request.aitask.AiTaskReqDTO;
import com.patsnap.drafting.response.aitranslation.TranslationKeywordResDTO;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/02/14
 */
@Data
public class AiTransContextBo extends AiTaskReqDTO {

    /**
     * 词表id
     */
    private String termListId;

    /**
     * 标题
     */
    private String title;

    /**
     * 原文语言
     */
    private String sourceLang;

    /**
     * 目标语言
     */
    private String targetLang;

    /**
     * 原文
     */
    private String input;

    /**
     * 技术话题
     */
    private String techTopic;

    /**
     * 翻译结果
     */
    private List<TranslationBO> results = new ArrayList<>();

    /**
     * 推荐术语列表
     */
    private List<TranslationKeywordResDTO> suggestedTerms = new ArrayList<>();

    /**
     * 用户自定义术语
     */
    private List<TranslationKeywordResDTO> customTerms = new ArrayList<>();

    public Map<String, String> getMatchCustomTerms() {
        List<TranslationKeywordResDTO> customTerms = this.getCustomTerms();
        Map<String, String> translationKeywordByOriginal = new HashMap<>();
        for (TranslationKeywordResDTO term : customTerms) {
            term.getOriginal().forEach(original -> {
                translationKeywordByOriginal.put(original, term.getTranslation());
            });
        }
        return translationKeywordByOriginal;
    }

    public List<TranslationKeywordResDTO> getTerms() {
        List<TranslationKeywordResDTO> terms = new ArrayList<>();
        terms.addAll(suggestedTerms);
        terms.addAll(customTerms);
        return terms;
    }
}

package com.patsnap.drafting.model.storage;

import com.patsnap.common.entity.EntityObject;

/**
 * Created by liyc on 2016/2/15.
 */
public class SignedUrl extends EntityObject {

    private static final long serialVersionUID = 6397327735794305489L;
    /**
     * subResource name needs to be signed, ex. s3 object key or uri path
     */
    private String subResource;

    /**
     * signed url
     */
    private String url;

    /**
     * sign result code, 200 - success 400 - bad request 500 - service
     * internal error
     */
    private int code = 200;

    /**
     * sign result message
     */
    private String msg;

    public String getSubResource() {
        return subResource;
    }

    public void setSubResource(String subResource) {
        this.subResource = subResource;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }
}

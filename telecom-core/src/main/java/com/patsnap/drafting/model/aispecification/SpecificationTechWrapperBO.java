package com.patsnap.drafting.model.aispecification;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

@Data
public class SpecificationTechWrapperBO {

    //技术问题
    @JsonProperty("technical_problem")
    private String technicalProblem;
    //技术手段
    @JsonProperty("technical_methods")
    private String technicalMethods;
    //技术功效
    @JsonProperty("benefit")
    private String benefit;
}

package com.patsnap.drafting.model.patent;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * 表示专利解决方案信息的类。
 */
@Data
public class PatentSolutionInfo {
    /** 解决方案类型 */
    @JsonProperty("SOLUTION_TYPE")
    private String solutionType;
    
    /** 解决方案 ID */
    @JsonProperty("SOLUTION_ID")
    private String solutionId;
    
    /** 专利号 */
    @JsonProperty("PN")
    private String pn;
    
    /** 标题 */
    @JsonProperty("TITLE")
    private String title;
    
    /** 摘要 */
    @JsonProperty("ABST")
    private String abst;
    
    /** 组织信息列表 */
    @JsonProperty("ORG_INFO")
    private List<OrgInfo> orgInfo;
}
package com.patsnap.drafting.model.aispecification;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@lombok.Builder
public class FigureDescriptionContentBO {

    @JsonProperty("figure_descriptions")
    @ApiModelProperty(value = "绘图说明列表")
    private List<FigureDescription> figureDescriptions;
}

package com.patsnap.drafting.model.share;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * AI任务分享链接数据传输对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("AI任务分享链接")
public class AiTaskShareLinkDTO {

    @ApiModelProperty("分享ID")
    private String shareId;

    @ApiModelProperty("资源类型")
    private String resourceType;

    @ApiModelProperty("资源ID")
    private String resourceId;

    @ApiModelProperty("是否公开分享")
    private Boolean publicShare;

    @ApiModelProperty("过期时间")
    private Long expiresAt;

    @ApiModelProperty("访问密码")
    private String password;

    @ApiModelProperty("角色")
    private String role;

    @ApiModelProperty("是否激活")
    private Boolean active;

    @ApiModelProperty("创建时间")
    private Long createdAt;

    @ApiModelProperty("创建人")
    private String createdBy;

    @ApiModelProperty("更新时间")
    private Long updatedAt;

    @ApiModelProperty("更新人")
    private String updatedBy;

    @ApiModelProperty("版本号")
    private Integer version;
} 
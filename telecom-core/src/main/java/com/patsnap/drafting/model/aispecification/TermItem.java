package com.patsnap.drafting.model.aispecification;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 标签列表项
 * 用于附图说明算法接口的标签参数
 * 
 * <AUTHOR> Assistant
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TermItem {

    /**
     * 编号
     */
    @JsonProperty("number")
    private String number;

    /**
     * 项目名称
     */
    @JsonProperty("item")
    private String item;
} 
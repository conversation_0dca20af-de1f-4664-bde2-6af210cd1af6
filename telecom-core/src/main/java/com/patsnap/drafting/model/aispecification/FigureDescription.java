package com.patsnap.drafting.model.aispecification;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FigureDescription {
    
    /**
     * 图号
     */
    @JsonProperty("number")
    private Integer number;
    
    /**
     * 图的描述
     */
    @JsonProperty("description")
    private String description;
}

package com.patsnap.drafting.model.aispecification;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.patsnap.drafting.request.aispecification.EmbodimentOutlineItem;
import lombok.Data;

import java.util.List;

/**
 * 说明书实施例大纲
 *
 * <AUTHOR>
 * @Date 2025/1/8 10:00
 */
@Data
public class SpecificationEmbodimentOutlineBO {

    // 实施例大纲列表
    @JsonProperty("outlines")
    private List<EmbodimentOutlineItem> outlines;
} 
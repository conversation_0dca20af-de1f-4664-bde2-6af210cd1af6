package com.patsnap.drafting.model.aispecification;

import com.patsnap.drafting.request.aispecification.Term;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@lombok.Builder
public class TermContentBO {

    @ApiModelProperty(value = "术语列表")
    private List<Term> terms;
}

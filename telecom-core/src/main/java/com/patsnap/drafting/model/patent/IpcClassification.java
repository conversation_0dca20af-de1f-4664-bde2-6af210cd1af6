package com.patsnap.drafting.model.patent;

import org.apache.commons.lang3.StringUtils;

import lombok.Data;

@Data
public class IpcClassification {

    private String ipc;
    private ClassificationDesc description;
    private IpcClassification classificationIpc;

    public String getDescriptionText() {
        if (description == null) {
            return classificationIpc == null ?
                    StringUtils.EMPTY : classificationIpc.getDescriptionText();
        }

        return description.getText();
    }
}

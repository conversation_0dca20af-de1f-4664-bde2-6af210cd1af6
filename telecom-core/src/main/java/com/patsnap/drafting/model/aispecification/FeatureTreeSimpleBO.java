package com.patsnap.drafting.model.aispecification;

import com.patsnap.drafting.enums.prompt.ClaimTypeEnum;

import lombok.Data;

/**
 * 权利要求中提取的简单对象，主要是映射到prompt中的参数
 */
@Data
public class FeatureTreeSimpleBO {

    /**
     * 权利要求编号
     */
    private String num;

    /**
     * 特征词
     */
    private String concept;

    /**
     * 特征词所在的特征语句，如果村子多个特征中，我们取第一个
     */
    private String feature;

    /**
     * 权利要求类型
     */
    private ClaimTypeEnum claimType;




}

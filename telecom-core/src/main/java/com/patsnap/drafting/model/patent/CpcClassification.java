package com.patsnap.drafting.model.patent;

import org.apache.commons.lang3.StringUtils;

import lombok.Data;

@Data
public class CpcClassification {

    private String cpc;
    private ClassificationDesc description;
    private CpcClassification classificationCpc;


    public String getDescriptionText() {
        if (description == null) {
            return StringUtils.EMPTY;
        }

        return description.getText();
    }

}

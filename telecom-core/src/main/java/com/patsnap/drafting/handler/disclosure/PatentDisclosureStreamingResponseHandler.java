package com.patsnap.drafting.handler.disclosure;

import com.patsnap.common.request.CorrelationIdHolder;
import com.patsnap.core.common.copilot.streaming.chat.AssistantMessage;
import com.patsnap.core.common.copilot.streaming.handler.StringStreamingResponseHandler;
import com.patsnap.core.common.copilot.streaming.model.GptResponseDTO;
import com.patsnap.core.common.copilot.streaming.response.Response;
import com.patsnap.drafting.util.ReferenceReplacer;
import com.patsnap.spring.boot.autoconfigure.web.entity.CommonResponse;

import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.FluxSink;

/**
 * 交底书撰写正文流式输出处理器
 *
 * <AUTHOR>
 * @date 2024/08/23
 */
@Slf4j
public class PatentDisclosureStreamingResponseHandler extends StringStreamingResponseHandler {

    private final String correlationId;
    
    private final ReferenceReplacer referenceReplacer;

    public PatentDisclosureStreamingResponseHandler(FluxSink<CommonResponse<GptResponseDTO<String>>> sink, String input,
            ReferenceReplacer referenceReplacer) {
        super(sink);
        this.correlationId = CorrelationIdHolder.get();
        this.referenceReplacer = referenceReplacer;
    }

    @Override
    public void onError(Throwable error) {
        CorrelationIdHolder.set(correlationId);
        super.onError(error);
    }

    @Override
    public void onComplete(Response<AssistantMessage> response) {
        CorrelationIdHolder.set(correlationId);
        log.info("PatentDisclosureStreamingResponseHandler onComplete response: {}", response);
        super.onComplete(response);
    }

    @Override
    public void onCancel() {
        super.onCancel();
    }
    
    @Override
    protected String convertContent(String content) {
        return referenceReplacer.process(content);
    }
}

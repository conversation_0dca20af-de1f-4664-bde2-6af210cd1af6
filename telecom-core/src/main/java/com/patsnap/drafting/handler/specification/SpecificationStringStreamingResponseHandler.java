package com.patsnap.drafting.handler.specification;

import com.patsnap.core.common.copilot.streaming.handler.StringStreamingResponseHandler;
import com.patsnap.core.common.copilot.streaming.model.GptResponseDTO;
import com.patsnap.spring.boot.autoconfigure.web.entity.CommonResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import reactor.core.publisher.FluxSink;

/**
 * desc
 *
 * <AUTHOR>
 * @Date 2025/5/9 10:00
 */
@Slf4j
public class SpecificationStringStreamingResponseHandler extends StringStreamingResponseHandler {

    public SpecificationStringStreamingResponseHandler(FluxSink<CommonResponse<GptResponseDTO<String>>> sink) {
        super(sink);
    }

    @Override
    protected String convertContent(String content) {
        // 替换掉content里面所有的#特殊字符,说明书在最后生成"发明内容"和"具体实施方式"时，不希望文本里出现#
        content = StringUtils.isBlank(content) ? content : content.replaceAll("#", "");
        return content;
    }
}

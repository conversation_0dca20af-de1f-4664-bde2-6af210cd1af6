package com.patsnap.drafting.handler.specification;

import com.patsnap.core.common.copilot.streaming.handler.StringStreamingResponseHandler;
import com.patsnap.core.common.copilot.streaming.model.GptResponseDTO;
import com.patsnap.spring.boot.autoconfigure.web.entity.CommonResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import reactor.core.publisher.FluxSink;

/**
 * 实施例内容流式响应处理器
 * 用于处理AI生成的实施例内容，移除开头的实施例标题
 *
 * <AUTHOR>
 * @Date 2025/1/15 16:00
 */
@Slf4j
public class SpecificationEmbodimentStringStreamingResponseHandler extends StringStreamingResponseHandler {

    public SpecificationEmbodimentStringStreamingResponseHandler(FluxSink<CommonResponse<GptResponseDTO<String>>> sink) {
        super(sink);
    }

    @Override
    protected String convertContent(String content) {
        // 移除实施例内容开头的实施例标记，兼容中英文冒号
        content = StringUtils.isBlank(content) ? content :
                content.replaceFirst("^实施例\\d+(?:[：:]|(?:\\r?\\n){1,2})", "");
        return content;
    }
} 
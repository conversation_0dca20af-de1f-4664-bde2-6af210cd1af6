package com.patsnap.drafting.constants;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.HashMap;

/**
 * 导出常量类
 *
 * <AUTHOR>
 * @Date 2025/4/14 15:05
 */
public class ExportConstant {

    /** 导出S3路径 */
    public static final String EXPORT_S3_PATH = "ai_drafting/%s/%s/%s";

    /** word文档里logo图片的位置 */
    public static final String EXPORT_TEMPLATE_PATH = "classpath:template/export/";

    // 不同内容的字体大小
    public static final int COVER_TITLE_FONT_SIZE = 26;  // word中的一号字体大小
    public static final int FIRST_TITLE_FONT_SIZE = 16;  // word中的三号字体大小
    public static final int TEXT_FONT_SIZE = 10;         // word中的五号字体大小

    // 表格表头单元格颜色,表格边界颜色
    public static final String TABLE_CELL_COLOR_BLUE = "DAE9F7";
    public static final String TABLE_BORDER_COLOR_BLUE = "ACB9CA";

    // 表格的行高
    public static final int TABLE_ROW_HEIGHT = 500;

    // 一些文案模板
    public static final String SUB_TITLE_TEMPLATE_CN = "完成了%s次检索，详细对比%s篇现有技术，找出了最接近的对比文献%s篇，次接近的对比文献%s篇";
    public static final String SUB_TITLE_TEMPLATE_EN = "After %s search rounds and comparison of %s references, the system identified the most relevant reference as (%s), with an additional (%s) references selected as the next closest matches.";

    // 专利查新的技术特征的a,b,c,d分类
    public static final List<String> TECHNICAL_FEATURE_CATEGORY = Arrays.asList("a", "b", "c", "d","e", "f", "g", "h", "i", "j", "k", "l", "m", "n", "o", "p", "q", "r", "s", "t", "u", "v", "w", "x", "y", "z");
    public static final List<String> TECHNICAL_FEATURE_CATEGORY_COLOR = Arrays.asList("E3F1DE", "DDEBF8", "D4F0F6", "FAECDA","E2D5ED", "F8DDE0", "E3F1DE", "DDEBF8", "D4F0F6", "FAECDA", "E2D5ED", "F8DDE0", "E3F1DE", "DDEBF8", "D4F0F6", "FAECDA", "E2D5ED", "F8DDE0", "E3F1DE", "DDEBF8", "D4F0F6", "FAECDA", "E2D5ED", "F8DDE0", "E3F1DE", "DDEBF8");

    /** 国家代码和国家名称映射表 (中文环境) */
    public static final Map<String, String> COUNTRY_CODE_NAME_MAP_CN;

    /** 国家代码和国家名称映射表 (英文环境) */
    public static final Map<String, String> COUNTRY_CODE_NAME_MAP_EN;

    /** 国家代码和国家名称映射表 (日语环境) */
    public static final Map<String, String> COUNTRY_CODE_NAME_MAP_JP;

    /** 简单法律状态映射表 (中文环境) */
    public static final Map<Integer, String> SIMPLE_LEGAL_STATUS_MAP_CN;

    /** 简单法律状态映射表 (英文环境) */
    public static final Map<Integer, String> SIMPLE_LEGAL_STATUS_MAP_EN;

    /** 简单法律状态映射表 (日语环境) */
    public static final Map<Integer, String> SIMPLE_LEGAL_STATUS_MAP_JP;

    static {
        COUNTRY_CODE_NAME_MAP_CN = new HashMap<>();
        COUNTRY_CODE_NAME_MAP_CN.put("AD", "安道尔");
        COUNTRY_CODE_NAME_MAP_CN.put("AE", "阿拉伯联合酋长国");
        COUNTRY_CODE_NAME_MAP_CN.put("AF", "阿富汗");
        COUNTRY_CODE_NAME_MAP_CN.put("AG", "安提瓜和巴布达");
        COUNTRY_CODE_NAME_MAP_CN.put("AI", "安圭拉");
        COUNTRY_CODE_NAME_MAP_CN.put("AL", "阿尔巴尼亚");
        COUNTRY_CODE_NAME_MAP_CN.put("AM", "亚美尼亚");
        COUNTRY_CODE_NAME_MAP_CN.put("AN", "荷属安的列斯群岛");
        COUNTRY_CODE_NAME_MAP_CN.put("AO", "安哥拉");
        COUNTRY_CODE_NAME_MAP_CN.put("AP", "非洲地区工业产权组织");
        COUNTRY_CODE_NAME_MAP_CN.put("AQ", "南极洲");
        COUNTRY_CODE_NAME_MAP_CN.put("AR", "阿根廷");
        COUNTRY_CODE_NAME_MAP_CN.put("AS", "美属萨摩亚");
        COUNTRY_CODE_NAME_MAP_CN.put("AT", "奥地利");
        COUNTRY_CODE_NAME_MAP_CN.put("AU", "澳大利亚");
        COUNTRY_CODE_NAME_MAP_CN.put("AW", "阿鲁巴");
        COUNTRY_CODE_NAME_MAP_CN.put("AX", "奥兰");
        COUNTRY_CODE_NAME_MAP_CN.put("AZ", "阿塞拜疆");
        COUNTRY_CODE_NAME_MAP_CN.put("BA", "波黑");
        COUNTRY_CODE_NAME_MAP_CN.put("BB", "巴巴多斯");
        COUNTRY_CODE_NAME_MAP_CN.put("BD", "孟加拉国");
        COUNTRY_CODE_NAME_MAP_CN.put("BE", "比利时");
        COUNTRY_CODE_NAME_MAP_CN.put("BF", "布基纳法索");
        COUNTRY_CODE_NAME_MAP_CN.put("BG", "保加利亚");
        COUNTRY_CODE_NAME_MAP_CN.put("BH", "巴林");
        COUNTRY_CODE_NAME_MAP_CN.put("BI", "布隆迪");
        COUNTRY_CODE_NAME_MAP_CN.put("BJ", "贝宁");
        COUNTRY_CODE_NAME_MAP_CN.put("BL", "圣巴泰勒米");
        COUNTRY_CODE_NAME_MAP_CN.put("BM", "百慕大");
        COUNTRY_CODE_NAME_MAP_CN.put("BN", "文莱");
        COUNTRY_CODE_NAME_MAP_CN.put("BO", "玻利维亚");
        COUNTRY_CODE_NAME_MAP_CN.put("BQ", "加勒比荷兰");
        COUNTRY_CODE_NAME_MAP_CN.put("BR", "巴西");
        COUNTRY_CODE_NAME_MAP_CN.put("BS", "巴哈马");
        COUNTRY_CODE_NAME_MAP_CN.put("BT", "不丹");
        COUNTRY_CODE_NAME_MAP_CN.put("BV", "布韦岛");
        COUNTRY_CODE_NAME_MAP_CN.put("BW", "博茨瓦纳");
        COUNTRY_CODE_NAME_MAP_CN.put("BX", "比荷卢经济联盟");
        COUNTRY_CODE_NAME_MAP_CN.put("BY", "白俄罗斯");
        COUNTRY_CODE_NAME_MAP_CN.put("BZ", "伯利兹");
        COUNTRY_CODE_NAME_MAP_CN.put("CA", "加拿大");
        COUNTRY_CODE_NAME_MAP_CN.put("CC", "科科斯（基林）群岛");
        COUNTRY_CODE_NAME_MAP_CN.put("CD", "刚果(金)");
        COUNTRY_CODE_NAME_MAP_CN.put("CF", "中非");
        COUNTRY_CODE_NAME_MAP_CN.put("CG", "刚果(布)");
        COUNTRY_CODE_NAME_MAP_CN.put("CH", "瑞士");
        COUNTRY_CODE_NAME_MAP_CN.put("CI", "科特迪瓦");
        COUNTRY_CODE_NAME_MAP_CN.put("CK", "库克群岛");
        COUNTRY_CODE_NAME_MAP_CN.put("CL", "智利");
        COUNTRY_CODE_NAME_MAP_CN.put("CM", "喀麦隆");
        COUNTRY_CODE_NAME_MAP_CN.put("CN", "中国");
        COUNTRY_CODE_NAME_MAP_CN.put("CO", "哥伦比亚");
        COUNTRY_CODE_NAME_MAP_CN.put("CS", "捷克和斯洛伐克共和国");
        COUNTRY_CODE_NAME_MAP_CN.put("CR", "哥斯达黎加");
        COUNTRY_CODE_NAME_MAP_CN.put("CU", "古巴");
        COUNTRY_CODE_NAME_MAP_CN.put("CV", "佛得角");
        COUNTRY_CODE_NAME_MAP_CN.put("CW", "库拉索");
        COUNTRY_CODE_NAME_MAP_CN.put("CX", "圣诞岛");
        COUNTRY_CODE_NAME_MAP_CN.put("CY", "塞浦路斯");
        COUNTRY_CODE_NAME_MAP_CN.put("CZ", "捷克");
        COUNTRY_CODE_NAME_MAP_CN.put("DD", "东德");
        COUNTRY_CODE_NAME_MAP_CN.put("DE", "德国");
        COUNTRY_CODE_NAME_MAP_CN.put("DJ", "吉布提");
        COUNTRY_CODE_NAME_MAP_CN.put("DK", "丹麦");
        COUNTRY_CODE_NAME_MAP_CN.put("DM", "多米尼克");
        COUNTRY_CODE_NAME_MAP_CN.put("DO", "多米尼加共和国");
        COUNTRY_CODE_NAME_MAP_CN.put("DZ", "阿尔及利亚");
        COUNTRY_CODE_NAME_MAP_CN.put("EA", "欧亚专利局");
        COUNTRY_CODE_NAME_MAP_CN.put("EC", "厄瓜多尔");
        COUNTRY_CODE_NAME_MAP_CN.put("EE", "爱沙尼亚");
        COUNTRY_CODE_NAME_MAP_CN.put("EG", "埃及");
        COUNTRY_CODE_NAME_MAP_CN.put("EH", "阿拉伯撒哈拉民主共和国");
        COUNTRY_CODE_NAME_MAP_CN.put("EM", "欧盟知识产权局");
        COUNTRY_CODE_NAME_MAP_CN.put("EP", "欧洲专利局");
        COUNTRY_CODE_NAME_MAP_CN.put("ER", "厄立特里亚");
        COUNTRY_CODE_NAME_MAP_CN.put("ES", "西班牙");
        COUNTRY_CODE_NAME_MAP_CN.put("ES-CT", "加泰罗尼亚");
        COUNTRY_CODE_NAME_MAP_CN.put("ET", "埃塞俄比亚");
        COUNTRY_CODE_NAME_MAP_CN.put("EU", "欧盟");
        COUNTRY_CODE_NAME_MAP_CN.put("FI", "芬兰");
        COUNTRY_CODE_NAME_MAP_CN.put("FJ", "斐济");
        COUNTRY_CODE_NAME_MAP_CN.put("FK", "福克兰群岛");
        COUNTRY_CODE_NAME_MAP_CN.put("FM", "密克罗尼西亚联邦");
        COUNTRY_CODE_NAME_MAP_CN.put("FO", "法罗群岛");
        COUNTRY_CODE_NAME_MAP_CN.put("FR", "法国");
        COUNTRY_CODE_NAME_MAP_CN.put("GA", "加蓬");
        COUNTRY_CODE_NAME_MAP_CN.put("GB", "英国");
        COUNTRY_CODE_NAME_MAP_CN.put("GC", "海湾地区阿拉伯国家合作委员会专利局");
        COUNTRY_CODE_NAME_MAP_CN.put("GD", "格林纳达");
        COUNTRY_CODE_NAME_MAP_CN.put("GE", "格鲁吉亚");
        COUNTRY_CODE_NAME_MAP_CN.put("GF", "法属圭亚那");
        COUNTRY_CODE_NAME_MAP_CN.put("GG", "根西");
        COUNTRY_CODE_NAME_MAP_CN.put("GH", "加纳");
        COUNTRY_CODE_NAME_MAP_CN.put("GI", "直布罗陀");
        COUNTRY_CODE_NAME_MAP_CN.put("GL", "格陵兰");
        COUNTRY_CODE_NAME_MAP_CN.put("GM", "冈比亚");
        COUNTRY_CODE_NAME_MAP_CN.put("GN", "几内亚");
        COUNTRY_CODE_NAME_MAP_CN.put("GP", "瓜德罗普");
        COUNTRY_CODE_NAME_MAP_CN.put("GQ", "赤道几内亚");
        COUNTRY_CODE_NAME_MAP_CN.put("GR", "希腊");
        COUNTRY_CODE_NAME_MAP_CN.put("GS", "南乔治亚和南桑威奇群岛");
        COUNTRY_CODE_NAME_MAP_CN.put("GT", "危地马拉");
        COUNTRY_CODE_NAME_MAP_CN.put("GU", "关岛");
        COUNTRY_CODE_NAME_MAP_CN.put("GW", "几内亚比绍");
        COUNTRY_CODE_NAME_MAP_CN.put("GY", "圭亚那");
        COUNTRY_CODE_NAME_MAP_CN.put("HK", "中国香港");
        COUNTRY_CODE_NAME_MAP_CN.put("HM", "赫德岛和麦克唐纳群岛");
        COUNTRY_CODE_NAME_MAP_CN.put("HN", "洪都拉斯");
        COUNTRY_CODE_NAME_MAP_CN.put("HR", "克罗地亚");
        COUNTRY_CODE_NAME_MAP_CN.put("HT", "海地");
        COUNTRY_CODE_NAME_MAP_CN.put("HU", "匈牙利");
        COUNTRY_CODE_NAME_MAP_CN.put("IB", "世界知识产权国际局");
        COUNTRY_CODE_NAME_MAP_CN.put("ID", "印度尼西亚");
        COUNTRY_CODE_NAME_MAP_CN.put("IE", "爱尔兰");
        COUNTRY_CODE_NAME_MAP_CN.put("IL", "以色列");
        COUNTRY_CODE_NAME_MAP_CN.put("IM", "马恩岛");
        COUNTRY_CODE_NAME_MAP_CN.put("IN", "印度");
        COUNTRY_CODE_NAME_MAP_CN.put("IO", "英属印度洋领地");
        COUNTRY_CODE_NAME_MAP_CN.put("IQ", "伊拉克");
        COUNTRY_CODE_NAME_MAP_CN.put("IR", "伊朗");
        COUNTRY_CODE_NAME_MAP_CN.put("IS", "冰岛");
        COUNTRY_CODE_NAME_MAP_CN.put("IT", "意大利");
        COUNTRY_CODE_NAME_MAP_CN.put("JE", "泽西");
        COUNTRY_CODE_NAME_MAP_CN.put("JM", "牙买加");
        COUNTRY_CODE_NAME_MAP_CN.put("JO", "约旦");
        COUNTRY_CODE_NAME_MAP_CN.put("JP", "日本");
        COUNTRY_CODE_NAME_MAP_CN.put("KE", "肯尼亚");
        COUNTRY_CODE_NAME_MAP_CN.put("KG", "吉尔吉斯斯坦");
        COUNTRY_CODE_NAME_MAP_CN.put("KH", "柬埔寨");
        COUNTRY_CODE_NAME_MAP_CN.put("KI", "基里巴斯");
        COUNTRY_CODE_NAME_MAP_CN.put("KM", "科摩罗");
        COUNTRY_CODE_NAME_MAP_CN.put("KN", "圣基茨和尼维斯");
        COUNTRY_CODE_NAME_MAP_CN.put("KP", "朝鲜");
        COUNTRY_CODE_NAME_MAP_CN.put("KR", "韩国");
        COUNTRY_CODE_NAME_MAP_CN.put("KW", "科威特");
        COUNTRY_CODE_NAME_MAP_CN.put("KY", "开曼群岛");
        COUNTRY_CODE_NAME_MAP_CN.put("KZ", "哈萨克斯坦");
        COUNTRY_CODE_NAME_MAP_CN.put("LA", "老挝");
        COUNTRY_CODE_NAME_MAP_CN.put("LB", "黎巴嫩");
        COUNTRY_CODE_NAME_MAP_CN.put("LC", "圣卢西亚");
        COUNTRY_CODE_NAME_MAP_CN.put("LI", "列支敦士登");
        COUNTRY_CODE_NAME_MAP_CN.put("LK", "斯里兰卡");
        COUNTRY_CODE_NAME_MAP_CN.put("LR", "利比里亚");
        COUNTRY_CODE_NAME_MAP_CN.put("LS", "莱索托");
        COUNTRY_CODE_NAME_MAP_CN.put("LT", "立陶宛");
        COUNTRY_CODE_NAME_MAP_CN.put("LU", "卢森堡");
        COUNTRY_CODE_NAME_MAP_CN.put("LV", "拉脱维亚");
        COUNTRY_CODE_NAME_MAP_CN.put("LY", "利比亚");
        COUNTRY_CODE_NAME_MAP_CN.put("MA", "摩洛哥");
        COUNTRY_CODE_NAME_MAP_CN.put("MC", "摩纳哥");
        COUNTRY_CODE_NAME_MAP_CN.put("MD", "摩尔多瓦");
        COUNTRY_CODE_NAME_MAP_CN.put("ME", "黑山共和国");
        COUNTRY_CODE_NAME_MAP_CN.put("MF", "法属圣马丁");
        COUNTRY_CODE_NAME_MAP_CN.put("MG", "马达加斯加");
        COUNTRY_CODE_NAME_MAP_CN.put("MH", "马绍尔群岛");
        COUNTRY_CODE_NAME_MAP_CN.put("MK", "北马其顿共和国");
        COUNTRY_CODE_NAME_MAP_CN.put("ML", "马里");
        COUNTRY_CODE_NAME_MAP_CN.put("MM", "缅甸");
        COUNTRY_CODE_NAME_MAP_CN.put("MN", "蒙古");
        COUNTRY_CODE_NAME_MAP_CN.put("MO", "中国澳门");
        COUNTRY_CODE_NAME_MAP_CN.put("MP", "北马里亚纳群岛");
        COUNTRY_CODE_NAME_MAP_CN.put("MQ", "马提尼克");
        COUNTRY_CODE_NAME_MAP_CN.put("MR", "毛里塔尼亚");
        COUNTRY_CODE_NAME_MAP_CN.put("MS", "蒙特塞拉特");
        COUNTRY_CODE_NAME_MAP_CN.put("MT", "马耳他");
        COUNTRY_CODE_NAME_MAP_CN.put("MU", "毛里求斯");
        COUNTRY_CODE_NAME_MAP_CN.put("MV", "马尔代夫");
        COUNTRY_CODE_NAME_MAP_CN.put("MW", "马拉维");
        COUNTRY_CODE_NAME_MAP_CN.put("MX", "墨西哥");
        COUNTRY_CODE_NAME_MAP_CN.put("MY", "马来西亚");
        COUNTRY_CODE_NAME_MAP_CN.put("MZ", "莫桑比克");
        COUNTRY_CODE_NAME_MAP_CN.put("NA", "纳米比亚");
        COUNTRY_CODE_NAME_MAP_CN.put("NC", "新喀里多尼亚");
        COUNTRY_CODE_NAME_MAP_CN.put("NE", "尼日尔");
        COUNTRY_CODE_NAME_MAP_CN.put("NF", "诺福克岛");
        COUNTRY_CODE_NAME_MAP_CN.put("NG", "尼日利亚");
        COUNTRY_CODE_NAME_MAP_CN.put("NI", "尼加拉瓜");
        COUNTRY_CODE_NAME_MAP_CN.put("NL", "荷兰");
        COUNTRY_CODE_NAME_MAP_CN.put("NO", "挪威");
        COUNTRY_CODE_NAME_MAP_CN.put("NP", "尼泊尔");
        COUNTRY_CODE_NAME_MAP_CN.put("NR", "瑙鲁");
        COUNTRY_CODE_NAME_MAP_CN.put("NU", "纽埃");
        COUNTRY_CODE_NAME_MAP_CN.put("NZ", "新西兰");
        COUNTRY_CODE_NAME_MAP_CN.put("OA", "非洲知识产权组织");
        COUNTRY_CODE_NAME_MAP_CN.put("OM", "阿曼");
        COUNTRY_CODE_NAME_MAP_CN.put("PA", "巴拿马");
        COUNTRY_CODE_NAME_MAP_CN.put("PE", "秘鲁");
        COUNTRY_CODE_NAME_MAP_CN.put("PF", "法属波利尼西亚");
        COUNTRY_CODE_NAME_MAP_CN.put("PG", "巴布亚新几内亚");
        COUNTRY_CODE_NAME_MAP_CN.put("PH", "菲律宾");
        COUNTRY_CODE_NAME_MAP_CN.put("PK", "巴基斯坦");
        COUNTRY_CODE_NAME_MAP_CN.put("PL", "波兰");
        COUNTRY_CODE_NAME_MAP_CN.put("PM", "圣皮埃尔和密克隆");
        COUNTRY_CODE_NAME_MAP_CN.put("PN", "皮特凯恩群岛");
        COUNTRY_CODE_NAME_MAP_CN.put("PR", "波多黎各");
        COUNTRY_CODE_NAME_MAP_CN.put("PS", "巴勒斯坦");
        COUNTRY_CODE_NAME_MAP_CN.put("PT", "葡萄牙");
        COUNTRY_CODE_NAME_MAP_CN.put("PW", "帕劳");
        COUNTRY_CODE_NAME_MAP_CN.put("PY", "巴拉圭");
        COUNTRY_CODE_NAME_MAP_CN.put("QA", "卡塔尔");
        COUNTRY_CODE_NAME_MAP_CN.put("QW", "卢旺达");
        COUNTRY_CODE_NAME_MAP_CN.put("QZ", "共同体植物品种局（欧共体）");
        COUNTRY_CODE_NAME_MAP_CN.put("RE", "留尼汪");
        COUNTRY_CODE_NAME_MAP_CN.put("RO", "罗马尼亚");
        COUNTRY_CODE_NAME_MAP_CN.put("RS", "塞尔维亚共和国");
        COUNTRY_CODE_NAME_MAP_CN.put("RU", "俄罗斯");
        COUNTRY_CODE_NAME_MAP_CN.put("RW", "卢旺达");
        COUNTRY_CODE_NAME_MAP_CN.put("SA", "沙特阿拉伯");
        COUNTRY_CODE_NAME_MAP_CN.put("SB", "所罗门群岛");
        COUNTRY_CODE_NAME_MAP_CN.put("SC", "塞舌尔");
        COUNTRY_CODE_NAME_MAP_CN.put("SD", "苏丹");
        COUNTRY_CODE_NAME_MAP_CN.put("SE", "瑞典");
        COUNTRY_CODE_NAME_MAP_CN.put("SG", "新加坡");
        COUNTRY_CODE_NAME_MAP_CN.put("SH", "圣赫勒拿");
        COUNTRY_CODE_NAME_MAP_CN.put("SI", "斯洛文尼亚");
        COUNTRY_CODE_NAME_MAP_CN.put("SJ", "挪威 斯瓦尔巴群岛和扬马延岛");
        COUNTRY_CODE_NAME_MAP_CN.put("SK", "斯洛伐克");
        COUNTRY_CODE_NAME_MAP_CN.put("SL", "塞拉利昂");
        COUNTRY_CODE_NAME_MAP_CN.put("SM", "圣马力诺");
        COUNTRY_CODE_NAME_MAP_CN.put("SN", "塞内加尔");
        COUNTRY_CODE_NAME_MAP_CN.put("SO", "索马里");
        COUNTRY_CODE_NAME_MAP_CN.put("SR", "苏里南");
        COUNTRY_CODE_NAME_MAP_CN.put("SS", "南苏丹");
        COUNTRY_CODE_NAME_MAP_CN.put("ST", "圣多美和普林西比");
        COUNTRY_CODE_NAME_MAP_CN.put("SU", "前苏联");
        COUNTRY_CODE_NAME_MAP_CN.put("SV", "萨尔瓦多");
        COUNTRY_CODE_NAME_MAP_CN.put("SX", "荷属圣马丁");
        COUNTRY_CODE_NAME_MAP_CN.put("SY", "叙利亚");
        COUNTRY_CODE_NAME_MAP_CN.put("SZ", "斯威士兰");
        COUNTRY_CODE_NAME_MAP_CN.put("TC", "特克斯和凯科斯群岛");
        COUNTRY_CODE_NAME_MAP_CN.put("TD", "乍得");
        COUNTRY_CODE_NAME_MAP_CN.put("TF", "法属南部领地");
        COUNTRY_CODE_NAME_MAP_CN.put("TG", "多哥");
        COUNTRY_CODE_NAME_MAP_CN.put("TH", "泰国");
        COUNTRY_CODE_NAME_MAP_CN.put("TJ", "塔吉克斯坦");
        COUNTRY_CODE_NAME_MAP_CN.put("TK", "托克劳");
        COUNTRY_CODE_NAME_MAP_CN.put("TL", "东帝汶");
        COUNTRY_CODE_NAME_MAP_CN.put("TM", "土库曼斯坦");
        COUNTRY_CODE_NAME_MAP_CN.put("TN", "突尼斯");
        COUNTRY_CODE_NAME_MAP_CN.put("TO", "汤加");
        COUNTRY_CODE_NAME_MAP_CN.put("TR", "土耳其");
        COUNTRY_CODE_NAME_MAP_CN.put("TT", "特立尼达和多巴哥");
        COUNTRY_CODE_NAME_MAP_CN.put("TV", "图瓦卢");
        COUNTRY_CODE_NAME_MAP_CN.put("TW", "中国台湾");
        COUNTRY_CODE_NAME_MAP_CN.put("TZ", "坦桑尼亚");
        COUNTRY_CODE_NAME_MAP_CN.put("UA", "乌克兰");
        COUNTRY_CODE_NAME_MAP_CN.put("UG", "乌干达");
        COUNTRY_CODE_NAME_MAP_CN.put("UM", "美国本土外小岛屿");
        COUNTRY_CODE_NAME_MAP_CN.put("US", "美国");
        COUNTRY_CODE_NAME_MAP_CN.put("UY", "乌拉圭");
        COUNTRY_CODE_NAME_MAP_CN.put("UZ", "乌兹别克斯坦");
        COUNTRY_CODE_NAME_MAP_CN.put("VA", "梵蒂冈");
        COUNTRY_CODE_NAME_MAP_CN.put("VC", "圣文森特和格林纳丁斯");
        COUNTRY_CODE_NAME_MAP_CN.put("VE", "委内瑞拉");
        COUNTRY_CODE_NAME_MAP_CN.put("VG", "英属维尔京群岛");
        COUNTRY_CODE_NAME_MAP_CN.put("VI", "美属维尔京群岛");
        COUNTRY_CODE_NAME_MAP_CN.put("VN", "越南");
        COUNTRY_CODE_NAME_MAP_CN.put("VU", "瓦努阿图");
        COUNTRY_CODE_NAME_MAP_CN.put("WF", "瓦利斯和富图纳");
        COUNTRY_CODE_NAME_MAP_CN.put("WO", "世界知识产权组织");
        COUNTRY_CODE_NAME_MAP_CN.put("WS", "萨摩亚");
        COUNTRY_CODE_NAME_MAP_CN.put("XK", "科索沃");
        COUNTRY_CODE_NAME_MAP_CN.put("XN", "北欧专利局");
        COUNTRY_CODE_NAME_MAP_CN.put("XU", "国际植物新品种保护联盟");
        COUNTRY_CODE_NAME_MAP_CN.put("XV", "维谢格拉德专利局");
        COUNTRY_CODE_NAME_MAP_CN.put("YE", "也门");
        COUNTRY_CODE_NAME_MAP_CN.put("YT", "马约特");
        COUNTRY_CODE_NAME_MAP_CN.put("YU", "南斯拉夫");
        COUNTRY_CODE_NAME_MAP_CN.put("ZA", "南非");
        COUNTRY_CODE_NAME_MAP_CN.put("ZM", "赞比亚");
        COUNTRY_CODE_NAME_MAP_CN.put("ZN", "桑给巴尔");
        COUNTRY_CODE_NAME_MAP_CN.put("ZR", "扎伊尔");
        COUNTRY_CODE_NAME_MAP_CN.put("ZW", "津巴布韦");
        COUNTRY_CODE_NAME_MAP_CN.put("others", "其他");

        COUNTRY_CODE_NAME_MAP_EN = new HashMap<>();
        COUNTRY_CODE_NAME_MAP_EN.put("AD", "Andorra");
        COUNTRY_CODE_NAME_MAP_EN.put("AE", "United Arab Emirates");
        COUNTRY_CODE_NAME_MAP_EN.put("AF", "Afghanistan");
        COUNTRY_CODE_NAME_MAP_EN.put("AG", "Antigua and Barbuda");
        COUNTRY_CODE_NAME_MAP_EN.put("AI", "Anguilla");
        COUNTRY_CODE_NAME_MAP_EN.put("AL", "Albania");
        COUNTRY_CODE_NAME_MAP_EN.put("AM", "Armenia");
        COUNTRY_CODE_NAME_MAP_EN.put("AN", "Netheriands Antilles");
        COUNTRY_CODE_NAME_MAP_EN.put("AO", "Angola");
        COUNTRY_CODE_NAME_MAP_EN.put("AP", "ARIPO");
        COUNTRY_CODE_NAME_MAP_EN.put("AQ", "Antarctica");
        COUNTRY_CODE_NAME_MAP_EN.put("AR", "Argentina");
        COUNTRY_CODE_NAME_MAP_EN.put("AS", "American Samoa");
        COUNTRY_CODE_NAME_MAP_EN.put("AT", "Austria");
        COUNTRY_CODE_NAME_MAP_EN.put("AU", "Australia");
        COUNTRY_CODE_NAME_MAP_EN.put("AW", "Aruba");
        COUNTRY_CODE_NAME_MAP_EN.put("AX", "Aland Islands");
        COUNTRY_CODE_NAME_MAP_EN.put("AZ", "Azerbaijan");
        COUNTRY_CODE_NAME_MAP_EN.put("BA", "Bosnia and Herzegovina");
        COUNTRY_CODE_NAME_MAP_EN.put("BB", "Barbados");
        COUNTRY_CODE_NAME_MAP_EN.put("BD", "Bangladesh");
        COUNTRY_CODE_NAME_MAP_EN.put("BE", "Belgium");
        COUNTRY_CODE_NAME_MAP_EN.put("BF", "Burkina Faso");
        COUNTRY_CODE_NAME_MAP_EN.put("BG", "Bulgaria");
        COUNTRY_CODE_NAME_MAP_EN.put("BH", "Bahrain");
        COUNTRY_CODE_NAME_MAP_EN.put("BI", "Burundi");
        COUNTRY_CODE_NAME_MAP_EN.put("BJ", "Benin");
        COUNTRY_CODE_NAME_MAP_EN.put("BL", "Saint Barthelemy");
        COUNTRY_CODE_NAME_MAP_EN.put("BM", "Bermuda");
        COUNTRY_CODE_NAME_MAP_EN.put("BN", "Brunei Darussalam");
        COUNTRY_CODE_NAME_MAP_EN.put("BO", "Bolivia");
        COUNTRY_CODE_NAME_MAP_EN.put("BQ", "Caribbean Netherlands");
        COUNTRY_CODE_NAME_MAP_EN.put("BR", "Brazil");
        COUNTRY_CODE_NAME_MAP_EN.put("BS", "Bahamas");
        COUNTRY_CODE_NAME_MAP_EN.put("BT", "Bhutan");
        COUNTRY_CODE_NAME_MAP_EN.put("BV", "Bouvet Island");
        COUNTRY_CODE_NAME_MAP_EN.put("BW", "Botswana");
        COUNTRY_CODE_NAME_MAP_EN.put("BX", "Benelux");
        COUNTRY_CODE_NAME_MAP_EN.put("BY", "Belarus");
        COUNTRY_CODE_NAME_MAP_EN.put("BZ", "Belize");
        COUNTRY_CODE_NAME_MAP_EN.put("CA", "Canada");
        COUNTRY_CODE_NAME_MAP_EN.put("CC", "Cocos (Keeling) Islands");
        COUNTRY_CODE_NAME_MAP_EN.put("CD", "Democratic Republic of the Congo");
        COUNTRY_CODE_NAME_MAP_EN.put("CF", "Central African Republic");
        COUNTRY_CODE_NAME_MAP_EN.put("CG", "Congo");
        COUNTRY_CODE_NAME_MAP_EN.put("CH", "Switzerland");
        COUNTRY_CODE_NAME_MAP_EN.put("CI", "Cote d'Ivoire");
        COUNTRY_CODE_NAME_MAP_EN.put("CK", "Cook Islands");
        COUNTRY_CODE_NAME_MAP_EN.put("CL", "Chile");
        COUNTRY_CODE_NAME_MAP_EN.put("CM", "Cameroon");
        COUNTRY_CODE_NAME_MAP_EN.put("CN", "China");
        COUNTRY_CODE_NAME_MAP_EN.put("CO", "Colombia");
        COUNTRY_CODE_NAME_MAP_EN.put("CS", "Czech Slovak Rep.");
        COUNTRY_CODE_NAME_MAP_EN.put("CR", "Costa Rica");
        COUNTRY_CODE_NAME_MAP_EN.put("CU", "Cuba");
        COUNTRY_CODE_NAME_MAP_EN.put("CV", "Cape Verde");
        COUNTRY_CODE_NAME_MAP_EN.put("CW", "Curacao");
        COUNTRY_CODE_NAME_MAP_EN.put("CX", "Christmas lsland");
        COUNTRY_CODE_NAME_MAP_EN.put("CY", "Cyprus");
        COUNTRY_CODE_NAME_MAP_EN.put("CZ", "Czech Republic");
        COUNTRY_CODE_NAME_MAP_EN.put("DD", "East Germany");
        COUNTRY_CODE_NAME_MAP_EN.put("DE", "Germany");
        COUNTRY_CODE_NAME_MAP_EN.put("DJ", "Djibouti");
        COUNTRY_CODE_NAME_MAP_EN.put("DK", "Denmark");
        COUNTRY_CODE_NAME_MAP_EN.put("DM", "Dominica");
        COUNTRY_CODE_NAME_MAP_EN.put("DO", "Dominican Rep.");
        COUNTRY_CODE_NAME_MAP_EN.put("DZ", "Algeria");
        COUNTRY_CODE_NAME_MAP_EN.put("EA", "EAPO");
        COUNTRY_CODE_NAME_MAP_EN.put("EC", "Ecuador");
        COUNTRY_CODE_NAME_MAP_EN.put("EE", "Estonia");
        COUNTRY_CODE_NAME_MAP_EN.put("EG", "Egypt");
        COUNTRY_CODE_NAME_MAP_EN.put("EH", "Western Sahara");
        COUNTRY_CODE_NAME_MAP_EN.put("EM", "EUIPO");
        COUNTRY_CODE_NAME_MAP_EN.put("EP", "EPO");
        COUNTRY_CODE_NAME_MAP_EN.put("ER", "Eritrea");
        COUNTRY_CODE_NAME_MAP_EN.put("ES", "Spain");
        COUNTRY_CODE_NAME_MAP_EN.put("ES-CT", "Catalen");
        COUNTRY_CODE_NAME_MAP_EN.put("ET", "Ethiopia");
        COUNTRY_CODE_NAME_MAP_EN.put("EU", "EUIPO");
        COUNTRY_CODE_NAME_MAP_EN.put("FI", "Finland");
        COUNTRY_CODE_NAME_MAP_EN.put("FJ", "Fiji");
        COUNTRY_CODE_NAME_MAP_EN.put("FK", "Falkland Islands");
        COUNTRY_CODE_NAME_MAP_EN.put("FM", "Micronesia");
        COUNTRY_CODE_NAME_MAP_EN.put("FO", "Faroe Islands");
        COUNTRY_CODE_NAME_MAP_EN.put("FR", "France");
        COUNTRY_CODE_NAME_MAP_EN.put("GA", "Gabon");
        COUNTRY_CODE_NAME_MAP_EN.put("GB", "Great Britain");
        COUNTRY_CODE_NAME_MAP_EN.put("GC", "GCC");
        COUNTRY_CODE_NAME_MAP_EN.put("GD", "Grenada");
        COUNTRY_CODE_NAME_MAP_EN.put("GE", "Georgia");
        COUNTRY_CODE_NAME_MAP_EN.put("GF", "French Guiana");
        COUNTRY_CODE_NAME_MAP_EN.put("GG", "Guernsey");
        COUNTRY_CODE_NAME_MAP_EN.put("GH", "Ghana");
        COUNTRY_CODE_NAME_MAP_EN.put("GI", "Gibraltar");
        COUNTRY_CODE_NAME_MAP_EN.put("GL", "Greenland");
        COUNTRY_CODE_NAME_MAP_EN.put("GM", "Gambia");
        COUNTRY_CODE_NAME_MAP_EN.put("GN", "Guinea");
        COUNTRY_CODE_NAME_MAP_EN.put("GP", "Guadeloupe");
        COUNTRY_CODE_NAME_MAP_EN.put("GQ", "Equatorial Guinea");
        COUNTRY_CODE_NAME_MAP_EN.put("GR", "Greece");
        COUNTRY_CODE_NAME_MAP_EN.put("GS", "South Georgia and The South Sandwich Islands");
        COUNTRY_CODE_NAME_MAP_EN.put("GT", "Guatemala");
        COUNTRY_CODE_NAME_MAP_EN.put("GU", "Guam");
        COUNTRY_CODE_NAME_MAP_EN.put("GW", "Guinea-Bissau");
        COUNTRY_CODE_NAME_MAP_EN.put("GY", "Guyana");
        COUNTRY_CODE_NAME_MAP_EN.put("HK", "Chinese Hong Kong");
        COUNTRY_CODE_NAME_MAP_EN.put("HM", "Heard Island and McDonald Islands");
        COUNTRY_CODE_NAME_MAP_EN.put("HN", "Honduras");
        COUNTRY_CODE_NAME_MAP_EN.put("HR", "Croatia");
        COUNTRY_CODE_NAME_MAP_EN.put("HT", "Haiti");
        COUNTRY_CODE_NAME_MAP_EN.put("HU", "Hungary");
        COUNTRY_CODE_NAME_MAP_EN.put("IB", "WIPO");
        COUNTRY_CODE_NAME_MAP_EN.put("ID", "Indonesia");
        COUNTRY_CODE_NAME_MAP_EN.put("IE", "Ireland");
        COUNTRY_CODE_NAME_MAP_EN.put("IL", "Israel");
        COUNTRY_CODE_NAME_MAP_EN.put("IM", "Isle of Man");
        COUNTRY_CODE_NAME_MAP_EN.put("IN", "India");
        COUNTRY_CODE_NAME_MAP_EN.put("IO", "British Indian Ocean Territory");
        COUNTRY_CODE_NAME_MAP_EN.put("IQ", "Iraq");
        COUNTRY_CODE_NAME_MAP_EN.put("IR", "Iran");
        COUNTRY_CODE_NAME_MAP_EN.put("IS", "Iceland");
        COUNTRY_CODE_NAME_MAP_EN.put("IT", "Italy");
        COUNTRY_CODE_NAME_MAP_EN.put("JE", "Jersey");
        COUNTRY_CODE_NAME_MAP_EN.put("JM", "Jamaica");
        COUNTRY_CODE_NAME_MAP_EN.put("JO", "Jordan");
        COUNTRY_CODE_NAME_MAP_EN.put("JP", "Japan");
        COUNTRY_CODE_NAME_MAP_EN.put("KE", "Kenya");
        COUNTRY_CODE_NAME_MAP_EN.put("KG", "Kyrgyzstan");
        COUNTRY_CODE_NAME_MAP_EN.put("KH", "Cambodia");
        COUNTRY_CODE_NAME_MAP_EN.put("KI", "Kiribati");
        COUNTRY_CODE_NAME_MAP_EN.put("KM", "Comoros");
        COUNTRY_CODE_NAME_MAP_EN.put("KN", "Saint Christopher and Nevis");
        COUNTRY_CODE_NAME_MAP_EN.put("KP", "People's Republic of Korea");
        COUNTRY_CODE_NAME_MAP_EN.put("KR", "Korea");
        COUNTRY_CODE_NAME_MAP_EN.put("KW", "Kuwait");
        COUNTRY_CODE_NAME_MAP_EN.put("KY", "Cayman Islands");
        COUNTRY_CODE_NAME_MAP_EN.put("KZ", "Kazakhstan");
        COUNTRY_CODE_NAME_MAP_EN.put("LA", "Lao People's Democratic Republic");
        COUNTRY_CODE_NAME_MAP_EN.put("LB", "Lebanon");
        COUNTRY_CODE_NAME_MAP_EN.put("LC", "Saint Lucia");
        COUNTRY_CODE_NAME_MAP_EN.put("LI", "Liechtenstein");
        COUNTRY_CODE_NAME_MAP_EN.put("LK", "Sri Lanka");
        COUNTRY_CODE_NAME_MAP_EN.put("LR", "Liberia");
        COUNTRY_CODE_NAME_MAP_EN.put("LS", "Lesotho");
        COUNTRY_CODE_NAME_MAP_EN.put("LT", "Lithuania");
        COUNTRY_CODE_NAME_MAP_EN.put("LU", "Luxembourg");
        COUNTRY_CODE_NAME_MAP_EN.put("LV", "Latvia");
        COUNTRY_CODE_NAME_MAP_EN.put("LY", "Libya");
        COUNTRY_CODE_NAME_MAP_EN.put("MA", "Morocco");
        COUNTRY_CODE_NAME_MAP_EN.put("MC", "Monaco");
        COUNTRY_CODE_NAME_MAP_EN.put("MD", "Moldova");
        COUNTRY_CODE_NAME_MAP_EN.put("ME", "Montenegro");
        COUNTRY_CODE_NAME_MAP_EN.put("MF", "Saint-Martin");
        COUNTRY_CODE_NAME_MAP_EN.put("MG", "Madagascar");
        COUNTRY_CODE_NAME_MAP_EN.put("MH", "Marshall Islands");
        COUNTRY_CODE_NAME_MAP_EN.put("MK", "The Republic of North Macedonia");
        COUNTRY_CODE_NAME_MAP_EN.put("ML", "Mali");
        COUNTRY_CODE_NAME_MAP_EN.put("MM", "Myanmar");
        COUNTRY_CODE_NAME_MAP_EN.put("MN", "Mongolia");
        COUNTRY_CODE_NAME_MAP_EN.put("MO", "Chinese Macao");
        COUNTRY_CODE_NAME_MAP_EN.put("MP", "Northern Mariana Islands");
        COUNTRY_CODE_NAME_MAP_EN.put("MQ", "Martinique");
        COUNTRY_CODE_NAME_MAP_EN.put("MR", "Mauritania");
        COUNTRY_CODE_NAME_MAP_EN.put("MS", "Montserrat");
        COUNTRY_CODE_NAME_MAP_EN.put("MT", "Malta");
        COUNTRY_CODE_NAME_MAP_EN.put("MU", "Mauritius");
        COUNTRY_CODE_NAME_MAP_EN.put("MV", "Maldives");
        COUNTRY_CODE_NAME_MAP_EN.put("MW", "Malawi");
        COUNTRY_CODE_NAME_MAP_EN.put("MX", "Mexico");
        COUNTRY_CODE_NAME_MAP_EN.put("MY", "Malaysia");
        COUNTRY_CODE_NAME_MAP_EN.put("MZ", "Mozambique");
        COUNTRY_CODE_NAME_MAP_EN.put("NA", "Namibia");
        COUNTRY_CODE_NAME_MAP_EN.put("NC", "New Caledonia");
        COUNTRY_CODE_NAME_MAP_EN.put("NE", "Niger");
        COUNTRY_CODE_NAME_MAP_EN.put("NF", "Norfolk Island");
        COUNTRY_CODE_NAME_MAP_EN.put("NG", "Nigeria");
        COUNTRY_CODE_NAME_MAP_EN.put("NI", "Nicaragua");
        COUNTRY_CODE_NAME_MAP_EN.put("NL", "Netherlands");
        COUNTRY_CODE_NAME_MAP_EN.put("NO", "Norway");
        COUNTRY_CODE_NAME_MAP_EN.put("NP", "Nepal");
        COUNTRY_CODE_NAME_MAP_EN.put("NR", "Nauru");
        COUNTRY_CODE_NAME_MAP_EN.put("NU", "Niue");
        COUNTRY_CODE_NAME_MAP_EN.put("NZ", "New Zealand");
        COUNTRY_CODE_NAME_MAP_EN.put("OA", "OAPI");
        COUNTRY_CODE_NAME_MAP_EN.put("OM", "Oman");
        COUNTRY_CODE_NAME_MAP_EN.put("PA", "Panama");
        COUNTRY_CODE_NAME_MAP_EN.put("PE", "Peru");
        COUNTRY_CODE_NAME_MAP_EN.put("PF", "French Polynesia");
        COUNTRY_CODE_NAME_MAP_EN.put("PG", "Papua New Guinea");
        COUNTRY_CODE_NAME_MAP_EN.put("PH", "Philippines");
        COUNTRY_CODE_NAME_MAP_EN.put("PK", "Pakistan");
        COUNTRY_CODE_NAME_MAP_EN.put("PL", "Poland");
        COUNTRY_CODE_NAME_MAP_EN.put("PM", "Saint Pierre and Miquelon");
        COUNTRY_CODE_NAME_MAP_EN.put("PN", "Pitcairn Islands");
        COUNTRY_CODE_NAME_MAP_EN.put("PR", "Puerto Rico");
        COUNTRY_CODE_NAME_MAP_EN.put("PS", "Palestine");
        COUNTRY_CODE_NAME_MAP_EN.put("PT", "Portugal");
        COUNTRY_CODE_NAME_MAP_EN.put("PW", "Palau");
        COUNTRY_CODE_NAME_MAP_EN.put("PY", "Paraguay");
        COUNTRY_CODE_NAME_MAP_EN.put("QA", "Qatar");
        COUNTRY_CODE_NAME_MAP_EN.put("QW", "Rwanda");
        COUNTRY_CODE_NAME_MAP_EN.put("QZ", "CPVO");
        COUNTRY_CODE_NAME_MAP_EN.put("RE", "Reunion");
        COUNTRY_CODE_NAME_MAP_EN.put("RO", "Romania");
        COUNTRY_CODE_NAME_MAP_EN.put("RS", "Republic of Serbia");
        COUNTRY_CODE_NAME_MAP_EN.put("RU", "Russia");
        COUNTRY_CODE_NAME_MAP_EN.put("RW", "Rwanda");
        COUNTRY_CODE_NAME_MAP_EN.put("SA", "Saudi Arabia");
        COUNTRY_CODE_NAME_MAP_EN.put("SB", "Solomon Islands");
        COUNTRY_CODE_NAME_MAP_EN.put("SC", "Seychelles");
        COUNTRY_CODE_NAME_MAP_EN.put("SD", "Sudan");
        COUNTRY_CODE_NAME_MAP_EN.put("SE", "Sweden");
        COUNTRY_CODE_NAME_MAP_EN.put("SG", "Singapore");
        COUNTRY_CODE_NAME_MAP_EN.put("SH", "Saint Helena");
        COUNTRY_CODE_NAME_MAP_EN.put("SI", "Slovenia");
        COUNTRY_CODE_NAME_MAP_EN.put("SJ", "Norway Svalbard and Jan Mayen");
        COUNTRY_CODE_NAME_MAP_EN.put("SK", "Slovakia");
        COUNTRY_CODE_NAME_MAP_EN.put("SL", "Sierra Leone");
        COUNTRY_CODE_NAME_MAP_EN.put("SM", "San Marino");
        COUNTRY_CODE_NAME_MAP_EN.put("SN", "Senegal");
        COUNTRY_CODE_NAME_MAP_EN.put("SO", "Somali");
        COUNTRY_CODE_NAME_MAP_EN.put("SR", "Suriname");
        COUNTRY_CODE_NAME_MAP_EN.put("SS", "South Sudan");
        COUNTRY_CODE_NAME_MAP_EN.put("ST", "Sao Tome and Principe");
        COUNTRY_CODE_NAME_MAP_EN.put("SU", "Soviet Union");
        COUNTRY_CODE_NAME_MAP_EN.put("SV", "El Salvador");
        COUNTRY_CODE_NAME_MAP_EN.put("SX", "Sint Maarten");
        COUNTRY_CODE_NAME_MAP_EN.put("SY", "Syria");
        COUNTRY_CODE_NAME_MAP_EN.put("SZ", "Swaziland");
        COUNTRY_CODE_NAME_MAP_EN.put("TC", "Turks and Caicos Islands");
        COUNTRY_CODE_NAME_MAP_EN.put("TD", "Chad");
        COUNTRY_CODE_NAME_MAP_EN.put("TF", "French Southern Territories");
        COUNTRY_CODE_NAME_MAP_EN.put("TG", "Togo");
        COUNTRY_CODE_NAME_MAP_EN.put("TH", "Thailand");
        COUNTRY_CODE_NAME_MAP_EN.put("TJ", "Tajikistan");
        COUNTRY_CODE_NAME_MAP_EN.put("TK", "Tokelau");
        COUNTRY_CODE_NAME_MAP_EN.put("TL", "Timor-Leste");
        COUNTRY_CODE_NAME_MAP_EN.put("TM", "Turkmenistan");
        COUNTRY_CODE_NAME_MAP_EN.put("TN", "Tunisia");
        COUNTRY_CODE_NAME_MAP_EN.put("TO", "Tonga");
        COUNTRY_CODE_NAME_MAP_EN.put("TR", "Turkey");
        COUNTRY_CODE_NAME_MAP_EN.put("TT", "Trinidad and Tobago");
        COUNTRY_CODE_NAME_MAP_EN.put("TV", "Tuvalu");
        COUNTRY_CODE_NAME_MAP_EN.put("TW", "Chinese Taiwan");
        COUNTRY_CODE_NAME_MAP_EN.put("TZ", "Tanzania");
        COUNTRY_CODE_NAME_MAP_EN.put("UA", "Ukraine");
        COUNTRY_CODE_NAME_MAP_EN.put("UG", "Uganda");
        COUNTRY_CODE_NAME_MAP_EN.put("UM", "United States Minor Outlying Islands");
        COUNTRY_CODE_NAME_MAP_EN.put("US", "United States");
        COUNTRY_CODE_NAME_MAP_EN.put("UY", "Uruguay");
        COUNTRY_CODE_NAME_MAP_EN.put("UZ", "Uzbekistan");
        COUNTRY_CODE_NAME_MAP_EN.put("VA", "Vatican city");
        COUNTRY_CODE_NAME_MAP_EN.put("VC", "Saint Vincent and the Grenadines");
        COUNTRY_CODE_NAME_MAP_EN.put("VE", "Venezuela");
        COUNTRY_CODE_NAME_MAP_EN.put("VG", "British Virgin Islands");
        COUNTRY_CODE_NAME_MAP_EN.put("VI", "Virgin Islands, United States");
        COUNTRY_CODE_NAME_MAP_EN.put("VN", "Vietnam");
        COUNTRY_CODE_NAME_MAP_EN.put("VU", "Vanuatu");
        COUNTRY_CODE_NAME_MAP_EN.put("WF", "Wallis and Futuna");
        COUNTRY_CODE_NAME_MAP_EN.put("WO", "WIPO");
        COUNTRY_CODE_NAME_MAP_EN.put("WS", "Samoa");
        COUNTRY_CODE_NAME_MAP_EN.put("XK", "Republic of Kosovo");
        COUNTRY_CODE_NAME_MAP_EN.put("XN", "Nordic Patent Institute");
        COUNTRY_CODE_NAME_MAP_EN.put("XU", "UPOV");
        COUNTRY_CODE_NAME_MAP_EN.put("XV", "VPI");
        COUNTRY_CODE_NAME_MAP_EN.put("YE", "Yemen");
        COUNTRY_CODE_NAME_MAP_EN.put("YT", "Mayotte");
        COUNTRY_CODE_NAME_MAP_EN.put("YU", "Yugoslavia");
        COUNTRY_CODE_NAME_MAP_EN.put("ZA", "South Africa");
        COUNTRY_CODE_NAME_MAP_EN.put("ZM", "Zambia");
        COUNTRY_CODE_NAME_MAP_EN.put("ZN", "Zanzibar");
        COUNTRY_CODE_NAME_MAP_EN.put("ZR", "Zaire");
        COUNTRY_CODE_NAME_MAP_EN.put("ZW", "Zimbabwe");
        COUNTRY_CODE_NAME_MAP_EN.put("others", "Others");

        COUNTRY_CODE_NAME_MAP_JP = new HashMap<>();
        COUNTRY_CODE_NAME_MAP_JP.put("AD", "アンドラ");
        COUNTRY_CODE_NAME_MAP_JP.put("AE", "アラブ首長国連邦");
        COUNTRY_CODE_NAME_MAP_JP.put("AF", "アフガニスタン");
        COUNTRY_CODE_NAME_MAP_JP.put("AG", "アンティグア・バーブーダ");
        COUNTRY_CODE_NAME_MAP_JP.put("AI", "アンギラ");
        COUNTRY_CODE_NAME_MAP_JP.put("AL", "アルバニア");
        COUNTRY_CODE_NAME_MAP_JP.put("AM", "アルメニア");
        COUNTRY_CODE_NAME_MAP_JP.put("AN", "オランダ領アンティル諸島");
        COUNTRY_CODE_NAME_MAP_JP.put("AO", "アンゴラ");
        COUNTRY_CODE_NAME_MAP_JP.put("AP", "アフリカ地域工業所有権機関");
        COUNTRY_CODE_NAME_MAP_JP.put("AQ", "南極大陸");
        COUNTRY_CODE_NAME_MAP_JP.put("AR", "アルゼンチン");
        COUNTRY_CODE_NAME_MAP_JP.put("AS", "アメリカ領サモア");
        COUNTRY_CODE_NAME_MAP_JP.put("AT", "オーストリア");
        COUNTRY_CODE_NAME_MAP_JP.put("AU", "オーストラリア");
        COUNTRY_CODE_NAME_MAP_JP.put("AW", "アルバ");
        COUNTRY_CODE_NAME_MAP_JP.put("AX", "オラン");
        COUNTRY_CODE_NAME_MAP_JP.put("AZ", "アゼルバイジャン");
        COUNTRY_CODE_NAME_MAP_JP.put("BA", "ボスニア・ヘルツェゴビナ");
        COUNTRY_CODE_NAME_MAP_JP.put("BB", "バルバドス");
        COUNTRY_CODE_NAME_MAP_JP.put("BD", "バングラデシュ");
        COUNTRY_CODE_NAME_MAP_JP.put("BE", "ベルギー");
        COUNTRY_CODE_NAME_MAP_JP.put("BF", "ブルキナファソ");
        COUNTRY_CODE_NAME_MAP_JP.put("BG", "ブルガリア");
        COUNTRY_CODE_NAME_MAP_JP.put("BH", "バーレーン");
        COUNTRY_CODE_NAME_MAP_JP.put("BI", "ブルンジ");
        COUNTRY_CODE_NAME_MAP_JP.put("BJ", "ベナン");
        COUNTRY_CODE_NAME_MAP_JP.put("BL", "セントバーセレミー");
        COUNTRY_CODE_NAME_MAP_JP.put("BM", "バミューダ");
        COUNTRY_CODE_NAME_MAP_JP.put("BN", "ブルネイ");
        COUNTRY_CODE_NAME_MAP_JP.put("BO", "ボリビア");
        COUNTRY_CODE_NAME_MAP_JP.put("BQ", "カリブ海オランダ");
        COUNTRY_CODE_NAME_MAP_JP.put("BR", "ブラジル");
        COUNTRY_CODE_NAME_MAP_JP.put("BS", "バハマ");
        COUNTRY_CODE_NAME_MAP_JP.put("BT", "ブータン");
        COUNTRY_CODE_NAME_MAP_JP.put("BV", "ブーベ島");
        COUNTRY_CODE_NAME_MAP_JP.put("BW", "ボツワナ");
        COUNTRY_CODE_NAME_MAP_JP.put("BX", "ベネルクス");
        COUNTRY_CODE_NAME_MAP_JP.put("BY", "ベラルーシ");
        COUNTRY_CODE_NAME_MAP_JP.put("BZ", "ベリーズ");
        COUNTRY_CODE_NAME_MAP_JP.put("CA", "カナダ");
        COUNTRY_CODE_NAME_MAP_JP.put("CC", "ココス(キーリング)諸島");
        COUNTRY_CODE_NAME_MAP_JP.put("CD", "コンゴ民主共和国");
        COUNTRY_CODE_NAME_MAP_JP.put("CF", "中央アフリカ共和国");
        COUNTRY_CODE_NAME_MAP_JP.put("CG", "コンゴ");
        COUNTRY_CODE_NAME_MAP_JP.put("CH", "スイス");
        COUNTRY_CODE_NAME_MAP_JP.put("CI", "コートジボワール");
        COUNTRY_CODE_NAME_MAP_JP.put("CK", "クック諸島");
        COUNTRY_CODE_NAME_MAP_JP.put("CL", "チリ");
        COUNTRY_CODE_NAME_MAP_JP.put("CM", "カメルーン");
        COUNTRY_CODE_NAME_MAP_JP.put("CN", "中国");
        COUNTRY_CODE_NAME_MAP_JP.put("CO", "コロンビア");
        COUNTRY_CODE_NAME_MAP_JP.put("CS", "チェコスロバキア共和国");
        COUNTRY_CODE_NAME_MAP_JP.put("CR", "コスタリカ");
        COUNTRY_CODE_NAME_MAP_JP.put("CU", "キューバ");
        COUNTRY_CODE_NAME_MAP_JP.put("CV", "カーボベルデ");
        COUNTRY_CODE_NAME_MAP_JP.put("CW", "キュラソー");
        COUNTRY_CODE_NAME_MAP_JP.put("CX", "クリスマス島");
        COUNTRY_CODE_NAME_MAP_JP.put("CY", "キプロス");
        COUNTRY_CODE_NAME_MAP_JP.put("CZ", "チェコ");
        COUNTRY_CODE_NAME_MAP_JP.put("DD", "東ドイツ");
        COUNTRY_CODE_NAME_MAP_JP.put("DE", "ドイツ");
        COUNTRY_CODE_NAME_MAP_JP.put("DJ", "ジブチ");
        COUNTRY_CODE_NAME_MAP_JP.put("DK", "デンマーク");
        COUNTRY_CODE_NAME_MAP_JP.put("DM", "ドミニカ国");
        COUNTRY_CODE_NAME_MAP_JP.put("DO", "ドミニカ共和国");
        COUNTRY_CODE_NAME_MAP_JP.put("DZ", "アルジェリア");
        COUNTRY_CODE_NAME_MAP_JP.put("EA", "ユーラシア特許庁");
        COUNTRY_CODE_NAME_MAP_JP.put("EC", "エクアドル");
        COUNTRY_CODE_NAME_MAP_JP.put("EE", "エストニア");
        COUNTRY_CODE_NAME_MAP_JP.put("EG", "エジプト");
        COUNTRY_CODE_NAME_MAP_JP.put("EH", "アラブサハラ民主共和国");
        COUNTRY_CODE_NAME_MAP_JP.put("EM", "EU知的財産局");
        COUNTRY_CODE_NAME_MAP_JP.put("EP", "ヨーロッパ");
        COUNTRY_CODE_NAME_MAP_JP.put("ER", "エリトリア");
        COUNTRY_CODE_NAME_MAP_JP.put("ES", "スペイン");
        COUNTRY_CODE_NAME_MAP_JP.put("ES-CT", "カタロニア");
        COUNTRY_CODE_NAME_MAP_JP.put("ET", "エチオピア");
        COUNTRY_CODE_NAME_MAP_JP.put("EU", "欧州連合知的財産庁");
        COUNTRY_CODE_NAME_MAP_JP.put("FI", "フィンランド");
        COUNTRY_CODE_NAME_MAP_JP.put("FJ", "フィジー");
        COUNTRY_CODE_NAME_MAP_JP.put("FK", "フォークランド諸島");
        COUNTRY_CODE_NAME_MAP_JP.put("FM", "ミクロネシア連邦");
        COUNTRY_CODE_NAME_MAP_JP.put("FO", "フェロー諸島");
        COUNTRY_CODE_NAME_MAP_JP.put("FR", "フランス");
        COUNTRY_CODE_NAME_MAP_JP.put("GA", "ガボン");
        COUNTRY_CODE_NAME_MAP_JP.put("GB", "イギリス");
        COUNTRY_CODE_NAME_MAP_JP.put("GC", "ベイエリアアラブ諸国協力協議会特許事務所");
        COUNTRY_CODE_NAME_MAP_JP.put("GD", "グレナダ");
        COUNTRY_CODE_NAME_MAP_JP.put("GE", "ジョージア");
        COUNTRY_CODE_NAME_MAP_JP.put("GF", "フランス領ギアナ");
        COUNTRY_CODE_NAME_MAP_JP.put("GG", "ガーンジー");
        COUNTRY_CODE_NAME_MAP_JP.put("GH", "ガーナ");
        COUNTRY_CODE_NAME_MAP_JP.put("GI", "ジブラルタル");
        COUNTRY_CODE_NAME_MAP_JP.put("GL", "グリーンランド");
        COUNTRY_CODE_NAME_MAP_JP.put("GM", "ガンビア");
        COUNTRY_CODE_NAME_MAP_JP.put("GN", "ギニア");
        COUNTRY_CODE_NAME_MAP_JP.put("GP", "グアドループ");
        COUNTRY_CODE_NAME_MAP_JP.put("GQ", "赤道ギニア");
        COUNTRY_CODE_NAME_MAP_JP.put("GR", "ギリシャ");
        COUNTRY_CODE_NAME_MAP_JP.put("GS", "サウスジョージアおよびサウスサンドイッチ諸島");
        COUNTRY_CODE_NAME_MAP_JP.put("GT", "グアテマラ");
        COUNTRY_CODE_NAME_MAP_JP.put("GU", "グアム");
        COUNTRY_CODE_NAME_MAP_JP.put("GW", "ギニアビサウ");
        COUNTRY_CODE_NAME_MAP_JP.put("GY", "ガイアナ");
        COUNTRY_CODE_NAME_MAP_JP.put("HK", "中国香港");
        COUNTRY_CODE_NAME_MAP_JP.put("HM", "ハード島とマクドナルド諸島");
        COUNTRY_CODE_NAME_MAP_JP.put("HN", "ホンジュラス");
        COUNTRY_CODE_NAME_MAP_JP.put("HR", "クロアチア");
        COUNTRY_CODE_NAME_MAP_JP.put("HT", "ハイチ");
        COUNTRY_CODE_NAME_MAP_JP.put("HU", "ハンガリー");
        COUNTRY_CODE_NAME_MAP_JP.put("IB", "世界知的所有権機関");
        COUNTRY_CODE_NAME_MAP_JP.put("ID", "インドネシア");
        COUNTRY_CODE_NAME_MAP_JP.put("IE", "アイルランド");
        COUNTRY_CODE_NAME_MAP_JP.put("IL", "イスラエル");
        COUNTRY_CODE_NAME_MAP_JP.put("IM", "マン島");
        COUNTRY_CODE_NAME_MAP_JP.put("IN", "インド");
        COUNTRY_CODE_NAME_MAP_JP.put("IO", "イギリス領インド洋地域");
        COUNTRY_CODE_NAME_MAP_JP.put("IQ", "イラク");
        COUNTRY_CODE_NAME_MAP_JP.put("IR", "イラン");
        COUNTRY_CODE_NAME_MAP_JP.put("IS", "アイスランド");
        COUNTRY_CODE_NAME_MAP_JP.put("IT", "イタリア");
        COUNTRY_CODE_NAME_MAP_JP.put("JE", "ジャージー");
        COUNTRY_CODE_NAME_MAP_JP.put("JM", "ジャマイカ");
        COUNTRY_CODE_NAME_MAP_JP.put("JO", "ヨルダン");
        COUNTRY_CODE_NAME_MAP_JP.put("JP", "日本");
        COUNTRY_CODE_NAME_MAP_JP.put("KE", "ケニア");
        COUNTRY_CODE_NAME_MAP_JP.put("KG", "キルギス");
        COUNTRY_CODE_NAME_MAP_JP.put("KH", "カンボジア");
        COUNTRY_CODE_NAME_MAP_JP.put("KI", "キリバス");
        COUNTRY_CODE_NAME_MAP_JP.put("KM", "コモロ");
        COUNTRY_CODE_NAME_MAP_JP.put("KN", "セントクリストファー・ネイビス");
        COUNTRY_CODE_NAME_MAP_JP.put("KP", "北朝鮮");
        COUNTRY_CODE_NAME_MAP_JP.put("KR", "韓国");
        COUNTRY_CODE_NAME_MAP_JP.put("KW", "クウェート");
        COUNTRY_CODE_NAME_MAP_JP.put("KY", "ケイマン諸島");
        COUNTRY_CODE_NAME_MAP_JP.put("KZ", "カザフスタン");
        COUNTRY_CODE_NAME_MAP_JP.put("LA", "ラオス");
        COUNTRY_CODE_NAME_MAP_JP.put("LB", "レバノン");
        COUNTRY_CODE_NAME_MAP_JP.put("LC", "セントルシア");
        COUNTRY_CODE_NAME_MAP_JP.put("LI", "リヒテンシュタイン");
        COUNTRY_CODE_NAME_MAP_JP.put("LK", "スリランカ");
        COUNTRY_CODE_NAME_MAP_JP.put("LR", "リベリア");
        COUNTRY_CODE_NAME_MAP_JP.put("LS", "レソト");
        COUNTRY_CODE_NAME_MAP_JP.put("LT", "リトアニア");
        COUNTRY_CODE_NAME_MAP_JP.put("LU", "ルクセンブルグ");
        COUNTRY_CODE_NAME_MAP_JP.put("LV", "ラトビア");
        COUNTRY_CODE_NAME_MAP_JP.put("LY", "リビア");
        COUNTRY_CODE_NAME_MAP_JP.put("MA", "モロッコ");
        COUNTRY_CODE_NAME_MAP_JP.put("MC", "モナコ");
        COUNTRY_CODE_NAME_MAP_JP.put("MD", "モルドバ");
        COUNTRY_CODE_NAME_MAP_JP.put("ME", "モンテネグロ");
        COUNTRY_CODE_NAME_MAP_JP.put("MF", "フランスのサンマルタン");
        COUNTRY_CODE_NAME_MAP_JP.put("MG", "マダガスカル");
        COUNTRY_CODE_NAME_MAP_JP.put("MH", "マーシャル諸島");
        COUNTRY_CODE_NAME_MAP_JP.put("MK", "北マケドニア共和国");
        COUNTRY_CODE_NAME_MAP_JP.put("ML", "マリ");
        COUNTRY_CODE_NAME_MAP_JP.put("MM", "ミャンマー");
        COUNTRY_CODE_NAME_MAP_JP.put("MN", "モンゴル");
        COUNTRY_CODE_NAME_MAP_JP.put("MO", "中国マカオ");
        COUNTRY_CODE_NAME_MAP_JP.put("MP", "北マリアナ諸島");
        COUNTRY_CODE_NAME_MAP_JP.put("MQ", "マルティニーク");
        COUNTRY_CODE_NAME_MAP_JP.put("MR", "モーリタニア");
        COUNTRY_CODE_NAME_MAP_JP.put("MS", "モントセラト");
        COUNTRY_CODE_NAME_MAP_JP.put("MT", "マルタ");
        COUNTRY_CODE_NAME_MAP_JP.put("MU", "モーリシャス");
        COUNTRY_CODE_NAME_MAP_JP.put("MV", "モルディブ");
        COUNTRY_CODE_NAME_MAP_JP.put("MW", "マラウイ");
        COUNTRY_CODE_NAME_MAP_JP.put("MX", "メキシコ");
        COUNTRY_CODE_NAME_MAP_JP.put("MY", "マレーシア");
        COUNTRY_CODE_NAME_MAP_JP.put("MZ", "モザンビーク");
        COUNTRY_CODE_NAME_MAP_JP.put("NA", "ナミビア");
        COUNTRY_CODE_NAME_MAP_JP.put("NC", "ニューカレドニア");
        COUNTRY_CODE_NAME_MAP_JP.put("NE", "ニジェール");
        COUNTRY_CODE_NAME_MAP_JP.put("NF", "ノーフォーク島");
        COUNTRY_CODE_NAME_MAP_JP.put("NG", "ナイジェリア");
        COUNTRY_CODE_NAME_MAP_JP.put("NI", "ニカラグア");
        COUNTRY_CODE_NAME_MAP_JP.put("NL", "オランダ");
        COUNTRY_CODE_NAME_MAP_JP.put("NO", "ノルウェー");
        COUNTRY_CODE_NAME_MAP_JP.put("NP", "ネパール");
        COUNTRY_CODE_NAME_MAP_JP.put("NR", "ナウル");
        COUNTRY_CODE_NAME_MAP_JP.put("NU", "ニウエ");
        COUNTRY_CODE_NAME_MAP_JP.put("NZ", "ニュージーランド");
        COUNTRY_CODE_NAME_MAP_JP.put("OA", "アフリカ知的財産機関");
        COUNTRY_CODE_NAME_MAP_JP.put("OM", "オマーン");
        COUNTRY_CODE_NAME_MAP_JP.put("PA", "パナマ");
        COUNTRY_CODE_NAME_MAP_JP.put("PE", "ペルー");
        COUNTRY_CODE_NAME_MAP_JP.put("PF", "フランス領ポリネシア");
        COUNTRY_CODE_NAME_MAP_JP.put("PG", "パプアニューギニア");
        COUNTRY_CODE_NAME_MAP_JP.put("PH", "フィリピン");
        COUNTRY_CODE_NAME_MAP_JP.put("PK", "パキスタン");
        COUNTRY_CODE_NAME_MAP_JP.put("PL", "ポーランド");
        COUNTRY_CODE_NAME_MAP_JP.put("PM", "サンピエール・ミクロン");
        COUNTRY_CODE_NAME_MAP_JP.put("PN", "ピトケアン諸島");
        COUNTRY_CODE_NAME_MAP_JP.put("PR", "プエルトリコ");
        COUNTRY_CODE_NAME_MAP_JP.put("PS", "パレスチナ");
        COUNTRY_CODE_NAME_MAP_JP.put("PT", "ポルトガル");
        COUNTRY_CODE_NAME_MAP_JP.put("PW", "パラオ");
        COUNTRY_CODE_NAME_MAP_JP.put("PY", "パラグアイ");
        COUNTRY_CODE_NAME_MAP_JP.put("QA", "カタール");
        COUNTRY_CODE_NAME_MAP_JP.put("QW", "ルワンダ");
        COUNTRY_CODE_NAME_MAP_JP.put("QZ", "欧州植物品種庁");
        COUNTRY_CODE_NAME_MAP_JP.put("RE", "レユニオン島");
        COUNTRY_CODE_NAME_MAP_JP.put("RO", "ルーマニア");
        COUNTRY_CODE_NAME_MAP_JP.put("RS", "セルビア共和国");
        COUNTRY_CODE_NAME_MAP_JP.put("RU", "ロシア");
        COUNTRY_CODE_NAME_MAP_JP.put("RW", "ルワンダ");
        COUNTRY_CODE_NAME_MAP_JP.put("SA", "サウジアラビア");
        COUNTRY_CODE_NAME_MAP_JP.put("SB", "ソロモン諸島");
        COUNTRY_CODE_NAME_MAP_JP.put("SC", "セイシェル");
        COUNTRY_CODE_NAME_MAP_JP.put("SD", "スーダン");
        COUNTRY_CODE_NAME_MAP_JP.put("SE", "スウェーデン");
        COUNTRY_CODE_NAME_MAP_JP.put("SG", "シンガポール");
        COUNTRY_CODE_NAME_MAP_JP.put("SH", "セントヘレナ");
        COUNTRY_CODE_NAME_MAP_JP.put("SI", "スロベニア");
        COUNTRY_CODE_NAME_MAP_JP.put("SJ", "ノルウェーのスバールバル諸島とヤンマイエン島");
        COUNTRY_CODE_NAME_MAP_JP.put("SK", "スロバキア");
        COUNTRY_CODE_NAME_MAP_JP.put("SL", "シエラレオネ");
        COUNTRY_CODE_NAME_MAP_JP.put("SM", "サンマリノ");
        COUNTRY_CODE_NAME_MAP_JP.put("SN", "セネガル");
        COUNTRY_CODE_NAME_MAP_JP.put("SO", "ソマリア");
        COUNTRY_CODE_NAME_MAP_JP.put("SR", "スリナム");
        COUNTRY_CODE_NAME_MAP_JP.put("SS", "南スーダン");
        COUNTRY_CODE_NAME_MAP_JP.put("ST", "サントメ・プリンシペ");
        COUNTRY_CODE_NAME_MAP_JP.put("SU", "ソビエト連邦");
        COUNTRY_CODE_NAME_MAP_JP.put("SV", "エルサルバドル");
        COUNTRY_CODE_NAME_MAP_JP.put("SX", "シント・マールテン");
        COUNTRY_CODE_NAME_MAP_JP.put("SY", "シリア");
        COUNTRY_CODE_NAME_MAP_JP.put("SZ", "スワジランド");
        COUNTRY_CODE_NAME_MAP_JP.put("TC", "タークスカイコス諸島");
        COUNTRY_CODE_NAME_MAP_JP.put("TD", "チャド");
        COUNTRY_CODE_NAME_MAP_JP.put("TF", "フランス南部領");
        COUNTRY_CODE_NAME_MAP_JP.put("TG", "トーゴ");
        COUNTRY_CODE_NAME_MAP_JP.put("TH", "タイ");
        COUNTRY_CODE_NAME_MAP_JP.put("TJ", "タジキスタン");
        COUNTRY_CODE_NAME_MAP_JP.put("TK", "トケラウ");
        COUNTRY_CODE_NAME_MAP_JP.put("TL", "東ティモール");
        COUNTRY_CODE_NAME_MAP_JP.put("TM", "トルクメニスタン");
        COUNTRY_CODE_NAME_MAP_JP.put("TN", "チュニジア");
        COUNTRY_CODE_NAME_MAP_JP.put("TO", "トンガ");
        COUNTRY_CODE_NAME_MAP_JP.put("TR", "トルコ");
        COUNTRY_CODE_NAME_MAP_JP.put("TT", "トリニダード・トバゴ");
        COUNTRY_CODE_NAME_MAP_JP.put("TV", "ツバル");
        COUNTRY_CODE_NAME_MAP_JP.put("TW", "中国台湾");
        COUNTRY_CODE_NAME_MAP_JP.put("TZ", "タンザニア");
        COUNTRY_CODE_NAME_MAP_JP.put("UA", "ウクライナ");
        COUNTRY_CODE_NAME_MAP_JP.put("UG", "ウガンダ");
        COUNTRY_CODE_NAME_MAP_JP.put("UM", "アメリカ合衆国外の小さな島");
        COUNTRY_CODE_NAME_MAP_JP.put("US", "アメリカ");
        COUNTRY_CODE_NAME_MAP_JP.put("UY", "ウルグアイ");
        COUNTRY_CODE_NAME_MAP_JP.put("UZ", "ウズベキスタン");
        COUNTRY_CODE_NAME_MAP_JP.put("VA", "バチカン");
        COUNTRY_CODE_NAME_MAP_JP.put("VC", "セントビンセントおよびグレナディーン諸島");
        COUNTRY_CODE_NAME_MAP_JP.put("VE", "ベネズエラ");
        COUNTRY_CODE_NAME_MAP_JP.put("VG", "英領ヴァージン諸島");
        COUNTRY_CODE_NAME_MAP_JP.put("VI", "米領ヴァージン諸島");
        COUNTRY_CODE_NAME_MAP_JP.put("VN", "ベトナム");
        COUNTRY_CODE_NAME_MAP_JP.put("VU", "バヌアツ");
        COUNTRY_CODE_NAME_MAP_JP.put("WF", "ウォリスとフツナ");
        COUNTRY_CODE_NAME_MAP_JP.put("WO", "世界知的所有権機関");
        COUNTRY_CODE_NAME_MAP_JP.put("WS", "サモア");
        COUNTRY_CODE_NAME_MAP_JP.put("XK", "コソボ");
        COUNTRY_CODE_NAME_MAP_JP.put("XN", "北欧特許庁");
        COUNTRY_CODE_NAME_MAP_JP.put("XU", "植物新品種保護国際同盟");
        COUNTRY_CODE_NAME_MAP_JP.put("XV", "ヴィシェグラード特許機構");
        COUNTRY_CODE_NAME_MAP_JP.put("YE", "イエメン");
        COUNTRY_CODE_NAME_MAP_JP.put("YT", "マヨット");
        COUNTRY_CODE_NAME_MAP_JP.put("YU", "ユーゴスラビア");
        COUNTRY_CODE_NAME_MAP_JP.put("ZA", "南アフリカ");
        COUNTRY_CODE_NAME_MAP_JP.put("ZM", "ザンビア");
        COUNTRY_CODE_NAME_MAP_JP.put("ZN", "ザンジバル");
        COUNTRY_CODE_NAME_MAP_JP.put("ZR", "ザイール");
        COUNTRY_CODE_NAME_MAP_JP.put("ZW", "ジンバブエ");
        COUNTRY_CODE_NAME_MAP_JP.put("others", "その他");

        SIMPLE_LEGAL_STATUS_MAP_CN = new HashMap<>();
        SIMPLE_LEGAL_STATUS_MAP_CN.put(0, "失效");
        SIMPLE_LEGAL_STATUS_MAP_CN.put(1, "有效");
        SIMPLE_LEGAL_STATUS_MAP_CN.put(2, "审中");
        SIMPLE_LEGAL_STATUS_MAP_CN.put(999, "未确认");

        SIMPLE_LEGAL_STATUS_MAP_EN = new HashMap<>();
        SIMPLE_LEGAL_STATUS_MAP_EN.put(0, "Inactive");
        SIMPLE_LEGAL_STATUS_MAP_EN.put(1, "Active");
        SIMPLE_LEGAL_STATUS_MAP_EN.put(2, "Pending");
        SIMPLE_LEGAL_STATUS_MAP_EN.put(999, "Undetermined");

        SIMPLE_LEGAL_STATUS_MAP_JP = new HashMap<>();
        SIMPLE_LEGAL_STATUS_MAP_JP.put(0, "無効");
        SIMPLE_LEGAL_STATUS_MAP_JP.put(1, "有効");
        SIMPLE_LEGAL_STATUS_MAP_JP.put(2, "審査係属中");
        SIMPLE_LEGAL_STATUS_MAP_JP.put(999, "未確認");
    }
}

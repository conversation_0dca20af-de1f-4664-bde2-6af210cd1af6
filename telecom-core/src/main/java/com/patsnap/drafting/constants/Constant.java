package com.patsnap.drafting.constants;

import com.patsnap.core.common.acl.LimitConstants;
import com.patsnap.drafting.enums.task.AiTaskTypeEnum;

import java.util.Map;

public class Constant {

    public static final String ALL = "ALL";
    public static final String EN = "EN";
    public static final String CN = "CN";
    public static final String DE = "DE";
    public static final String TW = "TW";
    public static final String KR = "KR";
    public static final String JP = "JP";
    
    // 分享链接的过期时间
    public static final Long SHARE_FOREVER = 9999999999999L;
    
    // 技术交底书的配置类型
    public static final String INVITATION_DISCLOSURE = "INVITATION_DISCLOSURE";
    // 用于判断请求是否来源与eureka
    public static final String X_PATSNAP_FROM_EUREKA = "s-core-eureka";

    public static final LimitConstants AI_SEARCH_TIMES = new LimitConstants("analytics_ai_search_times_month");
    
    public static final LimitConstants AI_NOVELTY_SEARCH_LIMIT = new LimitConstants("analytics_ai_novelty_search_basic_limit");
    public static final LimitConstants AI_FTO_SEARCH_LIMIT = new LimitConstants("analytics_ai_fto_search_basic_limit");
    public static final LimitConstants AI_TRANSLATION_LIMIT = new LimitConstants("analytics_ai_translation_basic_limit");
    public static final LimitConstants AI_PATENT_DISCLOSURE_LIMIT = new LimitConstants("analytics_ai_disclosure_basic_limit");
    public static final LimitConstants AI_SPECIFICATION_CN_LIMIT = new LimitConstants("analytics_ai_specification_cn_basic_limit");
    public static final LimitConstants AI_SPECIFICATION_US_LIMIT = new LimitConstants("analytics_ai_specification_us_basic_limit");
    public static final LimitConstants AI_SPECIFICATION_EU_LIMIT = new LimitConstants("analytics_ai_specification_eu_basic_limit");
    public static final LimitConstants AI_SPECIFICATION_UNIFIED_LIMIT = new LimitConstants("analytics_ai_specification_unified_basic_limit");
    public static final Map<AiTaskTypeEnum, LimitConstants> AI_TASK_TYPE_LIMIT_MAP = Map.of(
            AiTaskTypeEnum.AI_TRANSLATION, AI_TRANSLATION_LIMIT,
            AiTaskTypeEnum.AI_PATENT_DISCLOSURE, AI_PATENT_DISCLOSURE_LIMIT,
            AiTaskTypeEnum.AI_NOVELTY_SEARCH, AI_NOVELTY_SEARCH_LIMIT,
            AiTaskTypeEnum.AI_FTO_SEARCH, AI_FTO_SEARCH_LIMIT
    );
    
    public static final String CLASSIFICATION_TYPE_IPC = "ipc";
    public static final String CLASSIFICATION_TYPE_CPC = "cpc";
    
    public static final String NOVELTY_AGENT_TASK_PREFIX = "novelty_agent_";

    // 来自AI 平台的agent调用，请求的header参数，X-PatSnap-From=s-core-agents-service
    public static final String X_PATSNAP_FROM_AGENT = "s-core-agents-service";
}

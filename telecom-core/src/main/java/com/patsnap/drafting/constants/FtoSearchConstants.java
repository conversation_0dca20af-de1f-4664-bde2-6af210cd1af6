package com.patsnap.drafting.constants;

public class FtoSearchConstants {
    public static final class LegalStatus {
        public static final String VALID = "1";             // 有效
        public static final String PENDING = "2";           // 审中
        public static final String UNDETERMINED = "999";    // 未确认
    }
    
    public static final class Conclusion {
        public static final class ZH {
            public static final String SIMILAR_VALID = "通过对相关专利各权利要求和标的技术方案的技术特征进行分析可知：\n" +
                    "\n" +
                    "专利权利要求%s的技术特征与标的技术方案的技术特征等同，因此权利要求%s记载的方案涵盖了标的技术方案，标的技术方案落入权利要求%s保护范围。\n" +
                    "\n" +
                    "综上，在%s之前于%s不建议实施标的技术方案。或在专利到期日前，采取商业合作、授权许可，或做好稳定性分析处置预案后再于相应地区实施。";
                    
            public static final String SIMILAR_PENDING = "通过对权利要求%s和标的技术方案的技术特征进行分析可知：\n" +
                    "\n" +
                    "专利申请权利要求%s的技术特征与标的技术方案的技术特征等同，因此权利要求%s记载的技术方案涵盖了标的技术方案，标的技术方案落入权利要求%s保护范围。\n" +
                    "\n" +
                    "综上，该专利申请尚未获得专利权，建议持续监控专利案件审查状态，必要时提交公众意见，影响案件走向，确保标的技术方案不在其范围内。";
                    
            public static final String SIMILAR_OTHER = "通过对权利要求%s和标的技术方案的技术特征进行分析可知：\n" +
                    "\n" +
                    "专利申请权利要求%s的技术特征与标的技术方案的技术特征等同，因此权利要求%s记载的技术方案涵盖了标的技术方案，标的技术方案落入权利要求%s保护范围。\n";
                    
            public static final String NOT_SIMILAR = "通过对相关专利的权利要求和标的技术方案的技术特征进行分析可知：\n\n" +
                    "相关专利的权利要求技术方案未涵盖标的技术方案，二者不等同，标的技术方案未落入了权利要求保护范围。\n\n" +
                    "综上，该专利的权利要求对标的技术方案于%s地区不构成实施障碍。";
        }
        
        public static final class EN {
            public static final String SIMILAR_VALID = "Through analysis of the technical features in Claim 1 and the target technical solution:\n" +
                    "\n" +
                    "The technical features of the patent Claim 1 are equivalent to those of the target technical solution. Therefore, the solution described in Claim 1 encompasses the target technical solution, which consequently falls within the protective scope of Claim 1.";

            public static final String SIMILAR_PENDING = "Through analysis of the technical features in Claim 1 and the target technical solution:\n" +
                    "\n" +
                    "The technical features of the patent application Claim 1 are equivalent to those of the target technical solution. Therefore, the solution described in Claim 1 encompasses the target technical solution, which consequently falls within the protective scope of Claim 1.";

            public static final String SIMILAR_OTHER = "Through analysis of the technical features in Claim 1 and the target technical solution:\n" +
                    "\n" +
                    "The technical features of the patent application Claim 1 are equivalent to those of the target technical solution. Therefore, the solution described in Claim 1 encompasses the target technical solution, which consequently falls within the protective scope of Claim 1.";

            public static final String NOT_SIMILAR = "Through analysis of the technical features in Claim 1 and the target technical solution:\n" +
                    "\n" +
                    "The technical solution of Claim 1 neither encompasses nor demonstrates equivalence to the target technical solution in terms of claim limitations. Specifically, the target technical solution does not satisfy all limitations of Claim 1, and therefore does not fall within the protective scope of Claim 1.";
        }
    }

    public static final class SearchScope {
        public static final String EN = "This FTO search covers patent publications related to the target technical solution, within the selected search region, published on or before %s.\n" +
                "The analysis reflects filters set, including: jurisdiction, legal status, included/excluded companies, and start date.\n" +
                "Note: This search does not include unpublished patent applications that were filed before %s . These may still pose potential risks if they cover similar technical solutions.";

        public static final String ZH = "本次检索调查分析主要针对 %s（包括当日）前，检索区域范围内标的技术方案的相关专利公开（公告）的文献。\n" +
                "\n" +
                "不排除有于 %s 前提交申请但并未公开的检索区域范围内的专利申请可能涵盖本标的技术方案。";

        public static final String JP = "本検索調査分析は、主として %s 以前（当日を含む）に公開（公告）された、検索範囲内における対象技術方案に関連する特許文献を対象としています。\n" +
                "\n" +
                "本検索では、%s より前に検索範囲内に出願され、まだ公開されていない特許出願が対象技術方案を包含する可能性を排除するものではありません。";
    }

    public static final class ReportHeader {
        public static final String EN = "Completed %s rounds of searching, conducted a detailed comparison of %s references, and identified the %s closest references.";

        public static final String JP = "%s 回の検索を完了し、%s 件の関連特許を詳細に比較し、最も関連性の高い特許を %s 件特定しました。";
        
        public static final String ZH = "完成了%s次检索，详细对比%s篇相关专利，找出了最接近的相关专利%s篇。";
    }
} 
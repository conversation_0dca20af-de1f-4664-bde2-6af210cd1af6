package com.patsnap.drafting.constants;

/**
 * @description 埋点事件常量
 */
public class TrackEvent {
    
    // AI 图片相似度搜索 agent
    public static final String AI_IMAGE_SEARCH_AGENT = "SS IP Image Search Agent";

    public static final String TECH_MONITOR_AGENT_EVENT = "SS Monitor";

    public static final String TECH_MONITOR_AGENT_INTENT_FAILURE_SOURCE = "failure_identify";

    public static final String TECH_MONITOR_AGENT_REPORT_CONFIG_FAILURE_SOURCE = "report_config_failure_identify";

    public static final String TECH_MONITOR_AGENT_INTENT_SOURCE = "intent_done";

    public static final String TECH_MONITOR_AGENT_FEATURE_ANALYSIS_SOURCE = "feature_analysis_done";

    public static final String TECH_MONITOR_AGENT_RECOMMEND_SOURCE = "recommend_done";

    public static final String TECH_MONITOR_AGENT_PREVIEW_SOURCE = "preview_done";

    public static final String TECH_MONITOR_AGENT_PREVIEW_QUERY_ERROR_SOURCE = "query_error";

}

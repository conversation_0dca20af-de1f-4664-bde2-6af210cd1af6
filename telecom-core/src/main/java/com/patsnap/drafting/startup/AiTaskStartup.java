package com.patsnap.drafting.startup;

import com.amazonaws.services.sqs.AmazonSQS;
import com.patsnap.drafting.config.AiTranslationConfig;
import com.patsnap.drafting.listener.AiTaskMessageHandler;
import com.patsnap.drafting.manager.aitask.AiTaskManager;
import com.patsnap.drafting.manager.aitranslation.AiTranslationManager;
import com.patsnap.drafting.util.RedissonUtils;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

/**
 * AI异步任务处理的启动类
 *
 * <AUTHOR>
 * @date 2024/08/07
 */
@Component
@ConditionalOnProperty(name = "com.patsnap.ai.task.startup.enabled", havingValue = "true", matchIfMissing = true)
public class AiTaskStartup implements ApplicationRunner {

    private final AiTranslationManager aiTranslationManager;
    private final AiTaskManager aiTaskManager;
    private final AmazonSQS sqsClient;
    private final AiTranslationConfig aiTranslationConfig;
    private final ThreadPoolTaskExecutor taskExecutor;
    private final RedissonClient redissonClient;
    private final RedissonUtils redissonUtils;
    private int threadPoolSize;

    public AiTaskStartup(AiTranslationManager aiTranslationManager, AiTaskManager aiTaskManager, @Qualifier("aiTaskSqsClient") AmazonSQS sqsClient,
            AiTranslationConfig aiTranslationConfig, ThreadPoolTaskExecutor taskExecutor, RedissonClient redissonClient,
             @Value("${com.patsnap.ai.task.user-thread-pool.size:5}") int threadPoolSize, RedissonUtils redissonUtils) {
        this.aiTranslationManager = aiTranslationManager;
        this.aiTaskManager = aiTaskManager;
        this.sqsClient = sqsClient;
        this.aiTranslationConfig = aiTranslationConfig;
        this.taskExecutor = taskExecutor;
        this.redissonClient = redissonClient;
        this.threadPoolSize = threadPoolSize;
        this.redissonUtils = redissonUtils;
    }

    @Override
    public void run(ApplicationArguments args) {
        AiTaskMessageHandler aiTaskMessageHandler = new AiTaskMessageHandler(aiTranslationManager, sqsClient,
                aiTranslationConfig.getAiTaskSqsName(), redissonClient, aiTaskManager, threadPoolSize, redissonUtils);
        taskExecutor.execute(aiTaskMessageHandler::doTask);
    }
}

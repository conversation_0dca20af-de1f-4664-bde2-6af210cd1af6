package com.patsnap.drafting.config;

import com.patsnap.core.common.copilot.gpt.enums.GPTModelEnum;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/07/23
 */
@Data
@Component
public class AiTranslationConfig {
    @Value("${configs.com.patsnap.data.sqs.ai-task.queue-name}")
    private String aiTaskSqsName;

    @Value("${configs.com.patsnap.drafting.ai-translation.text-max-length:200000}")
    private int textMaxLength = 200000;

    @Value("${configs.com.patsnap.drafting.ai-translation.rewrite-prefix-length:3}")
    private int rewritePrefixLength = 3;

    @Value("${configs.com.patsnap.drafting.ai-translation.paragraph-max-length:4000}")
    private int paragraphMaxLength = 4000;

    @Value("${configs.com.patsnap.drafting.ai-translation.http-request-timeout:120000}")
    private int httpRequestTimeout = 120000;

    @Value("${configs.com.patsnap.drafting.ai-translation.full-text-model:translation-gpt}")
    private String fullTextModel = GPTModelEnum.TRANSLATION_GPT.getModelName();

    @Value("${configs.com.patsnap.drafting.ai-translation.paragraph-model:translation-gpt}")
    private String paragraphModel = GPTModelEnum.TRANSLATION_GPT.getModelName();

    @Value("${configs.com.patsnap.drafting.ai-translation.rewrite-model:translation-gpt}")
    private String rewriteModel = GPTModelEnum.TRANSLATION_GPT.getModelName();

    @Value("${configs.com.patsnap.drafting.ai-translation.term-list-max-length:1000}")
    private int termListMaxLength = 1000;

    @Value("${configs.com.patsnap.drafting.ai-translation.notice-link:/ai-drafting/translation/}")
    private String noticeLink = "/ai-drafting/translation/";
}

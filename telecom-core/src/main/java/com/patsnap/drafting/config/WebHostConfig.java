package com.patsnap.drafting.config;

import com.patsnap.core.common.request.TenantIdHolder;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * @Description 管理配置的域名
 * <AUTHOR>
 * @Date 2023/2/24 16:46
 */
@Service
public class WebHostConfig {
    @Value("${com.patsnap.analytics.service.email.host}")
    private String websiteHost;

    @Value("${configs.com.patsnap.account.eu.customize.companies}")
    private String companies;

    private static List<String> companyList;

    @Value("${configs.com.patsnap.account.eu.customize.analytics.host}")
    private String customizeAnalyticsHost;

    @PostConstruct
    public void init() {
        if (StringUtils.isNotBlank(companies)) {
            companyList = Arrays.asList(companies.split(","));
        }else{
            companyList = Collections.emptyList();
        }
    }

    public String getWebsiteHost() {
        return getWebsiteHost(TenantIdHolder.get().orElse(StringUtils.EMPTY));
    }

    public String getWebsiteHost(String companyId) {
        if (StringUtils.isBlank(customizeAnalyticsHost)) {
            return websiteHost;
        }
        if (companyList.contains(companyId)) {
            return customizeAnalyticsHost;
        }
        return websiteHost;
    }
}

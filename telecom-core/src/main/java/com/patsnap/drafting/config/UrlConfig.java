package com.patsnap.drafting.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/07/11
 */
@Data
@Component
public class UrlConfig {

    @Value("${com.patsnap.analytics.service.gpt.base-url}")
    private String gptBaseUrl;
    @Value("${com.patsnap.analytics.service.gpt.api-key}")
    private String gptApiKey;
    @Value("${com.patsnap.analytics.service.ai-lab-disclosure-url}")
    private String aiLabDisclosureUrl;
    @Value("${configs.com.patsnap.compute.patsnap-check-input.url}")
    private String rdCheckInputService;
    @Value("${configs.com.patsnap.compute.lang-detect.url}")
    private String rdLangDetectUrl;
    @Value("${configs.com.patsnap.compute.ipc.url}")
    private String rdIpcUrl;
    @Value("${configs.com.patsnap.compute.cpc.url}")
    private String rdCpcUrl;
    @Value("${configs.com.patsnap.compute.specification.url}")
    private String rdSpecificationUrl;

}

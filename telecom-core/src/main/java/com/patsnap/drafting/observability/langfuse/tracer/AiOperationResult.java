package com.patsnap.drafting.observability.langfuse.tracer;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Map;

/**
 * AI 操作结果
 *
 * <AUTHOR> Assistant
 * @date 2025/01/31
 */
@Data
@Builder
public class AiOperationResult {

    /**
     * 完成内容/响应内容
     */
    private String completion;

    /**
     * 提示词 token 数量
     */
    private Long promptTokens;

    /**
     * 完成 token 数量
     */
    private Long completionTokens;

    /**
     * 总 token 数量
     */
    private Long totalTokens;

    /**
     * 成本信息
     */
    private BigDecimal cost;

    /**
     * 货币单位
     */
    private String currency;

    /**
     * 延迟时间（毫秒）
     */
    private Long latencyMs;

    /**
     * 模型响应的其他元数据
     */
    private Map<String, Object> metadata;

    /**
     * 错误信息（如果有）
     */
    private String error;

    /**
     * 是否成功
     */
    private boolean success;

    /**
     * 创建成功的结果
     */
    public static AiOperationResult success(String completion, Long promptTokens, Long completionTokens) {
        return AiOperationResult.builder()
                .completion(completion)
                .promptTokens(promptTokens)
                .completionTokens(completionTokens)
                .totalTokens(promptTokens + completionTokens)
                .success(true)
                .build();
    }

    /**
     * 创建成功的结果，包含成本信息
     */
    public static AiOperationResult success(String completion, Long promptTokens, Long completionTokens, 
                                          BigDecimal cost, String currency) {
        return AiOperationResult.builder()
                .completion(completion)
                .promptTokens(promptTokens)
                .completionTokens(completionTokens)
                .totalTokens(promptTokens + completionTokens)
                .cost(cost)
                .currency(currency)
                .success(true)
                .build();
    }

    /**
     * 创建失败的结果
     */
    public static AiOperationResult failure(String error) {
        return AiOperationResult.builder()
                .error(error)
                .success(false)
                .build();
    }

    /**
     * 创建简单的成功结果
     */
    public static AiOperationResult simpleSuccess(String completion) {
        return AiOperationResult.builder()
                .completion(completion)
                .success(true)
                .build();
    }
}

package com.patsnap.drafting.observability.langfuse.tracer;

import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * AI 操作上下文
 *
 * <AUTHOR> Assistant
 * @date 2025/01/31
 */
@Data
@Builder
public class AiOperationContext {

    /**
     * AI 系统名称 (如: openai, anthropic)
     */
    private String system;

    /**
     * 模型名称
     */
    private String model;

    /**
     * 温度参数
     */
    private Double temperature;

    /**
     * 提示词内容
     */
    private String prompt;

    /**
     * 用户 ID
     */
    private String userId;

    /**
     * 会话 ID
     */
    private String sessionId;

    /**
     * 提示词模板名称
     */
    private String promptName;

    /**
     * 提示词版本
     */
    private String promptVersion;

    /**
     * 标签列表
     */
    private List<String> tags;

    /**
     * 元数据
     */
    private Map<String, Object> metadata;

    /**
     * 操作类型 (如: chat_completion, embedding, image_generation)
     */
    private String operationType;

    /**
     * 最大 token 数
     */
    private Integer maxTokens;

    /**
     * top_p 参数
     */
    private Double topP;

    /**
     * frequency_penalty 参数
     */
    private Double frequencyPenalty;

    /**
     * presence_penalty 参数
     */
    private Double presencePenalty;

    /**
     * 创建聊天完成上下文
     */
    public static AiOperationContext chatCompletion(String model, String prompt) {
        return AiOperationContext.builder()
                .system("openai")
                .model(model)
                .prompt(prompt)
                .operationType("chat_completion")
                .build();
    }

    /**
     * 创建聊天完成上下文，包含用户信息
     */
    public static AiOperationContext chatCompletion(String model, String prompt, String userId, String sessionId) {
        return AiOperationContext.builder()
                .system("openai")
                .model(model)
                .prompt(prompt)
                .userId(userId)
                .sessionId(sessionId)
                .operationType("chat_completion")
                .build();
    }

    /**
     * 创建嵌入上下文
     */
    public static AiOperationContext embedding(String model, String text) {
        return AiOperationContext.builder()
                .system("openai")
                .model(model)
                .prompt(text)
                .operationType("embedding")
                .build();
    }

    /**
     * 创建自定义 AI 操作上下文
     */
    public static AiOperationContext custom(String system, String operationType) {
        return AiOperationContext.builder()
                .system(system)
                .operationType(operationType)
                .build();
    }
}

package com.patsnap.drafting.observability.langfuse.config;

import io.opentelemetry.api.OpenTelemetry;
import io.opentelemetry.api.common.AttributeKey;
import io.opentelemetry.api.common.Attributes;
import io.opentelemetry.api.trace.Tracer;
import io.opentelemetry.exporter.otlp.http.trace.OtlpHttpSpanExporter;
import io.opentelemetry.sdk.OpenTelemetrySdk;
import io.opentelemetry.sdk.resources.Resource;
import io.opentelemetry.sdk.trace.SdkTracerProvider;
import io.opentelemetry.sdk.trace.export.BatchSpanProcessor;
import io.opentelemetry.sdk.trace.samplers.Sampler;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.time.Duration;

/**
 * OpenTelemetry 配置类
 *
 * <AUTHOR> Assistant
 * @date 2025/01/31
 */
@Slf4j
@Configuration
@RequiredArgsConstructor
@ConditionalOnProperty(prefix = "configs.com.patsnap.drafting.langfuse", name = "enabled", havingValue = "true")
public class OpenTelemetryConfig {

    private final LangfuseProperties langfuseProperties;

    /**
     * 配置 OpenTelemetry SDK
     */
    @Bean
    public OpenTelemetry openTelemetry() {
        if (!langfuseProperties.isValid()) {
            log.warn("Langfuse configuration is invalid, using no-op OpenTelemetry");
            return OpenTelemetry.noop();
        }

        try {
            // 创建资源
            Resource resource = Resource.getDefault()
                    .merge(Resource.create(Attributes.of(
                            AttributeKey.stringKey("service.name"), langfuseProperties.getServiceName(),
                            AttributeKey.stringKey("service.version"), "1.0.0"
                    )));

            // 创建 OTLP 导出器
            OtlpHttpSpanExporter spanExporter = OtlpHttpSpanExporter.builder()
                    .setEndpoint(langfuseProperties.getOtlpEndpoint())
                    .addHeader("Authorization", "Basic " + langfuseProperties.getAuthString())
                    .setTimeout(Duration.ofMillis(langfuseProperties.getTimeout().getReadTimeoutMillis()))
                    .build();

            // 创建批量处理器
            BatchSpanProcessor batchProcessor = BatchSpanProcessor.builder(spanExporter)
                    .setMaxExportBatchSize(langfuseProperties.getBatch().getMaxExportBatchSize())
                    .setExporterTimeout(Duration.ofMillis(langfuseProperties.getBatch().getExportTimeoutMillis()))
                    .setScheduleDelay(Duration.ofMillis(langfuseProperties.getBatch().getScheduleDelayMillis()))
                    .setMaxQueueSize(langfuseProperties.getBatch().getMaxQueueSize())
                    .build();

            // 创建采样器
            Sampler sampler = Sampler.traceIdRatioBased(langfuseProperties.getSamplingProbability());

            // 创建 TracerProvider
            SdkTracerProvider tracerProvider = SdkTracerProvider.builder()
                    .addSpanProcessor(batchProcessor)
                    .setResource(resource)
                    .setSampler(sampler)
                    .build();

            // 创建 OpenTelemetry SDK
            OpenTelemetrySdk openTelemetrySdk = OpenTelemetrySdk.builder()
                    .setTracerProvider(tracerProvider)
                    .build();

            // 注册关闭钩子
            Runtime.getRuntime().addShutdownHook(new Thread(() -> {
                try {
                    tracerProvider.close();
                    log.info("OpenTelemetry TracerProvider closed successfully");
                } catch (Exception e) {
                    log.error("Error closing OpenTelemetry TracerProvider", e);
                }
            }));

            log.info("OpenTelemetry configured successfully with Langfuse endpoint: {}", 
                    langfuseProperties.getOtlpEndpoint());
            
            return openTelemetrySdk;

        } catch (Exception e) {
            log.error("Failed to configure OpenTelemetry, using no-op implementation", e);
            return OpenTelemetry.noop();
        }
    }

    /**
     * 创建 Tracer Bean
     */
    @Bean
    public Tracer tracer(OpenTelemetry openTelemetry) {
        return openTelemetry.getTracer("com.patsnap.drafting", "1.0.0");
    }
}

package com.patsnap.drafting.observability.langfuse.tracer;

import com.patsnap.drafting.observability.langfuse.config.LangfuseProperties;
import io.opentelemetry.api.common.AttributeKey;
import io.opentelemetry.api.common.Attributes;
import io.opentelemetry.api.trace.Span;
import io.opentelemetry.api.trace.SpanBuilder;
import io.opentelemetry.api.trace.SpanKind;
import io.opentelemetry.api.trace.StatusCode;
import io.opentelemetry.api.trace.Tracer;
import io.opentelemetry.context.Context;
import io.opentelemetry.context.Scope;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.concurrent.Callable;
import java.util.function.Consumer;
import java.util.function.Function;

/**
 * Langfuse 追踪器封装类
 *
 * <AUTHOR> Assistant
 * @date 2025/01/31
 */
@Slf4j
@RequiredArgsConstructor
public class LangfuseTracer {

    private final Tracer tracer;
    private final LangfuseProperties properties;

    // Langfuse 特定属性键
    public static final AttributeKey<String> LANGFUSE_USER_ID = AttributeKey.stringKey("langfuse.user.id");
    public static final AttributeKey<String> LANGFUSE_SESSION_ID = AttributeKey.stringKey("langfuse.session.id");
    public static final AttributeKey<String> LANGFUSE_TRACE_NAME = AttributeKey.stringKey("langfuse.trace.name");
    public static final AttributeKey<String> LANGFUSE_PROMPT_NAME = AttributeKey.stringKey("langfuse.prompt.name");
    public static final AttributeKey<String> LANGFUSE_PROMPT_VERSION = AttributeKey.stringKey("langfuse.prompt.version");
    public static final AttributeKey<String> LANGFUSE_TAGS = AttributeKey.stringKey("langfuse.tags");
    public static final AttributeKey<String> LANGFUSE_METADATA = AttributeKey.stringKey("langfuse.metadata");

    // GenAI 语义约定属性键
    public static final AttributeKey<String> GEN_AI_SYSTEM = AttributeKey.stringKey("gen_ai.system");
    public static final AttributeKey<String> GEN_AI_REQUEST_MODEL = AttributeKey.stringKey("gen_ai.request.model");
    public static final AttributeKey<Double> GEN_AI_REQUEST_TEMPERATURE = AttributeKey.doubleKey("gen_ai.request.temperature");
    public static final AttributeKey<Long> GEN_AI_USAGE_PROMPT_TOKENS = AttributeKey.longKey("gen_ai.usage.prompt_tokens");
    public static final AttributeKey<Long> GEN_AI_USAGE_COMPLETION_TOKENS = AttributeKey.longKey("gen_ai.usage.completion_tokens");
    public static final AttributeKey<Long> GEN_AI_USAGE_TOTAL_TOKENS = AttributeKey.longKey("gen_ai.usage.total_tokens");
    public static final AttributeKey<String> GEN_AI_PROMPT = AttributeKey.stringKey("gen_ai.prompt");
    public static final AttributeKey<String> GEN_AI_COMPLETION = AttributeKey.stringKey("gen_ai.completion");

    /**
     * 创建并启动一个新的 span
     */
    public Span startSpan(String spanName) {
        return tracer.spanBuilder(spanName)
                .setSpanKind(SpanKind.INTERNAL)
                .startSpan();
    }

    /**
     * 创建并启动一个新的 span，带有用户上下文
     */
    public Span startSpan(String spanName, String userId, String sessionId) {
        SpanBuilder spanBuilder = tracer.spanBuilder(spanName)
                .setSpanKind(SpanKind.INTERNAL);

        if (userId != null) {
            spanBuilder.setAttribute(LANGFUSE_USER_ID, userId);
        }
        if (sessionId != null) {
            spanBuilder.setAttribute(LANGFUSE_SESSION_ID, sessionId);
        }

        return spanBuilder.startSpan();
    }

    /**
     * 执行带追踪的操作
     */
    public <T> T trace(String spanName, Callable<T> operation) {
        return trace(spanName, null, null, operation);
    }

    /**
     * 执行带追踪的操作，包含用户上下文
     */
    public <T> T trace(String spanName, String userId, String sessionId, Callable<T> operation) {
        Span span = startSpan(spanName, userId, sessionId);
        try (Scope scope = span.makeCurrent()) {
            T result = operation.call();
            span.setStatus(StatusCode.OK);
            return result;
        } catch (Exception e) {
            span.setStatus(StatusCode.ERROR, e.getMessage());
            span.recordException(e);
            throw new RuntimeException(e);
        } finally {
            span.end();
        }
    }

    /**
     * 执行带追踪的 AI 操作
     */
    public <T> T traceAiOperation(String operationName, AiOperationContext context, Callable<T> operation) {
        Span span = tracer.spanBuilder(operationName)
                .setSpanKind(SpanKind.INTERNAL)
                .startSpan();

        // 设置 AI 相关属性
        setAiAttributes(span, context);

        try (Scope scope = span.makeCurrent()) {
            T result = operation.call();
            
            // 如果结果包含 token 使用信息，更新 span
            if (result instanceof AiOperationResult) {
                updateSpanWithResult(span, (AiOperationResult) result);
            }
            
            span.setStatus(StatusCode.OK);
            return result;
        } catch (Exception e) {
            span.setStatus(StatusCode.ERROR, e.getMessage());
            span.recordException(e);
            throw new RuntimeException(e);
        } finally {
            span.end();
        }
    }

    /**
     * 设置 AI 操作相关属性
     */
    private void setAiAttributes(Span span, AiOperationContext context) {
        if (context.getSystem() != null) {
            span.setAttribute(GEN_AI_SYSTEM, context.getSystem());
        }
        if (context.getModel() != null) {
            span.setAttribute(GEN_AI_REQUEST_MODEL, context.getModel());
        }
        if (context.getTemperature() != null) {
            span.setAttribute(GEN_AI_REQUEST_TEMPERATURE, context.getTemperature());
        }
        if (context.getPrompt() != null) {
            span.setAttribute(GEN_AI_PROMPT, context.getPrompt());
        }
        if (context.getUserId() != null) {
            span.setAttribute(LANGFUSE_USER_ID, context.getUserId());
        }
        if (context.getSessionId() != null) {
            span.setAttribute(LANGFUSE_SESSION_ID, context.getSessionId());
        }
        if (context.getPromptName() != null) {
            span.setAttribute(LANGFUSE_PROMPT_NAME, context.getPromptName());
        }
        if (context.getTags() != null && !context.getTags().isEmpty()) {
            span.setAttribute(LANGFUSE_TAGS, String.join(",", context.getTags()));
        }
        if (context.getMetadata() != null && !context.getMetadata().isEmpty()) {
            // 将 metadata 转换为 JSON 字符串
            span.setAttribute(LANGFUSE_METADATA, convertMapToJson(context.getMetadata()));
        }
    }

    /**
     * 使用操作结果更新 span
     */
    private void updateSpanWithResult(Span span, AiOperationResult result) {
        if (result.getCompletion() != null) {
            span.setAttribute(GEN_AI_COMPLETION, result.getCompletion());
        }
        if (result.getPromptTokens() != null) {
            span.setAttribute(GEN_AI_USAGE_PROMPT_TOKENS, result.getPromptTokens());
        }
        if (result.getCompletionTokens() != null) {
            span.setAttribute(GEN_AI_USAGE_COMPLETION_TOKENS, result.getCompletionTokens());
        }
        if (result.getTotalTokens() != null) {
            span.setAttribute(GEN_AI_USAGE_TOTAL_TOKENS, result.getTotalTokens());
        }
    }

    /**
     * 将 Map 转换为 JSON 字符串
     */
    private String convertMapToJson(Map<String, Object> map) {
        try {
            // 简单的 JSON 转换，实际项目中可以使用 Jackson 或其他 JSON 库
            StringBuilder json = new StringBuilder("{");
            boolean first = true;
            for (Map.Entry<String, Object> entry : map.entrySet()) {
                if (!first) {
                    json.append(",");
                }
                json.append("\"").append(entry.getKey()).append("\":\"")
                    .append(entry.getValue()).append("\"");
                first = false;
            }
            json.append("}");
            return json.toString();
        } catch (Exception e) {
            log.warn("Failed to convert metadata to JSON", e);
            return "{}";
        }
    }

    /**
     * 获取当前活跃的 span
     */
    public Span getCurrentSpan() {
        return Span.current();
    }

    /**
     * 检查追踪是否启用
     */
    public boolean isEnabled() {
        return properties.isEnabled() && properties.isValid();
    }
}

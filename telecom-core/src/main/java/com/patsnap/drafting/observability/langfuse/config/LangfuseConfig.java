package com.patsnap.drafting.observability.langfuse.config;

import com.langfuse.client.LangfuseClient;
import com.patsnap.drafting.observability.langfuse.health.LangfuseHealthIndicator;
import com.patsnap.drafting.observability.langfuse.tracer.LangfuseTracer;
import com.patsnap.drafting.observability.langfuse.tracer.TracingContextHolder;
import io.opentelemetry.api.trace.Tracer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.actuate.health.HealthIndicator;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;

/**
 * Langfuse 主配置类
 *
 * <AUTHOR> Assistant
 * @date 2025/01/31
 */
@Slf4j
@Configuration
@RequiredArgsConstructor
@EnableAspectJAutoProxy
@ConditionalOnProperty(prefix = "configs.com.patsnap.drafting.langfuse", name = "enabled", havingValue = "true")
public class LangfuseConfig {

    private final LangfuseProperties langfuseProperties;
    
    @Bean
    public LangfuseClient langfuseClient() {
        return LangfuseClient.builder()
                .url(langfuseProperties.getHost())
                .credentials(langfuseProperties.getPublicKey(), langfuseProperties.getSecretKey())
                .build();
    }

    /**
     * 创建 Langfuse Tracer
     */
    @Bean
    public LangfuseTracer langfuseTracer(Tracer tracer) {
        log.info("Creating LangfuseTracer with service name: {}", langfuseProperties.getServiceName());
        return new LangfuseTracer(tracer, langfuseProperties);
    }

    /**
     * 创建追踪上下文持有者
     */
    @Bean
    public TracingContextHolder tracingContextHolder() {
        return new TracingContextHolder();
    }

    /**
     * 创建 Langfuse 健康检查指示器
     */
    @Bean
    public HealthIndicator langfuseHealthIndicator() {
        return new LangfuseHealthIndicator(langfuseProperties);
    }
}

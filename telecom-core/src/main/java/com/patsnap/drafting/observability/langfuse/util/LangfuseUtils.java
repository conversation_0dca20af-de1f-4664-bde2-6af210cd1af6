package com.patsnap.drafting.observability.langfuse.util;

import com.patsnap.drafting.observability.langfuse.tracer.AiOperationContext;
import com.patsnap.drafting.observability.langfuse.tracer.AiOperationResult;
import com.patsnap.drafting.observability.langfuse.tracer.TracingContextHolder;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Langfuse 工具类
 *
 * <AUTHOR> Assistant
 * @date 2025/01/31
 */
@Slf4j
@UtilityClass
public class LangfuseUtils {

    /**
     * 设置用户追踪上下文
     */
    public static void setUserContext(String userId, String sessionId) {
        TracingContextHolder.setContext(userId, sessionId);
    }

    /**
     * 清除追踪上下文
     */
    public static void clearContext() {
        TracingContextHolder.clear();
    }

    /**
     * 创建 OpenAI 聊天完成上下文
     */
    public static AiOperationContext createOpenAiChatContext(String model, String prompt, String userId, String sessionId) {
        return AiOperationContext.builder()
                .system("openai")
                .model(model)
                .prompt(prompt)
                .userId(userId)
                .sessionId(sessionId)
                .operationType("chat_completion")
                .build();
    }

    /**
     * 创建算法服务上下文
     */
    public static AiOperationContext createAlgorithmContext(String algorithmName, String taskId) {
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("task_id", taskId);
        metadata.put("algorithm_name", algorithmName);

        return AiOperationContext.builder()
                .system("patsnap_algorithm")
                .operationType("algorithm_processing")
                .metadata(metadata)
                .tags(Arrays.asList("algorithm", "ai_processing"))
                .build();
    }

    /**
     * 创建成功的 AI 操作结果
     */
    public static AiOperationResult createSuccessResult(String completion, Long promptTokens, Long completionTokens) {
        return AiOperationResult.success(completion, promptTokens, completionTokens);
    }

    /**
     * 创建带成本信息的成功结果
     */
    public static AiOperationResult createSuccessResultWithCost(String completion, Long promptTokens, 
                                                               Long completionTokens, BigDecimal cost) {
        return AiOperationResult.success(completion, promptTokens, completionTokens, cost, "USD");
    }

    /**
     * 创建失败的 AI 操作结果
     */
    public static AiOperationResult createFailureResult(String error) {
        return AiOperationResult.failure(error);
    }

    /**
     * 计算 OpenAI 模型的成本（简化版本）
     */
    public static BigDecimal calculateOpenAiCost(String model, Long promptTokens, Long completionTokens) {
        if (promptTokens == null || completionTokens == null) {
            return BigDecimal.ZERO;
        }

        // 简化的价格计算，实际项目中应该从配置文件或数据库获取最新价格
        BigDecimal promptCost = BigDecimal.ZERO;
        BigDecimal completionCost = BigDecimal.ZERO;

        switch (model.toLowerCase()) {
            case "gpt-4":
            case "gpt-4-0613":
                promptCost = new BigDecimal("0.03").multiply(new BigDecimal(promptTokens)).divide(new BigDecimal("1000"));
                completionCost = new BigDecimal("0.06").multiply(new BigDecimal(completionTokens)).divide(new BigDecimal("1000"));
                break;
            case "gpt-4-turbo":
            case "gpt-4-turbo-preview":
                promptCost = new BigDecimal("0.01").multiply(new BigDecimal(promptTokens)).divide(new BigDecimal("1000"));
                completionCost = new BigDecimal("0.03").multiply(new BigDecimal(completionTokens)).divide(new BigDecimal("1000"));
                break;
            case "gpt-3.5-turbo":
            case "gpt-3.5-turbo-0613":
                promptCost = new BigDecimal("0.0015").multiply(new BigDecimal(promptTokens)).divide(new BigDecimal("1000"));
                completionCost = new BigDecimal("0.002").multiply(new BigDecimal(completionTokens)).divide(new BigDecimal("1000"));
                break;
            default:
                log.warn("Unknown model for cost calculation: {}", model);
                return BigDecimal.ZERO;
        }

        return promptCost.add(completionCost);
    }

    /**
     * 从异常中提取错误信息
     */
    public static String extractErrorMessage(Throwable throwable) {
        if (throwable == null) {
            return "Unknown error";
        }

        String message = throwable.getMessage();
        if (message == null || message.isEmpty()) {
            message = throwable.getClass().getSimpleName();
        }

        // 限制错误信息长度
        if (message.length() > 500) {
            message = message.substring(0, 500) + "...";
        }

        return message;
    }

    /**
     * 生成会话 ID
     */
    public static String generateSessionId() {
        return "session_" + System.currentTimeMillis() + "_" + 
               Integer.toHexString((int) (Math.random() * 0x10000));
    }

    /**
     * 验证追踪上下文是否有效
     */
    public static boolean isValidContext(String userId, String sessionId) {
        return userId != null && !userId.trim().isEmpty() && 
               sessionId != null && !sessionId.trim().isEmpty();
    }

    /**
     * 安全地截断文本内容
     */
    public static String truncateText(String text, int maxLength) {
        if (text == null) {
            return null;
        }
        if (text.length() <= maxLength) {
            return text;
        }
        return text.substring(0, maxLength - 3) + "...";
    }

    /**
     * 创建标准的元数据 Map
     */
    public static Map<String, Object> createMetadata(String... keyValuePairs) {
        Map<String, Object> metadata = new HashMap<>();
        for (int i = 0; i < keyValuePairs.length - 1; i += 2) {
            metadata.put(keyValuePairs[i], keyValuePairs[i + 1]);
        }
        return metadata;
    }

    /**
     * 创建标准的标签列表
     */
    public static List<String> createTags(String... tags) {
        return Arrays.asList(tags);
    }
}

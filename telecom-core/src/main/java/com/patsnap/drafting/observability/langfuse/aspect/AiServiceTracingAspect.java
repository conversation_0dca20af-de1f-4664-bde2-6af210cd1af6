package com.patsnap.drafting.observability.langfuse.aspect;

import com.patsnap.drafting.observability.langfuse.tracer.AiOperationContext;
import com.patsnap.drafting.observability.langfuse.tracer.AiOperationResult;
import com.patsnap.drafting.observability.langfuse.tracer.LangfuseTracer;
import com.patsnap.drafting.observability.langfuse.tracer.TracingContextHolder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * AI 服务追踪切面
 *
 * <AUTHOR> Assistant
 * @date 2025/01/31
 */
@Slf4j
@Aspect
@Component
@RequiredArgsConstructor
@ConditionalOnProperty(prefix = "configs.com.patsnap.drafting.langfuse", name = "enabled", havingValue = "true")
public class AiServiceTracingAspect {

    private final LangfuseTracer langfuseTracer;

    /**
     * 拦截带有 @LangfuseTracing 注解的方法
     */
    @Around("@annotation(langfuseTracing)")
    public Object traceAiOperation(ProceedingJoinPoint joinPoint, LangfuseTracing langfuseTracing) throws Throwable {
        if (!langfuseTracer.isEnabled()) {
            return joinPoint.proceed();
        }

        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        Object[] args = joinPoint.getArgs();

        // 构建操作名称
        String operationName = langfuseTracing.operationName().isEmpty() 
                ? method.getDeclaringClass().getSimpleName() + "." + method.getName()
                : langfuseTracing.operationName();

        // 构建 AI 操作上下文
        AiOperationContext context = buildAiOperationContext(langfuseTracing, method, args);

        long startTime = System.currentTimeMillis();

        try {
            // 执行追踪操作
            Object result = langfuseTracer.traceAiOperation(operationName, context, () -> {
                try {
                    return joinPoint.proceed();
                } catch (Throwable e) {
                    throw new RuntimeException(e);
                }
            });

            // 如果结果不是 AiOperationResult，尝试包装
            if (!(result instanceof AiOperationResult) && langfuseTracing.recordResult()) {
                long latency = System.currentTimeMillis() - startTime;
                result = wrapResult(result, latency);
            }

            return result;

        } catch (Exception e) {
            log.error("Error in AI operation tracing for method: {}", operationName, e);
            throw e;
        }
    }

    /**
     * 构建 AI 操作上下文
     */
    private AiOperationContext buildAiOperationContext(LangfuseTracing annotation, Method method, Object[] args) {
        AiOperationContext.AiOperationContextBuilder builder = AiOperationContext.builder();

        // 设置基本信息
        builder.system(annotation.system().isEmpty() ? "custom" : annotation.system());
        builder.operationType(annotation.operationType().isEmpty() ? "ai_operation" : annotation.operationType());

        // 设置标签
        if (annotation.tags().length > 0) {
            builder.tags(Arrays.asList(annotation.tags()));
        }

        // 从方法参数中提取信息
        Parameter[] parameters = method.getParameters();
        Map<String, Object> metadata = new HashMap<>();

        for (int i = 0; i < parameters.length && i < args.length; i++) {
            Parameter param = parameters[i];
            Object arg = args[i];

            if (arg == null) continue;

            // 提取提示词
            if (!annotation.promptParameter().isEmpty() && 
                param.getName().equals(annotation.promptParameter())) {
                builder.prompt(arg.toString());
            }

            // 提取模型信息
            if (!annotation.modelParameter().isEmpty() && 
                param.getName().equals(annotation.modelParameter())) {
                builder.model(arg.toString());
            }

            // 记录参数到元数据
            if (annotation.recordParameters()) {
                metadata.put(param.getName(), arg.toString());
            }
        }

        // 设置用户和会话信息
        TracingContextHolder.TracingContext tracingContext = TracingContextHolder.getContext();
        if (tracingContext != null) {
            builder.userId(tracingContext.getUserId());
            builder.sessionId(tracingContext.getSessionId());
            
            if (tracingContext.getTags() != null) {
                List<String> existingTags = builder.build().getTags();
                if (existingTags != null) {
                    existingTags.addAll(tracingContext.getTags());
                } else {
                    builder.tags(tracingContext.getTags());
                }
            }
        }

        builder.metadata(metadata);
        return builder.build();
    }

    /**
     * 包装结果为 AiOperationResult
     */
    private AiOperationResult wrapResult(Object result, long latency) {
        if (result == null) {
            return AiOperationResult.builder()
                    .success(true)
                    .latencyMs(latency)
                    .build();
        }

        return AiOperationResult.builder()
                .completion(result.toString())
                .success(true)
                .latencyMs(latency)
                .build();
    }
}

package com.patsnap.drafting.observability.langfuse.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * Langfuse 配置属性
 *
 * <AUTHOR> Assistant
 * @date 2025/01/31
 */
@Data
@Component
@ConfigurationProperties(prefix = "configs.com.patsnap.drafting.langfuse")
public class LangfuseProperties {

    /**
     * 是否启用 Langfuse 追踪
     */
    private boolean enabled = false;

    /**
     * Langfuse 公钥
     */
    private String publicKey;

    /**
     * Langfuse 私钥
     */
    private String secretKey;

    /**
     * Langfuse 主机地址
     */
    private String host = "https://cloud.langfuse.com";

    /**
     * OpenTelemetry 导出器端点
     */
    private String otlpEndpoint;

    /**
     * 服务名称
     */
    private String serviceName = "s-analytics-telecom";

    /**
     * 采样率 (0.0 - 1.0)
     */
    private double samplingProbability = 1.0;

    /**
     * 批量导出配置
     */
    private BatchConfig batch = new BatchConfig();

    /**
     * 超时配置
     */
    private TimeoutConfig timeout = new TimeoutConfig();

    /**
     * 重试配置
     */
    private RetryConfig retry = new RetryConfig();

    @Data
    public static class BatchConfig {
        /**
         * 批量大小
         */
        private int maxExportBatchSize = 512;

        /**
         * 导出超时时间（毫秒）
         */
        private long exportTimeoutMillis = 30000;

        /**
         * 调度延迟（毫秒）
         */
        private long scheduleDelayMillis = 5000;

        /**
         * 最大队列大小
         */
        private int maxQueueSize = 2048;
    }

    @Data
    public static class TimeoutConfig {
        /**
         * 连接超时时间（毫秒）
         */
        private long connectTimeoutMillis = 10000;

        /**
         * 读取超时时间（毫秒）
         */
        private long readTimeoutMillis = 30000;

        /**
         * 写入超时时间（毫秒）
         */
        private long writeTimeoutMillis = 30000;
    }

    @Data
    public static class RetryConfig {
        /**
         * 最大重试次数
         */
        private int maxRetries = 3;

        /**
         * 重试延迟（毫秒）
         */
        private long retryDelayMillis = 1000;

        /**
         * 重试延迟倍数
         */
        private double retryDelayMultiplier = 2.0;
    }

    /**
     * 获取完整的 OTLP 端点 URL
     */
    public String getOtlpEndpoint() {
        if (otlpEndpoint != null && !otlpEndpoint.isEmpty()) {
            return otlpEndpoint;
        }
        return host + "/api/public/otel";
    }

    /**
     * 获取 Basic Auth 认证字符串
     */
    public String getAuthString() {
        if (publicKey == null || secretKey == null) {
            return null;
        }
        String credentials = publicKey + ":" + secretKey;
        return java.util.Base64.getEncoder().encodeToString(credentials.getBytes());
    }

    /**
     * 验证配置是否有效
     */
    public boolean isValid() {
        return enabled && publicKey != null && !publicKey.isEmpty() 
               && secretKey != null && !secretKey.isEmpty()
               && host != null && !host.isEmpty();
    }
}

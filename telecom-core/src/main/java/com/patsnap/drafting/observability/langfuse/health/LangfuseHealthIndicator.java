package com.patsnap.drafting.observability.langfuse.health;

import com.patsnap.drafting.observability.langfuse.config.LangfuseProperties;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.actuate.health.Health;
import org.springframework.boot.actuate.health.HealthIndicator;

import java.net.HttpURLConnection;
import java.net.URL;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * Langfuse 健康检查指示器
 *
 * <AUTHOR> Assistant
 * @date 2025/01/31
 */
@Slf4j
@RequiredArgsConstructor
public class LangfuseHealthIndicator implements HealthIndicator {

    private final LangfuseProperties langfuseProperties;

    @Override
    public Health health() {
        if (!langfuseProperties.isEnabled()) {
            return Health.up()
                    .withDetail("status", "disabled")
                    .withDetail("message", "Langfuse tracing is disabled")
                    .build();
        }

        if (!langfuseProperties.isValid()) {
            return Health.down()
                    .withDetail("status", "invalid_config")
                    .withDetail("message", "Langfuse configuration is invalid")
                    .withDetail("host", langfuseProperties.getHost())
                    .withDetail("hasPublicKey", langfuseProperties.getPublicKey() != null)
                    .withDetail("hasSecretKey", langfuseProperties.getSecretKey() != null)
                    .build();
        }

        try {
            // 异步检查连接性
            CompletableFuture<Boolean> connectivityCheck = CompletableFuture.supplyAsync(() -> {
                try {
                    return checkConnectivity();
                } catch (Exception e) {
                    log.debug("Langfuse connectivity check failed", e);
                    return false;
                }
            });

            // 等待最多 5 秒
            boolean isConnected = connectivityCheck.get(5, TimeUnit.SECONDS);

            if (isConnected) {
                return Health.up()
                        .withDetail("status", "connected")
                        .withDetail("endpoint", langfuseProperties.getOtlpEndpoint())
                        .withDetail("serviceName", langfuseProperties.getServiceName())
                        .withDetail("samplingProbability", langfuseProperties.getSamplingProbability())
                        .build();
            } else {
                return Health.down()
                        .withDetail("status", "connection_failed")
                        .withDetail("endpoint", langfuseProperties.getOtlpEndpoint())
                        .withDetail("message", "Unable to connect to Langfuse endpoint")
                        .build();
            }

        } catch (Exception e) {
            log.warn("Langfuse health check failed", e);
            return Health.down()
                    .withDetail("status", "health_check_error")
                    .withDetail("error", e.getMessage())
                    .withDetail("endpoint", langfuseProperties.getOtlpEndpoint())
                    .build();
        }
    }

    /**
     * 检查与 Langfuse 的连接性
     */
    private boolean checkConnectivity() {
        try {
            // 尝试连接到 Langfuse 主机
            URL url = new URL(langfuseProperties.getHost());
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("HEAD");
            connection.setConnectTimeout(5000);
            connection.setReadTimeout(5000);
            
            int responseCode = connection.getResponseCode();
            connection.disconnect();
            
            // 200-299 或 401/403 都表示服务可达（401/403 表示认证问题，但服务是可用的）
            return (responseCode >= 200 && responseCode < 300) || 
                   responseCode == 401 || responseCode == 403;
                   
        } catch (Exception e) {
            log.debug("Connectivity check failed for host: {}", langfuseProperties.getHost(), e);
            return false;
        }
    }
}

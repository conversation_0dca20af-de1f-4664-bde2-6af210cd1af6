package com.patsnap.drafting.observability.langfuse.example;

import com.patsnap.drafting.service.LangfuseService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Langfuse Java SDK 使用示例
 * 展示如何在业务代码中使用 Langfuse Java SDK
 *
 * <AUTHOR> Assistant
 * @date 2025/01/31
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnProperty(prefix = "configs.com.patsnap.drafting.langfuse", name = "enabled", havingValue = "true")
public class LangfuseJavaSdkUsageExample {

    private final LangfuseService langfuseService;

    /**
     * 示例：获取所有提示词模板
     */
    public void exampleGetPrompts() {
        log.info("=== Langfuse API 示例：获取提示词模板 ===");
        
        try {
            var promptsOptional = langfuseService.getPrompts();
            if (promptsOptional.isPresent()) {
                List<Map<String, Object>> prompts = promptsOptional.get();
                log.info("成功获取 {} 个提示词模板", prompts.size());
                
                // 打印前几个提示词的信息
                prompts.stream()
                        .limit(3)
                        .forEach(prompt -> {
                            log.info("提示词 - 名称: {}, 版本: {}, 标签: {}", 
                                    prompt.get("name"), 
                                    prompt.get("version"), 
                                    prompt.get("tags"));
                        });
            } else {
                log.warn("未能获取提示词模板");
            }
        } catch (Exception e) {
            log.error("获取提示词模板时发生错误", e);
        }
    }

    /**
     * 示例：使用带追踪的 AI 操作
     */
    public String exampleAiOperationWithTracing() {
        log.info("=== Langfuse Java SDK 示例：带追踪的 AI 操作 ===");
        
        try {
            return langfuseService.executeWithTracing(
                    "ai_text_generation",           // 操作名称
                    "openai",                       // 系统名称
                    "text_generation",              // 操作类型
                    "请生成一个关于专利技术的摘要",        // 输入内容
                    "gpt-4",                        // 模型名称
                    () -> {
                        // 模拟 AI 操作
                        Thread.sleep(1000); // 模拟处理时间
                        return "这是一个关于人工智能技术的专利摘要，涵盖了机器学习算法的创新应用...";
                    }
            );
        } catch (Exception e) {
            log.error("执行 AI 操作时发生错误", e);
            return null;
        }
    }

    /**
     * 示例：手动管理追踪生命周期
     */
    public String exampleManualTracing() {
        log.info("=== Langfuse API 示例：手动追踪管理 ===");
        
        // 创建元数据
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("user_id", "user_12345");
        metadata.put("session_id", "session_67890");
        metadata.put("request_source", "web_ui");
        
        // 创建追踪上下文并执行操作
        var context = langfuseService.createTrace(
                "patent_translation",           // 操作名称
                "google_translate",             // 系统名称  
                "translation",                  // 操作类型
                "源文本：这是一个技术专利文档",     // 输入内容
                "translate-api-v3",             // 模型名称
                metadata,                       // 元数据
                "translation", "patent"         // 标签
        );
        
        return langfuseService.executeWithContext("patent_translation", context, () -> {
            try {
                // 模拟翻译操作
                Thread.sleep(500);
                String translatedText = "Source text: This is a technical patent document";
                log.info("翻译操作完成");
                return translatedText;
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new RuntimeException("翻译操作被中断", e);
            }
        });
    }

    /**
     * 示例：查询特定追踪
     */
    public void exampleGetTrace(String traceId) {
        log.info("=== Langfuse API 示例：查询追踪 ===");
        
        try {
            var traceOptional = langfuseService.getTrace(traceId);
            if (traceOptional.isPresent()) {
                var trace = traceOptional.get();
                log.info("追踪详情 - ID: {}, 名称: {}, 元数据: {}", 
                        trace.get("id"), 
                        trace.get("name"), 
                        trace.get("metadata"));
            } else {
                log.warn("未找到追踪: {}", traceId);
            }
        } catch (Exception e) {
            log.error("查询追踪时发生错误: {}", traceId, e);
        }
    }

    /**
     * 示例：健康检查
     */
    public void exampleHealthCheck() {
        log.info("=== Langfuse API 示例：健康检查 ===");
        
        boolean isHealthy = langfuseService.isHealthy();
        log.info("Langfuse 服务健康状态: {}", isHealthy ? "健康" : "不健康");
    }

    /**
     * 运行所有示例
     */
    public void runAllExamples() {
        log.info("开始运行 Langfuse API 所有示例...");
        
        try {
            exampleHealthCheck();
            exampleGetPrompts();
            String result1 = exampleAiOperationWithTracing();
            log.info("带追踪的 AI 操作结果: {}", result1);
            
            String result2 = exampleManualTracing();
            log.info("手动追踪操作结果: {}", result2);
            
        } catch (Exception e) {
            log.error("运行示例时发生错误", e);
        }
        
        log.info("Langfuse API 示例运行完成");
    }
}
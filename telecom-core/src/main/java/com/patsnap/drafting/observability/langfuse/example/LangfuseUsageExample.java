package com.patsnap.drafting.observability.langfuse.example;

import com.patsnap.drafting.observability.langfuse.aspect.LangfuseTracing;
import com.patsnap.drafting.observability.langfuse.tracer.AiOperationContext;
import com.patsnap.drafting.observability.langfuse.tracer.AiOperationResult;
import com.patsnap.drafting.observability.langfuse.tracer.LangfuseTracer;
import com.patsnap.drafting.observability.langfuse.tracer.TracingContextHolder;
import com.patsnap.drafting.observability.langfuse.util.LangfuseUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.concurrent.Callable;

/**
 * Langfuse 使用示例
 * 展示如何在实际业务中使用 Langfuse 追踪
 *
 * <AUTHOR> Assistant
 * @date 2025/01/31
 */
@Slf4j
@Service
@RequiredArgsConstructor
@ConditionalOnProperty(prefix = "configs.com.patsnap.drafting.langfuse", name = "enabled", havingValue = "true")
public class LangfuseUsageExample {

    private final LangfuseTracer langfuseTracer;

    /**
     * 示例1：使用注解进行自动追踪
     */
    @LangfuseTracing(
            operationName = "example_ai_chat",
            system = "openai",
            operationType = "chat_completion",
            promptParameter = "prompt",
            modelParameter = "model",
            tags = {"example", "demo"}
    )
    public String exampleWithAnnotation(String model, String prompt) {
        log.info("Processing AI request with model: {} and prompt: {}", model, prompt);
        
        // 模拟 AI 处理
        try {
            Thread.sleep(100); // 模拟处理时间
            return "This is a simulated AI response for: " + prompt;
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("Processing interrupted", e);
        }
    }

    /**
     * 示例2：手动使用 LangfuseTracer 进行追踪
     */
    public String exampleWithManualTracing(String userId, String sessionId, String model, String prompt) {
        // 创建 AI 操作上下文
        AiOperationContext context = LangfuseUtils.createOpenAiChatContext(model, prompt, userId, sessionId);

        // 使用 tracer 执行追踪操作
        return langfuseTracer.traceAiOperation("manual_ai_chat", context, () -> {
            log.info("Processing manual AI request");
            
            // 模拟 AI 处理
            try {
                Thread.sleep(150);
                String response = "Manual traced response for: " + prompt;
                
                // 返回包含详细信息的结果
                return AiOperationResult.success(response, 50L, 100L);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new RuntimeException("Processing interrupted", e);
            }
        }).toString();
    }

    /**
     * 示例3：使用 TracingContextHolder 设置用户上下文
     */
    public String exampleWithContextHolder(String userId, String sessionId, String prompt) {
        try {
            // 设置追踪上下文
            LangfuseUtils.setUserContext(userId, sessionId);
            
            // 调用带注解的方法，会自动使用上下文中的用户信息
            return exampleWithAnnotation("gpt-3.5-turbo", prompt);
            
        } finally {
            // 清除上下文
            LangfuseUtils.clearContext();
        }
    }

    /**
     * 示例4：复杂的 AI 操作追踪，包含成本计算
     */
    public AiOperationResult exampleWithCostTracking(String model, String prompt) {
        AiOperationContext context = AiOperationContext.builder()
                .system("openai")
                .model(model)
                .prompt(prompt)
                .operationType("chat_completion")
                .temperature(0.7)
                .maxTokens(1000)
                .build();

        return (AiOperationResult) langfuseTracer.traceAiOperation("cost_tracked_ai_chat", context, () -> {
            log.info("Processing AI request with cost tracking");
            
            try {
                Thread.sleep(200);
                
                // 模拟 AI 响应和 token 使用
                String response = "Cost-tracked response for: " + prompt;
                Long promptTokens = 45L;
                Long completionTokens = 120L;
                
                // 计算成本
                BigDecimal cost = LangfuseUtils.calculateOpenAiCost(model, promptTokens, completionTokens);
                
                return LangfuseUtils.createSuccessResultWithCost(response, promptTokens, completionTokens, cost);
                
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                return LangfuseUtils.createFailureResult("Processing interrupted: " + e.getMessage());
            }
        });
    }

    /**
     * 示例5：错误处理和异常追踪
     */
    @LangfuseTracing(
            operationName = "example_with_error_handling",
            system = "openai",
            operationType = "chat_completion",
            recordException = true
    )
    public String exampleWithErrorHandling(String prompt, boolean shouldFail) {
        if (shouldFail) {
            throw new RuntimeException("Simulated AI processing error");
        }
        
        return "Success response for: " + prompt;
    }

    /**
     * 示例6：批量操作追踪
     */
    public void exampleBatchProcessing(String userId, String sessionId, String[] prompts) {
        try {
            LangfuseUtils.setUserContext(userId, sessionId);
            
            for (int i = 0; i < prompts.length; i++) {
                final int index = i;  // 创建 final 副本
                final String prompt = prompts[i];  // 创建 final 副本
                
                // 为每个批量项创建单独的追踪
                langfuseTracer.trace("batch_item_" + index, () -> {
                    log.info("Processing batch item {}: {}", index, prompt);
                    
                    // 模拟处理
                    try {
                        Thread.sleep(50);
                        return "Batch response " + index + " for: " + prompt;
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        throw new RuntimeException("Batch processing interrupted", e);
                    }
                });
            }
            
        } finally {
            LangfuseUtils.clearContext();
        }
    }

    /**
     * 示例7：嵌套追踪操作
     */
    public String exampleNestedTracing(String userId, String sessionId, String prompt) {
        return langfuseTracer.trace("parent_operation", userId, sessionId, () -> {
            log.info("Starting parent operation");
            
            // 第一个子操作
            String preprocessed = langfuseTracer.trace("preprocess_prompt", () -> {
                log.info("Preprocessing prompt");
                return "Preprocessed: " + prompt;
            });
            
            // 第二个子操作
            String aiResponse = langfuseTracer.trace("ai_generation", () -> {
                log.info("Generating AI response");
                try {
                    Thread.sleep(100);
                    return "AI response for: " + preprocessed;
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    throw new RuntimeException("AI generation interrupted", e);
                }
            });
            
            // 第三个子操作
            return langfuseTracer.trace("postprocess_response", () -> {
                log.info("Postprocessing response");
                return "Final: " + aiResponse;
            });
        });
    }
}

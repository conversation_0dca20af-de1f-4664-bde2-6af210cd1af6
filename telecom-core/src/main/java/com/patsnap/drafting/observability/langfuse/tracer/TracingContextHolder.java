package com.patsnap.drafting.observability.langfuse.tracer;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * 追踪上下文持有者
 * 用于在请求生命周期内保持用户和会话信息
 *
 * <AUTHOR> Assistant
 * @date 2025/01/31
 */
@Slf4j
public class TracingContextHolder {

    private static final ThreadLocal<TracingContext> CONTEXT_HOLDER = new ThreadLocal<>();

    /**
     * 设置追踪上下文
     */
    public static void setContext(TracingContext context) {
        CONTEXT_HOLDER.set(context);
        log.debug("Set tracing context: userId={}, sessionId={}", 
                context.getUserId(), context.getSessionId());
    }

    /**
     * 设置追踪上下文
     */
    public static void setContext(String userId, String sessionId) {
        TracingContext context = new TracingContext();
        context.setUserId(userId);
        context.setSessionId(sessionId);
        setContext(context);
    }

    /**
     * 获取当前追踪上下文
     */
    public static TracingContext getContext() {
        return CONTEXT_HOLDER.get();
    }

    /**
     * 获取当前用户 ID
     */
    public static String getCurrentUserId() {
        TracingContext context = getContext();
        return context != null ? context.getUserId() : null;
    }

    /**
     * 获取当前会话 ID
     */
    public static String getCurrentSessionId() {
        TracingContext context = getContext();
        return context != null ? context.getSessionId() : null;
    }

    /**
     * 清除追踪上下文
     */
    public static void clear() {
        CONTEXT_HOLDER.remove();
        log.debug("Cleared tracing context");
    }

    /**
     * 追踪上下文数据类
     */
    @Data
    public static class TracingContext {
        /**
         * 用户 ID
         */
        private String userId;

        /**
         * 会话 ID
         */
        private String sessionId;

        /**
         * 追踪名称
         */
        private String traceName;

        /**
         * 额外的元数据
         */
        private java.util.Map<String, String> metadata;

        /**
         * 标签
         */
        private java.util.List<String> tags;
    }
}

package com.patsnap.drafting.observability.langfuse.aspect;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Langfuse 追踪注解
 * 用于标记需要进行 Langfuse 追踪的方法
 *
 * <AUTHOR> Assistant
 * @date 2025/01/31
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface LangfuseTracing {

    /**
     * 操作名称，默认使用方法名
     */
    String operationName() default "";

    /**
     * AI 系统名称 (如: openai, anthropic)
     */
    String system() default "";

    /**
     * 操作类型 (如: chat_completion, embedding)
     */
    String operationType() default "";

    /**
     * 是否记录方法参数
     */
    boolean recordParameters() default true;

    /**
     * 是否记录返回值
     */
    boolean recordResult() default true;

    /**
     * 是否记录异常
     */
    boolean recordException() default true;

    /**
     * 自定义标签
     */
    String[] tags() default {};

    /**
     * 提示词参数名称（用于从方法参数中提取提示词）
     */
    String promptParameter() default "";

    /**
     * 模型参数名称（用于从方法参数中提取模型信息）
     */
    String modelParameter() default "";
}

package com.patsnap.drafting.util;

import com.patsnap.drafting.model.aitranslation.ParagraphBO;
import com.patsnap.drafting.model.aitranslation.TranslationBO;
import jodd.util.StringPool;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 段落处理工具类
 *
 * <AUTHOR>
 * @date 2024/07/25
 */
public class ParagraphUtils {

    public static final String PARAGRAPH_PREFIX = "{¥¥¥}";
    public static final String CARRIAGE_RETURN_TAG = "\n";

    public static List<String> splitText(String text, int maxLength) {
        List<String> chunks = splitText(text, maxLength, 0, true, true);

        // 添加兜底策略，确保最后一个字符不丢失
        if (!chunks.isEmpty() && text != null && !text.isEmpty()) {
            String lastChunk = chunks.get(chunks.size() - 1);
            char lastCharInText = text.charAt(text.length() - 1);
            char lastCharInChunk = lastChunk.charAt(lastChunk.length() - 1);

            // 如果最后一个字符不匹配，将原文的最后一个字符添加到最后一个分段
            if (lastCharInText != lastCharInChunk) {
                chunks.set(chunks.size() - 1, lastChunk + lastCharInText);
            }
        }

        return chunks;
    }

    /**
     * 在每个段落的开头添加特定的标识符
     *
     * @param text 长文本内容
     * @return
     */
    public static String addParagraphPrefix(String text) {
        if (text == null) {
            return null;
        }
        text = PARAGRAPH_PREFIX + text;
        return text.replace("\n", "\n" + PARAGRAPH_PREFIX);
    }



    /**
     * 恢复文本中携带的回车
     *
     * @param text 长文本内容
     * @return
     */
    public static String removeCarriageReturn(String text) {
        if (text == null) {
            return null;
        }
        return text.replace(CARRIAGE_RETURN_TAG, "");
    }

    /**
     * 恢复文本中携带的回车为空格，字符串为null返回"null"
     *
     * @param text 原文本内容
     * @return 替换后的文本
     */
    public static String replaceCarriageReturn(String text) {
        return Optional.ofNullable(text)
                .map(item -> item.replace(StringPool.NEWLINE, StringPool.SPACE))
                .orElse(StringPool.NULL);
    }

    /**
     * 恢复文本中携带的回车为空格，字符串为null返回"null"
     *
     * @param text 原文本内容
     * @return 替换后的文本
     */
    public static String replaceMultipleCarriageReturn(String text) {
        return Optional.ofNullable(text)
                .map(item -> item.replaceAll("\\n{2,10}", StringPool.NEWLINE))
                .orElse(StringPool.EMPTY);
    }

    /**
     * 根据段落前缀, 计算每个句子所属的段落index
     *
     * @param sentences 句子列表
     */
    public static void addParagraphIndex(List<? extends ParagraphBO> sentences) {
        if (CollectionUtils.isEmpty(sentences)) {
            return;
        }
        int paragraphIndex = -1;
        for (ParagraphBO sentence : sentences) {
            String srcText = sentence.getSrcText();
            if (srcText.startsWith(PARAGRAPH_PREFIX)) {
                paragraphIndex++;
                srcText = srcText.substring(PARAGRAPH_PREFIX.length()).replace(PARAGRAPH_PREFIX, "\n");
                sentence.setSrcText(srcText);
            }
            sentence.setGraphIndex(paragraphIndex);
        }
    }

    /**
     * 根据段落前缀, 计算每个句子所属的段落index
     * 该方法与addParagraphIndex的区别在于，使用回车标记区分段落，不使用段落前缀
     * 原因：自研模型需输入带有段落前缀的文本，故而使用另一种标记来区分段落
     * @param sentences 句子列表
     */
    public static void addParagraphIndexPlus(List<TranslationBO> sentences,
            List<Integer> paragraphIndexes) {
        if (CollectionUtils.isEmpty(sentences)) {
            return;
        }
        if (sentences.size() != paragraphIndexes.size()) {
            // 以实际返回分段
            int paragraphIndex = 0;
            for (TranslationBO sentence : sentences) {
                String srcText = sentence.getSrcText();
                sentence.setGraphIndex(paragraphIndex);
                paragraphIndex++;
                if (sentence.getSrcText() != null) {
                    sentence.setSrcText(sentence.getSrcText().replace(PARAGRAPH_PREFIX, ""));
                }
                if (sentence.getTranslatedText() != null) {
                    sentence.setTranslatedText(sentence.getTranslatedText().replace(PARAGRAPH_PREFIX, ""));
                }
            }
            return;
        }
        for (int i = 0; i < sentences.size(); i++) {
            TranslationBO sentence = sentences.get(i);
            sentence.setGraphIndex(paragraphIndexes.get(i));
            if (sentence.getSrcText() != null) {
                sentence.setSrcText(sentence.getSrcText().replace(PARAGRAPH_PREFIX, ""));
            }
            if (sentence.getTranslatedText() != null) {
                sentence.setTranslatedText(sentence.getTranslatedText().replace(PARAGRAPH_PREFIX, ""));
            }
        }

    }

    /**
     * 递归地将文本分割成指定大小的块
     *
     * @param text 要分割的文本
     * @param chunkSize 每个块的最大大小
     * @param chunkOverlap 块之间的重叠大小
     * @param adjustSeparatorPosition 是否调整分隔符位置（将分隔符从块开头移到前一个块的末尾）
     * @param mergeSmallChunks 是否合并小块
     * @return 分割后的文本块列表
     */
    public static List<String> splitText(String text, int chunkSize, int chunkOverlap,
            boolean adjustSeparatorPosition, boolean mergeSmallChunks) {
        // 定义分隔符列表，按优先级排序
        List<String> separators = Arrays.asList(
                "\n\n", "\n", "。", "，", ".", ";", ",", " ", ""
        );

        // 分割文本
        List<String> chunks = recursiveSplit(text, separators, chunkSize, chunkOverlap);

        // 调整分隔符位置
        if (adjustSeparatorPosition) {
            chunks = adjustSeparatorPositions(chunks);
        }

        // 合并小块
        if (mergeSmallChunks) {
            chunks = mergeSmallChunks(chunks, chunkSize);
        }

        return chunks;
    }

    /**
     * 递归地将文本分割成指定大小的块
     *
     * @param text 要分割的文本
     * @param separators 分隔符列表，按优先级排序
     * @param chunkSize 每个块的最大大小
     * @param chunkOverlap 块之间的重叠大小
     * @return 分割后的文本块列表
     */
    private static List<String> recursiveSplit(String text, List<String> separators, int chunkSize, int chunkOverlap) {
        // 如果文本为空，返回空列表
        if (text == null || text.isEmpty()) {
            return new ArrayList<>();
        }

        // 如果文本长度小于等于chunkSize，直接返回
        if (text.length() <= chunkSize) {
            List<String> result = new ArrayList<>();
            result.add(text);
            return result;
        }

        // 如果没有分隔符，或者已经用完了所有分隔符，则在chunkSize处直接分割
        if (separators.isEmpty()) {
            return splitBySize(text, chunkSize, chunkOverlap);
        }

        // 获取当前优先级最高的分隔符
        String separator = separators.get(0);
        List<String> remainingSeparators = separators.subList(1, separators.size());

        // 如果分隔符为空字符串，则在chunkSize处直接分割
        if (separator.isEmpty()) {
            return splitBySize(text, chunkSize, chunkOverlap);
        }

        // 使用当前分隔符分割文本
        String[] splits = text.split(Pattern.quote(separator));

        // 如果分割后只有一个块，则使用下一个分隔符继续分割
        if (splits.length == 1) {
            return recursiveSplit(text, remainingSeparators, chunkSize, chunkOverlap);
        }

        // 重新组合分割后的文本，保留分隔符
        List<String> chunks = new ArrayList<>();
        StringBuilder currentChunk = new StringBuilder();

        for (int i = 0; i < splits.length; i++) {
            String split = splits[i];
            StringBuilder nextPiece = new StringBuilder();

            // 如果不是第一个分割，添加分隔符，保留原始分隔符
            if (i > 0) {
                // 检查原文中这个位置是否确实有分隔符
                int prevEnd = 0;
                for (int j = 0; j < i; j++) {
                    prevEnd += splits[j].length() + separator.length();
                }
                // 从原文中获取实际的分隔符（可能包含多个字符）
                String actualSeparator = text.substring(prevEnd - separator.length(), prevEnd);
                nextPiece.append(actualSeparator);
            }
            nextPiece.append(split);

            // 如果是最后一个分割，检查原文是否以分隔符结束
            if (i == splits.length - 1 && text.endsWith(separator)) {
                nextPiece.append(separator);
            }

            // 如果当前块为空，直接添加
            if (currentChunk.length() == 0) {
                if (nextPiece.length() > chunkSize) {
                    // 如果单个片段超过大小限制，递归分割
                    chunks.addAll(recursiveSplit(nextPiece.toString(), remainingSeparators, chunkSize, chunkOverlap));
                } else {
                    currentChunk.append(nextPiece);
                }
                continue;
            }

            // 检查添加下一个片段是否会超过大小限制
            if (currentChunk.length() + nextPiece.length() <= chunkSize) {
                currentChunk.append(nextPiece);
            } else {
                // 保存当前块
                chunks.add(currentChunk.toString());
                currentChunk = new StringBuilder(nextPiece);

                // 如果新块超过大小限制，递归分割
                if (currentChunk.length() > chunkSize) {
                    chunks.addAll(recursiveSplit(currentChunk.toString(), remainingSeparators, chunkSize, chunkOverlap));
                    currentChunk = new StringBuilder();
                }
            }
        }

        // 添加最后一个块
        if (currentChunk.length() > 0) {
            if (currentChunk.length() > chunkSize) {
                chunks.addAll(recursiveSplit(currentChunk.toString(), remainingSeparators, chunkSize, chunkOverlap));
            } else {
                chunks.add(currentChunk.toString());
            }
        }

        // 处理重叠（如果需要）
        if (chunkOverlap > 0) {
            chunks = handleOverlap(chunks, chunkOverlap);
        }

        // 最后检查确保没有块超过大小限制
        List<String> finalChunks = new ArrayList<>();
        for (String chunk : chunks) {
            if (chunk.length() > chunkSize) {
                finalChunks.addAll(splitBySize(chunk, chunkSize, chunkOverlap));
            } else {
                finalChunks.add(chunk);
            }
        }

        return finalChunks;
    }

    /**
     * 处理文本块之间的重叠
     */
    private static List<String> handleOverlap(List<String> chunks, int chunkOverlap) {
        if (chunks.size() <= 1 || chunkOverlap <= 0) {
            return chunks;
        }

        List<String> result = new ArrayList<>();
        for (int i = 0; i < chunks.size(); i++) {
            String chunk = chunks.get(i);

            // 如果不是最后一个块，添加重叠部分
            if (i < chunks.size() - 1) {
                String nextChunk = chunks.get(i + 1);
                int overlapSize = Math.min(chunkOverlap, Math.min(chunk.length(), nextChunk.length()));

                // 如果当前块末尾和下一个块开头有重叠，保留较长的部分
                if (chunk.endsWith(nextChunk.substring(0, overlapSize))) {
                    result.add(chunk);
                } else {
                    // 否则添加重叠部分
                    result.add(chunk + nextChunk.substring(0, overlapSize));
                }
            } else {
                // 最后一个块直接添加
                result.add(chunk);
            }
        }

        return result;
    }

    /**
     * 按大小分割文本，不考虑语义边界
     *
     * @param text 要分割的文本
     * @param chunkSize 每个块的最大大小
     * @param chunkOverlap 块之间的重叠大小
     * @return 分割后的文本块列表
     */
    private static List<String> splitBySize(String text, int chunkSize, int chunkOverlap) {
        List<String> chunks = new ArrayList<>();

        if (text.length() <= chunkSize) {
            chunks.add(text);
            return chunks;
        }

        int start = 0;
        while (start < text.length()) {
            int end = Math.min(start + chunkSize, text.length());
            chunks.add(text.substring(start, end));
            start = end - chunkOverlap;
        }

        return chunks;
    }

    /**
     * 将分隔符从块开头移到前一个块的末尾
     *
     * @param chunks 文本块列表
     * @return 调整后的文本块列表
     */
    private static List<String> adjustSeparatorPositions(List<String> chunks) {
        if (chunks == null || chunks.size() <= 1) {
            return chunks;
        }

        List<String> adjustedChunks = new ArrayList<>();
        adjustedChunks.add(chunks.get(0));

        // 使用与splitText方法相同的分隔符列表
        String allSeparators = Pattern.quote("\n\n") + "|" +
                Pattern.quote("\n") + "|" +
                Pattern.quote("。") + "|" +
                Pattern.quote("，") + "|" +
                Pattern.quote(".") + "|" +
                Pattern.quote(";") + "|" +
                Pattern.quote(",") + "|" +
                Pattern.quote(" ");

        // 定义分隔符模式，匹配开头的任意分隔符
        Pattern separatorPattern = Pattern.compile("^(" + allSeparators + "+)(.*)");

        for (int i = 1; i < chunks.size(); i++) {
            String chunk = chunks.get(i);
            Matcher matcher = separatorPattern.matcher(chunk);

            if (matcher.find()) {
                // 找到开头的分隔符
                String separator = matcher.group(1);
                String remainingText = matcher.group(2).trim();

                // 将分隔符添加到前一个块的末尾
                String previousChunk = adjustedChunks.get(adjustedChunks.size() - 1);
                adjustedChunks.set(adjustedChunks.size() - 1, previousChunk + separator);

                // 如果剩余文本不为空，添加到新块中
                if (!remainingText.isEmpty()) {
                    if (separator != null && chunk != null && separator.length() < chunk.length()) {
                        adjustedChunks.add(chunk.substring(separator.length(), chunk.length()));
                    } else {
                        adjustedChunks.add(chunk);
                    }
                }
            }else {
                // 没有找到分隔符，直接添加
                adjustedChunks.add(chunk);
            }
        }

        return adjustedChunks;
    }

    /**
     * 合并小于指定大小的相邻块
     *
     * @param chunks 文本块列表
     * @param maxSize 最大块大小
     * @return 合并后的文本块列表
     */
    private static List<String> mergeSmallChunks(List<String> chunks, int maxSize) {
        if (chunks == null || chunks.isEmpty()) {
            return chunks;
        }

        List<String> mergedChunks = new ArrayList<>();
        StringBuilder currentChunk = new StringBuilder(chunks.get(0));

        for (int i = 1; i < chunks.size(); i++) {
            String nextChunk = chunks.get(i);

            // 如果合并后不超过最大大小，则合并
            if (currentChunk.length() + nextChunk.length() <= maxSize) {
                currentChunk.append(nextChunk);
            } else {
                // 保存当前块并开始新的块
                mergedChunks.add(currentChunk.toString());
                currentChunk = new StringBuilder(nextChunk);
            }
        }

        // 添加最后一个块
        if (currentChunk.length() > 0) {
            mergedChunks.add(currentChunk.toString());
        }

        return mergedChunks;
    }

    public static List<Integer> getParagraphIndexes(String sourceInput) {
        List<Integer> indexes = new ArrayList<>();
        String[] paragraphs =
                sourceInput.split("\\Q{¥¥¥}\\E");
        int paragraphIndex = 0;
        for (int i = 1; i < paragraphs.length; i++) {
            indexes.add(paragraphIndex);
            if (paragraphs[i].endsWith(CARRIAGE_RETURN_TAG)) {
                paragraphIndex++;
            }
        }
        return indexes;
    }
}

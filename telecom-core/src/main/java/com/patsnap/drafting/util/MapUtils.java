package com.patsnap.drafting.util;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import com.jayway.jsonpath.Configuration;
import com.jayway.jsonpath.DocumentContext;
import com.jayway.jsonpath.JsonPath;
import com.jayway.jsonpath.Option;
import com.jayway.jsonpath.PathNotFoundException;

/**
 * <AUTHOR>
 * @date 8/3/16.
 */
public class MapUtils {

    private static final String SPLIT_CHAR = ".";
    private static final String WILD_CHAR = "*";
    private static final String BLANK_CHAR = "";

    public static Object get(Object json, String path) {
        if (json == null || path == null) {
            return null;
        }
        String key;
        String subPath = null;
        int pos = path.indexOf(SPLIT_CHAR);
        if (pos > -1) {
            key = path.substring(0, pos);
            subPath = path.substring(pos + 1);
        } else {
            key = path;
        }
        boolean lastKey = (subPath == null || !subPath.contains(SPLIT_CHAR));
        Object subJson = null;
        if (json instanceof Map) {
            subJson = ((Map) json).get(key);
        } else if (json instanceof List) {
            if (((List) json).isEmpty()) {
                return null;
            }
            if (key.matches("\\d+")) {
                if (((List) json).get(0) instanceof List) {
                    if (Integer.valueOf(key) < ((List) ((List) json).get(0)).size()) {
                        subJson = ((List<List>) json).stream()
                                .map(each -> each.get(Integer.valueOf(key)))
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList());
                    }
                } else if (Integer.valueOf(key) < ((List) json).size()) {
                    subJson = ((List) json).get(Integer.valueOf(key));
                }
            } else if (!WILD_CHAR.equals(key)) {
                if (((List) json).get(0) instanceof Map) {
                    subJson = ((List<Map<String, Object>>) json).stream()
                            .map(each -> {
                                Object value = each.get(key);
                                return value == null && lastKey ? BLANK_CHAR : value;
                            })
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());
                } else if (((List) json).get(0) instanceof List) {
                    subJson = ((List<List<Map<String, Object>>>) json).stream()
                            .flatMap(List::stream)
                            .map(each -> {
                                Object value = each.get(key);
                                return value == null && lastKey ? BLANK_CHAR : value;
                            })
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());
                }
            } else {
                subJson = json;
            }
        }
        if (subPath != null) {
            return get(subJson, subPath);
        } else {
            return subJson;
        }
    }

    public static Object get(Object json, String path, Object defaultValue) {
        Object result = get(json, path);
        if (null == result) {
            result = defaultValue;
        }
        return result;
    }

    public static Map<String, Object> copy(Map<String, Object> source, List<String> keys) {
        if (source == null) {
            return Collections.emptyMap();
        }
        Map<String, Object> result = new HashMap<>((int) (keys.size() / .75 + 1));
        for (String key : keys) {
            if (source.containsKey(key)) {
                result.put(key, source.get(key));
            }
        }
        return result;
    }

    /**
     * 根据路径更新Map中的值
     *
     * @param map      待更新的Map对象
     * @param jsonPath     Map中属性的完整路径: $.a.b[1].c
     * @param newValue 新值
     */
    public static void updateMapAtPath(Map<String, Object> map, String jsonPath, Object newValue) {
        Configuration conf = Configuration.defaultConfiguration().addOptions(Option.DEFAULT_PATH_LEAF_TO_NULL);
        DocumentContext ctx = JsonPath.using(conf).parse(map);
        safeSet(ctx, jsonPath, newValue);
    }

    private static void safeSet(DocumentContext ctx, String jsonPath, Object newValue) {
        try {
            ctx.read(jsonPath);
        } catch (PathNotFoundException e) {
            // split the jsonPath
            String parentPath;
            String lastKey;
            if (jsonPath.lastIndexOf(".") != -1) {
                parentPath = jsonPath.substring(0, jsonPath.lastIndexOf("."));
                lastKey = jsonPath.substring(jsonPath.lastIndexOf(".") + 1);
            } else {
                parentPath = "$";
                lastKey = jsonPath;
            }
            // if parent also not exists
            safeSet(ctx, parentPath, new HashMap<>());
            ctx.put(parentPath, lastKey, newValue);
        }
        // if path exists, just set the value
        ctx.set(jsonPath, newValue);
    }

    public static void main(String[] args) {
        Map<String, Object> data = new HashMap<>();
        ArrayList<Map<String, Object>> list = new ArrayList<>() {{
            add(new HashMap<>() {{
                put("c", 1);
            }});
            add(new HashMap<>() {{
                put("d", 2);
            }});
            add(new HashMap<>() {{
                put("c", 3);
            }});
        }};
        data.put("a", new HashMap<String, Object>() {{
            put("b", list);
        }});
        data.put("c", "原始值");
        updateMapAtPath(data, "$.a.b[1].c", "updated");
        System.out.println("Updated data: " + data);
    }

}

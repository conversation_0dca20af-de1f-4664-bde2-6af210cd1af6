package com.patsnap.drafting.util;

import com.patsnap.core.common.copilot.gpt.enums.GPTModelEnum;
import com.patsnap.drafting.client.model.ContentDTO;
import com.patsnap.drafting.client.model.MessageDTO;
import com.patsnap.drafting.client.model.ModelCompletionDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.entity.ContentType;
import org.springframework.stereotype.Component;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 段落处理工具类
 *
 * <AUTHOR>
 * @date 2024/07/25
 */
@Slf4j
@Component
public class ImagePromptUtils {
    
    /**
     * 构建模型请求DTO，支持多模态内容（文本+图片）
     *
     * @param prompt 包含特殊标记的提示词，使用 <image>URL</image> 格式标记图片
     * @return ModelCompletionDTO对象
     */
    public ModelCompletionDTO buildModelCompletion(String prompt, GPTModelEnum model) {
        // 创建模型完成请求对象
        ModelCompletionDTO modelCompletion = new ModelCompletionDTO();
        modelCompletion.setModel(model.getName());
        modelCompletion.setMaxTokens(model.getLimit());
        modelCompletion.setTemperature(0.1);
        
        // 构建消息内容列表
        List<ContentDTO> contents = parsePromptContent(prompt);
        
        // 创建用户消息
        List<MessageDTO> messages = new ArrayList<>();
        messages.add(new MessageDTO(MessageDTO.Role.USER.getValue(), contents));
        modelCompletion.setMessages(messages);
        
        return modelCompletion;
    }
    
    /**
     * 解析提示词内容，提取文本和图片
     *
     * @param prompt 包含特殊标记的提示词
     * @return 内容列表
     */
    private List<ContentDTO> parsePromptContent(String prompt) {
        List<ContentDTO> contents = new ArrayList<>();
        
        // 使用正则表达式匹配图片标记
        Pattern imagePattern = Pattern.compile("<image>(.*?)</image>");
        Matcher matcher = imagePattern.matcher(prompt);
        
        int lastEnd = 0;
        
        // 逐个处理匹配到的图片标记
        while (matcher.find()) {
            // 添加图片标记之前的文本内容
            addTextContentIfNotEmpty(contents, prompt.substring(lastEnd, matcher.start()));
            
            // 处理图片内容
            String imageUrl = matcher.group(1);
            addImageContent(contents, imageUrl);
            
            lastEnd = matcher.end();
        }
        
        // 添加剩余的文本内容
        addTextContentIfNotEmpty(contents, prompt.substring(lastEnd));
        
        return contents;
    }
    
    /**
     * 添加文本内容（如果不为空）
     *
     * @param contents 内容列表
     * @param text 文本内容
     */
    private void addTextContentIfNotEmpty(List<ContentDTO> contents, String text) {
        String trimmedText = text.trim();
        if (!trimmedText.isEmpty()) {
            contents.add(ContentDTO.of(
                    MessageDTO.ContentType.TEXT.getValue(),
                    trimmedText
            ));
        }
    }
    
    /**
     * 添加图片内容
     *
     * @param contents 内容列表
     * @param imageUrl 图片URL或Base64数据
     */
    private void addImageContent(List<ContentDTO> contents, String imageUrl) {
        try {
            // 检查是否为Base64编码的图片
            if (isBase64Image(imageUrl)) {
                addBase64ImageContent(contents, imageUrl);
            } else {
                addUrlImageContent(contents, imageUrl);
            }
        } catch (Exception e) {
            log.error("处理图片失败: {}", imageUrl, e);
            // 添加错误提示文本
            contents.add(ContentDTO.of(
                    MessageDTO.ContentType.TEXT.getValue(),
                    "[图片获取失败: " + imageUrl + "]"
            ));
        }
    }
    
    /**
     * 检查是否为Base64编码的图片
     *
     * @param imageUrl 图片URL
     * @return 是否为Base64图片
     */
    private boolean isBase64Image(String imageUrl) {
        return !imageUrl.startsWith("http://") && !imageUrl.startsWith("https://");
    }
    
    /**
     * 添加Base64编码的图片内容
     *
     * @param contents 内容列表
     * @param imageBase64 Base64图片数据
     */
    private void addBase64ImageContent(List<ContentDTO> contents, String imageBase64) {
        if (StringUtils.isBlank(imageBase64)) {
            log.error("无效的Base64图片格式: {}", imageBase64);
            return;
        }
        
        ContentDTO imageContent = ContentDTO.of(
                MessageDTO.ContentType.IMAGE.getValue(),
                imageBase64,
                ContentType.IMAGE_PNG.getMimeType(),
                "base64"
        );
        
        contents.add(imageContent);
    }
    
    /**
     * 从data URL中提取媒体类型
     *
     * @param dataUrlPrefix data URL前缀部分
     * @return 媒体类型
     */
    private String extractMediaTypeFromDataUrl(String dataUrlPrefix) {
        return dataUrlPrefix.split(";")[0].replace("data:", "");
    }
    
    /**
     * 添加URL图片内容
     *
     * @param contents 内容列表
     * @param imageUrl 图片URL
     * @throws IOException 如果下载图片失败
     */
    private void addUrlImageContent(List<ContentDTO> contents, String imageUrl) throws IOException {
        byte[] imageBytes = downloadImage(imageUrl);
        String base64Image = Base64.getEncoder().encodeToString(imageBytes);
        String mediaType = getMediaTypeFromUrl(imageUrl);
        
        ContentDTO imageContent = ContentDTO.of(
                MessageDTO.ContentType.IMAGE.getValue(),
                base64Image,
                mediaType,
                "base64"
        );
        
        contents.add(imageContent);
    }
    
    /**
     * 下载图片并返回字节数组
     *
     * @param imageUrl 图片URL
     * @return 图片字节数组
     * @throws IOException 如果下载过程中发生错误
     */
    private byte[] downloadImage(String imageUrl) throws IOException {
        URL url = new URL(imageUrl);
        try (InputStream in = url.openStream();
             ByteArrayOutputStream out = new ByteArrayOutputStream()) {
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = in.read(buffer)) != -1) {
                out.write(buffer, 0, bytesRead);
            }
            return out.toByteArray();
        }
    }
    
    /**
     * 根据URL获取媒体类型
     *
     * @param url 图片URL
     * @return 媒体类型
     */
    private String getMediaTypeFromUrl(String url) {
        String lowerCaseUrl = url.toLowerCase();
        if (lowerCaseUrl.contains(".jpg") || lowerCaseUrl.contains(".jpeg")) {
            return "image/jpeg";
        } else if (lowerCaseUrl.contains(".png")) {
            return "image/png";
        } else if (lowerCaseUrl.contains(".gif")) {
            return "image/gif";
        } else if (lowerCaseUrl.contains(".webp")) {
            return "image/webp";
        } else if (lowerCaseUrl.contains(".svg")) {
            return "image/svg+xml";
        } else if (lowerCaseUrl.contains(".bmp")) {
            return "image/bmp";
        } else {
            // 默认使用png类型
            return "image/png";
        }
    }

}

package com.patsnap.drafting.util;

import cn.hutool.core.util.StrUtil;
import com.patsnap.core.common.utils.DateFormatUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;

import com.patsnap.drafting.enums.common.Lang;
import org.apache.commons.collections.CollectionUtils;
import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;

/**
 * <AUTHOR>
 * @date 2023/5/26 09:34
 */
public class DateUtils {

    public static int getCurrentYear() {
        return DateTime.now(DateTimeZone.UTC).getYear();
    }

    public static Integer offsetYear(Date date, int offset) {
        Calendar cal = Calendar.getInstance();
        cal.setTimeInMillis(date.getTime());
        cal.add(Calendar.YEAR, offset);
        return cal.get(Calendar.YEAR);
    }

    public static void formatDateTime(Map<String, Object> data, List<String> fields) {
        if (data == null || CollectionUtils.isEmpty(fields)) {
            return;
        }

        for (String field : fields) {
            if (data.containsKey(field)) {
                data.put(field, DateFormatUtils.format(data.get(field).toString()));
            }
        }
    }

    /**
     * 不带时分秒的日期字符串转化
     *
     * @param input        输入的日期
     * @param outputFormat 输出日期的格式
     * @return 输出的日期，不带时分秒
     */
    public static String formattedDate(LocalDateTime input, String outputFormat) {
        DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern(outputFormat);
        return input.format(outputFormatter);

    }

    /**
     * 计算两个日期之间的天数,月数等
     * @param pattern
     * @param start
     * @param chronoUnit DAYS MONTHS ....
     * @return
     */
    public static long periodToNow(String pattern, String start, ChronoUnit chronoUnit) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
        LocalDate startDate = LocalDate.parse(start, formatter);
        LocalDate nowDate = LocalDate.now();
        nowDate.format(DateTimeFormatter.ofPattern(pattern));
        return startDate.until(nowDate, chronoUnit);
    }

    public static long period(String pattern, String start,String end, ChronoUnit chronoUnit) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern, Locale.ENGLISH);
        LocalDate startDate = LocalDate.parse(start, formatter);
        LocalDate endDate= LocalDate.parse(end,formatter);
        return startDate.until(endDate, chronoUnit);
    }

    /**
     * 根据语言格式化日期时间
     *
     * @param date 日期
     * @param lang 语言
     * @return
     */
    public static String formatDateTimeByLang(DateTime date, Lang lang) {
        String dateFormat = getDateFormat(lang);
        return date.withZone(DateTimeZone.UTC).toString(dateFormat + " HH:mm");
    }

    /**
     * 根据语言格式化日期时间
     *
     * @param date 日期
     * @param lang 语言
     * @return
     */
    public static String formatDateTimeByLang(DateTimeZone zone, DateTime date, Lang lang) {
        String dateFormat = getDateFormat(lang);
        return date.withZone(zone).toString(dateFormat + " HH:mm");
    }

    /**
     * 根据语言格式化日期
     *
     * @param date 日期
     * @param lang 语言
     * @return
     */
    public static String formatDateByLang(DateTime date, Lang lang) {
        String dateFormat = getDateFormat(lang);
        return date.withZone(DateTimeZone.UTC).toString(dateFormat);
    }

    public static String formatDateByFormat(DateTime date, String dateFormat) {
        return date.withZone(DateTimeZone.UTC).toString(dateFormat);
    }

    public static String formatDateStringByLang(String dateStr, Lang lang) {
        if (StrUtil.isBlank(dateStr) || dateStr.length() != 8) {
            return "";
        }
        String dateFormat = getDateFormat(lang);
        try {
            DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyyMMdd");
            DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern(dateFormat);
            LocalDate date = LocalDate.parse(dateStr, inputFormatter);
            return date.format(outputFormatter);
        } catch (Exception e) {
            // 如果解析失败，可以记录日志或返回原字符串
            return dateStr;
        }
    }

    /**
     * 获取页面语言获取日期格式
     *
     * @param lang
     * @return
     */
    public static String getDateFormat(Lang lang) {
        String dateType;
        if (lang == Lang.EN || lang == Lang.DE) {
            dateType = "dd MMM yyyy";
        } else if (lang == Lang.JP) {
            dateType = "yyyy.MM.dd";
        } else {
            dateType = "yyyy-MM-dd";
        }
        return dateType;
    }


    public static void main(String[] args) {
        System.out.println(periodToNow("MMdd YYYY", "2023-02-18", ChronoUnit.MONTHS));
        System.out.println(periodToNow("yyyy-MM-dd", "2023-01-18", ChronoUnit.MONTHS));
        System.out.println(period("yyyy-MM-dd","2023-02-18","2023-02-28",ChronoUnit.DAYS));
    }

}

package com.patsnap.drafting.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.management.MBeanServer;
import javax.management.ObjectName;
import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.MemoryUsage;
import java.lang.management.ThreadMXBean;

/**
 * 内存监控工具类
 * 用于监控JVM堆内存、直接内存、线程等资源使用情况
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class MemoryMonitorUtil {

    private static final MBeanServer mBeanServer = ManagementFactory.getPlatformMBeanServer();
    private static final MemoryMXBean memoryMXBean = ManagementFactory.getMemoryMXBean();
    private static final ThreadMXBean threadMXBean = ManagementFactory.getThreadMXBean();

    /**
     * 获取直接内存使用情况
     * 
     * @return 直接内存使用量（字节），如果获取失败返回-1
     */
    public static long getDirectMemoryUsed() {
        try {
            ObjectName objectName = new ObjectName("java.nio:type=BufferPool,name=direct");
            Long memoryUsed = (Long) mBeanServer.getAttribute(objectName, "MemoryUsed");
            return memoryUsed != null ? memoryUsed : -1;
        } catch (Exception e) {
            log.warn("获取直接内存使用量失败", e);
            return -1;
        }
    }

    /**
     * 获取直接内存总容量
     * 
     * @return 直接内存总容量（字节），如果获取失败返回-1
     */
    public static long getDirectMemoryCapacity() {
        try {
            ObjectName objectName = new ObjectName("java.nio:type=BufferPool,name=direct");
            Long totalCapacity = (Long) mBeanServer.getAttribute(objectName, "TotalCapacity");
            return totalCapacity != null ? totalCapacity : -1;
        } catch (Exception e) {
            log.warn("获取直接内存总容量失败", e);
            return -1;
        }
    }

    /**
     * 获取直接内存缓冲区数量
     * 
     * @return 直接内存缓冲区数量，如果获取失败返回-1
     */
    public static long getDirectMemoryCount() {
        try {
            ObjectName objectName = new ObjectName("java.nio:type=BufferPool,name=direct");
            Long count = (Long) mBeanServer.getAttribute(objectName, "Count");
            return count != null ? count : -1;
        } catch (Exception e) {
            log.warn("获取直接内存缓冲区数量失败", e);
            return -1;
        }
    }

    /**
     * 获取映射内存使用情况
     * 
     * @return 映射内存使用量（字节），如果获取失败返回-1
     */
    public static long getMappedMemoryUsed() {
        try {
            ObjectName objectName = new ObjectName("java.nio:type=BufferPool,name=mapped");
            Long memoryUsed = (Long) mBeanServer.getAttribute(objectName, "MemoryUsed");
            return memoryUsed != null ? memoryUsed : -1;
        } catch (Exception e) {
            log.warn("获取映射内存使用量失败", e);
            return -1;
        }
    }

    /**
     * 获取堆内存使用情况
     * 
     * @return 堆内存使用信息
     */
    public static MemoryUsage getHeapMemoryUsage() {
        return memoryMXBean.getHeapMemoryUsage();
    }

    /**
     * 获取非堆内存使用情况
     * 
     * @return 非堆内存使用信息
     */
    public static MemoryUsage getNonHeapMemoryUsage() {
        return memoryMXBean.getNonHeapMemoryUsage();
    }

    /**
     * 获取当前线程数
     * 
     * @return 当前线程数
     */
    public static int getCurrentThreadCount() {
        return threadMXBean.getThreadCount();
    }

    /**
     * 获取峰值线程数
     * 
     * @return 峰值线程数
     */
    public static int getPeakThreadCount() {
        return threadMXBean.getPeakThreadCount();
    }

    /**
     * 打印完整的内存使用情况
     */
    public static void printMemoryInfo() {
        try {
            StringBuilder memoryInfo = new StringBuilder();
            memoryInfo.append("=== JVM内存与线程使用情况 ===");
            
            // 堆内存信息
            MemoryUsage heapMemory = getHeapMemoryUsage();
            memoryInfo.append(" | 堆内存: 已使用=").append(heapMemory.getUsed() / 1024 / 1024).append("MB")
                     .append(", 已提交=").append(heapMemory.getCommitted() / 1024 / 1024).append("MB")
                     .append(", 最大值=").append(heapMemory.getMax() / 1024 / 1024).append("MB");

            // 非堆内存信息
            MemoryUsage nonHeapMemory = getNonHeapMemoryUsage();
            memoryInfo.append(" | 非堆内存: 已使用=").append(nonHeapMemory.getUsed() / 1024 / 1024).append("MB")
                     .append(", 已提交=").append(nonHeapMemory.getCommitted() / 1024 / 1024).append("MB")
                     .append(", 最大值=").append(nonHeapMemory.getMax() / 1024 / 1024).append("MB");

            // 直接内存信息
            long directMemoryUsed = getDirectMemoryUsed();
            long directMemoryCapacity = getDirectMemoryCapacity();
            long directMemoryCount = getDirectMemoryCount();
            memoryInfo.append(" | 直接内存: 已使用=").append(directMemoryUsed / 1024 / 1024).append("MB")
                     .append(", 总容量=").append(directMemoryCapacity / 1024 / 1024).append("MB")
                     .append(", 缓冲区数量=").append(directMemoryCount);

            // 映射内存信息
            long mappedMemoryUsed = getMappedMemoryUsed();
            memoryInfo.append(" | 映射内存: 已使用=").append(mappedMemoryUsed / 1024 / 1024).append("MB");

            // 线程信息
            memoryInfo.append(" | 线程: 当前数量=").append(getCurrentThreadCount())
                     .append(", 峰值数量=").append(getPeakThreadCount());

            log.info(memoryInfo.toString());

        } catch (Exception e) {
            log.error("打印内存信息时发生异常", e);
        }
    }

    /**
     * 格式化字节数为可读格式
     * 
     * @param bytes 字节数
     * @return 格式化后的字符串
     */
    public static String formatBytes(long bytes) {
        if (bytes < 0) {
            return "N/A";
        }
        
        if (bytes < 1024) {
            return bytes + " B";
        } else if (bytes < 1024 * 1024) {
            return String.format("%.2f KB", bytes / 1024.0);
        } else if (bytes < 1024 * 1024 * 1024) {
            return String.format("%.2f MB", bytes / 1024.0 / 1024.0);
        } else {
            return String.format("%.2f GB", bytes / 1024.0 / 1024.0 / 1024.0);
        }
    }

    /**
     * 获取内存使用情况的简要报告
     * 
     * @return 内存使用报告字符串
     */
    public static String getMemoryReport() {
        StringBuilder report = new StringBuilder();
        
        try {
            MemoryUsage heapMemory = getHeapMemoryUsage();
            MemoryUsage nonHeapMemory = getNonHeapMemoryUsage();
            long directMemoryUsed = getDirectMemoryUsed();
            long mappedMemoryUsed = getMappedMemoryUsed();
            
            report.append("内存使用报告: ");
            report.append("堆内存=").append(formatBytes(heapMemory.getUsed()));
            report.append(", 非堆内存=").append(formatBytes(nonHeapMemory.getUsed()));
            report.append(", 直接内存=").append(formatBytes(directMemoryUsed));
            report.append(", 映射内存=").append(formatBytes(mappedMemoryUsed));
            report.append(", 线程数=").append(getCurrentThreadCount());
            
        } catch (Exception e) {
            report.append("获取内存报告失败: ").append(e.getMessage());
        }
        
        return report.toString();
    }
} 
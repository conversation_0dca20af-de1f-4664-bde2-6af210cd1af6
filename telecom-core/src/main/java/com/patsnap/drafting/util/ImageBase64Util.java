package com.patsnap.drafting.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Base64;

/**
 * 图片Base64转换工具类
 * 
 * <AUTHOR> Assistant
 */
@Slf4j
public class ImageBase64Util {

    private static final int CONNECT_TIMEOUT = 10000; // 10秒连接超时
    private static final int READ_TIMEOUT = 30000; // 30秒读取超时
    private static final int MAX_IMAGE_SIZE = 10 * 1024 * 1024; // 最大10MB

    /**
     * 从URL获取图片并转换为base64
     * 
     * @param imageUrl 图片URL
     * @return base64编码的图片数据，如果失败返回null
     */
    public static String getBase64FromUrl(String imageUrl) {
        if (StringUtils.isBlank(imageUrl)) {
            log.warn("图片URL为空");
            return null;
        }

        HttpURLConnection connection = null;
        try {
            log.debug("开始从URL获取图片: {}", imageUrl);
            
            URL url = new URL(imageUrl);
            connection = (HttpURLConnection) url.openConnection();
            
            // 设置请求属性
            connection.setConnectTimeout(CONNECT_TIMEOUT);
            connection.setReadTimeout(READ_TIMEOUT);
            connection.setRequestMethod("GET");
            connection.setRequestProperty("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");
            
            // 检查响应码
            int responseCode = connection.getResponseCode();
            if (responseCode != HttpURLConnection.HTTP_OK) {
                log.warn("获取图片失败，HTTP响应码: {}, URL: {}", responseCode, imageUrl);
                return null;
            }
            
            // 检查内容类型
            String contentType = connection.getContentType();
            if (contentType == null || !contentType.startsWith("image/")) {
                log.warn("URL不是图片类型，Content-Type: {}, URL: {}", contentType, imageUrl);
                return null;
            }
            
            // 检查内容长度
            int contentLength = connection.getContentLength();
            if (contentLength > MAX_IMAGE_SIZE) {
                log.warn("图片文件过大: {} bytes, 最大允许: {} bytes, URL: {}", 
                        contentLength, MAX_IMAGE_SIZE, imageUrl);
                return null;
            }
            
            // 读取图片数据
            try (InputStream inputStream = connection.getInputStream();
                 ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
                
                byte[] buffer = new byte[8192];
                int bytesRead;
                int totalBytesRead = 0;
                
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    totalBytesRead += bytesRead;
                    
                    // 防止读取过大的文件
                    if (totalBytesRead > MAX_IMAGE_SIZE) {
                        log.warn("图片文件读取过程中超过大小限制: {} bytes, URL: {}", 
                                totalBytesRead, imageUrl);
                        return null;
                    }
                    
                    outputStream.write(buffer, 0, bytesRead);
                }
                
                // 转换为base64
                byte[] imageBytes = outputStream.toByteArray();
                String base64 = Base64.getEncoder().encodeToString(imageBytes);
                
                log.debug("图片转换为base64成功，大小: {} bytes, URL: {}", imageBytes.length, imageUrl);
                return base64;
                
            }
            
        } catch (IOException e) {
            log.error("从URL获取图片并转换为base64失败: {}", imageUrl, e);
            return null;
        } catch (Exception e) {
            log.error("处理图片URL时发生未知异常: {}", imageUrl, e);
            return null;
        } finally {
            // 确保连接被关闭
            if (connection != null) {
                try {
                    connection.disconnect();
                } catch (Exception e) {
                    log.warn("关闭HTTP连接时发生异常", e);
                }
            }
        }
    }

    /**
     * 验证base64字符串是否有效
     * 
     * @param base64String base64字符串
     * @return 是否有效
     */
    public static boolean isValidBase64(String base64String) {
        if (StringUtils.isBlank(base64String)) {
            return false;
        }
        
        try {
            Base64.getDecoder().decode(base64String);
            return true;
        } catch (IllegalArgumentException e) {
            return false;
        }
    }

    /**
     * 获取base64字符串的大小（字节）
     * 
     * @param base64String base64字符串
     * @return 字节大小，如果无效返回-1
     */
    public static int getBase64Size(String base64String) {
        if (!isValidBase64(base64String)) {
            return -1;
        }
        
        try {
            byte[] decoded = Base64.getDecoder().decode(base64String);
            return decoded.length;
        } catch (Exception e) {
            log.warn("计算base64大小失败", e);
            return -1;
        }
    }
} 
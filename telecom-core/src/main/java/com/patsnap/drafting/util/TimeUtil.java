package com.patsnap.drafting.util;

import java.util.Calendar;
import java.util.Date;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * @author: dongdong
 * @date: 11/30/16
 */
public class TimeUtil {

    private static final Logger LOGGER = LoggerFactory.getLogger(TimeUtil.class);


    public static void sleep(long ms) {
        if (ms <= 0) {
            return;
        }

        try {
            Thread.sleep(ms);
        } catch (Exception e) {
            LOGGER.warn("Interrupted when sleep. {}", e.getMessage());
        }
    }

    public static long getDaysBackTimeInMillis(Date originalDate, int daysAmount) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(originalDate);
        calendar.add(Calendar.DAY_OF_YEAR, -daysAmount);
        return calendar.getTimeInMillis();
    }

    public static long getExpireTime() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MONTH, 1);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        return calendar.getTimeInMillis() - DateTime.now().getMillis();
    }
}

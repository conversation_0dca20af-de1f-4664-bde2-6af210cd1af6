package com.patsnap.drafting.util;

import com.patsnap.drafting.request.imagesearch.ImagePatentInfo;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;

import javax.imageio.ImageIO;
import javax.imageio.ImageWriteParam;
import javax.imageio.ImageWriter;
import javax.imageio.stream.ImageOutputStream;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.net.URLConnection;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;

/**
 * 图像合并工具类
 * 功能特性：
 * 1. 支持多张图片合并为一张图片
 * 2. 支持网格布局排列
 * 3. 支持添加序号标识
 * 4. 支持从URL下载图片并合并
 * 5. 自动尺寸限制：确保合并后的图片尺寸不超过8000像素
 * 6. 支持图片压缩：提供多种压缩策略减小文件大小
 * 7. 主动释放堆外内存：防止内存泄漏
 * 
 * 尺寸限制说明：
 * - 最大允许尺寸：8000x8000像素
 * - 当计算出的合并图片尺寸超过限制时，会自动按比例缩放所有图片
 * - 缩放时保持图片的宽高比不变
 * - 缩放采用高质量的双三次插值算法
 * 
 * 压缩功能说明：
 * - 单张图片压缩：下载时自动将单张图片压缩到指定尺寸（默认800像素）
 * - JPEG质量压缩：支持设置JPEG压缩质量（0.0-1.0），质量越低文件越小
 * - 便捷压缩方法：提供高压缩、超高压缩等预设方案
 * - 压缩效果：通常可以将文件大小减少60%-90%
 * 
 * 内存管理说明：
 * - 主动释放堆外内存：使用完 BufferedImage 后主动释放堆外内存
 * - 防止内存泄漏：避免大量 BufferedImage 对象占用堆外内存
 * - 并发控制：限制同时处理的图片数量，防止内存溢出
 * 
 * 使用建议：
 * - 对于需要高质量的场景，使用PNG格式或高质量JPEG（0.8-0.9）
 * - 对于需要小文件的场景，使用mergeImagesFromUrlsHighCompression()方法
 * - 对于极限压缩需求，可自定义压缩参数（质量0.4-0.6，单张图片400-600像素）
 *
 * <AUTHOR>
 */
@Slf4j
public class ImageMergeUtil {

    /**
     * 最大允许的图片尺寸（像素）
     */
    private static final int MAX_IMAGE_SIZE = 1024;
    
    /**
     * 默认JPEG压缩质量（0.0-1.0，值越小文件越小但质量越低）
     */
    private static final float DEFAULT_JPEG_QUALITY = 0.5f;
    
    /**
     * 默认单张图片的最大尺寸（像素），用于压缩单张图片
     */
    private static final int DEFAULT_SINGLE_IMAGE_MAX_SIZE = 400;

    /**
     * 主动释放 BufferedImage 的堆外内存
     * 通过调用 flush() 方法释放图像数据占用的堆外内存
     * 
     * @param image 要释放的 BufferedImage 对象
     */
    private static void releaseImageMemory(BufferedImage image) {
        if (image != null) {
            try {
                // flush() 方法会释放图像数据占用的堆外内存
                image.flush();
                log.debug("已释放 BufferedImage 堆外内存，尺寸: {}x{}", image.getWidth(), image.getHeight());
            } catch (Exception e) {
                log.warn("释放 BufferedImage 内存时发生异常", e);
            }
        }
    }
    
    /**
     * 批量释放 BufferedImage 列表的堆外内存
     * 
     * @param images BufferedImage 列表
     */
    private static void releaseImageListMemory(List<BufferedImage> images) {
        if (images != null) {
            for (BufferedImage image : images) {
                releaseImageMemory(image);
            }
            log.debug("已释放 {} 个 BufferedImage 对象的堆外内存", images.size());
        }
    }

    /**
     * 计算缩放比例以确保图片尺寸不超过最大限制
     *
     * @param width  原始宽度
     * @param height 原始高度
     * @return 缩放比例（1.0表示不需要缩放）
     */
    private static double calculateScaleRatio(int width, int height) {
        if (width <= MAX_IMAGE_SIZE && height <= MAX_IMAGE_SIZE) {
            log.info("图片尺寸 {}x{} 在最大限制 {} 内，无需缩放", width, height, MAX_IMAGE_SIZE);
            return 1.0;
        }
        
        // 计算宽度和高度的缩放比例，取较小的那个以确保两个维度都不超过限制
        double widthRatio = (double) MAX_IMAGE_SIZE / width;
        double heightRatio = (double) MAX_IMAGE_SIZE / height;
        
        double scaleRatio = Math.min(widthRatio, heightRatio);
        log.info("图片尺寸 {}x{} 超过最大限制 {}，将缩放至比例: {}", width, height, MAX_IMAGE_SIZE, scaleRatio);
        
        return scaleRatio;
    }

    /**
     * 缩放BufferedImage到指定尺寸
     *
     * @param originalImage 原始图片
     * @param newWidth      新宽度
     * @param newHeight     新高度
     * @return 缩放后的图片
     */
    private static BufferedImage scaleImage(BufferedImage originalImage, int newWidth, int newHeight) {
        BufferedImage scaledImage = new BufferedImage(newWidth, newHeight, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g2d = scaledImage.createGraphics();
        
        try {
            // 设置高质量缩放
            g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BICUBIC);
            g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
            g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            
            g2d.drawImage(originalImage, 0, 0, newWidth, newHeight, null);
            
        } finally {
            g2d.dispose();
        }
        
        return scaledImage;
    }
    
    /**
     * 压缩单张图片尺寸
     * 如果图片的宽度或高度超过最大限制，则按比例缩放
     *
     * @param originalImage 原始图片
     * @param maxSize 最大尺寸（像素）
     * @return 压缩后的图片
     */
    private static BufferedImage compressSingleImage(BufferedImage originalImage, int maxSize) {
        if (originalImage == null) {
            return null;
        }
        
        int width = originalImage.getWidth();
        int height = originalImage.getHeight();
        
        // 如果图片尺寸已经在限制范围内，直接返回
        if (width <= maxSize && height <= maxSize) {
            return originalImage;
        }
        
        // 计算缩放比例
        double scaleRatio = Math.min((double) maxSize / width, (double) maxSize / height);
        int newWidth = (int) (width * scaleRatio);
        int newHeight = (int) (height * scaleRatio);
        
        log.debug("压缩单张图片：{}x{} -> {}x{}，缩放比例: {}", width, height, newWidth, newHeight, scaleRatio);
        
        BufferedImage compressedImage = scaleImage(originalImage, newWidth, newHeight);
        
        // 如果创建了新的压缩图片，释放原图片的内存（仅当不是同一个对象时）
        if (compressedImage != originalImage) {
            releaseImageMemory(originalImage);
        }
        
        return compressedImage;
    }
    
    /**
     * 将BufferedImage写入字节数组，支持压缩
     *
     * @param image 要写入的图片
     * @param outputFormat 输出格式（如"png"、"jpg"）
     * @param compressionQuality JPEG压缩质量（0.0-1.0），仅当outputFormat为"jpg"时有效
     * @param baos 字节数组输出流
     * @return 图片字节数组，失败时返回null
     */
    private static byte[] writeImageWithCompression(BufferedImage image, String outputFormat, 
                                                   float compressionQuality, ByteArrayOutputStream baos) {
        try {
            // 对于JPEG格式，使用压缩质量设置
            if ("jpg".equalsIgnoreCase(outputFormat) || "jpeg".equalsIgnoreCase(outputFormat)) {
                // 确保压缩质量在有效范围内
                compressionQuality = Math.max(0.0f, Math.min(1.0f, compressionQuality));
                
                // 获取JPEG写入器
                Iterator<ImageWriter> writers = ImageIO.getImageWritersByFormatName("jpg");
                if (!writers.hasNext()) {
                    log.error("没有找到JPEG图片写入器");
                    return null;
                }
                
                ImageWriter writer = writers.next();
                ImageWriteParam param = writer.getDefaultWriteParam();
                
                // 设置压缩模式和质量
                if (param.canWriteCompressed()) {
                    param.setCompressionMode(ImageWriteParam.MODE_EXPLICIT);
                    param.setCompressionQuality(compressionQuality);
                    log.debug("设置JPEG压缩质量: {}", compressionQuality);
                }
                
                // 转换为RGB格式（JPEG不支持透明度）
                BufferedImage rgbImage = new BufferedImage(image.getWidth(), image.getHeight(), BufferedImage.TYPE_INT_RGB);
                Graphics2D g2d = rgbImage.createGraphics();
                g2d.setColor(Color.WHITE);
                g2d.fillRect(0, 0, image.getWidth(), image.getHeight());
                g2d.drawImage(image, 0, 0, null);
                g2d.dispose();
                
                // 写入图片
                try (ImageOutputStream ios = ImageIO.createImageOutputStream(baos)) {
                    writer.setOutput(ios);
                    writer.write(null, new javax.imageio.IIOImage(rgbImage, null, null), param);
                    writer.dispose();
                    
                    byte[] imageBytes = baos.toByteArray();
                    log.debug("JPEG压缩完成，文件大小: {} bytes", imageBytes.length);
                    return imageBytes;
                }
                
            } else {
                // 对于其他格式（如PNG），使用标准写入方式
                boolean success = ImageIO.write(image, outputFormat, baos);
                if (success) {
                    byte[] imageBytes = baos.toByteArray();
                    log.debug("{}格式图片写入完成，文件大小: {} bytes", outputFormat.toUpperCase(), imageBytes.length);
                    return imageBytes;
                } else {
                    log.error("无法写入{}格式的图片", outputFormat);
                    return null;
                }
            }
            
        } catch (IOException e) {
            log.error("写入图片时发生IO错误", e);
            return null;
        } catch (Exception e) {
            log.error("写入图片时发生未预期错误", e);
            return null;
        }
    }

    /**
     * 从图片URL列表合并图片并返回字节数组（带压缩选项）
     *
     * @param imageUrls 图片URL列表
     * @param cols 每行显示的图片数量
     * @param margin 图片之间的间距（像素）
     * @param outputFormat 输出图片格式（如"png"、"jpg"）
     * @param addLabels 是否添加序号标识
     * @param startIndex 起始索引（用于分页或分批处理）
     * @param compressionQuality JPEG压缩质量（0.0-1.0），仅当outputFormat为"jpg"时有效
     * @param maxSingleImageSize 单张图片的最大尺寸（像素），用于压缩单张图片
     * @return 合并后的图片字节数组，失败时返回null
     */
    public static byte[] mergeImagesFromUrls(List<String> imageUrls, int cols, int margin, String outputFormat,
            boolean addLabels, int startIndex, float compressionQuality, int maxSingleImageSize) {
        // 参数校验
        if (imageUrls == null || imageUrls.isEmpty()) {
            log.error("图片URL列表为空");
            return null;
        }
        
        if (cols <= 0) {
            log.error("列数必须大于0，当前值: {}", cols);
            return null;
        }
        
        if (margin < 0) {
            log.error("间距不能为负数，当前值: {}", margin);
            return null;
        }
        
        if (outputFormat == null || outputFormat.trim().isEmpty()) {
            log.error("输出格式不能为空");
            return null;
        }
        
        try {
            // 从URL下载图片并进行压缩
            List<BufferedImage> images = downloadImagesFromUrls(imageUrls, maxSingleImageSize);
            
            // 注意：即使所有图片都下载失败，images列表也不会为空
            // 因为我们为每个URL都保留了对应的位置（可能为null）
            if (images.isEmpty()) {
                log.error("图片URL列表处理异常，无法生成图片列表");
                return null;
            }
            
            // 合并图片并转换为字节数组（包括空白占位符）
            return mergeBufferedImagesToBytes(images, cols, margin, outputFormat, addLabels, startIndex, compressionQuality);
            
        } catch (Exception e) {
            log.error("从URL合并图片时发生未预期的错误", e);
            return null;
        }
    }
    
    /**
     * 从图片URL列表合并图片并返回字节数组（原始方法，保持向后兼容）
     *
     * @param imageUrls 图片URL列表
     * @param cols 每行显示的图片数量
     * @param margin 图片之间的间距（像素）
     * @param outputFormat 输出图片格式（如"png"、"jpg"）
     * @param addLabels 是否添加序号标识
     * @param startIndex 起始索引（用于分页或分批处理）
     * @return 合并后的图片字节数组，失败时返回null
     */
    public static byte[] mergeImagesFromUrls(List<String> imageUrls, int cols, int margin, String outputFormat,
            boolean addLabels, int startIndex) {
        return mergeImagesFromUrls(imageUrls, cols, margin, outputFormat, addLabels, startIndex, 
                DEFAULT_JPEG_QUALITY, DEFAULT_SINGLE_IMAGE_MAX_SIZE);
    }
    
    /**
     * 从图片URL列表合并图片并返回字节数组（默认添加序号标识）
     *
     * @param imageUrls 图片URL列表
     * @param cols 每行显示的图片数量
     * @param startIndex 起始索引（用于分页或分批处理）
     * @return 合并后的图片字节数组，失败时返回null
     */
    public static byte[] mergeImagesFromUrls(List<String> imageUrls, int cols, int startIndex) {
        return mergeImagesFromUrls(imageUrls, cols, 10, "png", true, startIndex, 
                DEFAULT_JPEG_QUALITY, DEFAULT_SINGLE_IMAGE_MAX_SIZE);
    }
    
    /**
     * 从图片URL列表合并图片并返回压缩的JPEG字节数组（便捷方法）
     *
     * @param imageUrls 图片URL列表
     * @param cols 每行显示的图片数量
     * @param startIndex 起始索引（用于分页或分批处理）
     * @param compressionQuality JPEG压缩质量（0.0-1.0）
     * @return 合并后的JPEG图片字节数组，失败时返回null
     */
    public static byte[] mergeImagesFromUrlsCompressed(List<String> imageUrls, int cols, int startIndex, float compressionQuality) {
        return mergeImagesFromUrls(imageUrls, cols, 10, "jpg", true, startIndex, 
                compressionQuality, DEFAULT_SINGLE_IMAGE_MAX_SIZE);
    }
    
    /**
     * 从图片URL列表合并图片并返回高压缩的JPEG字节数组（便捷方法）
     * 使用较小的单张图片尺寸和较低的压缩质量来最大化压缩效果
     *
     * @param imageUrls 图片URL列表
     * @param cols 每行显示的图片数量
     * @param startIndex 起始索引（用于分页或分批处理）
     * @return 合并后的高压缩JPEG图片字节数组，失败时返回null
     */
    public static byte[] mergeImagesFromUrlsHighCompression(List<String> imageUrls, int cols, int startIndex) {
        return mergeImagesFromUrls(imageUrls, cols, 5, "jpg", true, startIndex, 
                0.6f, 600); // 使用较小的间距、较低的压缩质量和较小的单张图片尺寸
    }
    
    /**
     * 从URL列表下载图片并进行压缩
     * 注意：返回的列表与输入的URL列表保持相同的索引对应关系
     * 对于无法下载的图片，在对应位置插入null值，以便在合并时保留空白位置
     *
     * @param imageUrls 图片URL列表
     * @param maxSingleImageSize 单张图片的最大尺寸（像素），用于压缩单张图片
     * @return BufferedImage列表，与输入URL列表索引一一对应，无法下载的图片为null
     */
    private static List<BufferedImage> downloadImagesFromUrls(List<String> imageUrls, int maxSingleImageSize) {
        List<BufferedImage> images = new ArrayList<>();
        
        for (int i = 0; i < imageUrls.size(); i++) {
            String imageUrl = imageUrls.get(i);
            BufferedImage downloadedImage = null;
            
            if (imageUrl == null || imageUrl.trim().isEmpty()) {
                log.warn("第{}个图片URL为空，将在合并图中保留空白位置", i + 1);
            } else {
                try {
                    log.debug("开始下载图片: {}", imageUrl);
                    
                    // 创建URL连接
                    URLConnection connection = getUrlConnection(imageUrl);
                    
                    // 使用try-with-resources确保InputStream被正确关闭
                    try (InputStream inputStream = connection.getInputStream()) {
                        // 读取图片
                        BufferedImage originalImage = ImageIO.read(inputStream);
                        
                        if (originalImage != null) {
                            log.debug("成功下载图片: {}，原始尺寸: {}x{}", imageUrl, originalImage.getWidth(), originalImage.getHeight());
                            
                            // 压缩单张图片尺寸
                            downloadedImage = compressSingleImage(originalImage, maxSingleImageSize);
                            if (downloadedImage != null) {
                                log.debug("压缩后图片尺寸: {}x{}", downloadedImage.getWidth(), downloadedImage.getHeight());
                            }
                        } else {
                            log.warn("无法解析图片URL: {}，将在合并图中保留空白位置", imageUrl);
                        }
                    }
                    
                } catch (IOException e) {
                    log.warn("下载图片失败: {}，错误: {}，将在合并图中保留空白位置", imageUrl, e.getMessage());
                } catch (Exception e) {
                    log.warn("处理图片URL时发生未预期错误: {}，错误: {}，将在合并图中保留空白位置", imageUrl, e.getMessage());
                }
            }
            
            // 无论是否成功下载，都要添加到列表中以保持索引对应关系
            images.add(downloadedImage);
        }
        
        int successCount = (int) images.stream().filter(Objects::nonNull).count();
        log.info("成功下载{}张图片，总共尝试{}张，{}张将显示为空白位置", 
                successCount, imageUrls.size(), imageUrls.size() - successCount);
        return images;
    }
    
    private static @NotNull URLConnection getUrlConnection(String imageUrl) throws IOException {
        URL url = new URL(imageUrl.trim());
        URLConnection connection = url.openConnection();
        
        // 设置请求头，模拟浏览器访问
        connection.setRequestProperty("User-Agent",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36");
        connection.setConnectTimeout(10000); // 10秒连接超时
        connection.setReadTimeout(30000);    // 30秒读取超时
        return connection;
    }
    
    /**
     * 将多个BufferedImage合并为一张图片并返回字节数组（带压缩选项）
     * 注意：images列表中可能包含null值，表示无法下载的图片
     * 对于null值，将绘制空白占位符并显示相应的图片标识
     *
     * @param images 图片列表（可能包含null值）
     * @param cols 每行显示的图片数量
     * @param margin 图片之间的间距（像素）
     * @param outputFormat 输出图片格式
     * @param addLabels 是否添加序号标识
     * @param startIndex 起始索引（用于分页或分批处理）
     * @param compressionQuality JPEG压缩质量（0.0-1.0），仅当outputFormat为"jpg"时有效
     * @return 合并后的图片字节数组，失败时返回null
     */
    private static byte[] mergeBufferedImagesToBytes(List<BufferedImage> images, int cols, int margin, 
                                                    String outputFormat, boolean addLabels, int startIndex, float compressionQuality) {
        BufferedImage result = null;
        List<BufferedImage> scaledImages = new ArrayList<>();
        
        try {
            // 计算行数
            int rows = (int) Math.ceil((double) images.size() / cols);

            // 找出最大宽度和高度（只考虑非null的图片）
            int maxWidth = 0;
            int maxHeight = 0;
            for (BufferedImage image : images) {
                if (image != null) {
                    maxWidth = Math.max(maxWidth, image.getWidth());
                    maxHeight = Math.max(maxHeight, image.getHeight());
                }
            }
            
            // 如果所有图片都是null，设置默认尺寸
            if (maxWidth == 0 || maxHeight == 0) {
                maxWidth = 300;  // 默认宽度
                maxHeight = 200; // 默认高度
                log.info("所有图片都无法下载，使用默认尺寸: {}x{}", maxWidth, maxHeight);
            }

            // 序号标识的高度和边距
            int labelHeight = addLabels ? 30 : 0;  // 为标签预留的高度
            int labelMarginTop = 10;  // 标签与图片的上边距

            // 计算最终图片的宽度和高度
            int finalWidth = (maxWidth + margin) * cols - margin;
            int finalHeight = (maxHeight + margin + labelHeight) * rows - margin;
            
            // 检查最终图片尺寸是否超过限制，如果超过则进行缩放
            double scaleRatio = calculateScaleRatio(finalWidth, finalHeight);
            if (scaleRatio < 1.0) {
                // 需要缩放，重新计算各个尺寸
                maxWidth = (int) (maxWidth * scaleRatio);
                maxHeight = (int) (maxHeight * scaleRatio);
                labelHeight = (int) (labelHeight * scaleRatio);
                labelMarginTop = (int) (labelMarginTop * scaleRatio);
                margin = (int) (margin * scaleRatio);
                
                finalWidth = (maxWidth + margin) * cols - margin;
                finalHeight = (maxHeight + margin + labelHeight) * rows - margin;
                
                log.info("应用缩放比例 {} 后，最终图片尺寸: {}x{}", scaleRatio, finalWidth, finalHeight);
                
                // 预先缩放所有图片，避免在绘制时重复缩放
                for (BufferedImage image : images) {
                    if (image != null) {
                        int scaledWidth = (int) (image.getWidth() * scaleRatio);
                        int scaledHeight = (int) (image.getHeight() * scaleRatio);
                        BufferedImage scaledImage = scaleImage(image, scaledWidth, scaledHeight);
                        scaledImages.add(scaledImage);
                    } else {
                        scaledImages.add(null);
                    }
                }
            } else {
                // 不需要缩放，直接使用原图片
                scaledImages.addAll(images);
            }

            // 创建新图片
            result = new BufferedImage(finalWidth, finalHeight, BufferedImage.TYPE_INT_ARGB);
            Graphics2D g2d = result.createGraphics();
            
            try {
                // 设置更高质量的渲染
                g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BICUBIC);
                g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
                g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);

                // 设置背景为白色
                g2d.setColor(Color.WHITE);
                g2d.fillRect(0, 0, finalWidth, finalHeight);

                // 准备标签字体设置
                if (addLabels) {
                    g2d.setFont(new Font("Arial", Font.BOLD, 22));
                }

                // 绘制每张图片（包括空白占位符）
                for (int i = 0; i < scaledImages.size(); i++) {
                    int row = i / cols;
                    int col = i % cols;
                    int x = col * (maxWidth + margin);
                    int y = row * (maxHeight + margin + labelHeight);
                    
                    // 获取当前图片
                    BufferedImage currentImage = scaledImages.get(i);
                    
                    if (currentImage != null) {
                        // 处理有效图片
                        
                        // 计算居中位置
                        int imageWidth = currentImage.getWidth();
                        int imageHeight = currentImage.getHeight();
                        int xOffset = (maxWidth - imageWidth) / 2;
                        int yOffset = (maxHeight - imageHeight) / 2;
                        
                        // 绘制图片
                        g2d.drawImage(currentImage, x + xOffset, y + yOffset, null);
                        
                    } else {
                        // 处理空白占位符（图片下载失败或URL为空）
                        
                        // 绘制空白占位符背景
                        g2d.setColor(new Color(248, 248, 248)); // 浅灰色背景
                        g2d.fillRect(x, y, maxWidth, maxHeight);
                        
                        // 绘制边框
                        g2d.setColor(new Color(220, 220, 220)); // 深一点的灰色边框
                        g2d.setStroke(new BasicStroke(2));
                        g2d.drawRect(x, y, maxWidth, maxHeight);
                        
                        // 绘制"图片加载失败"提示
                        g2d.setColor(new Color(150, 150, 150)); // 中等灰色文字
                        Font originalFont = g2d.getFont();
                        g2d.setFont(new Font("Arial", Font.PLAIN, 16));
                        
                        String failureText = "图片加载失败";
                        FontMetrics failureMetrics = g2d.getFontMetrics();
                        int failureTextWidth = failureMetrics.stringWidth(failureText);
                        int failureTextX = x + (maxWidth - failureTextWidth) / 2;
                        int failureTextY = y + (maxHeight + failureMetrics.getAscent()) / 2;
                        
                        g2d.drawString(failureText, failureTextX, failureTextY);
                        
                        // 恢复原始字体
                        g2d.setFont(originalFont);
                    }
                    
                    // 添加序号标识（无论图片是否成功加载都要显示）
                    if (addLabels) {
                        // 设置序号文本
                        String label = String.format("picture %d", startIndex + i + 1);
                        
                        // 计算文本宽度以便居中显示
                        FontMetrics metrics = g2d.getFontMetrics();
                        int labelWidth = metrics.stringWidth(label);
                        int labelX = x + (maxWidth - labelWidth) / 2;
                        int labelY = y + maxHeight + labelMarginTop + metrics.getAscent();
                        
                        // 绘制序号背景
                        g2d.setColor(new Color(245, 245, 245));
                        g2d.fillRoundRect(labelX - 5, labelY - metrics.getAscent(), labelWidth + 10, metrics.getHeight(), 5, 5);
                        
                        // 绘制序号文本
                        g2d.setColor(Color.BLACK);
                        g2d.drawString(label, labelX, labelY);
                    }
                }
                
            } finally {
                g2d.dispose();
            }

            // 将BufferedImage转换为字节数组，支持压缩
            try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
                byte[] imageBytes = writeImageWithCompression(result, outputFormat, compressionQuality, baos);
                if (imageBytes != null) {
                    log.info("成功合并{}张图片，输出字节数组大小: {} bytes。合并后的图片分辨率: {}x{}", images.size(),
                            imageBytes.length, result.getWidth(), result.getHeight());
                    return imageBytes;
                } else {
                    log.error("无法将合并后的图片写入字节数组，格式: {}", outputFormat);
                    return null;
                }
            }
            
        } catch (IOException e) {
            log.error("合并BufferedImage为字节数组时发生错误", e);
            return null;
        } catch (Exception e) {
            log.error("合并图片时发生未预期的错误", e);
            return null;
        } finally {
            // 主动释放所有图片的堆外内存
            try {
                // 释放原始图片列表的内存
                releaseImageListMemory(images);
                
                // 释放缩放后图片列表的内存（如果与原始图片不同）
                if (scaledImages != images) {
                    releaseImageListMemory(scaledImages);
                }
                
                // 释放合并结果图片的内存
                releaseImageMemory(result);
                
                log.debug("已释放所有 BufferedImage 对象的堆外内存");
                
            } catch (Exception e) {
                log.warn("释放图片内存时发生异常", e);
            }
        }
    }

    /**
     * 从图片URL列表合并图片并返回字节数组（带受理局信息显示）
     *
     * @param imagePatentInfoList 图片专利信息列表，包含URL和受理局信息
     * @param cols 每行显示的图片数量
     * @param margin 图片之间的间距（像素）
     * @param outputFormat 输出图片格式（如"png"、"jpg"）
     * @param addLabels 是否添加序号标识
     * @param startIndex 起始索引（用于分页或分批处理）
     * @param compressionQuality JPEG压缩质量（0.0-1.0），仅当outputFormat为"jpg"时有效
     * @param maxSingleImageSize 单张图片的最大尺寸（像素），用于压缩单张图片
     * @return 合并后的图片字节数组，失败时返回null
     */
    public static byte[] mergeImagesFromUrlsWithAuthority(List<ImagePatentInfo> imagePatentInfoList,
            int cols, int margin, String outputFormat, boolean addLabels, int startIndex, 
            float compressionQuality, int maxSingleImageSize) {
        // 参数校验
        if (imagePatentInfoList == null || imagePatentInfoList.isEmpty()) {
            log.error("图片专利信息列表为空");
            return null;
        }
        
        if (cols <= 0) {
            log.error("列数必须大于0，当前值: {}", cols);
            return null;
        }
        
        if (margin < 0) {
            log.error("间距不能为负数，当前值: {}", margin);
            return null;
        }
        
        if (outputFormat == null || outputFormat.trim().isEmpty()) {
            log.error("输出格式不能为空");
            return null;
        }
        
        try {
            // 提取图片URL列表
            List<String> imageUrls = imagePatentInfoList.stream()
                    .map(com.patsnap.drafting.request.imagesearch.ImagePatentInfo::getImageUrl)
                    .collect(java.util.stream.Collectors.toList());
            
            // 从URL下载图片并进行压缩
            List<BufferedImage> images = downloadImagesFromUrls(imageUrls, maxSingleImageSize);
            
            // 注意：即使所有图片都下载失败，images列表也不会为空
            // 因为我们为每个URL都保留了对应的位置（可能为null）
            if (images.isEmpty()) {
                log.error("图片URL列表处理异常，无法生成图片列表");
                return null;
            }
            
            // 合并图片并转换为字节数组（包括空白占位符和受理局信息）
            return mergeBufferedImagesToBytesWithAuthority(images, imagePatentInfoList, cols, margin, outputFormat, 
                    addLabels, startIndex, compressionQuality);
            
        } catch (Exception e) {
            log.error("从URL合并图片时发生未预期的错误", e);
            return null;
        }
    }
    
    /**
     * 从图片URL列表合并图片并返回字节数组（带受理局信息，使用默认参数）
     *
     * @param imagePatentInfoList 图片专利信息列表，包含URL和受理局信息
     * @param cols 每行显示的图片数量
     * @param startIndex 起始索引（用于分页或分批处理）
     * @return 合并后的图片字节数组，失败时返回null
     */
    public static byte[] mergeImagesFromUrlsWithAuthority(List<com.patsnap.drafting.request.imagesearch.ImagePatentInfo> imagePatentInfoList, 
            int cols, int startIndex) {
        return mergeImagesFromUrlsWithAuthority(imagePatentInfoList, cols, 10, "png", true, startIndex, 
                DEFAULT_JPEG_QUALITY, DEFAULT_SINGLE_IMAGE_MAX_SIZE);
    }

    /**
     * 将多个BufferedImage合并为一张图片并返回字节数组（带受理局信息显示）
     * 注意：images列表中可能包含null值，表示无法下载的图片
     * 对于null值，将绘制空白占位符并显示相应的图片标识
     *
     * @param images 图片列表（可能包含null值）
     * @param imagePatentInfoList 图片专利信息列表，包含受理局信息
     * @param cols 每行显示的图片数量
     * @param margin 图片之间的间距（像素）
     * @param outputFormat 输出图片格式
     * @param addLabels 是否添加序号标识
     * @param startIndex 起始索引（用于分页或分批处理）
     * @param compressionQuality JPEG压缩质量（0.0-1.0），仅当outputFormat为"jpg"时有效
     * @return 合并后的图片字节数组，失败时返回null
     */
    private static byte[] mergeBufferedImagesToBytesWithAuthority(List<BufferedImage> images, List<ImagePatentInfo> imagePatentInfoList,
            int cols, int margin, String outputFormat, boolean addLabels, int startIndex, float compressionQuality) {
        try {
            // 计算行数
            int rows = (int) Math.ceil((double) images.size() / cols);

            // 找出最大宽度和高度（只考虑非null的图片）
            int maxWidth = 0;
            int maxHeight = 0;
            for (BufferedImage image : images) {
                if (image != null) {
                    maxWidth = Math.max(maxWidth, image.getWidth());
                    maxHeight = Math.max(maxHeight, image.getHeight());
                }
            }
            
            // 如果所有图片都是null，设置默认尺寸
            if (maxWidth == 0 || maxHeight == 0) {
                maxWidth = 300;  // 默认宽度
                maxHeight = 200; // 默认高度
                log.info("所有图片都无法下载，使用默认尺寸: {}x{}", maxWidth, maxHeight);
            }

            // 序号标识和受理局信息的高度和边距
            int labelHeight = addLabels ? 60 : 0;  // 为标签和受理局信息预留的高度（增加到60像素）
            int labelMarginTop = 8;  // 标签与图片的上边距

            // 计算最终图片的宽度和高度
            int finalWidth = (maxWidth + margin) * cols - margin;
            int finalHeight = (maxHeight + margin + labelHeight) * rows - margin;
            
            // 检查最终图片尺寸是否超过限制，如果超过则进行缩放
            double scaleRatio = calculateScaleRatio(finalWidth, finalHeight);
            if (scaleRatio < 1.0) {
                // 需要缩放，重新计算各个尺寸
                maxWidth = (int) (maxWidth * scaleRatio);
                maxHeight = (int) (maxHeight * scaleRatio);
                labelHeight = (int) (labelHeight * scaleRatio);
                labelMarginTop = (int) (labelMarginTop * scaleRatio);
                margin = (int) (margin * scaleRatio);
                
                finalWidth = (maxWidth + margin) * cols - margin;
                finalHeight = (maxHeight + margin + labelHeight) * rows - margin;
                
                log.info("应用缩放比例 {} 后，最终图片尺寸: {}x{}", scaleRatio, finalWidth, finalHeight);
            }

            // 创建新图片
            BufferedImage result = new BufferedImage(finalWidth, finalHeight, BufferedImage.TYPE_INT_ARGB);
            Graphics2D g2d = result.createGraphics();
            
            // 设置更高质量的渲染
            g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BICUBIC);
            g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
            g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);

            // 设置背景为白色
            g2d.setColor(Color.WHITE);
            g2d.fillRect(0, 0, finalWidth, finalHeight);

            // 准备标签字体设置
            Font labelFont = new Font("Arial", Font.BOLD, (int)(16 * Math.max(scaleRatio, 0.8))); // 确保字体不会太小
            Font authorityFont = new Font("Arial", Font.PLAIN, (int)(14 * Math.max(scaleRatio, 0.8))); // 受理局信息字体
            if (addLabels) {
                g2d.setFont(labelFont);
            }

            // 绘制每张图片（包括空白占位符）
            for (int i = 0; i < images.size(); i++) {
                int row = i / cols;
                int col = i % cols;
                int x = col * (maxWidth + margin);
                int y = row * (maxHeight + margin + labelHeight);
                
                // 获取当前图片
                BufferedImage currentImage = images.get(i);
                
                if (currentImage != null) {
                    // 处理有效图片
                    
                    // 如果需要缩放，则缩放当前图片
                    if (scaleRatio < 1.0) {
                        int scaledWidth = (int) (currentImage.getWidth() * scaleRatio);
                        int scaledHeight = (int) (currentImage.getHeight() * scaleRatio);
                        currentImage = scaleImage(currentImage, scaledWidth, scaledHeight);
                    }
                    
                    // 计算居中位置
                    int imageWidth = currentImage.getWidth();
                    int imageHeight = currentImage.getHeight();
                    int xOffset = (maxWidth - imageWidth) / 2;
                    int yOffset = (maxHeight - imageHeight) / 2;
                    
                    // 绘制图片
                    g2d.drawImage(currentImage, x + xOffset, y + yOffset, null);
                    
                } else {
                    // 处理空白占位符（图片下载失败或URL为空）
                    
                    // 绘制空白占位符背景
                    g2d.setColor(new Color(248, 248, 248)); // 浅灰色背景
                    g2d.fillRect(x, y, maxWidth, maxHeight);
                    
                    // 绘制边框
                    g2d.setColor(new Color(220, 220, 220)); // 深一点的灰色边框
                    g2d.setStroke(new BasicStroke(2));
                    g2d.drawRect(x, y, maxWidth, maxHeight);
                    
                    // 绘制"图片加载失败"提示
                    g2d.setColor(new Color(150, 150, 150)); // 中等灰色文字
                    Font originalFont = g2d.getFont();
                    g2d.setFont(new Font("Arial", Font.PLAIN, 14));
                    
                    String failureText = "图片加载失败";
                    FontMetrics failureMetrics = g2d.getFontMetrics();
                    int failureTextWidth = failureMetrics.stringWidth(failureText);
                    int failureTextX = x + (maxWidth - failureTextWidth) / 2;
                    int failureTextY = y + (maxHeight + failureMetrics.getAscent()) / 2;
                    
                    g2d.drawString(failureText, failureTextX, failureTextY);
                    
                    // 恢复原始字体
                    g2d.setFont(originalFont);
                }
                
                // 添加序号标识和受理局信息（无论图片是否成功加载都要显示）
                if (addLabels) {
                    // 设置序号文本
                    String label = String.format("picture %d", startIndex + i + 1);
                    
                    // 获取受理局信息
                    String authority = "";
                    if (i < imagePatentInfoList.size() && imagePatentInfoList.get(i) != null) {
                        String authorityValue = imagePatentInfoList.get(i).getAuthority();
                        if (authorityValue != null && !authorityValue.trim().isEmpty()) {
                            authority = "authority:" + authorityValue.trim();
                        }
                    }
                    
                    // 设置标签字体并计算文本宽度
                    g2d.setFont(labelFont);
                    FontMetrics labelMetrics = g2d.getFontMetrics();
                    int labelWidth = labelMetrics.stringWidth(label);
                    int labelTextHeight = labelMetrics.getHeight();
                    
                    // 设置受理局字体并计算文本宽度
                    g2d.setFont(authorityFont);
                    FontMetrics authorityMetrics = g2d.getFontMetrics();
                    int authorityWidth = !authority.isEmpty() ? authorityMetrics.stringWidth(authority) : 0;
                    int authorityHeight = authorityMetrics.getHeight();
                    
                    // 计算最大文本宽度和总高度
                    int maxTextWidth = Math.max(labelWidth, authorityWidth);
                    int totalTextHeight = labelTextHeight + (authority.isEmpty() ? 0 : authorityHeight + 3);
                    
                    // 计算文本位置
                    int labelX = x + (maxWidth - labelWidth) / 2;
                    int authorityX = x + (maxWidth - authorityWidth) / 2;
                    int labelY = y + maxHeight + labelMarginTop + labelMetrics.getAscent();
                    int authorityY = labelY + labelTextHeight + 3; // 受理局信息显示在序号下方，间距3像素
                    
                    // 绘制背景（如果有文本的话）
                    if (maxTextWidth > 0) {
                        g2d.setColor(new Color(245, 245, 245, 200)); // 半透明背景
                        int bgX = x + (maxWidth - maxTextWidth) / 2 - 6;
                        int bgY = labelY - labelMetrics.getAscent() - 2;
                        int bgWidth = maxTextWidth + 12;
                        int bgHeight = totalTextHeight + 6;
                        g2d.fillRoundRect(bgX, bgY, bgWidth, bgHeight, 6, 6);
                        
                        // 绘制背景边框
                        g2d.setColor(new Color(220, 220, 220));
                        g2d.setStroke(new BasicStroke(1));
                        g2d.drawRoundRect(bgX, bgY, bgWidth, bgHeight, 6, 6);
                    }
                    
                    // 绘制序号文本
                    g2d.setFont(labelFont);
                    g2d.setColor(Color.BLACK);
                    g2d.drawString(label, labelX, labelY);
                    
                    // 绘制受理局信息（如果有的话）
                    if (!authority.isEmpty()) {
                        g2d.setFont(authorityFont);
                        g2d.setColor(new Color(80, 80, 80)); // 深灰色，更易读
                        g2d.drawString(authority, authorityX, authorityY);
                    }
                }
            }
            
            g2d.dispose();

            // 将BufferedImage转换为字节数组，支持压缩
            try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
                byte[] imageBytes = writeImageWithCompression(result, outputFormat, compressionQuality, baos);
                if (imageBytes != null) {
                    log.info("成功合并{}张图片（带受理局信息），输出字节数组大小: {} bytes", images.size(), imageBytes.length);
                    return imageBytes;
                } else {
                    log.error("无法将合并后的图片写入字节数组，格式: {}", outputFormat);
                    return null;
                }
            }
            
        } catch (IOException e) {
            log.error("合并BufferedImage为字节数组时发生错误", e);
            return null;
        } catch (Exception e) {
            log.error("合并图片时发生未预期的错误", e);
            return null;
        }
    }

    public static void main(String[] args) {
        // 测试1: 尺寸限制测试
        testImageSizeLimit();
        
        // 测试2: URL图片合并
        testUrlImagesMerge();
        
        // 测试3: 带受理局信息的图片合并
        testUrlImagesMergeWithAuthority();
        
        // 测试4: 简单的受理局信息显示测试
        testAuthorityDisplaySimple();
    }
    
    /**
     * 测试图片尺寸限制功能
     */
    private static void testImageSizeLimit() {
        log.info("=== 开始测试图片尺寸限制功能 ===");
        
        // 测试计算缩放比例
        double ratio1 = calculateScaleRatio(5000, 6000);
        log.info("5000x6000 的缩放比例: {}", ratio1);
        
        double ratio2 = calculateScaleRatio(10000, 8000);
        log.info("10000x8000 的缩放比例: {}", ratio2);
        
        double ratio3 = calculateScaleRatio(7000, 7000);
        log.info("7000x7000 的缩放比例: {}", ratio3);
        
        double ratio4 = calculateScaleRatio(12000, 15000);
        log.info("12000x15000 的缩放比例: {}", ratio4);
        
        log.info("=== 图片尺寸限制功能测试完成 ===");
    }
    
    /**
     * 测试URL图片合并
     */
    private static void testUrlImagesMerge() {
        log.info("=== 开始测试URL图片合并 ===");
        
        // 准备测试用的图片URL列表（包含有效和无效的URL来测试空白占位符功能）
        List<String> imageUrls = new ArrayList<>();
        imageUrls.add("https://via.placeholder.com/800x600/FF0000/FFFFFF?text=Image+1");
        imageUrls.add(""); // 空URL，测试空白占位符
        imageUrls.add("https://via.placeholder.com/800x600/0000FF/FFFFFF?text=Image+3");
        imageUrls.add("https://invalid-url-that-does-not-exist.com/image.jpg"); // 无效URL，测试空白占位符
        imageUrls.add("https://via.placeholder.com/800x600/FF00FF/FFFFFF?text=Image+5");
        imageUrls.add(null); // null URL，测试空白占位符
        imageUrls.add("https://via.placeholder.com/800x600/00FFFF/000000?text=Image+7");
        imageUrls.add("https://via.placeholder.com/800x600/FFA500/FFFFFF?text=Image+8");
        
        String imageDir = "telecom-core/src/main/resources/image/";
        File directory = new File(imageDir);
        if (!directory.exists()) {
            directory.mkdirs();
        }
        
        // 测试1: 标准PNG合并
        log.info("--- 测试标准PNG合并 ---");
        byte[] pngBytes = mergeImagesFromUrls(imageUrls, 3, 0);
        if (pngBytes != null) {
            log.info("PNG合并成功，文件大小: {} bytes", pngBytes.length);
            saveTestImage(pngBytes, new File(directory, "merged_standard.png"));
        }
        
        // 测试2: 中等压缩JPEG合并
        log.info("--- 测试中等压缩JPEG合并 ---");
        byte[] jpegMediumBytes = mergeImagesFromUrlsCompressed(imageUrls, 3, 0, 0.8f);
        if (jpegMediumBytes != null) {
            log.info("中等压缩JPEG合并成功，文件大小: {} bytes", jpegMediumBytes.length);
            saveTestImage(jpegMediumBytes, new File(directory, "merged_medium_compression.jpg"));
        }
        
        // 测试3: 高压缩JPEG合并
        log.info("--- 测试高压缩JPEG合并 ---");
        byte[] jpegHighBytes = mergeImagesFromUrlsHighCompression(imageUrls, 3, 0);
        if (jpegHighBytes != null) {
            log.info("高压缩JPEG合并成功，文件大小: {} bytes", jpegHighBytes.length);
            saveTestImage(jpegHighBytes, new File(directory, "merged_high_compression.jpg"));
        }
        
        // 测试4: 超高压缩JPEG合并
        log.info("--- 测试超高压缩JPEG合并 ---");
        byte[] jpegUltraBytes = mergeImagesFromUrls(imageUrls, 3, 5, "jpg", true, 0, 0.4f, 400);
        if (jpegUltraBytes != null) {
            log.info("超高压缩JPEG合并成功，文件大小: {} bytes", jpegUltraBytes.length);
            saveTestImage(jpegUltraBytes, new File(directory, "merged_ultra_compression.jpg"));
        }
        
        // 输出压缩效果对比
        if (pngBytes != null && jpegMediumBytes != null && jpegHighBytes != null && jpegUltraBytes != null) {
            log.info("=== 压缩效果对比 ===");
            log.info("标准PNG: {} bytes (基准)", pngBytes.length);
            log.info("中等压缩JPEG: {} bytes (压缩率: {:.1f}%)", 
                    jpegMediumBytes.length, (1.0 - (double)jpegMediumBytes.length / pngBytes.length) * 100);
            log.info("高压缩JPEG: {} bytes (压缩率: {:.1f}%)", 
                    jpegHighBytes.length, (1.0 - (double)jpegHighBytes.length / pngBytes.length) * 100);
            log.info("超高压缩JPEG: {} bytes (压缩率: {:.1f}%)", 
                    jpegUltraBytes.length, (1.0 - (double)jpegUltraBytes.length / pngBytes.length) * 100);
        }
        
        log.info("=== URL图片合并测试完成 ===");
    }
    
    /**
     * 测试带受理局信息的图片合并功能
     */
    private static void testUrlImagesMergeWithAuthority() {
        log.info("=== 开始测试带受理局信息的URL图片合并 ===");
        
        // 准备测试用的图片专利信息列表
        List<ImagePatentInfo> imagePatentInfoList = new ArrayList<>();
        
        // 创建测试数据
        ImagePatentInfo info1 = new ImagePatentInfo();
        info1.setImageUrl("https://via.placeholder.com/800x600/FF0000/FFFFFF?text=CN+Patent");
        info1.setPatentId("patent-1");
        info1.setPatentPn("CN123456789A");
        info1.setTitle("中国专利示例");
        info1.setAuthority("CNIPA");
        imagePatentInfoList.add(info1);
        
        ImagePatentInfo info2 = new ImagePatentInfo();
        info2.setImageUrl("https://via.placeholder.com/800x600/0000FF/FFFFFF?text=US+Patent");
        info2.setPatentId("patent-2");
        info2.setPatentPn("US987654321B2");
        info2.setTitle("美国专利示例");
        info2.setAuthority("USPTO");
        imagePatentInfoList.add(info2);
        
        ImagePatentInfo info3 = new ImagePatentInfo();
        info3.setImageUrl("https://via.placeholder.com/800x600/00FF00/FFFFFF?text=EP+Patent");
        info3.setPatentId("patent-3");
        info3.setPatentPn("EP1234567A1");
        info3.setTitle("欧洲专利示例");
        info3.setAuthority("EPO");
        imagePatentInfoList.add(info3);
        
        // 添加一个空白占位符测试
        ImagePatentInfo info4 = new ImagePatentInfo();
        info4.setImageUrl(""); // 空URL，测试空白占位符
        info4.setPatentId("patent-4");
        info4.setPatentPn("JP2023123456A");
        info4.setTitle("日本专利示例");
        info4.setAuthority("JPO");
        imagePatentInfoList.add(info4);
        
        String imageDir = "telecom-core/src/main/resources/image/";
        File directory = new File(imageDir);
        if (!directory.exists()) {
            directory.mkdirs();
        }
        
        // 测试带受理局信息的PNG合并
        log.info("--- 测试带受理局信息的PNG合并 ---");
        byte[] pngWithAuthorityBytes = mergeImagesFromUrlsWithAuthority(imagePatentInfoList, 2, 0);
        if (pngWithAuthorityBytes != null) {
            log.info("带受理局信息的PNG合并成功，文件大小: {} bytes", pngWithAuthorityBytes.length);
            saveTestImage(pngWithAuthorityBytes, new File(directory, "merged_with_authority.png"));
        }
        
        // 测试带受理局信息的JPEG合并
        log.info("--- 测试带受理局信息的JPEG合并 ---");
        byte[] jpegWithAuthorityBytes = mergeImagesFromUrlsWithAuthority(imagePatentInfoList, 2, 10, "jpg", true, 0, 0.8f, 600);
        if (jpegWithAuthorityBytes != null) {
            log.info("带受理局信息的JPEG合并成功，文件大小: {} bytes", jpegWithAuthorityBytes.length);
            saveTestImage(jpegWithAuthorityBytes, new File(directory, "merged_with_authority.jpg"));
        }
        
        log.info("=== 带受理局信息的URL图片合并测试完成 ===");
    }
    
    /**
     * 简单的受理局信息显示测试 - 使用纯色图片
     */
    private static void testAuthorityDisplaySimple() {
        log.info("=== 开始简单的受理局信息显示测试 ===");
        
        try {
            // 创建测试用的纯色图片
            List<BufferedImage> testImages = new ArrayList<>();
            List<ImagePatentInfo> imagePatentInfoList = new ArrayList<>();
            
            // 创建4张不同颜色的测试图片
            String[] colors = {"红色", "蓝色", "绿色", "黄色"};
            Color[] colorValues = {Color.RED, Color.BLUE, Color.GREEN, Color.YELLOW};
            String[] authorities = {"CNIPA", "USPTO", "EPO", "JPO"};
            String[] patentPns = {"CN123456789A", "US987654321B2", "EP1234567A1", "JP2023123456A"};
            
            for (int i = 0; i < 4; i++) {
                // 创建纯色图片
                BufferedImage testImage = new BufferedImage(400, 300, BufferedImage.TYPE_INT_RGB);
                Graphics2D g2d = testImage.createGraphics();
                g2d.setColor(colorValues[i]);
                g2d.fillRect(0, 0, 400, 300);
                
                // 在图片中央添加文字
                g2d.setColor(Color.WHITE);
                g2d.setFont(new Font("Arial", Font.BOLD, 24));
                FontMetrics fm = g2d.getFontMetrics();
                String text = colors[i] + "专利图片";
                int textWidth = fm.stringWidth(text);
                int textX = (400 - textWidth) / 2;
                int textY = 150 + fm.getAscent() / 2;
                g2d.drawString(text, textX, textY);
                g2d.dispose();
                
                testImages.add(testImage);
                
                // 创建对应的专利信息
                ImagePatentInfo info = new ImagePatentInfo();
                info.setImageUrl("test-image-" + (i + 1)); // 模拟URL
                info.setPatentId("patent-" + (i + 1));
                info.setPatentPn(patentPns[i]);
                info.setTitle(colors[i] + "专利示例");
                info.setAuthority(authorities[i]);
                imagePatentInfoList.add(info);
            }
            
            // 使用带受理局信息的合并方法
            byte[] mergedBytes = mergeBufferedImagesToBytesWithAuthority(
                testImages, imagePatentInfoList, 2, 10, "png", true, 0, 0.8f);
            
            if (mergedBytes != null) {
                String imageDir = "telecom-core/src/main/resources/image/";
                File directory = new File(imageDir);
                if (!directory.exists()) {
                    directory.mkdirs();
                }
                
                File outputFile = new File(directory, "authority_display_test.png");
                saveTestImage(mergedBytes, outputFile);
                log.info("受理局信息显示测试成功，文件大小: {} bytes", mergedBytes.length);
            } else {
                log.error("受理局信息显示测试失败");
            }
            
        } catch (Exception e) {
            log.error("受理局信息显示测试发生错误", e);
        }
        
        log.info("=== 简单的受理局信息显示测试完成 ===");
    }
    
    /**
     * 保存测试图片到文件
     */
    private static void saveTestImage(byte[] imageBytes, File outputFile) {
        try {
            java.nio.file.Files.write(outputFile.toPath(), imageBytes);
            log.info("测试图片已保存到: {}", outputFile.getAbsolutePath());
        } catch (Exception e) {
            log.warn("保存测试图片时发生错误: {}", outputFile.getName(), e);
        }
    }
}
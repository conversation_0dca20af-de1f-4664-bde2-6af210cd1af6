package com.patsnap.drafting.util;

import com.patsnap.analytics.infrastructure.utils.SpringBeanUtil;
import freemarker.template.Configuration;
import freemarker.template.Template;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.util.IOUtils;
import org.apache.poi.util.Units;
import org.apache.poi.xwpf.usermodel.ParagraphAlignment;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;
import org.apache.poi.xwpf.usermodel.XWPFTable;
import org.apache.poi.xwpf.usermodel.XWPFTableCell;
import org.apache.poi.xwpf.usermodel.XWPFTableRow;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTInd;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTJc;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTOnOff;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTP;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTPPr;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTRPr;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTSpacing;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTTextAlignment;
import org.springframework.core.io.Resource;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.StringReader;
import java.io.StringWriter;
import java.net.URL;
import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 通用的操作word的工具类
 *
 * <AUTHOR>
 * @date 2024/08/06
 */
@Slf4j
public class WordUtils {

    /**
     * word中添加正文
     *
     * @param doc     word文档
     * @param content 正文内容
     */
    public static void addText(XWPFDocument doc, String content) {
        String[] splits = content.split("\n");
        for (String split : splits) {
            XWPFParagraph paragraph = doc.createParagraph();
            XWPFRun run = paragraph.createRun();
            run.setText(split);
            run.setFontSize(12);
        }
    }

    /**
     * word中添加标题
     *
     * @param doc   word文档
     * @param title 标题
     */
    public static void addTitle(XWPFDocument doc, String title) {
        XWPFParagraph paragraph = doc.createParagraph();
        paragraph.setAlignment(ParagraphAlignment.CENTER);
        XWPFRun run = paragraph.createRun();
        run.setBold(true);
        run.setText(title);
        run.setFontSize(14);
    }

    /**
     * 替换word中的占位符，支持图片占位符
     *
     * @param resource     输入文件
     * @param replacements 替换的内容（文本）
     * @param imageReplacements 图片替换内容（图片数据）
     */
    public static byte[] replaceInWordWithImages(Resource resource, Map<String, String> replacements, Map<String, List<ImageData>> imageReplacements)
            throws IOException {
        try (InputStream fis = resource.getInputStream(); XWPFDocument doc = new XWPFDocument(fis)) {

            // 处理文档中的段落
            int size = doc.getParagraphs().size();
            for (int i = size - 1; i >= 0; i--) {
                processParagraphWithImages(doc, doc.getParagraphs().get(i), replacements, imageReplacements);
            }

            // 处理文档中的表格
            for (XWPFTable table : doc.getTables()) {
                for (XWPFTableRow row : table.getRows()) {
                    for (XWPFTableCell cell : row.getTableCells()) {
                        for (XWPFParagraph paragraph : cell.getParagraphs()) {
                            processParagraphWithImages(doc, paragraph, replacements, imageReplacements);
                        }
                    }
                }
            }

            // 将文档写入字节数组
            try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
                doc.write(baos);
                return baos.toByteArray();
            }
        }
    }

    /**
     * 处理包含图片的段落替换
     */
    private static void processParagraphWithImages(XWPFDocument doc, XWPFParagraph paragraph, 
                                                  Map<String, String> replacements, 
                                                  Map<String, List<ImageData>> imageReplacements) {
        String text = paragraph.getText();
        
        // 先处理图片占位符
        for (Map.Entry<String, List<ImageData>> entry : imageReplacements.entrySet()) {
            String placeholder = "${" + entry.getKey() + "}";
            if (text.contains(placeholder)) {
                replaceWithImages(doc, paragraph, placeholder, entry.getValue());
                text = paragraph.getText(); // 更新文本内容
            }
        }
        
        // 再处理普通文本占位符
        processParagraphV2(doc, paragraph, replacements);
    }

    /**
     * 用图片替换占位符
     */
    private static void replaceWithImages(XWPFDocument doc, XWPFParagraph paragraph, String placeholder, List<ImageData> images) {
        List<XWPFRun> runs = paragraph.getRuns();
        
        // 调试日志：输出段落完整文本和占位符
        String paragraphFullText = paragraph.getText();
        log.debug("段落完整文本: [{}], 占位符: [{}], runs数量: {}", paragraphFullText, placeholder, runs.size());
        
        // 查找包含占位符的run
        for (int i = 0; i < runs.size(); i++) {
            XWPFRun run = runs.get(i);
            String runText = run.getText(0);
            
            // 调试日志：输出每个run的文本内容
            log.debug("Run[{}] 文本: [{}]", i, runText);
            
            if (runText != null && runText.contains(placeholder)) {
                // 清除占位符文本
                run.setText(runText.replace(placeholder, ""), 0);
                
                // 获取文档和当前段落位置，用于正确插入多张图片
                List<XWPFParagraph> paragraphs = paragraph.getDocument().getParagraphs();
                int currentParagraphIndex = paragraphs.indexOf(paragraph);
                
                // 插入图片
                for (int j = 0; j < images.size(); j++) {
                    ImageData imageData = images.get(j);
                    XWPFParagraph targetParagraph = null;
                    XWPFRun imageRun = null;
                    
                    try {
                        if (j == 0) {
                            // 第一张图片使用当前段落和run
                            targetParagraph = paragraph;
                            imageRun = run;
                        } else {
                            // 后续图片在当前段落后面插入新段落
                            CTP ctp = doc.getDocument().getBody().insertNewP(currentParagraphIndex + j);
                            targetParagraph = new XWPFParagraph(ctp, doc);
                            imageRun = targetParagraph.createRun();
                        }
                        
                        // 根据图片对齐方式设置段落对齐
                        setParagraphAlignment(targetParagraph, imageData.getImageAlignment());
                        
                        // 根据标题位置和内容插入标题和图片
                        insertImageWithTitle(targetParagraph, imageRun, imageData, j == 0);
                        
                        
                    } catch (Exception e) {
                        log.error("插入图片失败: {}", imageData.getTitle(), e);
                        // 插入错误提示文本
                        if (targetParagraph != null) {
                            XWPFRun errorRun = (imageRun != null) ? imageRun : targetParagraph.createRun();
                            errorRun.setText("[image error: " + imageData.getTitle() + "]");
                        }
                    }
                }
                break;
            }
        }
        
        // 如果单个run中没有找到占位符，尝试跨run搜索
        if (paragraphFullText.contains(placeholder)) {
            log.debug("占位符跨run分布，尝试跨run替换");
            replaceAcrossRuns(paragraph, placeholder, images);
        }
    }
    
    /**
     * 处理跨run的占位符替换
     */
    private static void replaceAcrossRuns(XWPFParagraph paragraph, String placeholder, List<ImageData> images) {
        try {
            String fullText = paragraph.getText();
            int placeholderIndex = fullText.indexOf(placeholder);
            
            if (placeholderIndex == -1) {
                return;
            }
            
            // 找到占位符在哪个run中开始和结束
            List<XWPFRun> runs = paragraph.getRuns();
            int currentPos = 0;
            int startRunIndex = -1;
            int endRunIndex = -1;
            int startPosInRun = -1;
            int endPosInRun = -1;
            
            for (int i = 0; i < runs.size(); i++) {
                XWPFRun run = runs.get(i);
                String runText = run.getText(0);
                if (runText == null) {
                    continue;
                }
                
                int runLength = runText.length();
                
                // 检查占位符开始位置
                if (startRunIndex == -1 && currentPos <= placeholderIndex && 
                    currentPos + runLength > placeholderIndex) {
                    startRunIndex = i;
                    startPosInRun = placeholderIndex - currentPos;
                }
                
                // 检查占位符结束位置
                if (currentPos + runLength >= placeholderIndex + placeholder.length()) {
                    endRunIndex = i;
                    endPosInRun = placeholderIndex + placeholder.length() - currentPos;
                    break;
                }
                
                currentPos += runLength;
            }
            
            if (startRunIndex != -1 && endRunIndex != -1) {
                // 清除占位符文本
                clearPlaceholderAcrossRuns(runs, startRunIndex, endRunIndex, startPosInRun, endPosInRun, placeholder);
                
                // 在第一个run位置插入图片
                insertImagesAtRun(paragraph, runs.get(startRunIndex), images);
            }
            
        } catch (Exception e) {
            log.error("跨run替换占位符失败", e);
        }
    }
    
    /**
     * 清除跨run的占位符文本
     */
    private static void clearPlaceholderAcrossRuns(List<XWPFRun> runs, int startRunIndex, int endRunIndex, 
                                                   int startPosInRun, int endPosInRun, String placeholder) {
        for (int i = startRunIndex; i <= endRunIndex; i++) {
            XWPFRun run = runs.get(i);
            String runText = run.getText(0);
            
            if (runText == null) {
                continue;
            }
            
            if (i == startRunIndex && i == endRunIndex) {
                // 占位符在同一个run中
                String newText = runText.substring(0, startPosInRun) + 
                               runText.substring(endPosInRun);
                run.setText(newText, 0);
            } else if (i == startRunIndex) {
                // 开始run，保留前面部分
                run.setText(runText.substring(0, startPosInRun), 0);
            } else if (i == endRunIndex) {
                // 结束run，保留后面部分
                run.setText(runText.substring(endPosInRun), 0);
            } else {
                // 中间的run，完全清除
                run.setText("", 0);
            }
        }
    }
    
    /**
     * 根据图片对齐方式设置段落对齐
     */
    private static void setParagraphAlignment(XWPFParagraph paragraph, ImageAlignment imageAlignment) {
        switch (imageAlignment) {
            case CENTER:
                paragraph.setAlignment(ParagraphAlignment.CENTER);
                break;
            case LEFT:
                paragraph.setAlignment(ParagraphAlignment.LEFT);
                break;
            case RIGHT:
                paragraph.setAlignment(ParagraphAlignment.RIGHT);
                break;
            default:
                paragraph.setAlignment(ParagraphAlignment.CENTER);
                break;
        }
    }

    /**
     * 根据标题位置插入图片和标题
     */
    private static void insertImageWithTitle(XWPFParagraph targetParagraph, XWPFRun imageRun, 
                                           ImageData imageData, boolean isFirstImage) {
        try {
            if (StringUtils.isNotBlank(imageData.getTitle())) {
                if (imageData.getTitlePosition() == TitlePosition.ABOVE) {
                    // 标题在图片上方
                    insertTitle(targetParagraph, imageRun, imageData, isFirstImage);
                    insertImage(targetParagraph, imageData);
                } else {
                    // 标题在图片下方（默认）
                    // 先在指定run中插入图片
                    insertImageToRun(imageRun, imageData);
                    // 再在图片后插入标题
                    insertTitle(targetParagraph, null, imageData, isFirstImage);
                }
            } else {
                // 没有标题，只插入图片
                insertImageToRun(imageRun, imageData);
            }
        } catch (Exception e) {
            log.error("插入图片和标题失败: {}", imageData.getTitle(), e);
            // 插入错误提示文本
            XWPFRun errorRun = (imageRun != null) ? imageRun : targetParagraph.createRun();
            errorRun.setText("[image error: " + imageData.getTitle() + "]");
        }
    }

    /**
     * 插入标题
     */
    private static void insertTitle(XWPFParagraph targetParagraph, XWPFRun imageRun, 
                                   ImageData imageData, boolean isFirstImage) {
        XWPFRun titleRun;
        
        // 为标题创建新的run
        if (imageData.getTitlePosition() == TitlePosition.ABOVE) {
            // 标题在上方，在当前段落前面插入标题
            titleRun = targetParagraph.createRun();
            // 设置标题内容和格式
            titleRun.setText(imageData.getTitle());
            titleRun.setBold(imageData.isTitleBold());
            titleRun.setItalic(imageData.isTitleItalic());
            // 在标题后添加换行，与图片分隔
            titleRun.addBreak();
        } else {
            // 标题在下方，在当前段落后面添加标题
            titleRun = targetParagraph.createRun();
            titleRun.addBreak(); // 在标题前换行
            // 设置标题内容和格式
            titleRun.setText(imageData.getTitle());
            titleRun.setBold(imageData.isTitleBold());
            titleRun.setItalic(imageData.isTitleItalic());
        }
        
        // 设置段落对齐方式
        setTitleParagraphAlignment(targetParagraph, imageData.getTitleAlignment());
    }

    /**
     * 插入图片到指定run
     */
    private static void insertImageToRun(XWPFRun imageRun, ImageData imageData) {
        if (StringUtils.isNotBlank(imageData.getBase64Data())) {
            try {
                // 清理 Base64 数据，移除可能的 Data URL 前缀和非法字符
                String cleanBase64Data = cleanBase64Data(imageData.getBase64Data());
                
                byte[] imageBytes = Base64.getDecoder().decode(cleanBase64Data);
                try (ByteArrayInputStream bis = new ByteArrayInputStream(imageBytes)) {
                    imageRun.addPicture(bis, XWPFDocument.PICTURE_TYPE_PNG, 
                                      imageData.getTitle(), 
                                      Units.toEMU(imageData.getWidth()), 
                                      Units.toEMU(imageData.getHeight()));
                    log.debug("成功插入图片: {}", imageData.getTitle());
                }
            } catch (IllegalArgumentException e) {
                log.error("Base64 数据格式错误，插入图片失败: {}, 错误: {}", imageData.getTitle(), e.getMessage());
                // 插入错误提示文本
                imageRun.setText("[image error: " + imageData.getTitle() + "]");
            } catch (Exception e) {
                log.error("插入图片失败: {}", imageData.getTitle(), e);
                // 插入错误提示文本
                imageRun.setText("[image error: " + imageData.getTitle() + "]");
            }
        } else {
            log.warn("图片数据为空，无法插入图片: {}", imageData.getTitle());
            // 插入提示文本
            imageRun.setText("[image error: " + imageData.getTitle() + "]");
        }
    }
    
    /**
     * 清理 Base64 数据，移除 Data URL 前缀和非法字符
     * 
     * @param base64Data 原始 Base64 数据
     * @return 清理后的 Base64 数据
     */
    public static String cleanBase64Data(String base64Data) {
        if (StringUtils.isBlank(base64Data)) {
            return base64Data;
        }
        
        String cleaned = base64Data.trim();
        
        // 移除 Data URL 前缀 (例如: data:image/png;base64,)
        if (cleaned.contains(",")) {
            int commaIndex = cleaned.indexOf(",");
            String prefix = cleaned.substring(0, commaIndex + 1);
            if (prefix.startsWith("data:") && prefix.contains("base64")) {
                cleaned = cleaned.substring(commaIndex + 1);
                log.debug("移除 Data URL 前缀: {}", prefix);
            }
        }
        
        // 移除换行符和空格
        cleaned = cleaned.replaceAll("\\s", "");
        
        return cleaned;
    }
    
    /**
     * 插入图片到段落
     */
    private static void insertImage(XWPFParagraph targetParagraph, ImageData imageData) {
        if (StringUtils.isNotBlank(imageData.getBase64Data())) {
            try {
                insertImageToParagraph(targetParagraph, imageData.getBase64Data(), 
                                     XWPFDocument.PICTURE_TYPE_PNG, 
                                     imageData.getWidth(), imageData.getHeight());
                log.debug("成功插入图片: {}", imageData.getTitle());
            } catch (IllegalArgumentException e) {
                log.error("Base64 数据格式错误，插入图片失败: {}, 错误: {}", imageData.getTitle(), e.getMessage());
                // 插入错误提示文本
                XWPFRun errorRun = targetParagraph.createRun();
                errorRun.setText("[image error: " + imageData.getTitle() + "]");
            } catch (Exception e) {
                log.error("插入图片失败: {}", imageData.getTitle(), e);
                // 插入错误提示文本
                XWPFRun errorRun = targetParagraph.createRun();
                errorRun.setText("[image error: " + imageData.getTitle() + "]");
            }
        } else {
            log.warn("图片数据为空，无法插入图片: {}", imageData.getTitle());
            // 插入提示文本
            XWPFRun warningRun = targetParagraph.createRun();
            warningRun.setText("[image error: " + imageData.getTitle() + "]");
        }
    }

    /**
     * 设置标题段落对齐方式
     */
    private static void setTitleParagraphAlignment(XWPFParagraph paragraph, TitleAlignment titleAlignment) {
        switch (titleAlignment) {
            case CENTER:
                paragraph.setAlignment(ParagraphAlignment.CENTER);
                break;
            case LEFT:
                paragraph.setAlignment(ParagraphAlignment.LEFT);
                break;
            case RIGHT:
                paragraph.setAlignment(ParagraphAlignment.RIGHT);
                break;
            default:
                paragraph.setAlignment(ParagraphAlignment.CENTER);
                break;
        }
    }

    /**
     * 在指定run位置插入图片
     */
    private static void insertImagesAtRun(XWPFParagraph paragraph, XWPFRun targetRun, List<ImageData> images) {
        XWPFDocument doc = paragraph.getDocument();
        
        // 获取当前段落在文档中的位置
        List<XWPFParagraph> paragraphs = doc.getParagraphs();
        int currentParagraphIndex = paragraphs.indexOf(paragraph);
        
        for (int j = 0; j < images.size(); j++) {
            ImageData imageData = images.get(j);
            XWPFParagraph targetParagraph = null;
            XWPFRun imageRun = null;
            
            try {
                if (j == 0) {
                    // 第一张图片使用当前段落
                    targetParagraph = paragraph;
                    imageRun = targetRun;
                } else {
                    // 后续图片在当前段落后面插入新段落
                    CTP ctp = doc.getDocument().getBody().insertNewP(currentParagraphIndex + j);
                    targetParagraph = new XWPFParagraph(ctp, doc);
                    imageRun = targetParagraph.createRun();
                }
                
                // 根据图片对齐方式设置段落对齐
                setParagraphAlignment(targetParagraph, imageData.getImageAlignment());
                
                // 根据标题位置和内容插入标题和图片
                insertImageWithTitle(targetParagraph, imageRun, imageData, j == 0);
                
            } catch (Exception e) {
                log.error("插入图片失败: {}", imageData.getTitle(), e);
                // 插入错误提示文本
                if (targetParagraph != null) {
                    XWPFRun errorRun = (imageRun != null) ? imageRun : targetParagraph.createRun();
                    errorRun.setText("[image error: " + imageData.getTitle() + "]");
                }
            }
        }
    }

    /**
     * 图片数据封装类
     */
    public static class ImageData {
        private String title;           // 图片标题
        private String base64Data;      // 图片base64数据
        private int width = 400;        // 图片宽度（磅）
        private int height = 300;       // 图片高度（磅）
        private boolean titleBold = false;      // 标题是否加粗
        private boolean titleItalic = false;    // 标题是否斜体
        private ImageAlignment imageAlignment = ImageAlignment.CENTER;  // 图片对齐方式
        private TitlePosition titlePosition = TitlePosition.BELOW;      // 标题位置
        private TitleAlignment titleAlignment = TitleAlignment.CENTER;  // 标题对齐方式

        public ImageData() {}

        public ImageData(String title, String base64Data) {
            this.title = title;
            this.base64Data = base64Data;
        }

        public ImageData(String title, String base64Data, int width, int height) {
            this.title = title;
            this.base64Data = base64Data;
            this.width = width;
            this.height = height;
        }

        // Getters and Setters
        public String getTitle() { return title; }
        public void setTitle(String title) { this.title = title; }
        
        public String getBase64Data() { return base64Data; }
        public void setBase64Data(String base64Data) { this.base64Data = base64Data; }
        
        public int getWidth() { return width; }
        public void setWidth(int width) { this.width = width; }
        
        public int getHeight() { return height; }
        public void setHeight(int height) { this.height = height; }
        
        public boolean isTitleBold() { return titleBold; }
        public void setTitleBold(boolean titleBold) { this.titleBold = titleBold; }
        
        public boolean isTitleItalic() { return titleItalic; }
        public void setTitleItalic(boolean titleItalic) { this.titleItalic = titleItalic; }
        
        public ImageAlignment getImageAlignment() { return imageAlignment; }
        public void setImageAlignment(ImageAlignment imageAlignment) { this.imageAlignment = imageAlignment; }
        
        public TitlePosition getTitlePosition() { return titlePosition; }
        public void setTitlePosition(TitlePosition titlePosition) { this.titlePosition = titlePosition; }
        
        public TitleAlignment getTitleAlignment() { return titleAlignment; }
        public void setTitleAlignment(TitleAlignment titleAlignment) { this.titleAlignment = titleAlignment; }
    }

    /**
     * 图片对齐方式枚举
     */
    public enum ImageAlignment {
        /** 居中 */
        CENTER,
        /** 靠左 */
        LEFT,
        /** 靠右 */
        RIGHT
    }

    /**
     * 标题位置枚举
     */
    public enum TitlePosition {
        /** 在图片上方 */
        ABOVE,
        /** 在图片下方 */
        BELOW
    }

    /**
     * 标题对齐方式枚举
     */
    public enum TitleAlignment {
        /** 居中 */
        CENTER,
        /** 靠左 */
        LEFT,
        /** 靠右 */
        RIGHT
    }

    /**
     * 处理段落中的变量
     */
    private static void processParagraphV2(XWPFDocument doc, XWPFParagraph paragraph, Map<String, String> dataModel) {
        String text = paragraph.getText();

        // 检查是否包含FreeMarker变量
        if (text.contains("${")) {
            // 保存原始格式
            XWPFRun templateRun = paragraph.getRuns().isEmpty() ?
                    paragraph.createRun() :
                    paragraph.getRuns().get(0);

            // 清除现有run
            int size = paragraph.getRuns().size();

            // 处理模板
            String processedText = processTemplate(text, dataModel);

            List<XWPFParagraph> bodyElements = doc.getParagraphs();
            int position = bodyElements.indexOf(paragraph);

            // 处理换行符
            String[] lines = processedText.split("\n");
            for (int i = 0; i < lines.length; i++) {
                if (i == 0) {
                    XWPFRun run = paragraph.createRun();
                    copyRunStyle(templateRun, run);
                    run.setText(lines[i]);
                } else {
                    CTP ctp = doc.getDocument().getBody().insertNewP(position + i);
                    XWPFParagraph newParagraph = new XWPFParagraph(ctp, doc);
                    XWPFRun run = newParagraph.createRun();
                    copyRunStyle(templateRun, run);
                    run.setText(lines[i]);
                    copyParagraphFormat(paragraph, newParagraph);
                }
            }

            // 从后往前清除原有runs
            for (int i = size - 1; i >= 0; i--) {
                paragraph.removeRun(i);
            }
        }
    }


    // 核心样式复制方法
    private static void copyRunStyle(XWPFRun source, XWPFRun target) {
        // 基础样式
        target.setFontFamily(source.getFontFamily());
        target.setFontSize(source.getFontSize());
        target.setColor(source.getColor());
        target.setBold(source.isBold());
        target.setItalic(source.isItalic());
        target.setUnderline(source.getUnderline());

        // 高级样式（需要处理CTR级别属性）
        CTRPr targetRPr = target.getCTR().isSetRPr() ?
                target.getCTR().getRPr() : target.getCTR().addNewRPr();
        CTRPr sourceRPr = source.getCTR().getRPr();

        if (sourceRPr != null) {
            // 深拷贝样式属性
            targetRPr.set(sourceRPr.copy());
        }

        // 特殊处理：文本位置
        if (sourceRPr.isSetPosition()) {
            target.setTextPosition(sourceRPr.getPosition().getVal().bitCount());
        }

        // 特殊处理：字符间距
        if (sourceRPr.isSetSpacing()) {
            target.setCharacterSpacing(sourceRPr.getSpacing().getVal().intValue());
        }
    }

    /**
     * 复制段落格式
     *
     * @param source 源段落
     * @param target 目标段落
     */
    public static void copyParagraphFormat(XWPFParagraph source, XWPFParagraph target) {
        if (source == null || target == null) return;

        // 1. 复制段落基本属性
        copyParagraphProperties(source, target);

        // 2. 复制段落间距
        copyParagraphSpacing(source, target);

        // 3. 复制缩进设置
        copyParagraphIndentation(source, target);

        // 4. 复制边框设置
        target.setBorderTop(source.getBorderTop());
        target.setBorderBottom(source.getBorderBottom());
        target.setBorderLeft(source.getBorderLeft());
        target.setBorderRight(source.getBorderRight());

        // 5. 复制背景颜色
        if (source.getCTP().getPPr() != null && source.getCTP().getPPr().getShd() != null) {
            CTPPr pPr = target.getCTP().getPPr();
            if (pPr == null) {
                pPr = target.getCTP().addNewPPr();
            }
            if (pPr.getShd() == null) {
                pPr.addNewShd();
            }
            pPr.getShd().setFill(source.getCTP().getPPr().getShd().getFill());
        }

        // 6. 复制段落样式名称（如果有）
        if (source.getStyle() != null) {
            target.setStyle(source.getStyle());
        }

        // 7. 复制段落制表位
        if (source.getCTP().getPPr() != null && source.getCTP().getPPr().getTabs() != null) {
            CTPPr pPr = target.getCTP().getPPr();
            if (pPr == null) {
                pPr = target.getCTP().addNewPPr();
            }
            if (source.getCTP().getPPr().getTabs() != null) {
                pPr.setTabs(source.getCTP().getPPr().getTabs());
            }
        }

        // 8. 复制段落编号设置
        if (source.getNumID() != null) {
            target.setNumID(source.getNumID());
            target.setNumILvl(source.getNumIlvl());
        }
    }

    public static void copyParagraphProperties(XWPFParagraph source, XWPFParagraph target) {
        // 获取源段落的段落属性
        CTPPr sourcePPr = source.getCTP().getPPr();
        if (sourcePPr == null) {
            return;
        }

        // 获取或创建目标段落的段落属性
        CTPPr targetPPr = target.getCTP().getPPr();
        if (targetPPr == null) {
            targetPPr = target.getCTP().addNewPPr();
        }

        // 1. 复制水平对齐方式
        if (sourcePPr.isSetJc()) {
            CTJc targetJc = targetPPr.isSetJc() ? targetPPr.getJc() : targetPPr.addNewJc();
            targetJc.setVal(sourcePPr.getJc().getVal());
        }

        // 2. 复制垂直对齐方式
        if (sourcePPr.isSetTextAlignment()) {
            CTTextAlignment targetVAlign = targetPPr.isSetTextAlignment() ?
                    targetPPr.getTextAlignment() : targetPPr.addNewTextAlignment();
            targetVAlign.setVal(sourcePPr.getTextAlignment().getVal());
        }

        // 3. 复制自动换行设置
        if (sourcePPr.isSetWordWrap()) {
            CTOnOff targetWrap = targetPPr.isSetWordWrap() ?
                    targetPPr.getWordWrap() : targetPPr.addNewWordWrap();
            targetWrap.setVal(sourcePPr.getWordWrap().getVal());
        }

        // 4. 复制分页设置
        if (sourcePPr.isSetPageBreakBefore()) {
            CTOnOff targetBreak = targetPPr.isSetPageBreakBefore() ?
                    targetPPr.getPageBreakBefore() : targetPPr.addNewPageBreakBefore();
            targetBreak.setVal(sourcePPr.getPageBreakBefore().getVal());
        }
    }


    public static void copyParagraphIndentation(XWPFParagraph source, XWPFParagraph target) {
        // 获取源段落的段落属性
        CTPPr sourcePPr = source.getCTP().getPPr();
        if (sourcePPr == null || !sourcePPr.isSetInd()) {
            return; // 如果源段落没有设置缩进，直接返回
        }

        // 获取源段落的缩进设置
        CTInd sourceInd = sourcePPr.getInd();

        // 获取或创建目标段落的段落属性
        CTPPr targetPPr = target.getCTP().getPPr();
        if (targetPPr == null) {
            targetPPr = target.getCTP().addNewPPr();
        }

        // 获取或创建目标段落的缩进设置
        CTInd targetInd = targetPPr.isSetInd() ? targetPPr.getInd() : targetPPr.addNewInd();

        // 复制左缩进
        if (sourceInd.isSetLeft()) {
            targetInd.setLeft(sourceInd.getLeft());
        }

        // 复制右缩进
        if (sourceInd.isSetRight()) {
            targetInd.setRight(sourceInd.getRight());
        }

        // 复制首行缩进
        if (sourceInd.isSetFirstLine()) {
            targetInd.setFirstLine(sourceInd.getFirstLine());
        }

        // 复制悬挂缩进
        if (sourceInd.isSetHanging()) {
            targetInd.setHanging(sourceInd.getHanging());
        }
    }


    public static void copyParagraphSpacing(XWPFParagraph source, XWPFParagraph target) {
        // 获取源段落的段落属性
        CTPPr sourcePPr = source.getCTP().getPPr();
        if (sourcePPr == null || !sourcePPr.isSetSpacing()) {
            return; // 如果源段落没有设置间距，直接返回
        }

        // 获取源段落的间距设置
        CTSpacing sourceSpacing = sourcePPr.getSpacing();

        // 获取或创建目标段落的段落属性
        CTPPr targetPPr = target.getCTP().getPPr();
        if (targetPPr == null) {
            targetPPr = target.getCTP().addNewPPr();
        }

        // 获取或创建目标段落的间距设置
        CTSpacing targetSpacing = targetPPr.isSetSpacing() ? targetPPr.getSpacing() : targetPPr.addNewSpacing();

        // 复制行距设置
        if (sourceSpacing.isSetLine()) {
            targetSpacing.setLine(sourceSpacing.getLine());
        }
        if (sourceSpacing.isSetLineRule()) {
            targetSpacing.setLineRule(sourceSpacing.getLineRule());
        }

        // 复制段前间距
        if (sourceSpacing.isSetBefore()) {
            targetSpacing.setBefore(sourceSpacing.getBefore());
        }
        if (sourceSpacing.isSetBeforeAutospacing()) {
            targetSpacing.setBeforeAutospacing(sourceSpacing.getBeforeAutospacing());
        }

        // 复制段后间距
        if (sourceSpacing.isSetAfter()) {
            targetSpacing.setAfter(sourceSpacing.getAfter());
        }
        if (sourceSpacing.isSetAfterAutospacing()) {
            targetSpacing.setAfterAutospacing(sourceSpacing.getAfterAutospacing());
        }
    }

    /**
     * 使用FreeMarker处理模板文本
     */
    private static String processTemplate(String templateText, Map<String, String> dataModel) {
        try (StringReader reader = new StringReader(templateText);
             StringWriter writer = new StringWriter()) {
            // 创建字符串模板
            Template template = new Template("stringTemplate",
                    reader,
                    SpringBeanUtil.getBean(Configuration.class));

            // 处理模板
            template.process(dataModel, writer);
            return writer.toString();
        } catch (Exception e) {
            // 如果处理失败，返回原文本
            return templateText;
        }
    }

    /**
     * 在Word文档中插入图片
     * 
     * @param paragraph 段落对象
     * @param imageBase64 图片的base64编码
     * @param pictureType 图片类型 (例如: XWPFDocument.PICTURE_TYPE_PNG)
     * @param width 图片宽度 (磅)
     * @param height 图片高度 (磅)
     */
    public static void insertImageToParagraph(XWPFParagraph paragraph, String imageBase64, int pictureType, int width, int height) {
        try {
            if (StringUtils.isBlank(imageBase64)) {
                return;
            }
            
            // 清理 Base64 数据，移除可能的 Data URL 前缀和非法字符
            String cleanBase64Data = cleanBase64Data(imageBase64);
            
            byte[] imageBytes = Base64.getDecoder().decode(cleanBase64Data);
            XWPFRun run = paragraph.createRun();
            
            try (ByteArrayInputStream inputStream = new ByteArrayInputStream(imageBytes)) {
                run.addPicture(inputStream, pictureType, "image", Units.toEMU(width), Units.toEMU(height));
            }
        } catch (IllegalArgumentException e) {
            log.error("Base64 数据格式错误，插入图片到段落失败: {}", e.getMessage());
            // 插入错误提示文本
            XWPFRun errorRun = paragraph.createRun();
            errorRun.setText("[image error]");
        } catch (Exception e) {
            log.error("插入图片到段落失败", e);
            // 插入错误提示文本
            XWPFRun errorRun = paragraph.createRun();
            errorRun.setText("[image error]");
        }
    }

    /**
     * 从URL下载图片并转换为base64
     * 
     * @param imageUrl 图片URL
     * @return base64编码的图片
     */
    public static String downloadImageToBase64(String imageUrl) {
        try {
            if (StringUtils.isBlank(imageUrl)) {
                return null;
            }
            
            try (InputStream inputStream = new URL(imageUrl).openStream()) {
                byte[] imageBytes = IOUtils.toByteArray(inputStream);
                return Base64.getEncoder().encodeToString(imageBytes);
            }
        } catch (Exception e) {
            log.error("下载图片转base64失败，URL: {}", imageUrl, e);
            return null;
        }
    }

    /**
     * 创建居中对齐的段落
     * 
     * @param document Word文档
     * @param text 段落文本
     * @return 创建的段落
     */
    public static XWPFParagraph createCenteredParagraph(XWPFDocument document, String text) {
        XWPFParagraph paragraph = document.createParagraph();
        paragraph.setAlignment(ParagraphAlignment.CENTER);
        
        if (StringUtils.isNotBlank(text)) {
            XWPFRun run = paragraph.createRun();
            run.setText(text);
            run.setBold(true);
        }
        
        return paragraph;
    }

    /**
     * 替换word中的占位符（向后兼容方法）
     *
     * @param resource     输入文件
     * @param replacements 替换的内容
     */
    public static byte[] replaceInWord(Resource resource, Map<String, String> replacements)
            throws IOException {
        return replaceInWordWithImages(resource, replacements, new HashMap<>());
    }

}

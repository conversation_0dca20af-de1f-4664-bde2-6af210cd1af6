package com.patsnap.drafting.util;

import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;

import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.Codec;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023/5/25 13:42
 */
@Component
@Slf4j
public class RedissonUtils {

    public <T> void set(RedissonClient client, String key, T object) {
        try {
            RBucket<T> bucket = client.getBucket(key);
            if (bucket != null) {
                bucket.set(object);
            }
        } catch (Exception e) {
            log.error("set to redis found exception !", e);
        }
    }

    public <T> void set(RedissonClient client, String key, T object, long timeToLive, TimeUnit timeUnit) {
        try {
            RBucket<T> bucket = client.getBucket(key);
            if (bucket != null) {
                bucket.set(object, timeToLive, timeUnit);
            }
        } catch (Exception e) {
            log.error("set to redis found exception !");
        }
    }

    public <T> void setAsync(RBucket<T> bucket, T object, long timeToLive, TimeUnit timeUnit, Executor executor) {
        executor.execute(()->{
            try {
                if (bucket != null) {
                    bucket.set(object, timeToLive, timeUnit);
                }
            } catch (Exception e) {
                log.error("set to redis found exception !", e);
            }
        });
    }

    public <T> void set(RBucket<T> bucket, T object, long timeToLive, TimeUnit timeUnit) {
        try {
            if (bucket != null) {
                bucket.set(object, timeToLive, timeUnit);
            }
        } catch (Exception e) {
            log.error("set to redis found exception !", e);
        }
    }

    public <T> T get(RedissonClient client, String key) {
        try {
            RBucket<T> bucket = client.getBucket(key);
            if (bucket == null) {
                return null;
            }
            return bucket.get();
        } catch (Exception e) {
            log.error("get from redis found exception !", e);
        }
        return null;
    }

    public <T> RBucket<T> getBucket(RedissonClient client, String key, Codec codec) {
        try {
            RBucket<T> bucket = client.getBucket(key, codec);
            return bucket;
        } catch (Exception e) {
            log.error("get bucket from redis found exception !", e);
        }
        return null;
    }

    public void delete(RBucket bucket) {
        try {
            if (bucket !=null) {
                bucket.delete();
            }
        } catch (Exception e) {
            log.error("remove data from redis found exception !", e);
        }
    }



    public <T> T get(RBucket<T> bucket) {
        try {
            return bucket.get();
        } catch (Exception e) {
            log.error("get bucket from redis found exception !", e);
        }
        return null;
    }

    public <T> RBucket<T> getBucket(RedissonClient client, String key) {
        try {
            RBucket<T> bucket = client.getBucket(key);
            return bucket;
        } catch (Exception e) {
            log.error("get bucket from redis found exception !", e);
        }
        return null;
    }


    public boolean isExists(RBucket bucket) {
        try {
            return bucket.isExists();
        } catch (Exception e) {
            log.error("get  redis bucket is exists found exception !", e);
        }
        return false;
    }

    /**
     * 获取Redis锁
     * @param client RedissonClient
     * @param waitTime 等待时间
     * @param leaseTime 锁过期时间
     * @param lockKey 锁key
     * @return
     */
    public boolean getLock(RedissonClient client, long waitTime, long leaseTime, String lockKey) {
        RLock rLock = client.getLock(lockKey);
        boolean getLock = false;
        try {
            getLock = rLock.tryLock(waitTime, leaseTime, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.warn("Failed to get redis lock", e);
        }
        return getLock;
    }

    /**
     * 释放Redis锁
     * @param client RedissonClient
     * @param lockKey 锁key
     */
    public void clearLock(RedissonClient client, String lockKey) {
        try {
            RLock rLock = client.getLock(lockKey);
            rLock.unlock();
        } catch (Exception e) {
            log.warn(" failed to unlock redis lock", e);
        }
    }
}

package com.patsnap.drafting.util;

import org.slf4j.MDC;

public class TaskIdHolder {
    private static final ThreadLocal<String> TASK_ID_HOLDER = new ThreadLocal();

    public TaskIdHolder() {
    }

    public static void set(String userId) {
        TASK_ID_HOLDER.set(userId);
    }

    public static String get() {
        return (String)TASK_ID_HOLDER.get();
    }

    public static String remove() {
        String userId = (String)TASK_ID_HOLDER.get();
        TASK_ID_HOLDER.remove();
        return userId;
    }
}

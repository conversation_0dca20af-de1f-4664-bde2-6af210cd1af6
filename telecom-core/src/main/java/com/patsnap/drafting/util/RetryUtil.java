package com.patsnap.drafting.util;

import java.util.function.Supplier;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2024/08/07
 */
@Slf4j
public class RetryUtil {

    /**
     * 方法请求失败时，重试执行3次方法, 每次重试间隔(5秒*执行次数)
     *
     * @param retryMethod 重试方法
     * @param <T>
     * @return
     */
    public static <T> T retry(Supplier<T> retryMethod) {
        return retry(retryMethod, 5000, 3);
    }

    public static void retry(Runnable retryMethod) {
        retry(retryMethod, 5000, 3);
    }

    /**
     * 方法请求失败时，重试执行方法
     *
     * @param retryMethod 重试方法
     * @param sleepTime   重试间隔时间，间隔时间=重试次数*sleepTime
     * @param maxRetries  最大重试次数
     * @return
     */
    public static <T> T retry(Supplier<T> retryMethod, long sleepTime, int maxRetries) {
        int retryCount = 0;
        while (true) {
            try {
                return retryMethod.get();
            } catch (Exception e) {
                log.error("执行方法异常", e);
                retryCount++;
                if (retryCount >= maxRetries) {
                    log.warn("重试次数超过限制，异常信息：{}", e.getMessage(), e);
                    throw new RuntimeException(e);
                }
                TimeUtil.sleep(sleepTime * retryCount);
            }
        }
    }

    public static void retry(Runnable retryMethod, long delay, int maxAttempts) {
        retry(() -> {
            retryMethod.run();
            return null;
        }, delay, maxAttempts);
    }
}

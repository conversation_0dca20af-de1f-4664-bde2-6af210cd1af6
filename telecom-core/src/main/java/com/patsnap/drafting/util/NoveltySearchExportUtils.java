package com.patsnap.drafting.util;

import com.patsnap.analytics.infrastructure.utils.SpringBeanUtil;
import freemarker.template.Configuration;
import freemarker.template.Template;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xwpf.usermodel.*;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.*;
import org.springframework.core.io.Resource;

import java.io.*;
import java.util.*;

import static com.patsnap.drafting.constants.ExportConstant.TEXT_FONT_SIZE;

/**
 * 查新导出工具类
 *
 * <AUTHOR>
 */
public class NoveltySearchExportUtils {
    
    /**
     * 表格字体大小常量
     */
    private static final int TABLE_FONT_SIZE = 9;


    /**
     * word中添加正文
     *
     * @param doc     word文档
     * @param content 正文内容
     */
    public static void addText(XWPFDocument doc, String content) {
        String[] splits = content.split("\n");
        for (String split : splits) {
            XWPFParagraph paragraph = doc.createParagraph();
            XWPFRun run = paragraph.createRun();
            run.setText(split);
            run.setFontSize(12);
        }
    }

    /**
     * word中添加标题
     *
     * @param doc   word文档
     * @param title 标题
     */
    public static void addTitle(XWPFDocument doc, String title) {
        XWPFParagraph paragraph = doc.createParagraph();
        paragraph.setAlignment(ParagraphAlignment.CENTER);
        XWPFRun run = paragraph.createRun();
        run.setBold(true);
        run.setText(title);
        run.setFontSize(14);
    }

    /**
     * 替换word中的占位符
     *
     * @param resource     输入文件
     * @param replacements 替换的内容
     */
    public static byte[] replaceInWord(Resource resource, Map<String, String> replacements)
            throws IOException {
        try (InputStream fis = resource.getInputStream(); XWPFDocument doc = new XWPFDocument(fis)) {

            // 收集所有需要处理的段落，避免在遍历过程中修改文档结构
            List<XWPFParagraph> allParagraphs = new ArrayList<>();

            // 收集文档中的段落
            allParagraphs.addAll(doc.getParagraphs());

            // 收集表格中的段落
            for (XWPFTable table : doc.getTables()) {
                for (XWPFTableRow row : table.getRows()) {
                    for (XWPFTableCell cell : row.getTableCells()) {
                        allParagraphs.addAll(cell.getParagraphs());
                    }
                }
            }

            // 处理所有段落
            for (XWPFParagraph paragraph : allParagraphs) {
                processParagraphSafely(paragraph, replacements);
            }

            // 将文档写入字节数组
            try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
                doc.write(baos);
                return baos.toByteArray();
            }
        }
    }

    private static void processParagraph(XWPFParagraph paragraph, Map<String, String> replacements) {
        StringBuilder paragraphText = new StringBuilder(paragraph.getText());
        List<XWPFRun> runs = paragraph.getRuns();

        for (Map.Entry<String, String> entry : replacements.entrySet()) {
            replaceAllOccurrences(paragraphText, runs, entry.getKey(), entry.getValue());
        }
    }

    private static void replaceAllOccurrences(StringBuilder text, List<XWPFRun> runs, String key, String value) {
        int keyIndex = text.indexOf(key);
        while (keyIndex != -1) {
            if (replaceOccurrence(text, runs, keyIndex, key, value)) {
                keyIndex = text.indexOf(key, keyIndex + value.length());
            } else {
                keyIndex = text.indexOf(key, keyIndex + 1);
            }
        }
    }

    private static boolean replaceOccurrence(StringBuilder text, List<XWPFRun> runs, int startIndex, String key,
                                             String value) {
        int endIndex = startIndex + key.length() - 1;
        int startRunIndex = getRunIndexForPosition(runs, startIndex);
        int endRunIndex = getRunIndexForPosition(runs, endIndex);

        if (startRunIndex != -1 && endRunIndex != -1) {
            replaceTextInRuns(runs, startRunIndex, endRunIndex, key, value);
            text.replace(startIndex, startIndex + key.length(), value);
            return true;
        }
        return false;
    }

    private static int getRunIndexForPosition(List<XWPFRun> runs, int position) {
        int currentPosition = 0;
        for (int i = 0; i < runs.size(); i++) {
            currentPosition += runs.get(i).getText(0).length();
            if (currentPosition > position) {
                return i;
            }
        }
        return -1;
    }

    private static void replaceTextInRuns(List<XWPFRun> runs, int startIndex, int endIndex, String oldText,
                                          String newText) {
        if (startIndex == endIndex) {
            // 替换发生在单个run内
            XWPFRun run = runs.get(startIndex);
            String text = run.getText(0);
            run.setText(text.replace(oldText, newText), 0);
        } else {
            // 替换跨越多个run
            StringBuilder sb = new StringBuilder();
            for (int i = startIndex; i <= endIndex; i++) {
                sb.append(runs.get(i).getText(0));
            }
            String combinedText = sb.toString();
            String newCombinedText = combinedText.replace(oldText, newText);

            // 更新第一个run，删除其他run
            runs.get(startIndex).setText(newCombinedText, 0);
            for (int i = endIndex; i > startIndex; i--) {
                runs.get(i).setText("", 0);
            }
        }
    }

    /**
     * 安全地处理段落中的变量，不修改文档结构
     */
    private static void processParagraphSafely(XWPFParagraph paragraph, Map<String, String> dataModel) {
        String text = paragraph.getText();

        // 检查是否包含FreeMarker变量
        if (text.contains("${")) {
            // 首先检查是否所有变量都在dataModel中存在
            if (!allVariablesExistInDataModel(text, dataModel)) {
                // 如果有变量不存在，跳过处理，保留原样
                return;
            }

            // 处理模板
            String processedText = processTemplate(text, dataModel);

            // 如果处理后的文本与原文本相同，说明没有成功替换，跳过
            if (processedText.equals(text)) {
                return;
            }

            // 安全地替换文本内容，保留非文本run，不修改文档结构
            replaceTextInPlace(paragraph, processedText);
        }
    }

    /**
     * 检查文本中的所有变量是否都在dataModel中存在
     */
    private static boolean allVariablesExistInDataModel(String text, Map<String, String> dataModel) {
        int start = 0;
        while ((start = text.indexOf("${", start)) != -1) {
            int end = text.indexOf("}", start);
            if (end == -1) {
                break;
            }
            String variable = text.substring(start + 2, end);
            if (!dataModel.containsKey(variable)) {
                return false;
            }
            start = end + 1;
        }
        return true;
    }

    /**
     * 在原地替换段落中的文本，不修改文档结构，保留非文本内容（如图片等）
     */
    private static void replaceTextInPlace(XWPFParagraph paragraph, String processedText) {
        List<XWPFRun> runs = paragraph.getRuns();

        // 重建段落文本：清空所有文本run，在第一个文本run中设置新文本
        boolean hasSetText = false;
        XWPFRun firstTextRun = null;

        // 第一遍：找到第一个文本run并记录
        for (XWPFRun run : runs) {
            String runText = run.getText(0);
            if (runText != null && firstTextRun == null) {
                firstTextRun = run;
            }
        }

        // 第二遍：清空所有包含文本的run
        for (XWPFRun run : runs) {
            String runText = run.getText(0);
            if (runText != null) {
                if (run == firstTextRun && !hasSetText) {
                    // 在第一个文本run中设置新文本，正确处理换行符
                    setTextWithLineBreaks(run, processedText);
                    hasSetText = true;
                } else {
                    // 清空其他文本run
                    run.setText("", 0);
                }
            }
            // 非文本run（如图片、对象等）保持不变
        }

        // 如果没有找到任何文本run，创建一个新的
        if (!hasSetText) {
            XWPFRun newRun = paragraph.createRun();
            setTextWithLineBreaks(newRun, processedText);
        }
    }

    /**
     * 在 XWPFRun 中设置文本，正确处理换行符
     *
     * @param run 文本运行对象
     * @param text 要设置的文本（可能包含换行符）
     */
    private static void setTextWithLineBreaks(XWPFRun run, String text) {
        if (text == null || text.isEmpty()) {
            run.setText("", 0);
            return;
        }

        // 按换行符分割文本
        String[] lines = text.split("\n", -1); // -1 保留空字符串

        if (lines.length == 1) {
            // 没有换行符，直接设置文本
            run.setText(text, 0);
        } else {
            run.setText("", 0); // 清空原有文本
            // 有换行符，使用底层API正确处理
            org.openxmlformats.schemas.wordprocessingml.x2006.main.CTR ctr = run.getCTR();
            
            // 为每一行添加文本和换行符
            for (int i = 0; i < lines.length; i++) {
                if (i > 0) {
                    ctr.addNewBr(); // 添加换行符
                }
                // 添加文本节点
                org.openxmlformats.schemas.wordprocessingml.x2006.main.CTText ctText = ctr.addNewT();
                ctText.setStringValue(lines[i]);
            }
        }
    }


    // 核心样式复制方法
    private static void copyRunStyle(XWPFRun source, XWPFRun target) {
        // 基础样式
        target.setFontFamily(source.getFontFamily());
        target.setFontSize(source.getFontSize());
        target.setColor(source.getColor());
        target.setBold(source.isBold());
        target.setItalic(source.isItalic());
        target.setUnderline(source.getUnderline());

        // 高级样式（需要处理CTR级别属性）
        CTRPr targetRPr = target.getCTR().isSetRPr() ?
                target.getCTR().getRPr() : target.getCTR().addNewRPr();
        CTRPr sourceRPr = source.getCTR().getRPr();

        if (sourceRPr != null) {
            // 深拷贝样式属性
            targetRPr.set(sourceRPr.copy());
        }

        // 特殊处理：文本位置
        if (sourceRPr.isSetPosition()) {
            target.setTextPosition(sourceRPr.getPosition().getVal().bitCount());
        }

        // 特殊处理：字符间距
        if (sourceRPr.isSetSpacing()) {
            target.setCharacterSpacing(sourceRPr.getSpacing().getVal().intValue());
        }
    }

    /**
     * 复制段落格式
     *
     * @param source 源段落
     * @param target 目标段落
     */
    public static void copyParagraphFormat(XWPFParagraph source, XWPFParagraph target) {
        if (source == null || target == null) return;

        // 1. 复制段落基本属性
        copyParagraphProperties(source, target);

        // 2. 复制段落间距
        copyParagraphSpacing(source, target);

        // 3. 复制缩进设置
        copyParagraphIndentation(source, target);

        // 4. 复制边框设置
        target.setBorderTop(source.getBorderTop());
        target.setBorderBottom(source.getBorderBottom());
        target.setBorderLeft(source.getBorderLeft());
        target.setBorderRight(source.getBorderRight());

        // 5. 复制背景颜色
        if (source.getCTP().getPPr() != null && source.getCTP().getPPr().getShd() != null) {
            CTPPr pPr = target.getCTP().getPPr();
            if (pPr == null) {
                pPr = target.getCTP().addNewPPr();
            }
            if (pPr.getShd() == null) {
                pPr.addNewShd();
            }
            pPr.getShd().setFill(source.getCTP().getPPr().getShd().getFill());
        }

        // 6. 复制段落样式名称（如果有）
        if (source.getStyle() != null) {
            target.setStyle(source.getStyle());
        }

        // 7. 复制段落制表位
        if (source.getCTP().getPPr() != null && source.getCTP().getPPr().getTabs() != null) {
            CTPPr pPr = target.getCTP().getPPr();
            if (pPr == null) {
                pPr = target.getCTP().addNewPPr();
            }
            if (source.getCTP().getPPr().getTabs() != null) {
                pPr.setTabs(source.getCTP().getPPr().getTabs());
            }
        }

        // 8. 复制段落编号设置
        if (source.getNumID() != null) {
            target.setNumID(source.getNumID());
            target.setNumILvl(source.getNumIlvl());
        }
    }

    public static void copyParagraphProperties(XWPFParagraph source, XWPFParagraph target) {
        // 获取源段落的段落属性
        CTPPr sourcePPr = source.getCTP().getPPr();
        if (sourcePPr == null) {
            return;
        }

        // 获取或创建目标段落的段落属性
        CTPPr targetPPr = target.getCTP().getPPr();
        if (targetPPr == null) {
            targetPPr = target.getCTP().addNewPPr();
        }

        // 1. 复制水平对齐方式
        if (sourcePPr.isSetJc()) {
            CTJc targetJc = targetPPr.isSetJc() ? targetPPr.getJc() : targetPPr.addNewJc();
            targetJc.setVal(sourcePPr.getJc().getVal());
        }

        // 2. 复制垂直对齐方式
        if (sourcePPr.isSetTextAlignment()) {
            CTTextAlignment targetVAlign = targetPPr.isSetTextAlignment() ?
                    targetPPr.getTextAlignment() : targetPPr.addNewTextAlignment();
            targetVAlign.setVal(sourcePPr.getTextAlignment().getVal());
        }

        // 3. 复制自动换行设置
        if (sourcePPr.isSetWordWrap()) {
            CTOnOff targetWrap = targetPPr.isSetWordWrap() ?
                    targetPPr.getWordWrap() : targetPPr.addNewWordWrap();
            targetWrap.setVal(sourcePPr.getWordWrap().getVal());
        }

        // 4. 复制分页设置
        if (sourcePPr.isSetPageBreakBefore()) {
            CTOnOff targetBreak = targetPPr.isSetPageBreakBefore() ?
                    targetPPr.getPageBreakBefore() : targetPPr.addNewPageBreakBefore();
            targetBreak.setVal(sourcePPr.getPageBreakBefore().getVal());
        }
    }


    public static void copyParagraphIndentation(XWPFParagraph source, XWPFParagraph target) {
        // 获取源段落的段落属性
        CTPPr sourcePPr = source.getCTP().getPPr();
        if (sourcePPr == null || !sourcePPr.isSetInd()) {
            return; // 如果源段落没有设置缩进，直接返回
        }

        // 获取源段落的缩进设置
        CTInd sourceInd = sourcePPr.getInd();

        // 获取或创建目标段落的段落属性
        CTPPr targetPPr = target.getCTP().getPPr();
        if (targetPPr == null) {
            targetPPr = target.getCTP().addNewPPr();
        }

        // 获取或创建目标段落的缩进设置
        CTInd targetInd = targetPPr.isSetInd() ? targetPPr.getInd() : targetPPr.addNewInd();

        // 复制左缩进
        if (sourceInd.isSetLeft()) {
            targetInd.setLeft(sourceInd.getLeft());
        }

        // 复制右缩进
        if (sourceInd.isSetRight()) {
            targetInd.setRight(sourceInd.getRight());
        }

        // 复制首行缩进
        if (sourceInd.isSetFirstLine()) {
            targetInd.setFirstLine(sourceInd.getFirstLine());
        }

        // 复制悬挂缩进
        if (sourceInd.isSetHanging()) {
            targetInd.setHanging(sourceInd.getHanging());
        }
    }


    public static void copyParagraphSpacing(XWPFParagraph source, XWPFParagraph target) {
        // 获取源段落的段落属性
        CTPPr sourcePPr = source.getCTP().getPPr();
        if (sourcePPr == null || !sourcePPr.isSetSpacing()) {
            return; // 如果源段落没有设置间距，直接返回
        }

        // 获取源段落的间距设置
        CTSpacing sourceSpacing = sourcePPr.getSpacing();

        // 获取或创建目标段落的段落属性
        CTPPr targetPPr = target.getCTP().getPPr();
        if (targetPPr == null) {
            targetPPr = target.getCTP().addNewPPr();
        }

        // 获取或创建目标段落的间距设置
        CTSpacing targetSpacing = targetPPr.isSetSpacing() ? targetPPr.getSpacing() : targetPPr.addNewSpacing();

        // 复制行距设置
        if (sourceSpacing.isSetLine()) {
            targetSpacing.setLine(sourceSpacing.getLine());
        }
        if (sourceSpacing.isSetLineRule()) {
            targetSpacing.setLineRule(sourceSpacing.getLineRule());
        }

        // 复制段前间距
        if (sourceSpacing.isSetBefore()) {
            targetSpacing.setBefore(sourceSpacing.getBefore());
        }
        if (sourceSpacing.isSetBeforeAutospacing()) {
            targetSpacing.setBeforeAutospacing(sourceSpacing.getBeforeAutospacing());
        }

        // 复制段后间距
        if (sourceSpacing.isSetAfter()) {
            targetSpacing.setAfter(sourceSpacing.getAfter());
        }
        if (sourceSpacing.isSetAfterAutospacing()) {
            targetSpacing.setAfterAutospacing(sourceSpacing.getAfterAutospacing());
        }
    }

    /**
     * 使用FreeMarker处理模板文本
     */
    private static String processTemplate(String templateText, Map<String, String> dataModel) {
        try {
            // 如果文本中没有变量，直接返回原文本
            if (!templateText.contains("${")) {
                return templateText;
            }

            // 创建字符串模板
            Template template = new Template("stringTemplate",
                    new StringReader(templateText),
                    SpringBeanUtil.getBean(Configuration.class));

            // 处理模板
            StringWriter writer = new StringWriter();
            template.process(dataModel, writer);
            return writer.toString();
        } catch (Exception e) {
            // 如果处理失败，返回原文本（保持原样）
            return templateText;
        }
    }


    /**
     * 设置单元格文本
     */
    public static void setCellText(XWPFTable table, int row, int col, String text, boolean isBold, ParagraphAlignment alignment, String cellColor) {
        XWPFTableCell cell = table.getRow(row).getCell(col);
        // 设置垂直对齐
        cell.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
        if(StringUtils.isNotBlank(cellColor)) {
            cell.setColor(cellColor);
        }

        XWPFParagraph paragraph = cell.getParagraphs().get(0);
        paragraph.setAlignment(ParagraphAlignment.CENTER);
        // 设置水平对齐
        paragraph.setAlignment(alignment);
        paragraph.setSpacingBefore(0);
        paragraph.setSpacingAfter(0);
        XWPFRun run = paragraph.createRun();
        setText(run, text, TABLE_FONT_SIZE, isBold);
    }

    /**
     * 设置单元格文本，支持换行符
     */
    public static void setCellTextWithLineBreaks(XWPFTable table, int row, int col, String text, boolean isBold, ParagraphAlignment alignment, String cellColor) {
        XWPFTableCell cell = table.getRow(row).getCell(col);
        // 设置垂直对齐
        cell.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
        
        // 通过底层API强制设置垂直对齐
        if (cell.getCTTc().getTcPr() == null) {
            cell.getCTTc().addNewTcPr();
        }
        if (cell.getCTTc().getTcPr().getVAlign() == null) {
            cell.getCTTc().getTcPr().addNewVAlign();
        }
        cell.getCTTc().getTcPr().getVAlign().setVal(STVerticalJc.CENTER);
        
        if(StringUtils.isNotBlank(cellColor)) {
            cell.setColor(cellColor);
        }

        XWPFParagraph paragraph = cell.getParagraphs().get(0);
        paragraph.setAlignment(ParagraphAlignment.CENTER);
        // 设置水平对齐
        paragraph.setAlignment(alignment);
        paragraph.setSpacingBefore(0);
        paragraph.setSpacingAfter(0);
        
        // 处理包含换行符的文本
        if (text != null && text.contains("\n")) {
            // 使用已有的setTextWithLineBreaks方法
            XWPFRun run = paragraph.createRun();
            run.setBold(isBold);
            run.setFontSize(TABLE_FONT_SIZE);
            run.setFontFamily("等线");
            setTextWithLineBreaks(run, text);
        } else {
            XWPFRun run = paragraph.createRun();
            setText(run, text, TABLE_FONT_SIZE, isBold);
        }
    }

    /**
     * 设置单元格文本
     */
    public static void setCellTextWithStrikeThrough(XWPFTable table, int row, int col, String text, boolean isBold, ParagraphAlignment alignment, String cellColor) {
        XWPFTableCell cell = table.getRow(row).getCell(col);
        // 设置垂直对齐
        cell.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
        if (StringUtils.isNotBlank(cellColor)) {
            cell.setColor(cellColor);
        }

        XWPFParagraph paragraph = cell.getParagraphs().get(0);
        paragraph.setAlignment(ParagraphAlignment.CENTER);
        // 设置水平对齐
        paragraph.setAlignment(alignment);
        paragraph.setSpacingBefore(0);
        paragraph.setSpacingAfter(0);
        XWPFRun run = paragraph.createRun();
        setTextWithStrikeThrough(run, text, TABLE_FONT_SIZE, isBold);
    }

    public static void setText(XWPFRun run, String text, Integer fontSize, boolean isBold) {
        run.setText(text);
        run.setBold(isBold);
        run.setFontSize(fontSize);
        run.setFontFamily("等线");
    }

    public static void setText(XWPFRun run, String text, String fontFamily, Integer fontSize, boolean isBold) {
        run.setText(text);
        run.setBold(isBold);
        run.setFontSize(fontSize);
        run.setFontFamily(fontFamily);
    }

    public static void setText(XWPFRun run, String text, Integer fontSize, boolean isBold, String fontFamily) {
        run.setText(text);
        run.setBold(isBold);
        run.setFontSize(fontSize);
        run.setFontFamily(fontFamily);
    }

    public static void setTextWithStrikeThrough(XWPFRun run, String text, Integer fontSize, boolean isBold) {
        run.setText(text);
        run.setBold(isBold);
        run.setFontSize(fontSize);
        run.setFontFamily("等线");
        run.setStrikeThrough(true); // 设置删除线效果
    }

    /**
     * 设置列宽
     */
    public static void setColumnWidth(XWPFTable table, int col, int width) {
        for (XWPFTableRow row : table.getRows()) {
            row.getCell(col).setWidth(width + "%");
        }
    }

    public static void setTextWithColor(XWPFRun run, String text, Integer fontSize, boolean isBold, String color) {
        run.setText(text);
        run.setBold(isBold);
        run.setFontSize(fontSize);
        run.setFontFamily("等线");
        // run.setColor("FFFFFF");

        // 设置底纹颜色（不是高亮）
        CTRPr rPr = run.getCTR().isSetRPr() ? run.getCTR().getRPr() : run.getCTR().addNewRPr();
        CTShd shd = rPr.isSetShd() ? rPr.getShd() : rPr.addNewShd();
        shd.setVal(STShd.CLEAR);              // 填充样式：CLEAR 表示纯色
        shd.setColor("auto");                 // 前景色（边框线等），一般设为 auto
        shd.setFill(color);                   // 背景填充色（十六进制，不带 #）
    }

    /**
     * 设置单元格文本(高亮关键字)
     */
    public static void setCellTextWithHighlight(XWPFTable table, int row, int col, String originalText, ParagraphAlignment alignment, Map<String, String> keywordColorList) {
        XWPFTableCell cell = table.getRow(row).getCell(col);
        // 设置垂直对齐
        cell.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);

        XWPFParagraph paragraph = cell.getParagraphs().get(0);
        paragraph.setAlignment(ParagraphAlignment.CENTER);
        // 设置水平对齐
        paragraph.setAlignment(alignment);
        paragraph.setSpacingBefore(0);
        paragraph.setSpacingAfter(0);

        // 按关键字拆分文本并标记颜色
        highlightKeywords(paragraph, originalText, keywordColorList);
    }

    /**
     * 设置单元格文本
     */
    public static void setCellTextWithColor(XWPFTable table, int row, int col, String text, boolean isBold, ParagraphAlignment alignment, String textColor) {
        XWPFTableCell cell = table.getRow(row).getCell(col);
        // 设置垂直对齐
        cell.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);

        XWPFParagraph paragraph = cell.getParagraphs().get(0);
        paragraph.setAlignment(ParagraphAlignment.CENTER);
        // 设置水平对齐
        paragraph.setAlignment(alignment);
        paragraph.setSpacingBefore(0);
        paragraph.setSpacingAfter(0);
        XWPFRun run = paragraph.createRun();
        setTextWithColor(run, text, TABLE_FONT_SIZE, isBold, textColor);
    }

    /**
     * 设置单元格文本(单元格内换行)
     */
    public static void setCellTextWithBreakAndHighlight(XWPFTable table, int row, int col, String text1, String text2, boolean isBold, ParagraphAlignment alignment, String cellColor, Map<String, String> keywordColorList) {
        XWPFTableCell cell = table.getRow(row).getCell(col);
        // 设置垂直对齐
        cell.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
        if(StringUtils.isNotBlank(cellColor)) {
            cell.setColor(cellColor);
        }

        XWPFParagraph paragraph = cell.getParagraphs().get(0);
        paragraph.setAlignment(ParagraphAlignment.CENTER);
        // 设置水平对齐
        paragraph.setAlignment(alignment);
        paragraph.setSpacingBefore(0);
        paragraph.setSpacingAfter(0);
        XWPFRun run = paragraph.createRun();
        run.setBold(isBold);
        run.setFontSize(TABLE_FONT_SIZE);
        run.setFontFamily("等线");
        
        // 使用底层API正确处理换行
        org.openxmlformats.schemas.wordprocessingml.x2006.main.CTR ctr = run.getCTR();
        
        // 添加第一行文本
        org.openxmlformats.schemas.wordprocessingml.x2006.main.CTText ctText1 = ctr.addNewT();
        ctText1.setStringValue(text1);
        
        // 添加换行符
        ctr.addNewBr();
        
        // 添加第二行文本
        org.openxmlformats.schemas.wordprocessingml.x2006.main.CTText ctText2 = ctr.addNewT();
        ctText2.setStringValue(text2);

        // 按关键字拆分文本并标记颜色
        // 高亮用此行代码
        // highlightKeywords(paragraph, text2, keywordColorList);
    }

    /**
     * 设置单元格文本(单元格内换行)
     */
    public static void setCellTextWithBreak(XWPFTable table, int row, int col, List<String> textList, boolean isBold, ParagraphAlignment alignment, String cellColor) {
        XWPFTableCell cell = table.getRow(row).getCell(col);
        // 设置垂直对齐
        cell.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
        if (StringUtils.isNotBlank(cellColor)) {
            cell.setColor(cellColor);
        }

        XWPFParagraph paragraph = cell.getParagraphs().get(0);
        paragraph.setAlignment(ParagraphAlignment.CENTER);
        // 设置水平对齐
        paragraph.setAlignment(alignment);
        paragraph.setSpacingBefore(0);
        paragraph.setSpacingAfter(0);
        XWPFRun run = paragraph.createRun();
        run.setBold(isBold);
        run.setFontSize(TABLE_FONT_SIZE);
        run.setFontFamily("等线");
        
        // 使用底层API正确处理换行
        org.openxmlformats.schemas.wordprocessingml.x2006.main.CTR ctr = run.getCTR();
        
        for (int i = 0; i < textList.size(); i++) {
            if (i > 0) {
                ctr.addNewBr(); // 添加换行符
            }
            // 添加文本节点
            org.openxmlformats.schemas.wordprocessingml.x2006.main.CTText ctText = ctr.addNewT();
            ctText.setStringValue(textList.get(i));
        }
    }

    /**
     * 处理原始文本并标记关键字颜色
     */
    public static void highlightKeywords(XWPFParagraph paragraph, String originalText, Map<String, String> keywordColorList) {
        int start = 0;

        // 遍历整个文本，根据关键字进行分割
        for (Map.Entry<String, String> entry : keywordColorList.entrySet()) {
            String keyword = entry.getKey();
            String color = entry.getValue();

            int index;
            while ((index = originalText.indexOf(keyword, start)) != -1) {
                // 添加前面的普通文本
                if (start < index) {
                    addRun(paragraph, originalText.substring(start, index), "000000"); // 黑色普通文本
                }

                // 添加高亮的关键字
                addRunWithHighlight(paragraph, keyword, color);

                // 更新起始位置，避免重复匹配
                start = index + keyword.length();
            }
        }

        // 添加剩余文本
        if (start < originalText.length()) {
            addRun(paragraph, originalText.substring(start), "000000");
        }
    }

    public static void highlightKeywordsWithOverlap(XWPFParagraph paragraph, String originalText, Map<String, String> keywordColorList) {
        class HighlightRegion {
            int start;
            int end;
            String color;

            HighlightRegion(int start, int end, String color) {
                this.start = start;
                this.end = end;
                this.color = color;
            }
        }

        List<HighlightRegion> regions = new ArrayList<>();

        // 查找所有关键词出现位置，建立高亮区域
        for (Map.Entry<String, String> entry : keywordColorList.entrySet()) {
            String keyword = entry.getKey();
            String color = entry.getValue();

            int index = 0;
            while ((index = originalText.indexOf(keyword, index)) != -1) {
                // todo 此处代码容易导致后端服务OOM服务，后续需要优化
                regions.add(new HighlightRegion(index, index + keyword.length(), color));
                index += 1; // 为了能匹配到重复的词组
            }
        }

        // 按起始位置排序
        regions.sort(Comparator.comparingInt(r -> r.start));

        // 合并重叠区域：只保留不重叠的最早的，后面的往前截断
        List<HighlightRegion> nonOverlapRegions = new ArrayList<>();
        int cursor = 0;

        for (HighlightRegion region : regions) {
            if (region.start >= cursor) {
                nonOverlapRegions.add(region);
                cursor = region.end;
            } else if (region.end > cursor) {
                // 截断当前区域，防止覆盖
                region.start = cursor;
                nonOverlapRegions.add(new HighlightRegion(region.start, region.end, region.color));
                cursor = region.end;
            }
        }

        // 开始构建段落内容
        int currentIndex = 0;
        for (HighlightRegion region : nonOverlapRegions) {
            if (currentIndex < region.start) {
                addRun(paragraph, originalText.substring(currentIndex, region.start), "000000"); // 黑色普通文本
            }
            addRunWithHighlight(paragraph, originalText.substring(region.start, region.end), region.color);
            currentIndex = region.end;
        }

        // 添加剩余文本
        if (currentIndex < originalText.length()) {
            addRun(paragraph, originalText.substring(currentIndex), "000000");
        }
    }


    /**
     * 创建 XWPFRun 并设置文本和颜色
     */
    public static void addRun(XWPFParagraph paragraph, String text, String fontColor) {
        XWPFRun run = paragraph.createRun();
        run.setText(text);
        run.setColor(fontColor);
        run.setFontSize(TABLE_FONT_SIZE);
        run.setFontFamily("等线");
    }

    /**
     * 创建 XWPFRun 并设置文本和颜色
     */
    public static void addRunWithHighlight(XWPFParagraph paragraph, String text, String fontColor) {
        XWPFRun run = paragraph.createRun();
        run.setText(text);
        // run.setColor(fontColor);
        run.setFontSize(TABLE_FONT_SIZE);
        run.setFontFamily("等线");

        // 设置底纹颜色（不是高亮）
        CTRPr rPr = run.getCTR().isSetRPr() ? run.getCTR().getRPr() : run.getCTR().addNewRPr();
        CTShd shd = rPr.isSetShd() ? rPr.getShd() : rPr.addNewShd();
        shd.setVal(STShd.CLEAR);              // 填充样式：CLEAR 表示纯色
        shd.setColor("auto");                 // 前景色（边框线等），一般设为 auto
        shd.setFill(fontColor);               // 背景填充色（十六进制，不带 #）
    }

    // 横向合并行中的多个单元格
    public static void mergeCellsHorizontally(XWPFTable table, int row, int fromCol, int toCol) {
        XWPFTableRow tableRow = table.getRow(row);
        for (int colIndex = fromCol; colIndex <= toCol; colIndex++) {
            XWPFTableCell cell = tableRow.getCell(colIndex);
            if (colIndex == fromCol) {
                cell.getCTTc().addNewTcPr().addNewHMerge().setVal(STMerge.RESTART);
            } else {
                cell.getCTTc().addNewTcPr().addNewHMerge().setVal(STMerge.CONTINUE);
            }
        }
    }

    // 纵向合并列中的多个单元格
    public static void mergeCellsVertically(XWPFTable table, int col, int fromRow, int toRow) {
        for (int rowIndex = fromRow; rowIndex <= toRow; rowIndex++) {
            XWPFTableCell cell = table.getRow(rowIndex).getCell(col);
            // 设置垂直对齐方式为顶部
            cell.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.TOP);

            if (rowIndex == fromRow) {
                cell.getCTTc().addNewTcPr().addNewVMerge().setVal(STMerge.RESTART);
            } else {
                cell.getCTTc().addNewTcPr().addNewVMerge().setVal(STMerge.CONTINUE);
            }
        }
    }

}

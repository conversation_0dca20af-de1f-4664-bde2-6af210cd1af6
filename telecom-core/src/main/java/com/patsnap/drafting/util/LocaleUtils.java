package com.patsnap.drafting.util;


import com.patsnap.drafting.enums.common.Lang;

import java.util.Locale;

/**
 * @Description:
 * <AUTHOR>
 * @Date: 2018/11/22 15:59
 */
public class LocaleUtils {

    public static Locale getLocale(Lang lang) {
        Locale locale;
        switch (lang) {
            case EN:
                locale = Locale.US;
                break;
            case CN:
                locale = Locale.SIMPLIFIED_CHINESE;
                break;
            case TW:
                locale = Locale.TRADITIONAL_CHINESE;
                break;
            case JP:
                locale = Locale.JAPAN;
                break;
            case DE:
                locale = Locale.US;
                break;
            default:
                locale = Locale.US;
        }
        return locale;
    }

    public static Locale getLocale(String lang) {
        Locale locale;
        lang = lang.toUpperCase();
        switch (lang) {
            case "CN":
                locale = Locale.SIMPLIFIED_CHINESE;
                break;
            case "TW":
                locale = Locale.TRADITIONAL_CHINESE;
                break;
            case "JP":
                locale = Locale.JAPAN;
                break;
            case "DE":
                locale = Locale.GERMANY;
                break;
            default: //EN, default
                locale = Locale.US;
        }
//        LOGGER.info("Returned locale = {} for language = {}", locale, lang);
        return locale;
    }
}

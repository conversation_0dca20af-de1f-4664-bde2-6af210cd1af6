package com.patsnap.drafting.util;

import com.patsnap.drafting.client.PatentApiClient;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 引用替换器 - 用于处理流式全量内容中的引用标记替换
 * 每次处理的内容都是完整内容，包含之前处理过的内容
 * 通过缓存上次的处理结果，避免重复处理相同的内容
 */
public class ReferenceReplacer {
    
    public static final String REF = "_ref";
    
    private final List<String> references;  // 引用列表
    private final String contentType;  // 生成内容的标识
    private final PatentApiClient patentApiClient;  // 生成内容的标识
    private String lastContent = "";  // 上次处理的原始内容
    private String lastProcessedResult = "";  // 上次处理的结果
    private Map<String, String> patentId2PnMap = new HashMap<>();

    /**
     * 构造函数
     * @param references 引用列表，索引从1开始
     */
    public ReferenceReplacer(List<String> references, String contentType, PatentApiClient patentApiClient) {
        this.references = references;
        this.contentType = contentType;
        this.patentApiClient = patentApiClient;
    }


    /**
     * 构造函数
     *
     * @param references 引用列表，索引从1开始
     */
    public ReferenceReplacer(List<String> references) {
        this.references = references;
        this.contentType = null;
        this.patentApiClient = null;
    }

    /**
     * 处理新到达的内容
     *
     * @param newContent 新到达的完整内容
     * @return 处理完成的内容
     */
    public String process(String newContent) {
        if (StringUtils.isNotBlank(contentType) && !contentType.contains(REF)) {
            return newContent;
        }
        return doProcess(newContent);
    }

    public String doProcess(String newContent) {
        if (CollectionUtils.isEmpty(references)) {
            return newContent;
        }
        if (StringUtils.isBlank(newContent)) {
            return newContent;
        }

        // 如果内容没有变化，直接返回上次的结果
        if (newContent.equals(lastContent)) {
            return lastProcessedResult;
        }

        // 处理完整内容
        lastContent = newContent;
        lastProcessedResult = processReferences(newContent);
        return lastProcessedResult;
    }

    /**
     * 处理引用标记
     * @param content 待处理内容
     * @return 处理后的内容
     */
    private String processReferences(String content) {
        if (MapUtils.isEmpty(patentId2PnMap) && Objects.nonNull(patentApiClient)) {
            patentId2PnMap = patentApiClient.getPnMap(references);
        }
        
        // 使用正则表达式匹配 [数字] 模式
        Pattern pattern = Pattern.compile("\\[(\\d+)\\]");
        Matcher matcher = pattern.matcher(content);
        StringBuilder sb = new StringBuilder();

        while (matcher.find()) {
            try {
                int index = Integer.parseInt(matcher.group(1)) - 1;  // 将1开始的索引转换为0开始
                String replacement = (index >= 0 && index < references.size())
                        ? "[" + patentId2PnMap.getOrDefault(references.get(index), references.get(index)) + "]"
                        : matcher.group(0);  // 如果索引无效，保留原始文本
                // 处理特殊字符
                replacement = Matcher.quoteReplacement(replacement);
                matcher.appendReplacement(sb, replacement);
            } catch (NumberFormatException e) {
                // 数字解析错误，保留原始文本
                matcher.appendReplacement(sb, matcher.group(0));
            }
        }
        matcher.appendTail(sb);

        return sb.toString();
    }

    /**
     * 重置处理器状态
     * 在需要重新开始处理新的内容时调用
     */
    public void reset() {
        lastContent = "";
        lastProcessedResult = "";
    }
} 
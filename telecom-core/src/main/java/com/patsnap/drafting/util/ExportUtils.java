package com.patsnap.drafting.util;

import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xwpf.usermodel.*;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTRPr;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTShd;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.STMerge;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.STShd;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;

import static com.patsnap.drafting.constants.ExportConstant.TEXT_FONT_SIZE;

/**
 * 导出工具类
 *
 * <AUTHOR>
 * @Date 2025/4/14 15:03
 */
public class ExportUtils {

    /**
     * 设置单元格文本
     */
    public static void setCellText(XWPFTable table, int row, int col, String text, boolean isBold, ParagraphAlignment alignment, String cellColor) {
        XWPFTableCell cell = table.getRow(row).getCell(col);
        // 设置垂直对齐
        cell.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
        if(StringUtils.isNotBlank(cellColor)) {
            cell.setColor(cellColor);
        }

        XWPFParagraph paragraph = cell.getParagraphs().get(0);
        paragraph.setAlignment(ParagraphAlignment.CENTER);
        // 设置水平对齐
        paragraph.setAlignment(alignment);
        paragraph.setSpacingBefore(0);
        paragraph.setSpacingAfter(0);
        XWPFRun run = paragraph.createRun();
        setText(run, text, TEXT_FONT_SIZE, isBold);
    }

    /**
     * 设置单元格文本
     */
    public static void setCellTextWithStrikeThrough(XWPFTable table, int row, int col, String text, boolean isBold, ParagraphAlignment alignment, String cellColor) {
        XWPFTableCell cell = table.getRow(row).getCell(col);
        // 设置垂直对齐
        cell.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
        if (StringUtils.isNotBlank(cellColor)) {
            cell.setColor(cellColor);
        }

        XWPFParagraph paragraph = cell.getParagraphs().get(0);
        paragraph.setAlignment(ParagraphAlignment.CENTER);
        // 设置水平对齐
        paragraph.setAlignment(alignment);
        paragraph.setSpacingBefore(0);
        paragraph.setSpacingAfter(0);
        XWPFRun run = paragraph.createRun();
        setTextWithStrikeThrough(run, text, TEXT_FONT_SIZE, isBold);
    }

    public static void setText(XWPFRun run, String text, Integer fontSize, boolean isBold) {
        run.setText(text);
        run.setBold(isBold);
        run.setFontSize(fontSize);
        run.setFontFamily("宋体");
    }

    public static void setTextWithStrikeThrough(XWPFRun run, String text, Integer fontSize, boolean isBold) {
        run.setText(text);
        run.setBold(isBold);
        run.setFontSize(fontSize);
        run.setFontFamily("宋体");
        run.setStrikeThrough(true); // 设置删除线效果
    }

    /**
     * 设置列宽
     */
    public static void setColumnWidth(XWPFTable table, int col, int width) {
        for (XWPFTableRow row : table.getRows()) {
            row.getCell(col).setWidth(width + "%");
        }
    }

    public static void setTextWithColor(XWPFRun run, String text, Integer fontSize, boolean isBold, String color) {
        run.setText(text);
        run.setBold(isBold);
        run.setFontSize(fontSize);
        run.setFontFamily("宋体");
        // run.setColor("FFFFFF");

        // 设置底纹颜色（不是高亮）
        CTRPr rPr = run.getCTR().isSetRPr() ? run.getCTR().getRPr() : run.getCTR().addNewRPr();
        CTShd shd = rPr.isSetShd() ? rPr.getShd() : rPr.addNewShd();
        shd.setVal(STShd.CLEAR);              // 填充样式：CLEAR 表示纯色
        shd.setColor("auto");                 // 前景色（边框线等），一般设为 auto
        shd.setFill(color);                   // 背景填充色（十六进制，不带 #）
    }

    /**
     * 设置单元格文本(高亮关键字)
     */
    public static void setCellTextWithHighlight(XWPFTable table, int row, int col, String originalText, ParagraphAlignment alignment, Map<String, String> keywordColorList) {
        XWPFTableCell cell = table.getRow(row).getCell(col);
        // 设置垂直对齐
        cell.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);

        XWPFParagraph paragraph = cell.getParagraphs().get(0);
        paragraph.setAlignment(ParagraphAlignment.CENTER);
        // 设置水平对齐
        paragraph.setAlignment(alignment);
        paragraph.setSpacingBefore(0);
        paragraph.setSpacingAfter(0);

        // 按关键字拆分文本并标记颜色
        highlightKeywords(paragraph, originalText, keywordColorList);
    }

    /**
     * 设置单元格文本
     */
    public static void setCellTextWithColor(XWPFTable table, int row, int col, String text, boolean isBold, ParagraphAlignment alignment, String textColor) {
        XWPFTableCell cell = table.getRow(row).getCell(col);
        // 设置垂直对齐
        cell.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);

        XWPFParagraph paragraph = cell.getParagraphs().get(0);
        paragraph.setAlignment(ParagraphAlignment.CENTER);
        // 设置水平对齐
        paragraph.setAlignment(alignment);
        paragraph.setSpacingBefore(0);
        paragraph.setSpacingAfter(0);
        XWPFRun run = paragraph.createRun();
        setTextWithColor(run, text, TEXT_FONT_SIZE, isBold, textColor);
    }

    /**
     * 设置单元格文本(单元格内换行)
     */
    public static void setCellTextWithBreakAndHighlight(XWPFTable table, int row, int col, String text1, String text2, boolean isBold, ParagraphAlignment alignment, String cellColor, Map<String, String> keywordColorList) {
        XWPFTableCell cell = table.getRow(row).getCell(col);
        // 设置垂直对齐
        cell.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
        if(StringUtils.isNotBlank(cellColor)) {
            cell.setColor(cellColor);
        }

        XWPFParagraph paragraph = cell.getParagraphs().get(0);
        paragraph.setAlignment(ParagraphAlignment.CENTER);
        // 设置水平对齐
        paragraph.setAlignment(alignment);
        paragraph.setSpacingBefore(0);
        paragraph.setSpacingAfter(0);
        XWPFRun run = paragraph.createRun();
        setText(run, text1, TEXT_FONT_SIZE, isBold);
        run.addBreak();

        setText(run, text2, TEXT_FONT_SIZE, isBold);

        // 按关键字拆分文本并标记颜色
        // 高亮用此行代码
        // highlightKeywords(paragraph, text2, keywordColorList);
    }

    /**
     * 设置单元格文本(单元格内换行)
     */
    public static void setCellTextWithBreak(XWPFTable table, int row, int col, List<String> textList, boolean isBold, ParagraphAlignment alignment, String cellColor) {
        XWPFTableCell cell = table.getRow(row).getCell(col);
        // 设置垂直对齐
        cell.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
        if (StringUtils.isNotBlank(cellColor)) {
            cell.setColor(cellColor);
        }

        XWPFParagraph paragraph = cell.getParagraphs().get(0);
        paragraph.setAlignment(ParagraphAlignment.CENTER);
        // 设置水平对齐
        paragraph.setAlignment(alignment);
        paragraph.setSpacingBefore(0);
        paragraph.setSpacingAfter(0);
        XWPFRun run = paragraph.createRun();
        for (int i = 0; i < textList.size(); i++) {
            setText(run, textList.get(i), TEXT_FONT_SIZE, isBold);
            if (i != textList.size() - 1) {
                run.addBreak();
            }
        }
    }

    /**
     * 处理原始文本并标记关键字颜色
     */
    public static void highlightKeywords(XWPFParagraph paragraph, String originalText, Map<String, String> keywordColorList) {
        int start = 0;

        // 遍历整个文本，根据关键字进行分割
        for (Map.Entry<String, String> entry : keywordColorList.entrySet()) {
            String keyword = entry.getKey();
            String color = entry.getValue();

            int index;
            while ((index = originalText.indexOf(keyword, start)) != -1) {
                // 添加前面的普通文本
                if (start < index) {
                    addRun(paragraph, originalText.substring(start, index), "000000"); // 黑色普通文本
                }

                // 添加高亮的关键字
                addRunWithHighlight(paragraph, keyword, color);

                // 更新起始位置，避免重复匹配
                start = index + keyword.length();
            }
        }

        // 添加剩余文本
        if (start < originalText.length()) {
            addRun(paragraph, originalText.substring(start), "000000");
        }
    }

    public static void highlightKeywordsWithOverlap(XWPFParagraph paragraph, String originalText, Map<String, String> keywordColorList) {
        class HighlightRegion {
            int start;
            int end;
            String color;

            HighlightRegion(int start, int end, String color) {
                this.start = start;
                this.end = end;
                this.color = color;
            }
        }

        List<HighlightRegion> regions = new ArrayList<>();

        // 查找所有关键词出现位置，建立高亮区域
        for (Map.Entry<String, String> entry : keywordColorList.entrySet()) {
            String keyword = entry.getKey();
            String color = entry.getValue();

            int index = 0;
            while ((index = originalText.indexOf(keyword, index)) != -1) {
                // todo 此处代码容易导致后端服务OOM服务，后续需要优化
                regions.add(new HighlightRegion(index, index + keyword.length(), color));
                index += 1; // 为了能匹配到重复的词组
            }
        }

        // 按起始位置排序
        regions.sort(Comparator.comparingInt(r -> r.start));

        // 合并重叠区域：只保留不重叠的最早的，后面的往前截断
        List<HighlightRegion> nonOverlapRegions = new ArrayList<>();
        int cursor = 0;

        for (HighlightRegion region : regions) {
            if (region.start >= cursor) {
                nonOverlapRegions.add(region);
                cursor = region.end;
            } else if (region.end > cursor) {
                // 截断当前区域，防止覆盖
                region.start = cursor;
                nonOverlapRegions.add(new HighlightRegion(region.start, region.end, region.color));
                cursor = region.end;
            }
        }

        // 开始构建段落内容
        int currentIndex = 0;
        for (HighlightRegion region : nonOverlapRegions) {
            if (currentIndex < region.start) {
                addRun(paragraph, originalText.substring(currentIndex, region.start), "000000"); // 黑色普通文本
            }
            addRunWithHighlight(paragraph, originalText.substring(region.start, region.end), region.color);
            currentIndex = region.end;
        }

        // 添加剩余文本
        if (currentIndex < originalText.length()) {
            addRun(paragraph, originalText.substring(currentIndex), "000000");
        }
    }


    /**
     * 创建 XWPFRun 并设置文本和颜色
     */
    public static void addRun(XWPFParagraph paragraph, String text, String fontColor) {
        XWPFRun run = paragraph.createRun();
        run.setText(text);
        run.setColor(fontColor);
        run.setFontSize(TEXT_FONT_SIZE);
        run.setFontFamily("宋体");
    }

    /**
     * 创建 XWPFRun 并设置文本和颜色
     */
    public static void addRunWithHighlight(XWPFParagraph paragraph, String text, String fontColor) {
        XWPFRun run = paragraph.createRun();
        run.setText(text);
        // run.setColor(fontColor);
        run.setFontSize(TEXT_FONT_SIZE);
        run.setFontFamily("宋体");

        // 设置底纹颜色（不是高亮）
        CTRPr rPr = run.getCTR().isSetRPr() ? run.getCTR().getRPr() : run.getCTR().addNewRPr();
        CTShd shd = rPr.isSetShd() ? rPr.getShd() : rPr.addNewShd();
        shd.setVal(STShd.CLEAR);              // 填充样式：CLEAR 表示纯色
        shd.setColor("auto");                 // 前景色（边框线等），一般设为 auto
        shd.setFill(fontColor);               // 背景填充色（十六进制，不带 #）
    }

    // 横向合并行中的多个单元格
    public static void mergeCellsHorizontally(XWPFTable table, int row, int fromCol, int toCol) {
        XWPFTableRow tableRow = table.getRow(row);
        for (int colIndex = fromCol; colIndex <= toCol; colIndex++) {
            XWPFTableCell cell = tableRow.getCell(colIndex);
            if (colIndex == fromCol) {
                cell.getCTTc().addNewTcPr().addNewHMerge().setVal(STMerge.RESTART);
            } else {
                cell.getCTTc().addNewTcPr().addNewHMerge().setVal(STMerge.CONTINUE);
            }
        }
    }

    // 纵向合并列中的多个单元格
    public static void mergeCellsVertically(XWPFTable table, int col, int fromRow, int toRow) {
        for (int rowIndex = fromRow; rowIndex <= toRow; rowIndex++) {
            XWPFTableCell cell = table.getRow(rowIndex).getCell(col);
            // 设置垂直对齐方式为顶部
            cell.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.TOP);

            if (rowIndex == fromRow) {
                cell.getCTTc().addNewTcPr().addNewVMerge().setVal(STMerge.RESTART);
            } else {
                cell.getCTTc().addNewTcPr().addNewVMerge().setVal(STMerge.CONTINUE);
            }
        }
    }

    /**
     * 将阿拉伯数字转换为中文数字
     *
     * @param number 要转换的数字
     * @return 对应的中文数字字符串
     */
    public static String convertNumberToChinese(int number) {
        if (number <= 0) {
            return String.valueOf(number);
        }

        String[] chineseNumbers = {"", "一", "二", "三", "四", "五", "六", "七", "八", "九"};
        String[] units = {"", "十", "百", "千", "万"};

        if (number < 10) {
            return chineseNumbers[number];
        }

        if (number < 100) {
            int tens = number / 10;
            int ones = number % 10;
            if (tens == 1) {
                return units[1] + (ones == 0 ? "" : chineseNumbers[ones]);
            } else {
                return chineseNumbers[tens] + units[1] + (ones == 0 ? "" : chineseNumbers[ones]);
            }
        }

        if (number < 1000) {
            int hundreds = number / 100;
            int remainder = number % 100;
            int tens = remainder / 10;
            int ones = remainder % 10;

            StringBuilder result = new StringBuilder();
            result.append(chineseNumbers[hundreds]).append(units[2]); // 百位

            if (remainder == 0) {
                // 整百数，如 200 -> "二百"
                return result.toString();
            } else if (tens == 0) {
                // 百位和个位之间没有十位，需要加"零"，如 205 -> "二百零五"
                result.append("零").append(chineseNumbers[ones]);
            } else if (tens == 1) {
                // 十位是1，如 110 -> "一百一十", 115 -> "一百一十五"
                result.append(chineseNumbers[tens]).append(units[1]);
                if (ones != 0) {
                    result.append(chineseNumbers[ones]);
                }
            } else {
                // 正常的百位+十位+个位，如 256 -> "二百五十六"
                result.append(chineseNumbers[tens]).append(units[1]);
                if (ones != 0) {
                    result.append(chineseNumbers[ones]);
                }
            }

            return result.toString();
        }

        // 处理超过1000的数字，直接返回数字字符串
        return String.valueOf(number);
    }

    /**
     * 将阿拉伯数字转换为中文数字（重载方法，支持字符串输入）
     *
     * @param numberStr 要转换的数字字符串
     * @return 对应的中文数字字符串
     */
    public static String convertNumberToChinese(String numberStr) {
        if (StringUtils.isBlank(numberStr)) {
            return numberStr;
        }

        try {
            int number = Integer.parseInt(numberStr.trim());
            return convertNumberToChinese(number);
        } catch (NumberFormatException e) {
            // 如果不是有效数字，直接返回原字符串
            return numberStr;
        }
    }

}

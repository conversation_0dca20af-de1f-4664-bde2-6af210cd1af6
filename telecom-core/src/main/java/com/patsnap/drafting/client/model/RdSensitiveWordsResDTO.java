package com.patsnap.drafting.client.model;

import lombok.Data;

@Data
public class RdSensitiveWordsResDTO {

    private Integer errorCode;
    private String errorMessage;
    private RdData data;

    @Data
    public static class RdData {

        private String lang;
        
        private String label;
        /**
         * errorCode == 2 表示有敏感词
         */
        private Integer errorCode;
        /**
         * 敏感词内容
         */
        private String out;
        
        public boolean hasSensitiveWords() {
            return errorCode == 2 && "Polity".equalsIgnoreCase(label);
        }
    }
}

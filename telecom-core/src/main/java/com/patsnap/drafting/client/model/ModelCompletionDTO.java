package com.patsnap.drafting.client.model;

import java.util.List;

import lombok.Data;

/**
 * openai_chatgpt_turbo api调用数据传输类
 * <AUTHOR>
 * @date 2024/4/12
 */
@Data
public class ModelCompletionDTO {

    private String model;

    private List<MessageDTO> messages;

    private String message;
    /**
     * 使用什么取样温度，0到2之间。越高越奔放。越低越保守。
     * <p>
     * 不要同时改这个和topP
     */
    private Double temperature;

    /**
     * 0-1 建议0.9 不要同时改这个和temperature
     */
    private Double topP;


    /**
     * 结果数。
     */
    private Integer n;


    /**
     * 是否流式输出. default:false
     */
    private Boolean stream;

    /**
     * 3.5 最大支持4096 4.0 最大32k
     */
    private Integer maxTokens;

    /**
     * patsnap gpt使用，repetition_penalty参数表示重复惩罚因子，推荐1.015/1.0
     */
    private Double repetitionPenalty;

    /**
     * patsnap gpt使用，输出最大token
     */
    private Integer maxNewToken;

    private Boolean doSample;

    private List<String> stop;
}

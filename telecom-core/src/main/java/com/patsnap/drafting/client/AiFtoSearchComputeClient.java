package com.patsnap.drafting.client;

import com.alibaba.fastjson.JSON;
import com.patsnap.drafting.client.api.AiNoveltyComputeApi;
import com.patsnap.drafting.client.model.FtoSearchRequestDTO;
import com.patsnap.drafting.client.model.FtoSearchResponseDTO;
import com.patsnap.drafting.client.model.NoveltySearchRequestDTO;
import com.patsnap.drafting.client.model.NoveltySearchResponseDTO;
import com.patsnap.drafting.exception.BizException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import retrofit2.Response;

import java.io.IOException;
import java.util.Map;

import static com.patsnap.drafting.exception.errorcode.ClientErrorCodeEnum.CLIENT_NOT_AVAILABLE;

@Slf4j
@Component
@RequiredArgsConstructor
public class AiFtoSearchComputeClient {

    private final AiNoveltyComputeApi aiNoveltyComputeApi;

    /**
     * AI 查新算法接口
     */
    public FtoSearchResponseDTO.Data ftoSearch(FtoSearchRequestDTO request) {
        try {
            request.setModel("gpt-4.1");
            NoveltySearchRequestDTO noveltySearchRequestDTO = new NoveltySearchRequestDTO();
            BeanUtils.copyProperties(request, noveltySearchRequestDTO);
            Response<NoveltySearchResponseDTO> response = aiNoveltyComputeApi.aiNoveltySearch(Map.of("data", noveltySearchRequestDTO)).execute();
            if (response.isSuccessful() && response.body() != null ) {

                FtoSearchResponseDTO.Data data = new FtoSearchResponseDTO.Data();
                if (response.body().getData() == null) {
                    return data;
                }
                NoveltySearchResponseDTO noveltySearchResponseDTO = response.body();
                FtoSearchResponseDTO ftoSearchResponseDTO = new FtoSearchResponseDTO();
                BeanUtils.copyProperties(noveltySearchResponseDTO, ftoSearchResponseDTO);

                BeanUtils.copyProperties(noveltySearchResponseDTO.getData(), data);
                return data;
            } else {
                log.warn("noveltySearch failed, code: {}, message: {}", response.code(), response.message());
                throw new BizException(CLIENT_NOT_AVAILABLE);
            }
        } catch (IOException e) {
            log.error("Failed to noveltySearch, request: {}", JSON.toJSONString(request), e);
            throw new BizException(CLIENT_NOT_AVAILABLE);
        }
    }
}

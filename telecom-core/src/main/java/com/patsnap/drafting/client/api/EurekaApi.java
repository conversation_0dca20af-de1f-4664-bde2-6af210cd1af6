package com.patsnap.drafting.client.api;

import com.patsnap.common.web.entity.CommonResponse;
import com.patsnap.drafting.client.model.CompanyDetailsResDTO;
import com.patsnap.drafting.client.model.CompanyRecommendResDTO;
import com.patsnap.drafting.client.model.HistoryRequestDTO;
import com.patsnap.drafting.response.techreport.TechReportCreateFieldsResDTO;
import retrofit2.Call;
import retrofit2.Response;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.POST;
import retrofit2.http.Query;

/**
 * Eureka API 接口定义
 */
public interface EurekaApi {
    
    /**
     * 保存历史记录
     *
     * @param request 历史记录请求
     * @return 是否保存成功
     */
    @POST("internal/history")
    Call<Boolean> saveHistory(@Body HistoryRequestDTO request);
    
    /**
     * 创建技术监控报告
     *
     * @param request 技术报告创建字段请求
     * @return 创建结果信息对象
     */
    @POST("tech-monitor/create")
    Call<CommonResponse<Object>> createTechMonitor(@Body TechReportCreateFieldsResDTO request);

    /**
     * 公司推荐接口
     * @param request 公司名称请求
     * @return 公司推荐响应（通用格式）
     */
    @GET("helper/corporate-tree/recommend")
    Call<CommonResponse<CompanyRecommendResDTO>> recommendCompany(@Query(value="query") String request);

    /**
     * 获取公司详情接口
     * @param companyId 公司ID
     * @return 公司详情响应（通用格式）
     */
    @GET("search/suggest/company-info")
    Call<CommonResponse<CompanyDetailsResDTO>> getCompanyDetails(@Query(value="company_id") String companyId);
} 
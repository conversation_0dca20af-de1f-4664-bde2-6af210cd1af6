package com.patsnap.drafting.client.model;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.patsnap.drafting.request.ainoveltysearch.AiSearchAgentFeature;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> chendepeng
 * @Date 2025/3/12 13:40
 */
@Accessors(chain = true)
@Data
public class AiFtoSearchAgentRequest {
    // 必传参数
    private String text;

    // 可选参数
    // private String model;

    // 必传参数
    private List<AiSearchAgentFeature> features;

    // 可选参数
    private List<Map<String, Object>> filters = new ArrayList<>();

    @JsonProperty("q_filters")
    private List<Map<String, Object>> qFilters = new ArrayList<>();

    public Map<String, Object> collapse = new HashMap<>();

    // 控制cc的数量，节省成本(通过header参数控制)
    private JSONObject testParams = new JSONObject();

    public void handleTestParams(Integer ccNum) {
        JSONObject testParams = new JSONObject();
        JSONObject ccNumInfo = new JSONObject();
        ccNumInfo.put("cc_num", ccNum);
        testParams.put("test_params", ccNumInfo);
        this.setTestParams(testParams);
    }
}

package com.patsnap.drafting.client.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import java.util.List;

@Accessors(chain = true)
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class NoveltySearchRetrievalAddElementResDTO {

    private Detail data;

    @Accessors(chain = true)
    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class Detail {

        @JsonProperty("extend_list")
        private List<String> extendList;

        @JsonProperty("ipc")
        private List<String> ipc;
    }
}
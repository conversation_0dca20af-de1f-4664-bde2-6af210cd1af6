package com.patsnap.drafting.client.model.ainovelty;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import java.util.Map;

/**
 * 块查询处理过程
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class BlockQueryProcess {

    // 尝试次数
    @JsonProperty("attempt")
    private Integer attempt;

    // 逻辑说明
    @JsonProperty("logic")
    private Map<String, String> logic;

    // 查询表达式
    @JsonProperty("query")
    private String query;

    // 命中数量
    @JsonProperty("count")
    private Integer count;

    // 结论
    @JsonProperty("conclusion")
    private Map<String, String> conclusion;
} 
package com.patsnap.drafting.client.model;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/4/22
 */
@Data
public class ContentDTO {

    private String text;
    private String type;
    private SourceDTO source;

    public static ContentDTO of(String type, String text) {
        ContentDTO content = new ContentDTO();
        content.setText(text);
        content.setType(type);
        return content;
    }

    public static ContentDTO of(String type, SourceDTO source) {
        ContentDTO content = new ContentDTO();
        content.setSource(source);
        content.setType(type);
        return content;
    }

    public static ContentDTO of(String type, String data, String mediaType, String sourceType) {
        SourceDTO source = new SourceDTO();
        source.setType(sourceType);
        source.setData(data);
        source.setMediaType(mediaType);
        return ContentDTO.of(type, source);
    }


    @Data
    public static class SourceDTO {

        private String data;
        private String mediaType;
        private String type;
    }
}

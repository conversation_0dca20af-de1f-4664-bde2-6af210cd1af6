package com.patsnap.drafting.client.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 公司详情响应DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class CompanyDetailsResDTO {

    @ApiModelProperty("公司logo")
    @JsonProperty("logo")
    private String logo;

    @ApiModelProperty("公司ID")
    @JsonProperty("company_id")
    private String companyId;

    @ApiModelProperty("答案ID")
    @JsonProperty("ans_id")
    private String ansId;

    @ApiModelProperty("中文名称")
    @JsonProperty("name_cn")
    private String nameCn;

    @ApiModelProperty("英文名称")
    @JsonProperty("name_en")
    private String nameEn;

    @ApiModelProperty("实体类型")
    @JsonProperty("entity_type")
    private String entityType;

    @ApiModelProperty("所有权类型")
    @JsonProperty("ownership_type")
    private List<String> ownershipType;

    @ApiModelProperty("子公司数量")
    @JsonProperty("subsidiaries")
    private Integer subsidiaries;

    @ApiModelProperty("网站域名")
    @JsonProperty("site")
    private String site;

    @ApiModelProperty("完整网址")
    @JsonProperty("website")
    private String website;

    @ApiModelProperty("公司描述")
    @JsonProperty("description")
    private String description;

    @ApiModelProperty("国家名称")
    @JsonProperty("country_name")
    private String countryName;

    @ApiModelProperty("州/省名称")
    @JsonProperty("state_name")
    private String stateName;

    @ApiModelProperty("成立日期")
    @JsonProperty("founded_date")
    private Long foundedDate;

    @ApiModelProperty("是否有访问权限")
    @JsonProperty("has_access")
    private Boolean hasAccess;

    @ApiModelProperty("最新动态")
    @JsonProperty("whats_new")
    private List<WhatsNew> whatsNew;

    @ApiModelProperty("员工数量")
    @JsonProperty("employee_number")
    private Integer employeeNumber;

    @ApiModelProperty("员工数量区间")
    @JsonProperty("employee_number_interval")
    private String employeeNumberInterval;

    @ApiModelProperty("公司名称")
    @JsonProperty("name")
    private String name;

    @ApiModelProperty("公司唯一标识")
    @JsonProperty("company_key")
    private String companyKey;

    /**
     * 最新动态内部类
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class WhatsNew {
        @ApiModelProperty("标题")
        @JsonProperty("title")
        private String title;

        @ApiModelProperty("内容")
        @JsonProperty("content")
        private String content;

        @ApiModelProperty("相关专利ID列表")
        @JsonProperty("patent_ids")
        private List<String> patentIds;
    }
} 
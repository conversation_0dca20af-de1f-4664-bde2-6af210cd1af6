package com.patsnap.drafting.client.api;

import com.patsnap.drafting.request.imagesearch.ImageLocPredictRequest;
import com.patsnap.drafting.request.imagesearch.ImageMultipleSimilarSearchRequest;
import com.patsnap.drafting.request.imagesearch.ImageSimilarSearchRequest;
import com.patsnap.drafting.response.imagesearch.ImageLocPredictResponse;
import com.patsnap.drafting.response.imagesearch.ImageSimilarSearchResponse;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.POST;

/**
 * open API 接口定义
 */
public interface OpenApi {
    
    /**
     * 专利图像检索-单图
     */
    @POST("search/patent/image-single")
    Call<ImageSimilarSearchResponse> searchImageBySingle(@Body ImageSimilarSearchRequest request);

    /**
     * 专利图像检索-多图
     */
    @POST("search/patent/image-multiple")
    Call<ImageSimilarSearchResponse> searchImageByMultiple(@Body ImageMultipleSimilarSearchRequest request);

    /**
     * 图像位置预测
     */
    @POST("ai/image/loc_predict")
    Call<ImageLocPredictResponse> predictImageLoc(@Body ImageLocPredictRequest request);
}
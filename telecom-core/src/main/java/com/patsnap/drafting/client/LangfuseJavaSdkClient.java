package com.patsnap.drafting.client;

import com.patsnap.drafting.observability.langfuse.config.LangfuseProperties;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.annotation.PostConstruct;
import java.util.Base64;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Langfuse API 客户端
 * 使用 REST API 访问 Langfuse 功能，直到 Java SDK 正式发布
 *
 * <AUTHOR> Assistant
 * @date 2025/01/31
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnProperty(prefix = "configs.com.patsnap.drafting.langfuse", name = "enabled", havingValue = "true")
public class LangfuseJavaSdkClient {

    private final LangfuseProperties langfuseProperties;
    private RestTemplate restTemplate;
    private HttpHeaders authHeaders;

    @PostConstruct
    public void initialize() {
        if (!langfuseProperties.isValid()) {
            log.warn("Langfuse configuration is invalid, LangfuseJavaSdkClient will not be initialized");
            return;
        }

        try {
            this.restTemplate = new RestTemplate();
            this.authHeaders = createAuthHeaders();
            
            log.info("LangfuseJavaSdkClient initialized successfully with host: {}", langfuseProperties.getHost());
        } catch (Exception e) {
            log.error("Failed to initialize LangfuseJavaSdkClient", e);
        }
    }

    private HttpHeaders createAuthHeaders() {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        
        String auth = langfuseProperties.getPublicKey() + ":" + langfuseProperties.getSecretKey();
        String encodedAuth = Base64.getEncoder().encodeToString(auth.getBytes());
        headers.set("Authorization", "Basic " + encodedAuth);
        
        return headers;
    }

    /**
     * 获取所有提示词模板
     *
     * @return 提示词模板列表
     */
    @SuppressWarnings("unchecked")
    public Optional<List<Map<String, Object>>> getPrompts() {
        if (restTemplate == null) {
            log.warn("RestTemplate is not initialized");
            return Optional.empty();
        }
        
        try {
            String url = langfuseProperties.getHost() + "/api/public/prompts";
            HttpEntity<String> entity = new HttpEntity<>(authHeaders);
            
            @SuppressWarnings("rawtypes")
            ResponseEntity<Map> response = restTemplate.exchange(url, HttpMethod.GET, entity, Map.class);
            
            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                @SuppressWarnings("unchecked")
                List<Map<String, Object>> prompts = (List<Map<String, Object>>) response.getBody().get("data");
                log.debug("Successfully retrieved {} prompts", prompts != null ? prompts.size() : 0);
                return Optional.ofNullable(prompts);
            }
            
            return Optional.empty();
        } catch (Exception e) {
            log.error("Failed to get prompts from Langfuse API", e);
            return Optional.empty();
        }
    }

    /**
     * 根据名称获取特定提示词模板
     *
     * @param promptName 提示词名称
     * @param version 版本（可选）
     * @return 提示词模板
     */
    public Optional<Map<String, Object>> getPrompt(String promptName, String version) {
        if (restTemplate == null) {
            log.warn("RestTemplate is not initialized");
            return Optional.empty();
        }

        try {
            String url = langfuseProperties.getHost() + "/api/public/prompts/" + promptName;
            if (version != null && !version.isEmpty()) {
                url += "?version=" + version;
            }
            
            HttpEntity<String> entity = new HttpEntity<>(authHeaders);
            @SuppressWarnings("rawtypes")
            ResponseEntity<Map> response = restTemplate.exchange(url, HttpMethod.GET, entity, Map.class);
            
            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                log.debug("Successfully retrieved prompt: {} with version: {}", promptName, version);
                @SuppressWarnings("unchecked")
                Map<String, Object> body = response.getBody();
                return Optional.of(body);
            }
            
            return Optional.empty();
        } catch (Exception e) {
            log.error("Failed to get prompt: {} with version: {}", promptName, version, e);
            return Optional.empty();
        }
    }

    /**
     * 获取追踪详情
     *
     * @param traceId 追踪ID
     * @return 追踪详情
     */
    public Optional<Map<String, Object>> getTrace(String traceId) {
        if (restTemplate == null) {
            log.warn("RestTemplate is not initialized");
            return Optional.empty();
        }

        try {
            String url = langfuseProperties.getHost() + "/api/public/traces/" + traceId;
            HttpEntity<String> entity = new HttpEntity<>(authHeaders);
            
            @SuppressWarnings("rawtypes")
            ResponseEntity<Map> response = restTemplate.exchange(url, HttpMethod.GET, entity, Map.class);
            
            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                log.debug("Successfully retrieved trace: {}", traceId);
                @SuppressWarnings("unchecked")
                Map<String, Object> body = response.getBody();
                return Optional.of(body);
            }
            
            return Optional.empty();
        } catch (Exception e) {
            log.error("Failed to get trace: {}", traceId, e);
            return Optional.empty();
        }
    }

    /**
     * 健康检查
     *
     * @return 是否健康
     */
    public boolean isHealthy() {
        if (restTemplate == null) {
            return false;
        }

        try {
            // 尝试获取提示词作为健康检查
            String url = langfuseProperties.getHost() + "/api/public/prompts";
            HttpEntity<String> entity = new HttpEntity<>(authHeaders);
            
            @SuppressWarnings("rawtypes")
            ResponseEntity<Map> response = restTemplate.exchange(url, HttpMethod.GET, entity, Map.class);
            return response.getStatusCode().is2xxSuccessful();
        } catch (Exception e) {
            log.warn("Langfuse health check failed", e);
            return false;
        }
    }

    /**
     * 获取项目信息
     *
     * @return 项目信息
     */
    public Optional<Map<String, Object>> getProject() {
        if (restTemplate == null) {
            log.warn("RestTemplate is not initialized");
            return Optional.empty();
        }

        try {
            String url = langfuseProperties.getHost() + "/api/public/projects";
            HttpEntity<String> entity = new HttpEntity<>(authHeaders);
            
            @SuppressWarnings("rawtypes")
            ResponseEntity<Map> response = restTemplate.exchange(url, HttpMethod.GET, entity, Map.class);
            
            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                log.debug("Successfully retrieved project information");
                @SuppressWarnings("unchecked")
                Map<String, Object> body = response.getBody();
                return Optional.of(body);
            }
            
            return Optional.empty();
        } catch (Exception e) {
            log.error("Failed to get project information", e);
            return Optional.empty();
        }
    }
}
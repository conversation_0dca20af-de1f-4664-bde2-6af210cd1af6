package com.patsnap.drafting.client.model.ainovelty;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import java.util.List;

/**
 * 查询信息
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class QueryInfo {

    // 查询编号
    @JsonProperty("query_no")
    private String queryNo;

    // 别名查询
    @JsonProperty("alias_query")
    private String aliasQuery;

    // 使用的查询编号
    @JsonProperty("using_query_no")
    private List<String> usingQueryNo;

    // 查询表达式
    @JsonProperty("query")
    private String query;

    // 命中数量
    @JsonProperty("count")
    private Integer count;
} 
package com.patsnap.drafting.client.model;

import java.util.List;
import java.util.Map;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

@Data
public class NoveltyAiSearchComputeReqDTO {

    private Data data;

    @lombok.Data
    public static class Data {
        private String model;
        private String tag;
        private String text;
        private String type;
        @JsonProperty("feature_extract_result")
        private Object featureExtractResult;
        private List<PatentComparisonData> patents;

        @lombok.Data
        public static class PatentComparisonData {
            private String patentId;
            @JsonProperty("feature_pair")
            private List<FeaturePair> featurePair;
        }

        @lombok.Data
        public static class FeaturePair {
            @JsonProperty("tech_feature")
            private String techFeature;

            @JsonProperty("comparison_feature")
            private String comparisonFeature;
        }
    }
} 
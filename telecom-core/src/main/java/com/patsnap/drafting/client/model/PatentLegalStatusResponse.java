package com.patsnap.drafting.client.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Map;

/**
 * 法律状态返回值
 *
 * <AUTHOR>
 * @Date 2025/4/17 10:12
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class PatentLegalStatusResponse {

    private Data data;

    @lombok.Data
    @Accessors(chain = true)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Data {

        @JsonProperty("legalEvents")
        private Map<Integer, Object> legalEvents;

        @JsonProperty("legalStatus")
        private Map<Integer, Object> legalStatus;

        @JsonProperty("simpleLegalStatus")
        private Map<Integer, Object> simpleLegalStatus;
    }

}

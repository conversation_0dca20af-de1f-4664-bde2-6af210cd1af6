package com.patsnap.drafting.client.api;

import com.patsnap.drafting.client.model.*;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.POST;

import java.util.List;
import java.util.Map;

public interface AiNoveltyComputeV2Api {
    
    @POST("novelty_ai_search_v2/")
    Call<NoveltySearchSummaryResDTO> aiNoveltySearchSummary(@Body Map<String, NoveltySearchRequestDTO> request);

    @POST("novelty_ai_search_v2/")
    Call<NoveltySearchResponseDTO> aiNoveltySearchPatentFeatureComparison(@Body Map<String, NoveltySearchRequestDTO> request);

    @POST("novelty_ai_search_v2/")
    Call<NoveltySearchFeatureExtractResDTO> aiNoveltySearchFeatureExtract(@Body Map<String, NoveltySearchRequestDTO> request);

    @POST("novelty_ai_search_v2/")
    Call<NoveltySearchRetrievalElementResDTO> aiNoveltySearchRetrievalElements(@Body Map<String, NoveltySearchRequestDTO> request);

    @POST("novelty_ai_search_v2/")
    Call<NoveltySearchRetrievalAddElementResDTO> aiNoveltySearchRetrievalAddElements(@Body Map<String, NoveltySearchRequestDTO> request);

    @POST("novelty_ai_search_v2/")
    Call<NoveltyAiSearchComputeResDTO> noveltyAiSearchCompute(@Body NoveltyAiSearchComputeReqDTO request);
}
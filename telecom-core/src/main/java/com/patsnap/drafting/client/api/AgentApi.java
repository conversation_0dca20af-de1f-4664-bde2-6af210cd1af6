package com.patsnap.drafting.client.api;

import com.patsnap.drafting.client.model.AgentToolDataRequestDTO;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.POST;

import java.util.Map;

/**
 * Agent 平台 接口定义
 */
public interface AgentApi {
    
    /**
     * 向 agent 平台提交数据
     */
    @POST("user-job-data/save")
    Call<Map<String, Object>> saveData(@Body AgentToolDataRequestDTO request);

    /**
     * 从 agent 平台获取数据
     */
    @POST("user-job-data/get")
    Call<Map<String, Object>> getData(@Body AgentToolDataRequestDTO request);
}
package com.patsnap.drafting.client.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Accessors(chain = true)
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class NoveltySearchFeatureExtractDataDTO {

    @JsonProperty("feature_num")
    private String featureNum;

    @JsonProperty("feature_text")
    private String featureText;

    @JsonProperty("feature_text_original")
    private String featureTextOriginal;

    @JsonProperty("feature_weight")
    private String featureWeight;

    @JsonProperty("feature_weight_reason")
    private String featureWeightReason;

    @JsonProperty("feature_function")
    private String featureFunction;

    @JsonProperty("feature_elements")
    private String featureElements;

    @JsonProperty("user_add")
    private Boolean userAdd = false;

    @JsonProperty("selected")
    private Boolean selected = true;
}
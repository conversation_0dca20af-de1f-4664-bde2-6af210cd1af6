package com.patsnap.drafting.client;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.patsnap.common.request.CorrelationIdHolder;
import com.patsnap.common.request.RequestFromHolder;
import com.patsnap.common.request.UserIdHolder;
import com.patsnap.core.common.AnalyticsCommonService;
import com.patsnap.core.common.CommonConstant;
import com.patsnap.core.common.copilot.gpt.enums.GPTModelEnum;
import com.patsnap.core.common.prompt.PromptPlatformEnum;
import com.patsnap.core.common.request.SiteLangHolder;
import com.patsnap.core.common.request.TenantIdHolder;
import com.patsnap.drafting.client.model.ModelCompletionDTO;
import com.patsnap.drafting.client.model.OpenAICompletionResponseDTO;
import com.patsnap.drafting.enums.prompt.ScenarioEnum;
import com.patsnap.drafting.exception.BizException;
import com.patsnap.drafting.exception.errorcode.ContentErrorCodeEnum;
import com.patsnap.drafting.observability.langfuse.aspect.LangfuseTracing;
import com.patsnap.drafting.observability.langfuse.tracer.AiOperationContext;
import com.patsnap.drafting.observability.langfuse.tracer.AiOperationResult;
import com.patsnap.drafting.observability.langfuse.tracer.LangfuseTracer;
import com.patsnap.drafting.observability.langfuse.tracer.TracingContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.HttpClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.client.BufferingClientHttpRequestFactory;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.lang.reflect.Type;
import java.util.Map;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.patsnap.common.request.RequestFromHolder.X_PATSNAP_FROM;
import static com.patsnap.core.common.copilot.restclient.OpenAIClient.HEADER_AI_ENGINE;
import static com.patsnap.core.common.copilot.restclient.OpenAIClient.HEADER_AI_FEATURE;

/**
 * <AUTHOR>
 * @date 2023/03/03 11:56 上午 星期四
 * @since 1.0
 */
@Slf4j
@Component
public class OpenAiClient {

    private static final String TURBO = "openai_chatgpt_turbo";

    @Value("${com.patsnap.analytics.service.openai-url}")
    private String openAiUrl;
    @Value("${com.patsnap.analytics.service.llm.temperature:0.1}")
    private Double temperature;
    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private AnalyticsCommonService analyticsCommonService;
    @Autowired
    private HttpClient httpClient;
    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 生成HttpEntity对象
     *
     * @param modelCompletion ModelCompletionDTO对象
     * @return HttpEntity<Object>对象
     */
    private HttpHeaders generateHttpHeader(ModelCompletionDTO modelCompletion, ScenarioEnum scenario) {
        HttpHeaders headers = new HttpHeaders();
        headers.add(HEADER_AI_FEATURE, scenario.getValue());
        headers.add(X_PATSNAP_FROM, CommonConstant.RequestFrom.REQUEST_FROM_SEARCH);
        headers.add(HEADER_AI_ENGINE,
                GPTModelEnum.getGptEngine(GPTModelEnum.getGptModelEnum(modelCompletion.getModel())));
        return headers;
    }


    /**
     * 使用GPT模型完成对话
     *
     * @param promptKey       提示语在config client中的key
     * @param params           提示词中的变量参数
     * @param model           GPT模型枚举
     * @return 返回对话结果
     */
    public <T> T chatCompletions(String promptKey, GPTModelEnum model, ScenarioEnum scenario,
            Class<T> clazz, Map<String, String> params) {
        return chatCompletions(promptKey, model, scenario, new TypeReference<>() {
            @Override
            public Type getType() {
                return clazz;
            }
        }, params);
    }

    public <T> T chatCompletions(String promptKey, GPTModelEnum model, ScenarioEnum scenario,
            TypeReference<T> typeReference, Map<String, String> params) {
        String message = buildPromptByPlatform(promptKey, params);
        //构造turbo调用参数
        ModelCompletionDTO modelCompletion = buildModelCompletionDTO(model, message);
        OpenAICompletionResponseDTO result = callGptWithLimit(modelCompletion, scenario);
        log.info("get response from llm: [{}]", JSON.toJSONString(result));
        return parseResponse(result, typeReference);
    }

    public <T> T chatCompletionsWithTimeout(String promptKey, GPTModelEnum model, ScenarioEnum scenario, int timeout,
            TypeReference<T> typeReference, Map<String, String> params) {
        String message = buildPromptByPlatform(promptKey, params);
        //构造turbo调用参数
        ModelCompletionDTO modelCompletion = buildModelCompletionDTO(model, message);
        OpenAICompletionResponseDTO result = callGptWithLimitAndTimeout(modelCompletion, timeout, scenario);
        log.info("get response from llm: [{}]", JSON.toJSONString(result));
        return parseResponse(result, typeReference);
    }

    private <T> T parseJson(TypeReference<T> typeReference, String gptRes) {
        try {
            return objectMapper.readValue(extractJson(gptRes), typeReference);
        } catch (JsonProcessingException e) {
            log.error("Failed to parse JSON response from LLM. Original response: {}", gptRes, e);
            throw new RuntimeException(e);
        }
    }
    
    public static String extractJson(String text) {
        // 使用正则表达式提取```json和```之间的内容
        Pattern pattern = Pattern.compile("```json\\n([\\s\\S]*?)\\n```");
        Matcher matcher = pattern.matcher(text);
        
        if (matcher.find()) {
            return matcher.group(1);
        }
        return text;
    }


    /**
     * 通过prompt管理平台构建prompt
     *
     * @param promptKey 管理平台的prompt key
     * @param params    prompt中的变量参数
     * @return
     */
    public String buildPromptByPlatform(String promptKey, Map<String, String> params) {
        return buildPromptByPlatform(promptKey, params, SiteLangHolder.get());
    }


    /**
     * 通过prompt管理平台构建prompt
     *
     * @param promptKey 管理平台的prompt key
     * @param params    prompt中的变量参数
     * @return
     */
    public String buildPromptByPlatform(String promptKey, Map<String, String> params, String lang) {
        String prompt = analyticsCommonService.getPrompt(promptKey, PromptPlatformEnum.CLAUDE, lang);
        if (StringUtils.isBlank(prompt)) {
            log.warn("Prompt not found for key: {}", promptKey);
            return null;
        }
        if (MapUtils.isEmpty(params)) {
            return prompt;
        }
        for (Map.Entry<String, String> entry : params.entrySet()) {
            prompt = prompt.replace("{" + entry.getKey() + "}", entry.getValue());
        }
        log.info("prompt: {}", prompt);
        return prompt;
    }

    /**
     * 调用GPT模型, 同时记录调用次数
     *
     * @param modelCompletion 请求体
     * @param scenario       AI功能名称
     * @return
     */
    private OpenAICompletionResponseDTO callGptWithLimit(ModelCompletionDTO modelCompletion,
            ScenarioEnum scenario) {
        return callGpt(modelCompletion, scenario);
    }

    private OpenAICompletionResponseDTO callGptWithLimitAndTimeout(ModelCompletionDTO modelCompletion, int readTimeout,
            ScenarioEnum scenario) {
        return callGptWithTimeout(modelCompletion, scenario, readTimeout);
    }

    /**
     * 调用GPT模型
     *
     * @param modelCompletion 请求体
     * @param scenario       AI功能名称
     * @return
     */
    private OpenAICompletionResponseDTO callGpt(ModelCompletionDTO modelCompletion, ScenarioEnum scenario) {
        HttpHeaders headers = generateHttpHeader(modelCompletion, scenario);
        addCommonHeader(headers);
        return this.restTemplate.exchange(openAiUrl + TURBO, HttpMethod.POST,
                new HttpEntity<>(modelCompletion, headers), OpenAICompletionResponseDTO.class).getBody();
    }

    /**
     * 调用GPT模型, 支持自定义请求超时时间
     *
     * @param modelCompletion 请求体
     * @param scenario       AI功能名称
     * @param readTimeout     读取超时时间
     * @return
     */
    private OpenAICompletionResponseDTO callGptWithTimeout(ModelCompletionDTO modelCompletion, ScenarioEnum scenario,
            int readTimeout) {
        HttpHeaders headers = generateHttpHeader(modelCompletion, scenario);
        addCommonHeader(headers);
        RestTemplate template = templateWithCustomTimeout(readTimeout);
        return template.exchange(openAiUrl + TURBO, HttpMethod.POST, new HttpEntity<>(modelCompletion, headers),
                OpenAICompletionResponseDTO.class).getBody();
    }

    /**
     * 使用自定义超时时间的RestTemplate
     *
     * @param readTimeout 读取超时时间
     * @return
     */
    private RestTemplate templateWithCustomTimeout(int readTimeout) {
        HttpComponentsClientHttpRequestFactory clientHttpRequestFactory = new HttpComponentsClientHttpRequestFactory(
                httpClient);
        clientHttpRequestFactory.setConnectTimeout(3000);
        clientHttpRequestFactory.setReadTimeout(readTimeout);

        RestTemplate customRestTemplate = new RestTemplate(
                new BufferingClientHttpRequestFactory(clientHttpRequestFactory));
        customRestTemplate.setInterceptors(restTemplate.getInterceptors());
        customRestTemplate.setMessageConverters(restTemplate.getMessageConverters());
        customRestTemplate.setErrorHandler(restTemplate.getErrorHandler());
        customRestTemplate.setUriTemplateHandler(restTemplate.getUriTemplateHandler());
        return customRestTemplate;
    }

    /**
     * 添加公共请求头
     *
     * @param headers 请求头信息
     */
    private void addCommonHeader(HttpHeaders headers) {
        headers.add(UserIdHolder.X_USER_ID, UserIdHolder.get());
        headers.add(TenantIdHolder.X_TENANT_ID, TenantIdHolder.get().orElse(null));
        headers.add(RequestFromHolder.X_PATSNAP_FROM, RequestFromHolder.get());
        headers.add(CorrelationIdHolder.X_CORRELATION_ID, CorrelationIdHolder.get());
    }


    /**
     * 调用GPT模型
     *
     * @param model         GPT模型
     * @param prompt        提示词
     * @param scenario      AI功能名称
     * @param typeReference 返回类型
     * @param <T>
     * @return
     */
    @LangfuseTracing(
            operationName = "openai_chat_completion",
            system = "openai",
            operationType = "chat_completion",
            promptParameter = "prompt",
            modelParameter = "model",
            tags = {"gpt", "chat_completion"}
    )
    public <T> T callGptByPrompt(GPTModelEnum model, String prompt, ScenarioEnum scenario,
            TypeReference<T> typeReference) {
        ModelCompletionDTO modelCompletion = buildModelCompletionDTO(model, prompt);
        OpenAICompletionResponseDTO result = callGpt(modelCompletion, scenario);
        return parseResponse(result, typeReference);
    }

    public <T> T callGptByPrompt(GPTModelEnum model, String prompt, ScenarioEnum scenario,
            Class<T> clazz) {
        return callGptByPrompt(model, prompt, scenario, new TypeReference<>() {
            @Override
            public Type getType() {
                return clazz;
            }
        });
    }
    
    @Retryable(
            value = {BizException.class},
            maxAttempts = 2,
            backoff = @Backoff(delay = 2000)
    )
    @LangfuseTracing(
            operationName = "callGptByPrompt",
            system = "patsnap_algorithm",
            operationType = "algorithm_processing",
            tags = {"algorithm", "ai_processing"}
    )
    public <T> T callGptByPrompt(ModelCompletionDTO modelCompletion, ScenarioEnum scenario,
            Class<T> clazz) {
        OpenAICompletionResponseDTO result = callGptWithTimeout(modelCompletion, scenario, 500000);
        if (result != null && result.getErrorCode() != 0) {
            log.warn("GPT call failed with error code: {}, message: {}", result.getErrorCode(), result.getMsg());
            throw new BizException(ContentErrorCodeEnum.SYSTEM_BUSY);
        }
        return parseResponse(result, new TypeReference<>() {
            @Override
            public Type getType() {
                return clazz;
            }
        });
    }

    /**
     * 解析GPT返回结果, 转化成目标对象
     *
     * @param result        GPT返回结果
     * @param typeReference 目标对象类型
     * @param <T>
     * @return
     */
    @SuppressWarnings("unchecked")
    private <T> T parseResponse(OpenAICompletionResponseDTO result, TypeReference<T> typeReference) {
        try {
            String gptRes = Optional.ofNullable(result).map(OpenAICompletionResponseDTO::getData)
                    .map(OpenAICompletionResponseDTO.CompletionData::getMessage)
                    .orElse(null);

            if (gptRes == null) {
                return null;
            }

            // 如果需要的是 String 类型，直接返回
            if (typeReference.getType().equals(String.class)) {
                return (T) gptRes;
            }

            // 否则，尝试解析 JSON
            return parseJson(typeReference, gptRes);
        } catch (Exception e) {
            log.warn("response from llm is not json", e);
            return null;
        }
    }

    /**
     * 构建GPT请求对象
     *
     * @param model   GPT模型枚举
     * @param message 提示语
     * @return
     */
    private ModelCompletionDTO buildModelCompletionDTO(GPTModelEnum model, String message) {
        ModelCompletionDTO modelCompletion = new ModelCompletionDTO();
        modelCompletion.setMaxTokens(model.getLimit());
        modelCompletion.setModel(model.getName());
        modelCompletion.setMessage(message);
        if (!"patsnap".equals(GPTModelEnum.getGptEngine(model))) {
            modelCompletion.setTemperature(temperature);
            modelCompletion.setTopP(0.9);
            modelCompletion.setN(1);
        }

        return modelCompletion;
    }


}

package com.patsnap.drafting.client.model;

import java.util.List;

public class AiSearchResponse {
    private int errorCode;
    private String errorMessage;
    private Data data;

    public static class Data {
        private List<Feature> features;
        private int threshold;

        public static class Feature {
            private String techFeature;
            private List<String> keywords;
            private int score;

            // Getters and Setters
            public String getTechFeature() {
                return techFeature;
            }

            public void setTechFeature(String techFeature) {
                this.techFeature = techFeature;
            }

            public List<String> getKeywords() {
                return keywords;
            }

            public void setKeywords(List<String> keywords) {
                this.keywords = keywords;
            }

            public int getScore() {
                return score;
            }

            public void setScore(int score) {
                this.score = score;
            }
        }

        // Getters and Setters
        public List<Feature> getFeatures() {
            return features;
        }

        public void setFeatures(List<Feature> features) {
            this.features = features;
        }

        public int getThreshold() {
            return threshold;
        }

        public void setThreshold(int threshold) {
            this.threshold = threshold;
        }
    }

    // Get<PERSON> and Setters
    public int getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(int errorCode) {
        this.errorCode = errorCode;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public Data getData() {
        return data;
    }

    public void setData(Data data) {
        this.data = data;
    }
}
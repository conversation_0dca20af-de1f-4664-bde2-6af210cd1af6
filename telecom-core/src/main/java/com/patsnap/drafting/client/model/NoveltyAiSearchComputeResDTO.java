package com.patsnap.drafting.client.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class NoveltyAiSearchComputeResDTO {

    private Data data;

    @lombok.Data
    @Accessors(chain = true)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Data {
        @JsonProperty("summary_feature")
        private String summaryFeature;
        private String reports;
        private List<PatentReport> paras;

        @lombok.Data
        public static class PatentReport {
            private String patentId;
            @JsonProperty("related_para")
            private String relatedPara;
        }
    }
} 
package com.patsnap.drafting.client.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.experimental.Accessors;
import java.util.List;

@Accessors(chain = true)
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class NoveltySearchRetrievalElementResDTO {

    private List<FeatureSearchEntity> data;

    @Accessors(chain = true)
    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class FeatureSearchEntity {

        /**
         * 原始特征文本
         */
        private String featureTextOriginal;

        /**
         * 特征类型
         */
        private String featureType;

        /**
         * 表达式列表
         */
        private List<Express> express;

        /**
         * 排名
         */
        private String rank;

        /**
         * 排名原因
         */
        private String rankRea;

        /**
         * 表达式实体类
         */
        @Accessors(chain = true)
        @JsonIgnoreProperties(ignoreUnknown = true)
        @Data
        public static class Express {
            /**
             * 关键词
             */
            private Word word;

            /**
             * 扩展词列表
             */
            private List<Extend> extend;

            /**
             * IPC分类号列表
             */
            private List<Ipc> ipc;
        }

        /**
         * 关键词实体类
         */
        @Data
        public static class Word {
            /**
             * 关键词
             */
            private String key;
        }

        /**
         * 扩展词实体类
         */
        @Data
        public static class Extend {
            /**
             * 扩展词
             */
            private String key;
        }

        /**
         * IPC分类号实体类
         */
        @Data
        public static class Ipc {
            /**
             * IPC分类号
             */
            private String key;
        }
    }
}
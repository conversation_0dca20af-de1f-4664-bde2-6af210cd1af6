package com.patsnap.drafting.client;


import com.patsnap.drafting.model.storage.SignatureResponse;
import com.patsnap.drafting.model.storage.SignedUrlResponse;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import com.google.common.collect.ImmutableMap;

/**
 * @author: zhaoxinsheng
 * @date: 5/16/2019
 */
@Service
public class StorageClient {

    public static final String HTTP_GET = "GET";
    public static final String HTTP_PUT = "PUT";
    private static final Logger LOGGER = LoggerFactory.getLogger(StorageClient.class);
    @Value("${configs.com.patsnap.analytics.storage.use-s3:false}")
    private boolean useS3 = false;

    @Value("${configs.com.patsnap.analytics.region}")
    private String region;

    @Value("${configs.com.patsnap.analytics.storage.bucket-domain}")
    private String bucketDomain;

    @Value("${configs.com.patsnap.signature.url}")
    private String signatureApi;

    @Autowired
    private RestTemplate restTemplate;

    public SignedUrlResponse createSignedUrl(String method, List<String> objectKeys, int expire, boolean https) {
        return createSignedUrl(useS3, method, bucketDomain, objectKeys, expire, https);
    }

    public SignedUrlResponse createCosSignedUrl(boolean bucket, String method, String bucketDomain, List<String> objectKeys, int expire, boolean https) {
        Map<String, Object> request = new HashMap<>();
        request.put("https", https);
        request.put(bucket ? "bucket" : "domain", bucketDomain);
        request.put("object_keys", objectKeys);
        request.put("expire", expire);
        request.put("http_method", method);
        request.put("s3_provider", "tencent");
//        request.put("region", "ap-beijing");
        return signature(bucket, request);
    }

    public SignedUrlResponse createSignedUrl(boolean bucket, String method, String bucketDomain, List<String> objectKeys, int expire, boolean https) {
        Map<String, Object> request = new HashMap<>();
        request.put("https", https);
        request.put(bucket ? "bucket" : "domain", bucketDomain);
        request.put("object_keys", objectKeys);
        request.put("expire", expire);
        request.put("http_method", method);
//        request.put("s3_provider", "tencent");
//        request.put("region", "ap-beijing");
        return signature(bucket, request);
    }

    public SignedUrlResponse createSignedUrl(String method, String objectKey, int expire, boolean https) {
        return createSignedUrl(method, objectKey, expire, https, false, null);
    }

    public SignedUrlResponse createSignedUrl(String method, String objectKey, int expire, boolean https, boolean download, String fileName) {
        return createSignedUrl(useS3, method, bucketDomain, objectKey, expire, https, download, fileName);
    }

    public SignedUrlResponse createSignedUrl(boolean bucket, String method, String bucketDomain, String objectKey, int expire, boolean isHttps, boolean download, String fileName) {
        Map<String, Object> request = new HashMap<>();
        request.put("https", isHttps);
        request.put(bucket ? "bucket" : "domain", bucketDomain);
        request.put("object_keys", Collections.singletonList(objectKey));
        request.put("expire", expire);
        request.put("http_method", method);
        if (download) {
            request.put("download", true);
            request.put("override_params", generateOverrideParams(objectKey, fileName));
        }
        return signature(bucket, request);
    }

    private SignedUrlResponse signature(boolean bucket, Map<String, Object> request) {
        SignatureResponse response;
        String url = signatureApi + (bucket ? "/sign/s3" : "/sign/cdn");
        try {
            response = restTemplate.postForObject(url, request, SignatureResponse.class);
        } catch (Exception e) {
            String bucketDomain = (String) request.get(bucket ? "bucket" : "domain");
            LOGGER.error("Sign service encountered error with domain : {}, objectKey : {}", bucketDomain, request.get("object_keys"), e);
            return new SignedUrlResponse(region, bucketDomain, SignedUrlResponse.INTERNAL_ERROR_CODE);
        }
        return new SignedUrlResponse(region, response.getResource(), response.getSignedUrls());
    }

    private Map<String, Object> generateOverrideParams(String objectKey, String fileName) {
        if (StringUtils.isBlank(fileName)) {
            return ImmutableMap.of();
        }

        Map<String, Object> subParams = new HashMap<>();
        subParams.put("file_name", fileName);

        Map<String, Object> overrideParams = new HashMap<>();
        overrideParams.put(objectKey, subParams);
        return overrideParams;
    }
}

package com.patsnap.drafting.client.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Accessors(chain = true)
@Data
public class FtoSearchRequestDTO {
    
    private String tag;
    
    private String text;
    
    private String model;

    // 技术特征对比及得分
    @JsonProperty("feature_score")
    private Object featureScore;
    // private List<AiSearchAgentFeatureScore> featureScore;

    // 专利ID或者专利PN号
    @JsonProperty("patent_ids")
    private List<String> patentIds;

    // 是否支持专利PN号
    @JsonProperty("support_pn")
    private Boolean supportPn;

    @JsonProperty("FTO")
    private Boolean fto = false;

}
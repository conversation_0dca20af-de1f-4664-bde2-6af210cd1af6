package com.patsnap.drafting.client.api;

import com.patsnap.drafting.client.model.ComputeCommonResDTO;
import com.patsnap.drafting.client.model.DrawingNarratorRequest;
import com.patsnap.drafting.client.model.DrawingNarratorResponse;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.POST;

import java.util.Map;

/**
 * 附图说明服务 API 接口定义
 * 
 * <AUTHOR> Assistant
 */
public interface DrawNarratorApi {
    
    /**
     * 附图说明生成接口
     * 提交附图+权要+标签，返回附图详细说明的列表
     * 
     * @param request 附图说明请求
     * @return 附图说明响应
     */
    @POST("drawing_narrator/")
    Call<ComputeCommonResDTO<DrawingNarratorResponse>> generateDrawingNarrator(@Body Map<String, DrawingNarratorRequest> request);
} 
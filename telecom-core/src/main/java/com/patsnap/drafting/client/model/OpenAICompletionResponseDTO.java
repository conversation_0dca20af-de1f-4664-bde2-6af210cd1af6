package com.patsnap.drafting.client.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * openai gpt Completion调用返回实体
 * <AUTHOR>
 * @date 2024/4/12
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class OpenAICompletionResponseDTO {

    private CompletionData data;
    
    @JsonProperty("error_code")
    private int errorCode;
    
    private String msg;

    @Data
    public static class CompletionData {

        private String message;
    }
}

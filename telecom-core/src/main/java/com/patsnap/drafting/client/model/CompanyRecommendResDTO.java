package com.patsnap.drafting.client.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 公司推荐响应DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class CompanyRecommendResDTO {

    /**
     * 推荐公司列表
     */
    @ApiModelProperty("推荐公司列表")
    @JsonProperty("suggest")
    private List<CompanyInfo> suggest;

    /**
     * 找到的记录数
     */
    @ApiModelProperty("找到的记录数")
    @JsonProperty("numFound")
    private Integer numFound;

    /**
     * 公司信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class CompanyInfo {
        /**
         * 公司ID
         */
        @ApiModelProperty("公司ID")
        @JsonProperty("company_id")
        private String companyId;

        /**
         * 公司名称
         */
        @ApiModelProperty("公司名称")
        @JsonProperty("company_name")
        private String companyName;

        /**
         * 公司中文名称
         */
        @ApiModelProperty("公司中文名称")
        @JsonProperty("cname_cn")
        private String cnameCn;

        /**
         * 公司标准中文名称
         */
        @ApiModelProperty("公司标准中文名称")
        @JsonProperty("nname_cn")
        private String nnameCn;

        /**
         * 计数
         */
        @ApiModelProperty("计数")
        @JsonProperty("n")
        private Integer n;
    }
} 
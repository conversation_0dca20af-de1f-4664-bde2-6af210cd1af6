package com.patsnap.drafting.client;

import com.patsnap.drafting.client.api.DrawNarratorApi;
import com.patsnap.drafting.client.model.ComputeCommonResDTO;
import com.patsnap.drafting.client.model.DrawingNarratorRequest;
import com.patsnap.drafting.client.model.DrawingNarratorResponse;
import com.patsnap.drafting.exception.BizException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import retrofit2.Response;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.patsnap.drafting.exception.errorcode.ClientErrorCodeEnum.CLIENT_NOT_AVAILABLE;

/**
 * 附图说明算法服务客户端
 * 负责调用外部附图说明生成算法接口
 * 
 * <AUTHOR> Assistant
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DrawNarratorClient {

    private final DrawNarratorApi drawNarratorApi;

    /**
     * 生成附图说明
     * 调用外部算法接口，根据图片、权利要求和标签信息生成附图说明文本
     * 
     * @param request 附图说明请求参数，包含图片base64数据、权利要求文本和标签列表
     * @return 附图说明响应结果
     * @throws BizException 当接口调用失败或返回异常时抛出
     */
    public DrawingNarratorResponse generateDrawingNarrator(DrawingNarratorRequest request) {
        // 记录请求参数信息
        logRequestInfo(request);
        
        try {
            // 调用算法接口
            Response<ComputeCommonResDTO<DrawingNarratorResponse>> response = 
                    drawNarratorApi.generateDrawingNarrator(Map.of("data", request)).execute();
            
            // 处理响应结果
            return handleResponse(response, request);
            
        } catch (IOException e) {
            log.error("附图说明算法调用网络异常", e);
            throw new BizException(CLIENT_NOT_AVAILABLE);
        }
    }
    
    /**
     * 记录请求参数信息
     * 
     * @param request 请求参数
     */
    private void logRequestInfo(DrawingNarratorRequest request) {
        int imageCount = Optional.ofNullable(request.getImages())
                .map(List::size)
                .orElse(0);
        
        int claimTextLength = Optional.ofNullable(request.getClaimText())
                .map(String::length)
                .orElse(0);
        
        int itemListSize = Optional.ofNullable(request.getItemList())
                .map(List::size)
                .orElse(0);
        
        log.info("开始调用附图说明算法，请求参数统计: 图片数量={}, 权利要求长度={}, 标签数量={}", 
                imageCount, claimTextLength, itemListSize);
    }
    
    /**
     * 处理算法接口响应
     * 
     * @param response HTTP响应
     * @param request 原始请求参数（用于异常日志）
     * @return 附图说明响应结果
     * @throws BizException 当响应异常时抛出
     */
    private DrawingNarratorResponse handleResponse(Response<ComputeCommonResDTO<DrawingNarratorResponse>> response, 
                                                   DrawingNarratorRequest request) {
        if (!response.isSuccessful() || response.body() == null) {
            log.warn("附图说明算法调用失败，HTTP状态码: {}, 错误信息: {}", response.code(), response.message());
            throw new BizException(CLIENT_NOT_AVAILABLE);
        }
        
        ComputeCommonResDTO<DrawingNarratorResponse> result = response.body();
        DrawingNarratorResponse data = result.getData();
        
        // 记录成功响应信息
        String resultPreview = Optional.ofNullable(data)
                .map(DrawingNarratorResponse::getResult)
                .map(resultStr -> resultStr.length() > 100 ? resultStr.substring(0, 100) + "..." : resultStr)
                .orElse("无返回结果");
        
        log.info("附图说明算法调用成功，返回结果预览: {}", resultPreview);
        
        return data;
    }
}
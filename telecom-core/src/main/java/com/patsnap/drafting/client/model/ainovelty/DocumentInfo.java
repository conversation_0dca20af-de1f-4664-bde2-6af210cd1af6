package com.patsnap.drafting.client.model.ainovelty;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 文档信息
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class DocumentInfo {

    // 专利ID
    @JsonProperty("patent_id")
    private String patentId;

    // 特征匹配数量
    @JsonProperty("feature_match_num")
    private Integer featureMatchNum;

    // 公开分数
    @JsonProperty("public_score")
    private float publicScore;

    // 得分
    @JsonProperty("score")
    private Integer score;

    //
    @JsonProperty("prev_hit")
    private Boolean prevHit = false;
} 
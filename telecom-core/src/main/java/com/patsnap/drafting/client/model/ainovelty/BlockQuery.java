package com.patsnap.drafting.client.model.ainovelty;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import java.util.List;

/**
 * 块查询信息
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class BlockQuery {

    // 查询类型
    @JsonProperty("type")
    private String type;

    // 原始特征文本
    @JsonProperty("feature_text_original")
    private String featureTextOriginal;

    // 最终检索式
    @JsonProperty("query")
    private String query;

    // 数量
    @JsonProperty("count")
    private Integer count;

    // 构建状态
    @JsonProperty("status")
    private String status;

    // 别名查询
    @JsonProperty("alias_query")
    private String aliasQuery;

    // 使用的查询编号
    @JsonProperty("using_query_no")
    private List<String> usingQueryNo;

    // 构建过程
    @JsonProperty("process")
    private List<BlockQueryProcess> process;
} 
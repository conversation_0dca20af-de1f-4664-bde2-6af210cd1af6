package com.patsnap.drafting.client;

import com.alibaba.fastjson.JSON;
import com.patsnap.drafting.client.api.AiNoveltyComputeApi;
import com.patsnap.drafting.client.api.AiNoveltyComputeV2Api;
import com.patsnap.drafting.client.model.*;
import com.patsnap.drafting.exception.BizException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import retrofit2.Response;

import java.io.IOException;
import java.util.List;
import java.util.Map;

import static com.patsnap.drafting.exception.errorcode.ClientErrorCodeEnum.CLIENT_NOT_AVAILABLE;

@Slf4j
@Component
@RequiredArgsConstructor
public class AiNoveltySearchComputeClient {

    private final AiNoveltyComputeApi aiNoveltyComputeApi;

    private final AiNoveltyComputeV2Api aiNoveltyComputeV2Api;

    /**
     * AI 查新算法接口
     */
    public NoveltySearchResponseDTO.Data noveltySearch(NoveltySearchRequestDTO request) {
        try {
            request.setModel("gpt-4.1");
            Response<NoveltySearchResponseDTO> response = aiNoveltyComputeApi.aiNoveltySearch(Map.of("data", request)).execute();
            if (response.isSuccessful() && response.body() != null && response.body().getData() != null) {
                return response.body().getData();
            } else {
                log.warn("noveltySearch failed, code: {}, message: {}", response.code(), response.message());
                throw new BizException(CLIENT_NOT_AVAILABLE);
            }
        } catch (IOException e) {
            log.error("Failed to noveltySearch, request: {}", JSON.toJSONString(request), e);
            throw new BizException(CLIENT_NOT_AVAILABLE);
        }
    }

    /**
     * AI 查新文本总结接口
     */
    public NoveltySearchSummaryResDTO aiNoveltySearchSummary(NoveltySearchRequestDTO request) {
        try {
            request.setModel("gpt-4.1");
            Response<NoveltySearchSummaryResDTO> response = aiNoveltyComputeV2Api.aiNoveltySearchSummary(Map.of("data", request)).execute();
            if (response.isSuccessful() && response.body() != null && response.body().getData() != null) {
                return response.body();
            } else {
                log.warn("noveltySearch failed, code: {}, message: {}", response.code(), response.message());
                throw new BizException(CLIENT_NOT_AVAILABLE);
            }
        } catch (IOException e) {
            log.error("Failed to noveltySearch, request: {}", JSON.toJSONString(request), e);
            throw new BizException(CLIENT_NOT_AVAILABLE);
        }
    }

    /**
     * AI 特征提取接口
     */
    public NoveltySearchFeatureExtractResDTO aiNoveltySearchFeatureExtract(NoveltySearchRequestDTO request) {
        try {
            request.setModel("gpt-4.1");
            Response<NoveltySearchFeatureExtractResDTO> response = aiNoveltyComputeV2Api.aiNoveltySearchFeatureExtract(Map.of("data", request)).execute();
            if (response.isSuccessful() && response.body() != null && response.body() != null) {
                return response.body();
            } else {
                log.warn("noveltySearch failed, code: {}, message: {}", response.code(), response.message());
                throw new BizException(CLIENT_NOT_AVAILABLE);
            }
        } catch (IOException e) {
            log.error("Failed to noveltySearch, request: {}", JSON.toJSONString(request), e);
            throw new BizException(CLIENT_NOT_AVAILABLE);
        }
    }

    /**
     * AI 检索要素提取接口
     */
    public NoveltySearchRetrievalElementResDTO aiNoveltySearchRetrievalElements(NoveltySearchRequestDTO request) {
        try {
            request.setModel("gpt-4.1");
            Response<NoveltySearchRetrievalElementResDTO> response = aiNoveltyComputeV2Api.aiNoveltySearchRetrievalElements(Map.of("data", request)).execute();
            if (response.isSuccessful() && response.body() != null && response.body() != null) {
                return response.body();
            } else {
                log.warn("noveltySearch failed, code: {}, message: {}", response.code(), response.message());
                throw new BizException(CLIENT_NOT_AVAILABLE);
            }
        } catch (IOException e) {
            log.error("Failed to noveltySearch, request: {}", JSON.toJSONString(request), e);
            throw new BizException(CLIENT_NOT_AVAILABLE);
        }
    }

    /**
     * AI 检索要素扩词接口
     */
    public NoveltySearchRetrievalAddElementResDTO aiNoveltySearchRetrievalAddElements(NoveltySearchRequestDTO request) {
        try {
            request.setModel("gpt-4.1");
            Response<NoveltySearchRetrievalAddElementResDTO> response = aiNoveltyComputeV2Api.aiNoveltySearchRetrievalAddElements(Map.of("data", request)).execute();
            if (response.isSuccessful() && response.body() != null && response.body() != null) {
                return response.body();
            } else {
                log.warn("noveltySearch failed, code: {}, message: {}", response.code(), response.message());
                throw new BizException(CLIENT_NOT_AVAILABLE);
            }
        } catch (IOException e) {
            log.error("Failed to noveltySearch, request: {}", JSON.toJSONString(request), e);
            throw new BizException(CLIENT_NOT_AVAILABLE);
        }
    }

    /**
     * AI 查新算法接口
     */
    public NoveltySearchPointExtractResponseDTO noveltyPointExtractSearch(NoveltySearchRequestDTO request) {
        try {
            request.setModel("gpt-4.1");
            Response<NoveltySearchPointExtractResponseDTO> response =
                    aiNoveltyComputeApi.aiNoveltyPointExtractSearch(Map.of("data", request)).execute();
            if (response.isSuccessful() && response.body() != null && response.body().getData() != null) {
                return response.body();
            } else {
                log.warn("noveltySearch failed, code: {}, message: {}", response.code(), response.message());
                throw new BizException(CLIENT_NOT_AVAILABLE);
            }
        } catch (IOException e) {
            log.error("Failed to noveltySearch, request: {}", JSON.toJSONString(request), e);
            throw new BizException(CLIENT_NOT_AVAILABLE);
        }
    }

    /**
     * AI 获取输入专利的特征对比
     */
    public NoveltySearchResponseDTO.Data aiNoveltySearchPatentFeatureComparison(NoveltySearchRequestDTO request) {
        try {
            request.setModel("gpt-4.1");
            Response<NoveltySearchResponseDTO> response = aiNoveltyComputeV2Api.aiNoveltySearchPatentFeatureComparison(Map.of("data", request)).execute();
            if (response.isSuccessful() && response.body() != null && response.body().getData() != null) {
                return response.body().getData();
            } else {
                log.warn("noveltySearch failed, code: {}, message: {}", response.code(), response.message());
                throw new BizException(CLIENT_NOT_AVAILABLE);
            }
        } catch (IOException e) {
            log.error("Failed to noveltySearch, request: {}", JSON.toJSONString(request), e);
            throw new BizException(CLIENT_NOT_AVAILABLE);
        }
    }

    /**
     * 生成报告：对比文献公开情况/新颖性评述
     * @param request 查新计算请求体
     * @return 计算结果
     */
    public NoveltyAiSearchComputeResDTO noveltyAiSearchCompute(
            NoveltyAiSearchComputeReqDTO request) {
        try {
            Response<NoveltyAiSearchComputeResDTO> response = aiNoveltyComputeV2Api.noveltyAiSearchCompute(request).execute();
            if (response.isSuccessful()) {
                return response.body();
            } else {
                log.warn("novelty_search_compute failed, code: {}, message: {}", response.code(), response.message());
                throw new BizException(CLIENT_NOT_AVAILABLE);
            }
        } catch (IOException e) {
            log.error("Failed to novelty_search_compute, request: {}", JSON.toJSONString(request), e);
            throw new BizException(CLIENT_NOT_AVAILABLE);
        }
    }
}

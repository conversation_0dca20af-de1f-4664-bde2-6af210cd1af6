package com.patsnap.drafting.client.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/4/22
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MessageDTO {

    private String role;
    private Object content;

    public static MessageDTO of(Object content) {
        return new MessageDTO(Role.USER.getValue(), content);
    }

    public static MessageDTO ofSystem(String content) {
        return new MessageDTO(Role.SYSTEM.getValue(), content);
    }

    @Getter
    public static enum Role {
        SYSTEM("system"),
        USER("user"),
        ASSISTANT("assistant");

        private final String value;

        Role(String value) {
            this.value = value;
        }
    }

    @Getter
    public static enum ContentType {
        IMAGE("image"),
        TEXT("text");

        private final String value;

        ContentType(String value) {
            this.value = value;
        }
    }
}

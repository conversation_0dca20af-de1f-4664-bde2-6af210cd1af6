package com.patsnap.drafting.client.model.ainovelty;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import java.util.List;

/**
 * 特征关键词
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class FeatureKeyword {

    // 特征
    @JsonProperty("feature")
    private String feature;

    // 关键词列表
    @JsonProperty("keywords")
    private List<String> keywords;

    // 权重
    @JsonProperty("weight")
    private Float weight;
} 
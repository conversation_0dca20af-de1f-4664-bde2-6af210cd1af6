package com.patsnap.drafting.client.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.patsnap.drafting.request.ainoveltysearch.AiSearchAgentFeature;
import com.patsnap.drafting.response.ainoveltysearch.FeatureComparisonFinalResItem;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Accessors(chain = true)
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class NoveltySearchResponseDTO {
    
    private Data data;
    
    @lombok.Data
    @Accessors(chain = true)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Data {
        
        @ApiModelProperty("技术特征")
        private List<AiSearchAgentFeature> features;
        
        @ApiModelProperty("阈值")
        private Integer threshold;
        
        @ApiModelProperty("标题")
        private String title;
        
        @ApiModelProperty("解决方案（文本总结）")
        private String solution;

        @ApiModelProperty("技术特征对比的最终结果")
        @JsonProperty("final_res")
        private List<FeatureComparisonFinalResItem> finalRes;
    }
}
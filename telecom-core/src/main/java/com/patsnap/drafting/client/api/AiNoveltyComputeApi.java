package com.patsnap.drafting.client.api;

import com.patsnap.drafting.client.model.*;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.POST;

import java.util.Map;

public interface AiNoveltyComputeApi {
    
    @POST("novelty_ai_search/")
    Call<NoveltySearchResponseDTO> aiNoveltySearch(@Body Map<String, NoveltySearchRequestDTO> request);

    @POST("novelty_ai_search/")
    Call<NoveltySearchPointExtractResponseDTO> aiNoveltyPointExtractSearch(@Body Map<String, NoveltySearchRequestDTO> request);

    @POST("novelty_ai_search/")
    Call<NoveltyAiSearchComputeResDTO> noveltyAiSearchCompute(@Body NoveltyAiSearchComputeReqDTO request);
}
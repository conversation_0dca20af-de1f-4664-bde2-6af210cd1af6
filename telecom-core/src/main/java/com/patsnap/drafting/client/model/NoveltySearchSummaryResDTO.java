package com.patsnap.drafting.client.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Accessors(chain = true)
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class NoveltySearchSummaryResDTO {
    
    private Data data;
    
    @lombok.Data
    @Accessors(chain = true)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Data {
        
        @JsonProperty("tech_problem")
        private String techProblem;

        @JsonProperty("tech_subject")
        private String techSubject;

        @JsonProperty("tech_solution")
        private String techSolution;

        @JsonProperty("tech_efficacy")
        private String techEfficacy;

        @JsonProperty("tech_feature_table")
        private List<TableData> techFeatureTable;
    }

    @lombok.Data
    @Accessors(chain = true)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class TableData {

        @JsonProperty("tech_feature")
        private String techFeature;

        @JsonProperty("tech_effect")
        private String techEffect;

        @JsonProperty("tech_feature_range")
        private String techFeatureRange;

        @JsonProperty("judge_public")
        private String judgePublic;

        @JsonProperty("judge_rea")
        private String judgeRea;
    }
}
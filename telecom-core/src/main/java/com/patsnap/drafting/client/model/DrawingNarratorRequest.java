package com.patsnap.drafting.client.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.patsnap.drafting.model.aispecification.TermItem;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 附图说明算法接口请求模型
 * 
 * <AUTHOR> Assistant
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DrawingNarratorRequest {

    /**
     * 附图列表，列表元素是base64编码的图片
     * 附图mime_type须为image/png
     */
    @JsonProperty("images")
    private List<String> images;

    /**
     * 权利要求文本
     */
    @JsonProperty("claim_text")
    private String claimText;

    /**
     * 标签列表
     * 示例：[{"number": "1", "item": "语言信息"}, {"number": "2", "item": "语言修正信息"}]
     */
    @JsonProperty("item_list")
    private List<TermItem> itemList;

    /**
     * 语言
     */
    @JsonProperty("lang")
    private String lang;
} 
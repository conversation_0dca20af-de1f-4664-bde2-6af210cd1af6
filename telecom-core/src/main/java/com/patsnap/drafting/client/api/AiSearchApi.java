package com.patsnap.drafting.client.api;

import com.patsnap.drafting.client.model.AiFtoSearchAgentRequest;
import com.patsnap.drafting.client.model.AiSearchAgentRequest;
import com.patsnap.drafting.client.model.AiSearchAgentResponse;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.POST;
import retrofit2.http.Query;

public interface AiSearchApi {
    
    @POST("api/novelty_search_agent/")
    Call<AiSearchAgentResponse> noveltyAiSearchAgent(@Body AiSearchAgentRequest request);

    @GET("api/novelty_search_result/")
    Call<AiSearchAgentResponse> noveltyAiSearchResult(@Query(value="task_id") String taskId, @Query(value="is_finished") Boolean isFinished);

    @POST("api/fto_search_agent/")
    Call<AiSearchAgentResponse> ftoAiSearchAgent(@Body AiFtoSearchAgentRequest request);
}
package com.patsnap.drafting.client.model;

import com.patsnap.drafting.request.aispecification.DisclosureEmbodimentItem;
import lombok.Data;

import java.util.List;

@Data
public class AiSpecificationComputeData {

    private String patentType;

    private String claimText;

    private String disclosureText;

    private String disclosureProblem;

    private String disclosureEfficacy;

    private String disclosureSolution;

    private String firstIndependClaim;

    private List<ComputeClaimFeature> claimFeatures;

    private List<String> predictIpcs;

    private List<String> predictIpcDescriptions;

    private List<String> predictCpcs;

    private List<String> predictCpcDescriptions;

    private String Jurisdiction;
    
    /** 受理局 */
    private String patentOffice;

    //生成摘要时候需要
    private String techField;

    //生成摘要时候需要
    private String summary;

    // 交底书抽取的实施例
    private List<DisclosureEmbodimentItem> disclosureEmbodiments;

    // 附图算法需要
    private String drawingNarrative;

    // 生成单个实施例需要：大纲内容，大纲编号，参考实施例文本
    private String outline;
    private Integer embodimentNumber;
    private String referenceEmbodimentText;
}

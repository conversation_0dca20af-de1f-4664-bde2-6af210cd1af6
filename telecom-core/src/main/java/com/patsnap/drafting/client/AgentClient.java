package com.patsnap.drafting.client;

import com.alibaba.fastjson.JSON;
import com.patsnap.drafting.client.api.AgentApi;
import com.patsnap.drafting.client.model.AgentToolDataRequestDTO;
import com.patsnap.drafting.exception.BizException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import retrofit2.Response;

import java.io.IOException;
import java.util.Map;

import static com.patsnap.drafting.exception.errorcode.ClientErrorCodeEnum.CLIENT_NOT_AVAILABLE;

@Slf4j
@Component
@RequiredArgsConstructor
public class AgentClient {

    private final AgentApi agentApi;
    
    public Map<String, Object> saveData(AgentToolDataRequestDTO request) {
        try {
            Response<Map<String, Object>> response = agentApi.saveData(request).execute();
            if (response.isSuccessful()) {
                log.info("saveData success, response: {}", JSON.toJSONString(response.body()));
                return response.body();
            } else {
                log.warn("saveData failed, code: {}, message: {}", response.code(), response.message());
                throw new BizException(CLIENT_NOT_AVAILABLE);
            }
        } catch (IOException e) {
            log.error("Failed to saveData, request: {}", JSON.toJSONString(request), e);
            throw new BizException(CLIENT_NOT_AVAILABLE);
        }
    }

    public Map<String, Object> getData(AgentToolDataRequestDTO request) {
        try {
            Response<Map<String, Object>> response = agentApi.getData(request).execute();
            if (response.isSuccessful()) {
                log.info("getData success, response: {}", JSON.toJSONString(response.body()));
                return response.body();
            } else {
                log.warn("getData failed, code: {}, message: {}", response.code(), response.message());
                throw new BizException(CLIENT_NOT_AVAILABLE);
            }
        } catch (IOException e) {
            log.error("Failed to getData, request: {}", JSON.toJSONString(request), e);
            throw new BizException(CLIENT_NOT_AVAILABLE);
        }
    }
}

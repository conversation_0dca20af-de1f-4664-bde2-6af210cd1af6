package com.patsnap.drafting.client.model.ainovelty;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import java.util.List;

/**
 * 关键词提取结果
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class KeywordExtractResult {

    // 原始特征文本
    @JsonProperty("feature_text_original")
    private String featureTextOriginal;

    // 特征类型
    @JsonProperty("feature_type")
    private String featureType;

    // 表达式
    @JsonProperty("express")
    private List<KeywordExpress> express;

    @JsonProperty("selected")
    private Boolean selected = true;
} 
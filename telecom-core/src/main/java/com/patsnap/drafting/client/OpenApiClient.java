package com.patsnap.drafting.client;

import com.alibaba.fastjson.JSON;
import com.patsnap.drafting.client.api.OpenApi;
import com.patsnap.drafting.exception.BizException;
import com.patsnap.drafting.request.imagesearch.ImageLocPredictRequest;
import com.patsnap.drafting.request.imagesearch.ImageMultipleSimilarSearchRequest;
import com.patsnap.drafting.request.imagesearch.ImageSimilarSearchRequest;
import com.patsnap.drafting.response.imagesearch.ImageLocPredictResponse;
import com.patsnap.drafting.response.imagesearch.ImageSimilarSearchResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import retrofit2.Response;

import java.io.IOException;

import static com.patsnap.drafting.exception.errorcode.ClientErrorCodeEnum.CLIENT_NOT_AVAILABLE;

@Slf4j
@Component
@RequiredArgsConstructor
public class OpenApiClient {

    private final OpenApi openApi;
    
    public ImageSimilarSearchResponse searchImageBySingle(ImageSimilarSearchRequest request) {
        try {
            Response<ImageSimilarSearchResponse> response = openApi.searchImageBySingle(request).execute();
            if (response.isSuccessful()) {
                log.info("searchImageBySingle success, response: {}", JSON.toJSONString(response.body()));
                return response.body();
            } else {
                log.warn("searchImageBySingle failed, code: {}, message: {}", response.code(), response.message());
                throw new BizException(CLIENT_NOT_AVAILABLE);
            }
        } catch (IOException e) {
            log.error("Failed to searchImageBySingle, request: {}", JSON.toJSONString(request), e);
            throw new BizException(CLIENT_NOT_AVAILABLE);
        }
    }

    public ImageSimilarSearchResponse searchImageByMultiple(ImageMultipleSimilarSearchRequest request) {
        try {
            Response<ImageSimilarSearchResponse> response = openApi.searchImageByMultiple(request).execute();
            if (response.isSuccessful()) {
                log.info("searchImageByMultiple success, response: {}", JSON.toJSONString(response.body()));
                return response.body();
            } else {
                log.warn("searchImageByMultiple failed, code: {}, message: {}", response.code(), response.message());
                throw new BizException(CLIENT_NOT_AVAILABLE);
            }
        } catch (IOException e) {
            log.error("Failed to searchImageByMultiple, request: {}", JSON.toJSONString(request), e);
            throw new BizException(CLIENT_NOT_AVAILABLE);
        }
    }

    /**
     * 调用智慧芽图像位置预测API
     *
     * @param request 图像位置预测请求
     * @return 图像位置预测响应
     */
    public ImageLocPredictResponse predictImageLoc(ImageLocPredictRequest request) {
        try {
            Response<ImageLocPredictResponse> response = openApi.predictImageLoc(request).execute();
            if (response.isSuccessful()) {
                log.info("predictImageLoc success, response: {}", JSON.toJSONString(response.body()));
                return response.body();
            } else {
                log.warn("predictImageLoc failed, code: {}, message: {}", response.code(), response.message());
                throw new BizException(CLIENT_NOT_AVAILABLE);
            }
        } catch (IOException e) {
            log.error("Failed to predictImageLoc, request: {}", JSON.toJSONString(request), e);
            throw new BizException(CLIENT_NOT_AVAILABLE);
        }
    }
}

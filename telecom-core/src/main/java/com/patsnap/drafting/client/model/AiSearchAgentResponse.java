package com.patsnap.drafting.client.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.patsnap.drafting.request.ainoveltysearch.*;
import com.patsnap.drafting.request.aitask.AiTaskReqDTO;
import lombok.Data;
import java.util.List;

/**
 * 查新结果返回信息
 * <AUTHOR> chendepeng
 * @Date 2025/3/12 13:41
 *
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class AiSearchAgentResponse extends AiTaskReqDTO {

    // 任务 id
    @JsonProperty("task_id")
    private String taskId;

    // 当前任务执行状态
    @JsonProperty("task_status")
    private String taskStatus;

    // 总共比对的专利数量
    @JsonProperty("total_comparison_patent_num")
    private int totalComparisonPatentNum;

    // 已经找到疑似相似专利数量
    @JsonProperty("find_patent_num")
    private int findPatentNum;

    // 执行的检索次数
    @JsonProperty("excute_times")
    private int excuteTimes;

    // cc 对比的阈值，超过这个阈值则是确认的特征，低于这个阈值则是待确认的特征
    @JsonProperty("cc_threshold")
    private float ccThreshold;

    // cc 对比的专利列表
    @JsonProperty("cc_pids")
    private List<String> ccPids;

    // 任务总耗时（s）
    @JsonProperty("task_cost")
    private float taskCost;

    // 结果最后更新的时间
    @JsonProperty("update_ts")
    private String updateTs;

    // 是否是异步请求，与请求中 is_async 参数一致
    @JsonProperty("is_async")
    private boolean isAsync;

    // 找到的疑似相关专利的比对信息，已经按公开度排序过。
    @JsonProperty("final_result")
    private List<AiSearchFinalResult> finalResult;

    @JsonProperty("feature_keywords")
    private List<AiSearchFeatureKeyword> featureKeywords;

    // 每次对比的结果
    @JsonProperty("process_dict")
    private AiSearchProcessDict processDict;

    // 语言
    @JsonProperty("lang")
    private String lang;

    // 请求体
    @JsonProperty("request")
    private Object request;

    @JsonProperty("debug_detail")
    // 开启 debug=true，会返回，主要用于内测分析
    private Object debugDetail;
}

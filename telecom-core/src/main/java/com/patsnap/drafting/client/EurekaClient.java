package com.patsnap.drafting.client;

import com.alibaba.fastjson.JSON;
import com.patsnap.common.exception.ForbiddenException;
import com.patsnap.common.web.entity.CommonResponse;
import com.patsnap.drafting.client.api.EurekaApi;
import com.patsnap.drafting.client.model.CompanyDetailsResDTO;
import com.patsnap.drafting.client.model.CompanyRecommendResDTO;
import com.patsnap.drafting.client.model.HistoryRequestDTO;
import com.patsnap.drafting.exception.BizException;
import com.patsnap.drafting.response.techreport.TechReportCreateFieldsResDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import retrofit2.Response;

import java.io.IOException;

import static com.patsnap.drafting.exception.errorcode.ClientErrorCodeEnum.CLIENT_NOT_AVAILABLE;

@Slf4j
@Component
@RequiredArgsConstructor
public class EurekaClient {

    private final EurekaApi eurekaApi;

    /**
     * 保存历史记录
     *
     * @param request 历史记录请求
     */
    public void saveHistory(HistoryRequestDTO request) {
        try {
            Response<Boolean> response = eurekaApi.saveHistory(request).execute();
            if (response.isSuccessful()) {
                log.info("Save history success, response: {}", JSON.toJSONString(response.body()));
            } else {
                log.warn("Save history failed, code: {}, message: {}", response.code(), response.message());
            }
        } catch (IOException e) {
            log.error("Failed to save history, request: {}", JSON.toJSONString(request), e);
        }
    }

    /**
     * 创建技术监控报告
     *
     * @param request 技术报告创建字段请求
     * @return 返回完整的响应对象，不做任何格式转化
     */
    public CommonResponse<String> createTechMonitor(TechReportCreateFieldsResDTO request) {
        try {
            Response<CommonResponse<Object>> response = eurekaApi.createTechMonitor(request).execute();
            
            if (response.isSuccessful()) {
                CommonResponse<Object> body = response.body();
                if (body.isStatus()) {
                    log.info("Create tech monitor success, response: {}", JSON.toJSONString(response.body()));
                    return CommonResponse.<String>builder().withData(String.valueOf(body.getData())).build();
                } else {
                    throw new ForbiddenException(body.getErrorCode(), body.getErrorMsg(), null, body.getErrorParams());
                }
            } else {
                log.warn("Create tech monitor failed, code: {}, message: {}", response.code(), response.message());
                throw new BizException(CLIENT_NOT_AVAILABLE);
            }
        } catch (IOException e) {
            log.error("Failed to Create tech monitor, request: {}", JSON.toJSONString(request), e);
            throw new BizException(CLIENT_NOT_AVAILABLE);
        }
    }

    /**
     * 公司推荐
     * @param companyName 公司名称
     * @return 推荐结果
     */
    public CompanyRecommendResDTO recommendCompany(String companyName) {
        try {
            Response<CommonResponse<CompanyRecommendResDTO>> response = eurekaApi.recommendCompany(companyName).execute();
            if (response.isSuccessful()) {
                log.info("推荐公司成功，公司名称: {}, 响应: {}", companyName, JSON.toJSONString(response.body()));
                return response.body().getData();
            } else {
                log.warn("推荐公司失败，公司名称: {}, 状态码: {}, 错误信息: {}", 
                        companyName, response.code(), response.message());
                return null;
            }
        } catch (Exception e) {
            log.error("调用推荐公司接口异常，公司名称: {}", companyName, e);
            return null;
        }
    }
    
    /**
     * 获取公司信息
     * @param companyId 公司ID
     * @return 公司信息结果
     */
    public Response<CommonResponse<CompanyDetailsResDTO>> getCompanyDetails(String companyId) {
        try {
            Response<CommonResponse<CompanyDetailsResDTO>> response = eurekaApi.getCompanyDetails(companyId).execute();
            if (response.isSuccessful()) {
                log.info("获取公司信息成功，公司ID: {}, 响应: {}", companyId, JSON.toJSONString(response.body()));
                return response;
            } else {
                log.warn("获取公司信息失败，公司ID: {}, 状态码: {}, 错误信息: {}", 
                        companyId, response.code(), response.message());
                return null;
            }
        } catch (Exception e) {
            log.error("调用获取公司信息接口异常，公司ID: {}", companyId, e);
            return null;
        }
    }
}

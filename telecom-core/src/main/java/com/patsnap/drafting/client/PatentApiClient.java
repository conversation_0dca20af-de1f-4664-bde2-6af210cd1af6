package com.patsnap.drafting.client;

import com.google.common.base.Preconditions;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;

import com.patsnap.common.request.UserIdHolder;
import com.patsnap.core.common.copilot.model.PatentInfoDTO;
import com.patsnap.core.common.copilot.model.PatentResponseDTO;
import com.patsnap.core.common.eureka.EurekaServiceClient;
import com.patsnap.core.common.eureka.EurekaSolutionDataRequest;
import com.patsnap.core.common.request.SiteLangHolder;
import com.patsnap.drafting.model.patent.PatentClassification;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.patsnap.core.common.Constant.Header.SITE_LANG;

@Slf4j
@Component
public class PatentApiClient {

    private static final String DETAIL = "detail";
    private static final String FROM = "Analytics";
    public static String TRANS_LANG = "trans_lang";
    public static final List<String> EN_FIELDS = Arrays.asList("AN", "ANC", "AN_ADD", "ANC_ADD");


    @Value("${configs.com.patsnap.patent_api.url}")
    private String patentApi;

    @Value("${configs.com.patsnap.patent_aggregate_api.url}")
    private String patentAggregateApi;

    // administrative division fields
    private static final Map<String, Map<String, String>> admDivisionFields = ImmutableMap.of("AN_ADD_ORIGINAL",
            ImmutableMap.of("addrField", "AN_ADD", "districtField", "AN_ADD_DISTRICT"), "ANC_ADD_ORIGINAL",
            ImmutableMap.of("addrField", "ANC_ADD", "districtField", "ANC_ADD_DISTRICT"));

    private static final String PATENT_ORIGIN_PN = "/patent/original/pn/";

    private static final String PATENT_CLASSIFICATION_EXPLAIN = "/classification/{type}?{type}={ids}&lang={lang}";


    @Autowired
    private RestTemplate restTemplate;


    @Autowired
    private EurekaServiceClient eurekaServiceClient;


    public Map<String, Map<String, Object>> exchangeForPatentDetails(Map<String, Object> body, String userId) {
        return exchangeForPatentDetails(body, userId, Boolean.TRUE);
    }

    public Map<String, Map<String, Object>> exchangeForPatentDetails(Map<String, Object> body, String userId,
            Boolean format) {
        if (null == body.get("patent_ids") || ((List<String>) body.get("patent_ids")).isEmpty()) {
            return Collections.emptyMap();
        }

        HttpHeaders headers = new HttpHeaders();
        if (StringUtils.isNotEmpty(userId)) {
            headers.add(UserIdHolder.X_USER_ID, userId);
        }
        if (body.get("lang") != null && StringUtils.isNotEmpty(body.get("lang").toString())) {
            headers.add(SITE_LANG, body.get("lang").toString());
        }
        headers.add("X-API-Version", "2.5");
        ResponseEntity<Map> response = restTemplate.exchange(serviceUrl(patentAggregateApi, DETAIL), HttpMethod.POST,
                new HttpEntity<>(body, headers), Map.class, FROM);
        Map bodyRes = response.getBody();
        Map<String, Map<String, Object>> result = Optional.ofNullable(bodyRes).orElseGet(HashMap::new);
        if (Boolean.TRUE.equals(format)) {
            for (Map.Entry<String, Map<String, Object>> patent : result.entrySet()) {
                formatPatent(patent.getValue(), body.get(TRANS_LANG) != null);
            }
        }
        return result;
    }

    public String serviceUrl(String baseUrl, String path) {
        if (org.apache.commons.lang3.StringUtils.isBlank(baseUrl) || org.apache.commons.lang3.StringUtils.isBlank(
                path)) {
            return "";
        }

        if (!baseUrl.endsWith("/")) {
            baseUrl += "/";
        }

        if (path.startsWith("/")) {
            path = path.substring(1);
        }
        return baseUrl + path;
    }


    public PatentClassification getClassificationExplain(String type, List<String> ids, String lang) {
        try {
            String url = patentApi + PATENT_CLASSIFICATION_EXPLAIN;
            HttpEntity<String> entity = generateHttpEntity(StringUtils.EMPTY);
            ResponseEntity<PatentClassification> responseEntity = restTemplate.exchange(url,
                    HttpMethod.GET,
                    entity,
                    new ParameterizedTypeReference<PatentClassification>() {
                    }, type, type, StringUtils.join(ids, ","), lang);
            return responseEntity.getBody();
        } catch (Exception e) {

        }
        return null;
    }


    /**
     * Deal with image_id, used for PATSNAP_IMAGE OR PATSNAP_IMAGE_120
     *
     * @param value
     * @param patentDataItem
     */
    private void checkAdaptImageId(Object value, Map<String, Object> patentDataItem) {
        if (null == value || null == patentDataItem) {
            return;
        }

        if (value instanceof Map && MapUtils.isNotEmpty((Map) value) && null != ((Map) value).get("image_id")) {
            patentDataItem.put("IMAGE_ID", ((Map) value).get("image_id"));
        }

    }

    private Object getValue(Object value, String key) {
        if (null == value) {
            return null;
        }
        if (!(value instanceof Map)) {
            return null;
        }
        return ((Map) value).get(key);

    }


    /**
     * @param patentDataItem
     * @param needTrans      因为技术功效和技术问题在patent-data 底层服务实现方式不一样， 他们是以x-site-lang作为兜底翻译语言，其实WS 不是依据它进行翻译的！
     */
    private void formatPatent(Map<String, Object> patentDataItem, boolean needTrans) {
        List<String> keys = new ArrayList<>(patentDataItem.keySet());
        for (String key : keys) {
            Object valueOfKeyByItem = patentDataItem.get(key);
            // s1
            checkAdaptImageId(valueOfKeyByItem, patentDataItem);

            if (EN_FIELDS.contains(key) && null != valueOfKeyByItem) {
                Object enFieldValue = patentDataItem.get(key + "_EN");
                patentDataItem.put(key,
                        ImmutableMap.of("OFFICIAL", valueOfKeyByItem, "EN", null == enFieldValue ? "" : enFieldValue));
                patentDataItem.remove(key + "_EN");
            }
            // s2
            if ("THUMB".equals(key)) {
                patentDataItem.put("PATSNAP_IMAGE", valueOfKeyByItem);
                patentDataItem.remove(key);
            }
            if ("THUMB_120".equals(key)) {
                patentDataItem.put("PATSNAP_IMAGE_120", valueOfKeyByItem);
                patentDataItem.remove(key);
            }
            Object urlValue = getValue(valueOfKeyByItem, "url");
            if (("PDF".equals(key) || "PDF_D".equals(key)) && null != urlValue) {
                patentDataItem.put(key, urlValue);
            }

            // 翻译
            patentDataTran(patentDataItem, needTrans);
        }

        // s3
        for (String k : keys) {
            Object valueOfKeyByItem = patentDataItem.get(k);
            Map<String, String> admDivisionMap = admDivisionFields.get(k);
            if (null == admDivisionMap) {
                continue;
            }
            String addrField = admDivisionMap.get("addrField");
            String districtField = admDivisionMap.get("districtField");

            Object addrItem = patentDataItem.get(addrField);
            if (null != addrItem && addrItem instanceof Map) {
                Map<String, Object> addrItemMap = (Map<String, Object>) addrItem;
                Map<String, Object> addrItemMut = Maps.newHashMapWithExpectedSize(addrItemMap.size());
                patentDataItem.put(addrField, addrItemMut);
                addrItemMut.put("ORIGINAL", valueOfKeyByItem);
                addrItemMut.put("DISTRICT", patentDataItem.get(districtField));
                // 保留之前的值！
                for (String key : addrItemMap.keySet()) {
                    addrItemMut.put(key, addrItemMap.get(key));
                }
            }
            // 下面值不用了!
            patentDataItem.remove(k);
            patentDataItem.remove(districtField);
        }
    }

    /**
     * 专利列表，是否需要根据用户勾选的 原文优先，或者翻译优先，来决定是否需要翻译 翻译方法
     */
    private void patentDataTran(Map<String, Object> patentDataItem, boolean needTrans) {
        handlerPatentDataTranslation(patentDataItem, "TITLE_TRAN", "TITLE");
        handlerPatentDataTranslation(patentDataItem, "ABST_TRAN", "ABST");
        handlerPatentDataTranslation(patentDataItem, "MCLMS_TRAN", "MCLMS");
        if (needTrans) {
            handlerPatentDataTranslation(patentDataItem, "EFFICIENCY_SENTENCE_TRAN", "EFFICIENCY_SENTENCE");
            handlerPatentDataTranslation(patentDataItem, "TECHNICAL_PROBLEM_TRAN", "TECHNICAL_PROBLEM");
        }
    }

    /**
     * 参数和目前数据的校验
     */
    private boolean isInvalidParameters(Map<String, Object> patentDataItem, String tranFieldName,
            String viewFiledName) {
        // 判断参数无效
        boolean parameterInvalidate = MapUtils.isEmpty(patentDataItem)
                || StringUtils.isBlank(tranFieldName)
                || StringUtils.isBlank(viewFiledName);
        if (parameterInvalidate) {
            return true;
        }

        // 判断目标数据无效
        Object dstValueObject = patentDataItem.get(tranFieldName);
        boolean valueInvalidate = Objects.isNull(dstValueObject)
                || (dstValueObject instanceof List && CollectionUtils.isEmpty(
                (List) patentDataItem.get(tranFieldName)));
        return valueInvalidate;
    }

    /**
     * 翻译的辅助方法
     */
    private void handlerPatentDataTranslation(Map<String, Object> patentDataItem, String tranFieldName,
            String viewFiledName) {
        if (isInvalidParameters(patentDataItem, tranFieldName, viewFiledName)) {
            return;
        }
        Object tranDataValue = patentDataItem.get(tranFieldName);
        handlerDataBasedOnType(patentDataItem, tranDataValue, viewFiledName);
    }

    /**
     * Maps data to target field based on its type.
     */
    private void handlerDataBasedOnType(Map<String, Object> patentData, Object sourceData, String targetField) {
        if (sourceData instanceof String) {
            handlerStringData(patentData, (String) sourceData, targetField);
        } else if (sourceData instanceof List) {
            handlerListData(patentData, (List<Object>) sourceData, targetField);
        }
    }

    /**
     * Maps non-empty string data to the specified target field.
     */
    private void handlerStringData(Map<String, Object> patentData, String stringData, String targetField) {
        if (StringUtils.isNotEmpty(stringData)) {
            patentData.put(targetField, stringData);
        }
    }

    /**
     * Maps non-empty list data to the specified target field.
     */
    private void handlerListData(Map<String, Object> patentData, List<Object> listData, String targetField) {
        if (CollectionUtils.isNotEmpty(listData)) {
            patentData.put(targetField, listData);
        }
    }


    /**
     * 根据专利ID列表获取专利号映射 此方法用于批量获取专利号，根据提供的专利ID列表进行查询，并将结果映射为一个字典 其中专利ID为键，专利号为值如果输入列表为空，则返回一个空的映射
     *
     * @param patentIds 专利ID列表，用于查询专利详情
     * @return 返回一个映射，键为专利ID，值为对应的专利号如果输入为空，则返回空映射
     */
    public Map<String, String> getPnMap(List<String> patentIds) {
        if (CollectionUtils.isEmpty(patentIds)) {
            return Collections.emptyMap();
        }

        Map<String, Object> patentDataRequest = Map.of(
                "patent_ids", patentIds,
                "fields", List.of("PN")
        );

        Map<String, Map<String, Object>> resultMap = exchangeForPatentDetails(patentDataRequest, UserIdHolder.get());
        log.info("Get PN by patent id, resultMap is {}", resultMap);

        return resultMap.entrySet().stream()
                .filter(entry -> entry.getValue() != null && entry.getValue().containsKey("PN"))
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> (String) entry.getValue().get("PN"),
                        (v1, v2) -> v1,
                        HashMap::new
                ));
    }

    /**
     * 根据专利ID列表获取指定字段的专利信息映射
     * 此方法用于批量获取专利信息，根据提供的专利ID列表和字段列表进行查询，并将结果映射为一个嵌套字典
     * 其中外层字典的键为专利ID，值为包含请求字段的内部字典
     * 如果输入列表为空，则返回一个空的映射
     *
     * @param patentIds 专利ID列表，用于查询专利详情
     * @param fields 要获取的字段列表
     * @return 返回一个嵌套映射，外层键为专利ID，内层包含请求的字段及其值；如果输入为空，则返回空映射
     */
    public Map<String, Map<String, Object>> getPatentFieldsMap(List<String> patentIds, List<String> fields) {
        if (CollectionUtils.isEmpty(patentIds) || CollectionUtils.isEmpty(fields)) {
            return Collections.emptyMap();
        }
        Map<String, Object> patentDataRequest = Map.of(
                "patent_ids", patentIds,
                "fields", fields,
                "lang", SiteLangHolder.get(),
                "trans_lang", SiteLangHolder.get(),
                "parameter", Map.of("https", true, "alternative", true)
        );

        Map<String, Map<String, Object>> resultMap = exchangeForPatentDetails(patentDataRequest, UserIdHolder.get());
        log.info("Get fields {} by patent id, resultMap size is {}", fields, resultMap.size());

        return resultMap.entrySet().stream()
                .filter(entry -> entry.getValue() != null)
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (v1, v2) -> v1,
                        HashMap::new
                ));
    }

    public Map<String, Map<String, Object>> getPatentDetails(List<String> patentIds) {
        return eurekaServiceClient.getEurekaSolutionData(buildEurekaPatentDataReq(patentIds));
    }

    public List<String> getPatentIdByPatentNos(String pnNos) {
        try {
            String url = patentApi + PATENT_ORIGIN_PN + pnNos;
            HttpEntity<String> entity = generateHttpEntity(StringUtils.EMPTY);
            ResponseEntity<PatentResponseDTO> responseEntity = restTemplate.exchange(url,
                    HttpMethod.GET,
                    entity,
                    new ParameterizedTypeReference<PatentResponseDTO>() {
                    });
            Preconditions.checkArgument(Objects.nonNull(responseEntity), "responseEntity is null");
            PatentResponseDTO commonResponse = responseEntity.getBody();
            log.info("Get patent id by patent no, response is {}", commonResponse);
            if (Objects.isNull(commonResponse)) {
                log.warn("Get patent id by patent no error, response is null");
                return Collections.emptyList();
            }
            Preconditions.checkArgument("200".equals(commonResponse.getCode()), "Get patentId service error");
            if (CollectionUtils.isEmpty(commonResponse.getPatent())) {
                log.warn("Get patent id by patent no error, response patent is empty");
                return Collections.emptyList();
            }
            return commonResponse.getPatent().stream().map(PatentInfoDTO::getPatentId).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("Get patent id by patent no error", e);
        }
        return Collections.emptyList();
    }


    private <T> HttpEntity<T> generateHttpEntity(T body) {
        HttpHeaders headers = new HttpHeaders();
        headers.add("X-User-ID", UserIdHolder.get());
        headers.add("Content-Type", "application/json");
        headers.add("Accept", "application/json");
        return new HttpEntity<>(body, headers);
    }


    private EurekaSolutionDataRequest buildEurekaPatentDataReq(List<String> patentIds) {
        EurekaSolutionDataRequest request = new EurekaSolutionDataRequest();
        request.setSolutionIds(patentIds);
        request.setSolutionDataType("COPILOT_SEARCH_PATENT");
        return request;
    }

    /**
     * 根据专利ID列表获取专利图片URL映射
     * 此方法用于批量获取专利图片URL，根据提供的专利ID列表进行查询，并将结果映射为一个嵌套字典
     * 其中外层字典的键为专利ID，值为包含图片信息的内部字典
     * 如果输入列表为空，则返回一个空的映射
     *
     * @param patentIds 专利ID列表，用于查询专利详情
     * @return 返回一个嵌套映射，外层键为专利ID，内层包含图片URL和图片ID；如果输入为空，则返回空映射
     */
    public Map<String, Map<String, Object>> getPatentImagesMap(List<String> patentIds) {
        if (CollectionUtils.isEmpty(patentIds)) {
            return Collections.emptyMap();
        }

        // 调用getPatentFieldsMap方法获取THUMB和THUMB_120字段，这些字段会在formatPatent方法中被转换为PATSNAP_IMAGE和PATSNAP_IMAGE_120
        Map<String, Map<String, Object>> patentFieldsMap = getPatentFieldsMap(patentIds, List.of("THUMB", "THUMB_120"));
        log.info("Get patent images by patent id, resultMap = {}", patentFieldsMap);

        // 返回PATSNAP_IMAGE和PATSNAP_IMAGE_120字段的专利
        return patentFieldsMap.entrySet().stream()
                .filter(entry -> entry.getValue() != null)
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> {
                            Map<String, Object> imageInfo = new HashMap<>();
                            Map<String, Object> patentData = entry.getValue();

                            // 提取PATSNAP_IMAGE字段的url
                            if (patentData.containsKey("PATSNAP_IMAGE")) {
                                Object patentImage = patentData.get("PATSNAP_IMAGE");
                                if (patentImage instanceof Map && ((Map<?, ?>) patentImage).containsKey("url")) {
                                    imageInfo.put("url", ((Map<?, ?>) patentImage).get("url"));
                                }
                            }

                            // 提取PATSNAP_IMAGE_120字段的url
                            if (patentData.containsKey("PATSNAP_IMAGE_120")) {
                                Object patentImage120 = patentData.get("PATSNAP_IMAGE_120");
                                if (patentImage120 instanceof Map && ((Map<?, ?>) patentImage120).containsKey("url")) {
                                    imageInfo.put("url_120", ((Map<?, ?>) patentImage120).get("url"));
                                }
                            }

                            return imageInfo;
                        },
                        (v1, v2) -> v1,
                        HashMap::new
                ));
    }
}

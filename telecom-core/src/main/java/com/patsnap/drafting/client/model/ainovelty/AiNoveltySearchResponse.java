package com.patsnap.drafting.client.model.ainovelty;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.patsnap.drafting.request.ainoveltysearch.AiSearchFinalResult;
import com.patsnap.drafting.request.aitask.AiTaskReqDTO;
import lombok.Data;
import java.util.List;
import java.util.Map;

/**
 * 创新性检索结果返回信息
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class AiNoveltySearchResponse extends AiTaskReqDTO {

    // 任务 id
    @JsonProperty("task_id")
    private String taskId;

    // 特征分析结果
    @JsonProperty("feature_analysis_result")
    private FeatureAnalysisResult featureAnalysisResult;

    // 处理字典的键列表
    @JsonProperty("process_dict_keys")
    private List<String> processDictKeys;

    // 每个检索过程的结果
    @JsonProperty("process_dict")
    private Map<String, ProcessDetail> processDict;

    // 找到的疑似相关专利的比对信息，已经按公开度排序过
    @JsonProperty("final_result")
    private List<AiSearchFinalResult> finalResult;

    // cc 对比的阈值，超过这个阈值则是确认的特征，低于这个阈值则是待确认的特征
    @JsonProperty("cc_threshold")
    private float ccThreshold;

    // cc 对比的专利列表
    @JsonProperty("cc_pids")
    private List<String> ccPids;

    // 当前任务执行状态
    @JsonProperty("task_status")
    private String taskStatus;

    // 总共比对的专利数量
    @JsonProperty("cc_docs_num")
    private int ccDocsNum;

    // 已经找到疑似相似专利数量
    @JsonProperty("find_patent_num")
    private int findPatentNum;

    // 执行的检索次数
    @JsonProperty("excute_times")
    private int excuteTimes;

    // 任务总耗时（s）
    @JsonProperty("task_cost")
    private float taskCost;

    // 是否是异步请求
    @JsonProperty("is_async")
    private boolean isAsync;

    // 结果最后更新的时间
    @JsonProperty("update_ts")
    private String updateTs;

    // 请求体
    @JsonProperty("request")
    private Object request;

    // 开启 debug=true，会返回，主要用于内测分析
    @JsonProperty("debug_detail")
    private Object debugDetail;
    
    // 特征关键词
    @JsonProperty("feature_keywords")
    private List<FeatureKeyword> featureKeywords;
    
    // 语言
    @JsonProperty("lang")
    private String lang;
} 
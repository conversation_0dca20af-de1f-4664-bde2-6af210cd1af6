package com.patsnap.drafting.client.model.ainovelty;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import java.util.List;
import java.util.Map;

/**
 * 特征分析结果
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class FeatureAnalysisResult {

    // 摘要结果
    @JsonProperty("summary_result")
    private Map<String, Object> summaryResult;

    // 特征提取结果
    @JsonProperty("feature_extract_result")
    private List<Object> featureExtractResult;

    // 关键词提取结果
    @JsonProperty("keywords_extract_result")
    private List<KeywordExtractResult> keywordsExtractResult;

    // 块查询
    @JsonProperty("block_query")
    private Map<String, BlockQuery> blockQuery;
} 
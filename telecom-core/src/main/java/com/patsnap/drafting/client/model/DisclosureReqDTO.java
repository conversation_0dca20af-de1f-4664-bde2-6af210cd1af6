package com.patsnap.drafting.client.model;


import com.patsnap.drafting.model.aidisclosure.StructUserInputBO;

import java.util.List;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 交底书撰写SA接口请求参数
 *
 * <AUTHOR>
 * @date 2024/08/21
 */
@NoArgsConstructor
@Data
public class DisclosureReqDTO {

    /**
     * 背景技术的描述
     */
    private String background;
    /**
     * 现有技术的缺点
     */
    private String drawbacks;
    /**
     * 实施例
     */
    private String embodiment;
    /**
     * 选择的操作模式，可选择的有:
     * extract：抽取信息，结构化用户输入
     * recommend：推荐发明主体，应用领域
     * classify：对用户输入进行分类
     * title：生成标题
     * tech_field：生成技术领域
     * background：生成背景技术描述
     * reference：生成引用文献介绍
     * drawbacks：生成现有技术的缺点
     * tech_problem：生成发明目的的问题描述
     * tech_effect：生成发明目的的有益效果
     * tech_means：生成技术方案
     * embodiment：生成实施例
     * simplify：简写
     * expand：扩写
     * polish：改写
     * add_background：新增背景技术的描述
     * add_reference：新增一篇引用专利
     * add_drawbacks：新增一个现有技术缺点
     * add_problem：新增一个问题
     * add_effect：新增一个有益效果
     * add_embodiment：新增一个实施例
     * other_extract：用户修改others修改重新抽取
     */
    private String mode;
    /**
     * 原始用户输入
     */
    private String originUserInput;
    /**
     * 语义检索后top20专利号
     */
    private List<String> patentIds;
    /**
     * 引用文献介绍
     */
    private String reference;
    /**
     * 结构化的用户输入
     */
    private StructUserInputBO structUserInput;
    /**
     * 发明目的中有益效果
     */
    private String techEffect;
    /**
     * 技术领域
     */
    private String techField;
    /**
     * 技术方案
     */
    private String techMeans;
    /**
     * 发明目的中的问题描述
     */
    private String techProblem;
    /**
     * 需要简写、扩写、改写的文本
     * 如果mode为simplify/expand/polish，则该字段必不为空，其余mode，该字段可以为空
     */
    private String text;
    /**
     * 标题
     */
    private String title;
    /**
     * 生成专利的类型，由分类产生
     */
    private String typeSelect;

    public DisclosureReqDTO(String mode) {
        this.mode = mode;
    }
}

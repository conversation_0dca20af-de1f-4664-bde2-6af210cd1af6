package com.patsnap.drafting.client;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.patsnap.drafting.client.api.AiSearchApi;
import com.patsnap.drafting.client.api.AiSearch2Api;
import com.patsnap.drafting.client.model.*;
import com.patsnap.drafting.client.model.ainovelty.AiNoveltySearchResponse;
import com.patsnap.drafting.exception.BizException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import retrofit2.Response;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import static com.patsnap.drafting.exception.errorcode.ClientErrorCodeEnum.CLIENT_NOT_AVAILABLE;

@Slf4j
@Component
@RequiredArgsConstructor
public class AiSearchClient {

    private final AiSearchApi aiSearchApi;

    private final AiSearch2Api aiSearch2Api;

    /**
     * 执行查新检索
     * @param request 检索请求体
     * @return 任务Id
     */
    public AiNoveltySearchResponse noveltyAiSearchAgent(AiSearchAgentRequest request) {
        try {
            Map<String, Object> filtered = new HashMap<>();
            filtered.put("text", request.getText());
            filtered.put("summary_result", request.getSummaryResult());
            filtered.put("feature_extract_result", request.getFeatureExtractResult());
            filtered.put("keywords_extract_result", request.getKeywordsExtractResult());
            if (CollUtil.isNotEmpty(request.getCollapse())) {
                filtered.put("collapse", request.getCollapse());
            }
            if (CollUtil.isNotEmpty(request.getFilters())) {
                filtered.put("filters", request.getFilters());
            }
            if (request.getTestParams() != null) {
                filtered.put("test_params", request.getTestParams());
            }
            Response<AiNoveltySearchResponse> response = aiSearch2Api.noveltyAiSearchAgent(filtered).execute();
            if (response.isSuccessful()) {
                return response.body();
            } else {
                log.warn("novelty_search_agent failed, code: {}, message: {}", response.code(), response.message());
                throw new BizException(CLIENT_NOT_AVAILABLE);
            }
        } catch (IOException e) {
            log.error("Failed to novelty_search_agent, request: {}", JSON.toJSONString(request), e);
            throw new BizException(CLIENT_NOT_AVAILABLE);
        }
    }

    /**
     * 执行FTO检索
     * @param request 检索请求体
     * @return 任务Id
     */
    public AiSearchAgentResponse ftoAiSearchAgent(AiFtoSearchAgentRequest request) {
        try {
            Response<AiSearchAgentResponse> response = aiSearchApi.ftoAiSearchAgent(request).execute();
            if (response.isSuccessful()) {
                return response.body();
            } else {
                log.warn("novelty_search_agent failed, code: {}, message: {}", response.code(), response.message());
                throw new BizException(CLIENT_NOT_AVAILABLE);
            }
        } catch (IOException e) {
            log.error("Failed to novelty_search_agent, request: {}", JSON.toJSONString(request), e);
            throw new BizException(CLIENT_NOT_AVAILABLE);
        }
    }

    /**
     * 获取查新结果
     * @param taskId 执行查新检索agent时，算法接口返回的task_id
     * @param isFinished 是否终止检索
     * @return 查询结果数据
     */
    public AiNoveltySearchResponse noveltyAiSearchResult(String taskId, Boolean isFinished) {
        try {
            Response<AiNoveltySearchResponse> response = aiSearch2Api.noveltyAiSearchResult(taskId, isFinished).execute();
            if (response.isSuccessful()) {
                return response.body();
            } else {
                log.warn("novelty_search_result failed, code: {}, message: {}", response.code(), response.message());
                throw new BizException(CLIENT_NOT_AVAILABLE);
            }
        } catch (IOException e) {
            log.error("Failed to novelty_search_result, request: task_id={}, is_finished={}", taskId, isFinished, e);
            throw new BizException(CLIENT_NOT_AVAILABLE);
        }
    }

    /**
     * 获取FTO查新结果
     * @param taskId 执行查新检索agent时，算法接口返回的task_id
     * @param isFinished 是否终止检索
     * @return 查询结果数据
     */
    public AiSearchAgentResponse ftoAiSearchResult(String taskId, Boolean isFinished) {
        try {
            Response<AiSearchAgentResponse> response = aiSearchApi.noveltyAiSearchResult(taskId, isFinished).execute();
            if (response.isSuccessful()) {
                return response.body();
            } else {
                log.warn("novelty_search_result failed, code: {}, message: {}", response.code(), response.message());
                throw new BizException(CLIENT_NOT_AVAILABLE);
            }
        } catch (IOException e) {
            log.error("Failed to novelty_search_result, request: task_id={}, is_finished={}", taskId, isFinished, e);
            throw new BizException(CLIENT_NOT_AVAILABLE);
        }
    }
}

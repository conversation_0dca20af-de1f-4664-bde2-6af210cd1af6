package com.patsnap.drafting.client.model.ainovelty;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import java.util.List;
import java.util.Map;

/**
 * 检索过程详情
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ProcessDetail {

    // 标题
    @JsonProperty("title")
    private Map<String, String> title;

    // 查询思路
    @JsonProperty("query_thinking")
    private Map<String, String> queryThinking;

    // 块查询思路
    @JsonProperty("block_query_thinking")
    private Map<String, String> blockQueryThinking;

    // 块查询
    @JsonProperty("block_query")
    private Map<String, Object> blockQuery;

    // 查询列表
    @JsonProperty("queries")
    private List<QueryInfo> queries;

    // 比对结果
    @JsonProperty("comparison_results")
    private ComparisonResults comparisonResults;

    // 下一步计划
    @JsonProperty("next_plan")
    private Object nextPlan;

    // 检索类型
    @JsonProperty("type")
    private String type;

    // 耗时
    @JsonProperty("cost")
    private float cost;

    // 状态
    @JsonProperty("status")
    private String status;
} 
package com.patsnap.drafting.client;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import com.patsnap.drafting.client.model.AiSpecificationComputeReqDTO;
import com.patsnap.drafting.client.model.ClassificationReqDTO;
import com.patsnap.drafting.client.model.ClassificationResDTO;
import com.patsnap.drafting.client.model.DisclosureReqDTO;
import com.patsnap.drafting.client.model.LangDetectReqDTO;
import com.patsnap.drafting.client.model.RdSensitiveWordsResDTO;
import com.patsnap.drafting.config.UrlConfig;
import com.patsnap.drafting.constants.Constant;
import com.patsnap.drafting.exception.BizException;
import com.patsnap.drafting.exception.errorcode.ContentErrorCodeEnum;
import com.patsnap.drafting.response.aidisclosure.DisclosureResDTO;
import com.patsnap.drafting.response.aispecification.SpecificationRdResDTO;
import com.patsnap.spring.boot.autoconfigure.web.entity.CommonResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URI;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR> 2022/9/1
 * @Desc 调用R&D接口
 */
@Slf4j
@Service
public class ComputeClient {

    private final UrlConfig urlConfig;
    private final RestTemplate restTemplate;

    private final RestTemplate longTimeoutRestTemplate;


    private static final String DEFAULT_LANGUAGE = Constant.EN;
    private static final String SESSION_STRING = "string";

    public ComputeClient(UrlConfig urlConfig, RestTemplate restTemplate,
            @Qualifier("longTimeoutRestTemplate") RestTemplate longTimeoutRestTemplate) {
        this.urlConfig = urlConfig;
        this.restTemplate = restTemplate;
        this.longTimeoutRestTemplate = restTemplate;
    }

    private HttpHeaders getHeaders() {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.add("Content-Type", "application/json");
        httpHeaders.add("X-PatSnap-Version", "v1");
        return httpHeaders;
    }

    /**
     * 获取AI交底书撰写各场景的提示词
     *
     * @param params
     * @return
     */
    public DisclosureResDTO getDisclosurePrompt(DisclosureReqDTO params) {
        try {
            URI uri = UriComponentsBuilder.fromUriString(urlConfig.getAiLabDisclosureUrl()).build().encode().toUri();
            CommonResponse<DisclosureResDTO> response = restTemplate.exchange(uri, HttpMethod.POST,
                    new HttpEntity<>(ImmutableMap.of("data", params), getHeaders()),
                    new ParameterizedTypeReference<CommonResponse<DisclosureResDTO>>() {
                    }).getBody();
            if (response == null) {
                return null;
            }
            return response.getData();
        } catch (Exception e) {
            log.warn("get disclosure prompt error", e);
            throw new BizException(ContentErrorCodeEnum.SYSTEM_BUSY);
        }
    }

    /**
     * check input has sensitive words
     *
     * @param text
     * @return sensitive words
     */
    public RdSensitiveWordsResDTO.RdData checkInputHasSensitiveWords(String text) {
        RdSensitiveWordsResDTO response;
        URI uri = UriComponentsBuilder.fromUriString(urlConfig.getRdCheckInputService()).build().encode().toUri();
        try {
            response = restTemplate.exchange(uri, HttpMethod.POST,
                    new HttpEntity<>(new LangDetectReqDTO(text), getHeaders()), RdSensitiveWordsResDTO.class).getBody();
        } catch (Exception e) {
            log.warn("check input has sensitive words error", e);
            return null;
        }
        return Optional.ofNullable(response).map(RdSensitiveWordsResDTO::getData).orElse(null);
    }


    /**
     * 检测给定文本的语言
     *
     * @param doc 提供的文本
     * @return 语言CN，EN ，KR，JP等等大写
     */
    public String getCheckLang(String doc) {
        try {
            Map<String, Object> bodyObj = Maps.newHashMap();
            Map<String, Object> dataMap = Maps.newHashMap();
            dataMap.put("text", doc);
            bodyObj.put("data", dataMap);
            bodyObj.put("session", SESSION_STRING);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.add("X-PatSnap-Version", "v2");

            ResponseEntity<Map> response = restTemplate.exchange(urlConfig.getRdLangDetectUrl(), HttpMethod.POST,
                    new HttpEntity<>(bodyObj, headers), Map.class);

            if (response.getBody() != null) {
                Object dataObj = response.getBody().get("data");
                if (dataObj instanceof Map) {
                    Map<String, Map<String, Object>> data = (Map<String, Map<String, Object>>) dataObj;
                    return StringUtils.defaultString(String.valueOf(data.get("lang")), DEFAULT_LANGUAGE).toUpperCase();
                }
            }
        } catch (RestClientException e) {
            log.error("HTTP request failed: {}", e.getMessage());
        } catch (Exception e) {
            log.error("An unexpected error occurred: {}", e.getMessage());
        }
        return DEFAULT_LANGUAGE;
    }


    public List<String> getIpcListByDoc(String title, String abst, String claim) {
        ClassificationResDTO response = null;
        URI uri = UriComponentsBuilder.fromUriString(urlConfig.getRdIpcUrl()).build().encode().toUri();
        try {
            response = longTimeoutRestTemplate.exchange(uri, HttpMethod.POST,
                    new HttpEntity<>(new ClassificationReqDTO(new ClassificationReqDTO.Text(title, abst, claim)),
                            getHeaders()),
                    ClassificationResDTO.class).getBody();
        } catch (Exception e) {
            log.warn("get ipc classification error", e);
        }
        return Optional.ofNullable(response).map(ClassificationResDTO::getData)
                .map(ClassificationResDTO.ClassificationResult::getResult).orElse(null);
    }


    public List<String> getCpcListByDoc(String title, String abst, String claim) {
        ClassificationResDTO response = null;
        URI uri = UriComponentsBuilder.fromUriString(urlConfig.getRdCpcUrl()).build().encode().toUri();
        try {
            response = longTimeoutRestTemplate.exchange(uri, HttpMethod.POST,
                    new HttpEntity<>(new ClassificationReqDTO(new ClassificationReqDTO.Text(title, abst, claim)),
                            getHeaders()),
                    ClassificationResDTO.class).getBody();
        } catch (Exception e) {
            log.warn("get cpc classification error", e);
        }
        return Optional.ofNullable(response).map(ClassificationResDTO::getData)
                .map(ClassificationResDTO.ClassificationResult::getResult).orElse(null);
    }


    /**
     * 获取AI说明书书撰写各场景的提示词
     *
     * @param params
     * @return
     */
    public SpecificationRdResDTO getSpecificationPrompt(AiSpecificationComputeReqDTO params) {
        URI uri = UriComponentsBuilder.fromUriString(urlConfig.getRdSpecificationUrl()).build().encode().toUri();
        CommonResponse<SpecificationRdResDTO> response = restTemplate.exchange(uri, HttpMethod.POST,
                new HttpEntity<>(ImmutableMap.of("data", params), getHeaders()),
                new ParameterizedTypeReference<CommonResponse<SpecificationRdResDTO>>() {
                }).getBody();
        if (response == null) {
            return null;
        }
        return response.getData();
    }


}

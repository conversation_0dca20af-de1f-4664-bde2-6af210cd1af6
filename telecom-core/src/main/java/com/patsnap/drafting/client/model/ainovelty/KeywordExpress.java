package com.patsnap.drafting.client.model.ainovelty;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import java.util.List;

/**
 * 关键词表达式
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class KeywordExpress {

    // 单词信息
    @JsonProperty("word")
    private KeywordInfo word;

    // 扩展关键词
    @JsonProperty("extend")
    private List<KeywordInfo> extend;

    // IPC分类号
    @JsonProperty("ipc")
    private List<KeywordInfo> ipc;
} 
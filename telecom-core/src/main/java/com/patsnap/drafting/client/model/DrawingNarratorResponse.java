package com.patsnap.drafting.client.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 附图说明算法接口响应模型
 * 
 * <AUTHOR> Assistant
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DrawingNarratorResponse {

    /**
     * 是否成功
     */
    @JsonProperty("success")
    private Boolean success;

    /**
     * 生成结果
     * 示例：[{"number": 1, "description": "图1是多意图识别方法的整体流程图，显示..."}]
     */
    @JsonProperty("result")
    private String result;
} 
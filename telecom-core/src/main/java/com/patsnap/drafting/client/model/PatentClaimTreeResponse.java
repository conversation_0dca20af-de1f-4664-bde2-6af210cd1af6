package com.patsnap.drafting.client.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 专利权利要求树返回值
 *
 * <AUTHOR>
 * @Date 2025/1/19 16:00
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class PatentClaimTreeResponse {

    private Claims data;

    @lombok.Data
    @Accessors(chain = true)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Claims {
        
        @JsonProperty("CLMS_TREE")
        private List<ClaimTree> clmsTree;
        
        @JsonProperty("PN")
        private String pn;
    }

    @lombok.Data
    @Accessors(chain = true)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ClaimTree {
        
        @JsonProperty("integerNum")
        private Integer integerNum;
        
        @JsonProperty("preamble")
        private Preamble preamble;
        
        @JsonProperty("transitional")
        private Transitional transitional;
        
        @JsonProperty("body")
        private Body body;
        
        @JsonProperty("num")
        private String num;
        
        @JsonProperty("wholeText")
        private String wholeText;
        
        @JsonProperty("parentNums")
        private List<String> parentNums;
    }

    @lombok.Data
    @Accessors(chain = true)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Preamble {
        
        @JsonProperty("text")
        private String text;
    }

    @lombok.Data
    @Accessors(chain = true)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Body {
        
        @JsonProperty("text")
        private String text;
    }

    @lombok.Data
    @Accessors(chain = true)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Transitional {
        // 根据 JSON 结构，这里是空对象，可以根据需要添加字段
    }
} 
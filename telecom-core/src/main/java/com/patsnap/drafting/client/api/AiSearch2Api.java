package com.patsnap.drafting.client.api;

import com.patsnap.drafting.client.model.AiSearchAgentRequest;
import com.patsnap.drafting.client.model.ainovelty.AiNoveltySearchResponse;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.POST;
import retrofit2.http.Query;

import java.util.Map;

public interface AiSearch2Api {
    
    @POST("api/novelty_search_agent/")
    Call<AiNoveltySearchResponse> noveltyAiSearchAgent(@Body Map<String, Object> filteredRequest);

    @GET("api/novelty_search_result/")
    Call<AiNoveltySearchResponse> noveltyAiSearchResult(@Query(value="task_id") String taskId, @Query(value="is_finished") Boolean isFinished);
}
package com.patsnap.drafting.client.model;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ClassificationReqDTO {

    private Text data;

    @Data
    @AllArgsConstructor
    public static class Text {

        private String title;

        @JsonProperty("abstract")
        private String abst;

        private String claim;
    }
}

package com.patsnap.drafting.client.model;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.patsnap.drafting.request.ainoveltysearch.AiSearchAgentFeature;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> chendepeng
 * @Date 2025/3/12 13:40
 */
@Accessors(chain = true)
@Data
public class AiSearchAgentRequest {
    // 必传参数
    private String text;

    @JsonProperty("summary_result")
    private Object summaryResult;

    @JsonProperty("feature_extract_result")
    private Object featureExtractResult;

    @JsonProperty("keywords_extract_result")
    private Object keywordsExtractResult;

    // filter参数
    private List<Map<String, Object>> filters = new ArrayList<>();

    private Map<String, Object> collapse = new HashMap<>();

    // 控制cc的数量，节省成本(通过header参数控制)
    private JSONObject testParams = new JSONObject();

    public void handleTestParams(Integer ccNum) {
        JSONObject testParams = new JSONObject();
        JSONObject ccNumInfo = new JSONObject();
        ccNumInfo.put("cc_num", ccNum);
        testParams.put("test_params", ccNumInfo);
        this.setTestParams(testParams);
    }
}

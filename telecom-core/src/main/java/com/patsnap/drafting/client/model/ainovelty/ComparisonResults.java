package com.patsnap.drafting.client.model.ainovelty;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import java.util.List;

/**
 * 比对结果
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ComparisonResults {

    // 比对文档数量
    @JsonProperty("comparison_doc_num")
    private Integer comparisonDocNum;

    // 找到的文档数量
    @JsonProperty("find_doc_num")
    private Integer findDocNum;

    // 文档列表
    @JsonProperty("documents")
    private List<DocumentInfo> documents;
} 
package com.patsnap.drafting.listener;

import com.patsnap.drafting.util.MemoryMonitorUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 内存监控定时任务
 * 定期监控和记录内存使用情况，帮助诊断内存泄漏问题
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
@ConditionalOnProperty(name = "memory.monitor.enabled", havingValue = "true", matchIfMissing = false)
public class MemoryMonitorScheduler {

    /**
     * 每5分钟记录一次内存使用情况
     */
    @Scheduled(fixedRate = 15 * 60 * 1000) // 15分钟
    public void logMemoryUsage() {
        try {
            String memoryReport = MemoryMonitorUtil.getMemoryReport();
            log.info("定时内存监控: {}", memoryReport);
            
            // 检查直接内存使用情况，如果超过阈值则打印详细信息
            long directMemoryUsed = MemoryMonitorUtil.getDirectMemoryUsed();
            if (directMemoryUsed > 100 * 1024 * 1024) { // 超过100MB
                log.warn("直接内存使用量较高: {}, 打印详细内存信息", MemoryMonitorUtil.formatBytes(directMemoryUsed));
                MemoryMonitorUtil.printMemoryInfo();
            }
            
        } catch (Exception e) {
            log.error("定时内存监控任务执行失败", e);
        }
    }

    /**
     * 每小时记录一次详细的内存使用情况
     */
    @Scheduled(fixedRate = 60 * 60 * 1000) // 1小时
    public void logDetailedMemoryUsage() {
        try {
            log.info("=== 每小时详细内存监控 ===");
            MemoryMonitorUtil.printMemoryInfo();
        } catch (Exception e) {
            log.error("详细内存监控任务执行失败", e);
        }
    }
} 
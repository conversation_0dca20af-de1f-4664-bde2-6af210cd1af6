package com.patsnap.drafting.listener;

import com.patsnap.common.request.CorrelationIdHolder;
import com.patsnap.core.common.request.RoleIdsHolder;
import com.patsnap.common.request.SessionIdHolder;
import com.patsnap.common.request.UserIdHolder;
import com.patsnap.core.common.copilot.util.SimpleJsonMapper;
import com.patsnap.drafting.enums.task.AsyncTaskStatusEnum;
import com.patsnap.drafting.manager.aitask.AiTaskManager;
import com.patsnap.drafting.manager.aitranslation.AiTranslationManager;
import com.patsnap.drafting.model.aitask.AiTaskBO;
import com.patsnap.drafting.repository.aitask.entity.AnalyticsAiTaskHistoryPO;
import com.patsnap.drafting.util.RedissonUtils;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import org.apache.commons.collections.CollectionUtils;
import org.redisson.api.RedissonClient;
import com.amazonaws.services.sqs.AmazonSQS;
import com.amazonaws.services.sqs.model.Message;
import com.amazonaws.services.sqs.model.ReceiveMessageResult;
import lombok.extern.slf4j.Slf4j;

/**
 * AI异步任务消息处理器
 *
 * <AUTHOR>
 * @date 2024/07/19
 */
@Slf4j
public class AiTaskMessageHandler{

    private final AiTranslationManager aiTranslationManager;
    private final AmazonSQS sqsClient;
    private final String sqsName;
    private final RedissonClient redissonClient;
    private final String DRAFTING_TASK_ASYNC_TASK_LOCK = "DRAFTING:TASK:ASYNC:TRANSLATION:LOCK";
    private ExecutorService executorService;
    private final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    private final AiTaskManager aiTaskManager;
    private final RedissonUtils redissonUtils;
    private static final int MAX_RETRY_COUNT = 10; // Assuming a default value, adjust as needed
    private static final int LOCK_TIMEOUT = 1800; // 锁超时时间设置为30分钟，避免长时间占用
    private static final int LOCK_WAIT_TIME = 30;
    private static final String PROCESSING_ERROR_MSG = "处理任务时发生异常, taskId: {}, error: {}";

    public AiTaskMessageHandler(AiTranslationManager aiTranslationManager, AmazonSQS sqsClient, String sqsName,
            RedissonClient redissonClient, AiTaskManager aiTaskManager, int threadPoolSize, RedissonUtils redissonUtils) {
        this.aiTranslationManager = aiTranslationManager;
        this.sqsClient = sqsClient;
        this.sqsName = sqsName;
        this.redissonClient = redissonClient;
        this.aiTaskManager = aiTaskManager;
        this.redissonUtils = redissonUtils;
        this.executorService = Executors.newFixedThreadPool(threadPoolSize);
    }

    public void doTask() {
        scheduler.scheduleWithFixedDelay(() -> {
            try {
                processNextMessage();
            } catch (Exception e) {
                log.error("Message processing failed in scheduler", e);
            }
        }, 0, 5, TimeUnit.SECONDS);
    }

    private void processNextMessage() {
        Message message = getMessage();
        if (message != null) {
            String messageId = message.getMessageId();
            long startTime = System.currentTimeMillis();
            log.info("开始处理消息: {}", messageId);

            executorService.submit(() -> {
                try {
                    processMessageWithRetry(message);
                } catch (Exception e) {
                    log.error("消息处理失败: {}", messageId, e);
                } finally {
                    long processingTime = System.currentTimeMillis() - startTime;
                    log.info("消息处理完成: {}, 耗时: {}ms", messageId, processingTime);
                }
            });
        }
    }

    private void processMessageWithRetry(Message message) {
        try {
            AiTaskBO aiTask = SimpleJsonMapper.readValue(message.getBody(), AiTaskBO.class);
            String taskId = aiTask.getTaskId();
            String userId = aiTask.getUserId();

            // 检查任务状态
            if (!isTaskValid(taskId)) {
                log.info("任务 [{}] 已完成或不存在, 不处理此消息", taskId);
                deleteMessage(message);
                return;
            }

            // 检查重试次数
            if (aiTask.getRetryCount() >= MAX_RETRY_COUNT) {
                handleMaxRetryReached(aiTask, message);
                return;
            }

            String lockKey = DRAFTING_TASK_ASYNC_TASK_LOCK + ":" + userId;
            boolean lockAcquired = false;
            try {
                // 使用较短的锁超时时间
                lockAcquired = redissonUtils.getLock(redissonClient, LOCK_WAIT_TIME, LOCK_TIMEOUT, lockKey);
                if (lockAcquired) {
                    processTaskWithContext(aiTask, message);
                } else {
                    handleLockFailure(aiTask, message);
                }
            } finally {
                if (lockAcquired) {
                    try {
                        redissonUtils.clearLock(redissonClient, lockKey);
                    } catch (Exception e) {
                        log.error("释放锁失败: {}", lockKey, e);
                    }
                }
            }
        } catch (Exception e) {
            log.error(PROCESSING_ERROR_MSG, message.getMessageId(), e.getMessage(), e);
            deleteMessage(message);
        }
    }

    private boolean isTaskValid(String taskId) {
        AnalyticsAiTaskHistoryPO history = aiTaskManager.getTaskById(taskId);
        return history != null && 
               AsyncTaskStatusEnum.Ready.getValue().equals(history.getAsyncStatus());
    }

    private void handleMaxRetryReached(AiTaskBO aiTask, Message message) {
        log.warn("任务 [{}] 重试次数超过限制 {}, 放弃处理", aiTask.getTaskId(), MAX_RETRY_COUNT);
        aiTaskManager.updateTaskStatus(aiTask.getTaskId(), AsyncTaskStatusEnum.Failed);
        deleteMessage(message);
    }

    private void handleLockFailure(AiTaskBO aiTask, Message message) {
        log.info("用户 [{}] 的任务正在处理中, 重新入队 taskId [{}]", aiTask.getUserId(), aiTask.getTaskId());
        deleteMessage(message);
        requeueMessage(aiTask);
    }

    private void requeueMessage(AiTaskBO aiTask) {
        try {
            aiTask.setRetryCount(aiTask.getRetryCount() + 1);
            String json = SimpleJsonMapper.writeValue(aiTask);
            sqsClient.sendMessage(sqsName, json);
        } catch (Exception e) {
            log.error("消息重新入队失败: {}", aiTask.getTaskId(), e);
        }
    }

    private void processTaskWithContext(AiTaskBO aiTask, Message message) {
        try {
            // 设置上下文
            setContext(aiTask);
            
            log.info("开始处理用户 [{}] 的任务: [{}]", aiTask.getUserId(), aiTask.getTaskId());
            aiTranslationManager.translationAsync(aiTask.getTaskId());
            log.info("完成处理用户 [{}] 的任务: [{}]", aiTask.getUserId(), aiTask.getTaskId());
            
            deleteMessage(message);
        } catch (Exception e) {
            log.error("任务处理失败: {}", aiTask.getTaskId(), e);
            aiTaskManager.updateTaskStatus(aiTask.getTaskId(), AsyncTaskStatusEnum.Failed);
            deleteMessage(message);
        } finally {
            // 清理上下文
            clearContext();
        }
    }

    private void setContext(AiTaskBO aiTask) {
        CorrelationIdHolder.set(aiTask.getCorrelationId());
        UserIdHolder.set(aiTask.getUserId());
        SessionIdHolder.set(aiTask.getSessionId());
        RoleIdsHolder.set(aiTask.getRoleIds());
    }

    private void clearContext() {
        UserIdHolder.remove();
        RoleIdsHolder.remove();
        SessionIdHolder.remove();
        CorrelationIdHolder.remove();
    }

    /**
     * 获取消息
     *
     * @return
     */
    private Message getMessage() {
        ReceiveMessageResult receiveMessageResult = sqsClient.receiveMessage(sqsName);
        List<Message> messages = receiveMessageResult.getMessages();
        if (CollectionUtils.isEmpty(messages)) {
            return null;
        }
        return messages.get(0);
    }

    /**
     * 删除消息
     *
     * @param message 待删除的消息
     */
    private void deleteMessage(Message message) {
        if (message == null) {
            return;
        }
        sqsClient.deleteMessage(sqsName, message.getReceiptHandle());
    }

}

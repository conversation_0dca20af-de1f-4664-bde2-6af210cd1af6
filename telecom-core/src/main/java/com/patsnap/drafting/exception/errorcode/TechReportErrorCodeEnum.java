package com.patsnap.drafting.exception.errorcode;

import lombok.Getter;

/**
 * 技术简报相关的错误码
 */
@Getter
public enum TechReportErrorCodeEnum implements ErrorCode {
    
    INTENT_INVALID(140001, "errors.com.patsnap.drafting.tech_report.intent_invalid", "intent type is invalid, only support TechKeyword, CompanyName, TechDomainCompanies"),
    TECH_REPORT_NOT_EXIST(140002, "errors.com.patsnap.drafting.tech_report.not_exist", "tech report not exist"),
    TECH_REPORT_CREATE_FAILED(140003, "errors.com.patsnap.drafting.tech_report.create_failed", "tech report create failed"),
    TECH_REPORT_QUERY_FAILED(140004, "errors.com.patsnap.drafting.tech_report.query_failed", "tech report query failed"),
    REQUIRED_FIELDS_MISSING(140005, "errors.com.patsnap.drafting.tech_report.required_fields_missing", "required fields are missing") ;
    
    private final int numericErrCode;
    private final String errCode;
    private final String pattern;

    TechReportErrorCodeEnum(int numericErrCode, String errCode, String pattern) {
        this.numericErrCode = numericErrCode;
        this.errCode = errCode;
        this.pattern = pattern;
    }
} 
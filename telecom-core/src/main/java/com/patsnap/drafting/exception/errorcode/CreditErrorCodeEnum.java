package com.patsnap.drafting.exception.errorcode;

import lombok.Getter;

/**
 * 积分相关的错误码
 */
@Getter
public enum CreditErrorCodeEnum implements ErrorCode {
    
    CREDIT_EXCEED_LIMIT(100006, "errors.com.patsnap.drafting.credit.exceed_limit", "credit exceed limit"),
    EXCEED_LIMIT(100007, "errors.com.patsnap.drafting.exceed_limit", "exceed limit"),
    ;
    
    private final int numericErrCode;
    private final String errCode;
    private final String pattern;

    CreditErrorCodeEnum(int numericErrCode, String errCode, String pattern) {
        this.numericErrCode = numericErrCode;
        this.errCode = errCode;
        this.pattern = pattern;
    }
} 
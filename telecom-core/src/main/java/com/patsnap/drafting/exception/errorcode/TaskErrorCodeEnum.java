package com.patsnap.drafting.exception.errorcode;

import lombok.Getter;

/**
 * 任务相关的 error code
 */
@Getter
public enum TaskErrorCodeEnum implements ErrorCode {
    
    TASK_NOT_EXIST(110001, "errors.com.patsnap.drafting.task.not_exist", "task not existed"),
    TASK_ALREADY_EXIST(110002, "errors.com.patsnap.drafting.task.has_exist", "task has existed"),
    TASK_TYPE_NOT_EXIST(110003, "errors.com.patsnap.drafting.task_type.not_exist", "task_type not existed"),
    TASK_NO_PERMISSION_ACCESS(110004, "errors.com.patsnap.drafting.task.no_permission", "have no permission"),
    TASK_DATA_VERSION_HAS_EXPIRED(110005, "errors.com.patsnap.drafting.task.data_version_expired", "data version has expired"),
    ;
    
    
    private final int numericErrCode;

    private final String errCode;

    private final String pattern;


    TaskErrorCodeEnum(int numericErrCode, String errCode, String pattern) {
        this.numericErrCode = numericErrCode;
        this.errCode = errCode;
        this.pattern = pattern;
    }
}

package com.patsnap.drafting.exception.errorcode;

import lombok.Getter;

/**
 * 积分相关的错误码
 */
@Getter
public enum ClientErrorCodeEnum implements ErrorCode {
    
    CLIENT_NOT_AVAILABLE(130001, "errors.com.patsnap.drafting.client.not_available", "client not available");
    
    private final int numericErrCode;
    private final String errCode;
    private final String pattern;

    ClientErrorCodeEnum(int numericErrCode, String errCode, String pattern) {
        this.numericErrCode = numericErrCode;
        this.errCode = errCode;
        this.pattern = pattern;
    }
} 
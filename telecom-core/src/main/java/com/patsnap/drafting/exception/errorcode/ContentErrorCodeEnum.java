package com.patsnap.drafting.exception.errorcode;

import lombok.Getter;

/**
 * 内容相关的错误码
 */
@Getter
public enum ContentErrorCodeEnum implements ErrorCode {
    
    CONTENT_EMPTY(120001, "errors.com.patsnap.drafting.content.empty", "content cannot be empty"),
    TASK_ID_EMPTY(120002, "errors.com.patsnap.drafting.content.task_id_empty", "task id cannot be empty"),
    CONTENT_TYPE_INVALID(120003, "errors.com.patsnap.drafting.content.type_invalid", "content type is invalid"),
    OPERATION_TOO_FREQUENT(120004, "errors.com.patsnap.drafting.content.operation_too_frequent", "operation too frequent, please try again later"),
    SYSTEM_BUSY(120005, "errors.com.patsnap.drafting.content.system_busy", "system is busy, please try again later"),
    SAVE_CONTENT_FAILED(120006, "errors.com.patsnap.drafting.content.save_failed", "failed to save content"),
    SENSITIVE_WORDS_EXIST(120007, "errors.com.patsnap.drafting.content.sensitive_words_exist", "sensitive words exist"),
    OPERATION_TYPE_INVALID(120008, "errors.com.patsnap.drafting.operation.type_invalid", "operation type is invalid"),
    INPUT_TOO_LONG(120009, "errors.com.patsnap.drafting.content.input", "input is too long"),
    TERM_IMPORT_FAILED(120010, "errors.com.patsnap.drafting.content.term.import", "Failed to import terms from file"),
    TERM_INVALID_EXCEL_FORMAT(120011, "errors.com.patsnap.drafting.content.term.invalid_excel_format", "The Excel format is incorrect. Chinese, and English columns must be included."),
    TERM_INPUT_VALUE_EXCEEDS_LIMIT(120012, "errors.com.patsnap.drafting.content.term.input_value_exceeds_limit", "The values in the input exceed the maximum limit."),
    LANG_NOT_MATCH(120013, "errors.com.patsnap.drafting.content.lang_not_match", "input lang not match"),
    INPUT_INVALID(120014, "errors.com.patsnap.drafting.content.input_invalid", "invalid input")
    ;
    
    private final int numericErrCode;
    private final String errCode;
    private final String pattern;

    ContentErrorCodeEnum(int numericErrCode, String errCode, String pattern) {
        this.numericErrCode = numericErrCode;
        this.errCode = errCode;
        this.pattern = pattern;
    }
} 
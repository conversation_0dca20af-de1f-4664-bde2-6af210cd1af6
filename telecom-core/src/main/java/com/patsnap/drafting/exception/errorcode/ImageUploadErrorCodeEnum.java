package com.patsnap.drafting.exception.errorcode;

import lombok.Getter;

/**
 * 图片上传相关的错误码
 * 
 * <AUTHOR> Assistant
 * @date 2024/12/19
 */
@Getter
public enum ImageUploadErrorCodeEnum implements ErrorCode {
    
    // 文件相关错误 (140001-140020)
    FILE_EMPTY(140001, "errors.com.patsnap.drafting.image.file_empty", "上传的文件为空"),
    FILE_TOO_LARGE(140002, "errors.com.patsnap.drafting.image.file_too_large", "文件大小超过限制，最大支持1MB"),
    FILE_COUNT_EXCEEDED(140003, "errors.com.patsnap.drafting.image.file_count_exceeded", "文件数量超过限制，最多支持20个文件"),
    FILE_NAME_INVALID(140004, "errors.com.patsnap.drafting.image.file_name_invalid", "文件名无效"),
    FILE_TYPE_NOT_SUPPORTED(140005, "errors.com.patsnap.drafting.image.file_type_not_supported", "不支持的文件类型，仅支持jpg、jpeg、png格式"),
    FILE_CORRUPTED(140006, "errors.com.patsnap.drafting.image.file_corrupted", "文件已损坏，无法读取"),
    
    // 上传相关错误 (140021-140040)
    UPLOAD_FAILED(140021, "errors.com.patsnap.drafting.image.upload_failed", "文件上传失败"),
    UPLOAD_TIMEOUT(140022, "errors.com.patsnap.drafting.image.upload_timeout", "文件上传超时"),
    STORAGE_UNAVAILABLE(140023, "errors.com.patsnap.drafting.image.storage_unavailable", "存储服务不可用"),
    STORAGE_QUOTA_EXCEEDED(140024, "errors.com.patsnap.drafting.image.storage_quota_exceeded", "存储空间配额已满"),
    
    // 参数相关错误 (140041-140060)
    REQUEST_PARAM_INVALID(140041, "errors.com.patsnap.drafting.image.request_param_invalid", "请求参数无效"),
    FOLDER_PATH_INVALID(140042, "errors.com.patsnap.drafting.image.folder_path_invalid", "文件夹路径无效"),
    FILE_PREFIX_INVALID(140043, "errors.com.patsnap.drafting.image.file_prefix_invalid", "文件前缀无效"),
    
    // 权限相关错误 (140061-140080)
    UPLOAD_PERMISSION_DENIED(140061, "errors.com.patsnap.drafting.image.upload_permission_denied", "没有上传权限"),
    USER_NOT_AUTHENTICATED(140062, "errors.com.patsnap.drafting.image.user_not_authenticated", "用户未认证"),
    
    // 系统相关错误 (140081-140100)
    SYSTEM_ERROR(140081, "errors.com.patsnap.drafting.image.system_error", "系统内部错误"),
    SERVICE_UNAVAILABLE(140082, "errors.com.patsnap.drafting.image.service_unavailable", "图片上传服务暂时不可用"),
    NETWORK_ERROR(140083, "errors.com.patsnap.drafting.image.network_error", "网络连接错误"),
    ;
    
    private final int numericErrCode;
    private final String errCode;
    private final String pattern;

    ImageUploadErrorCodeEnum(int numericErrCode, String errCode, String pattern) {
        this.numericErrCode = numericErrCode;
        this.errCode = errCode;
        this.pattern = pattern;
    }
} 
package com.patsnap.drafting.exception;

import com.patsnap.common.exception.ForbiddenException;
import com.patsnap.drafting.exception.errorcode.ErrorCode;

import java.util.Map;

/**
 * biz exception
 *
 * <p>业务逻辑异常</p>
 */
public class BizException extends ForbiddenException {

    public BizException(ErrorCode errorCode, Object... args) {
        super(errorCode.getNumericErrCode(), errorCode.getErrCode(), errorCode.getPattern(), args);
    }
    
    public BizException(ErrorCode errorCode, Map<String, Object> errorParams, Object... args) {
        super(errorCode.getNumericErrCode(), errorCode.getErrCode(), errorCode.getPattern(), args);
        setErrorParams(errorParams);
    }
}

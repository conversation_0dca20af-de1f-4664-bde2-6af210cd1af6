package com.patsnap.drafting.repository.creditusage.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.patsnap.common.mybatis.entity.AbstractVersionEntity;
import lombok.*;

/**
 * 用户积分使用明细表
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
// @TableName(value = "analytics_credit_usage_detail", autoResultMap = true)
public class AnalyticsCreditUsageDetailPO extends AbstractVersionEntity<String> {

    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_UUID)
    @TableField("pk")
    private String pk;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private String userId;

    /**
     * 是否是系统行为
     */
    @TableField("if_system")
    private Boolean ifSystem;

    /**
     * 任务ID
     */
    @TableField("task_id")
    private String taskId;

    /**
     * 任务类型(查新检索，专利说明书，专利交底书，专利翻译)
     */
    @TableField("task_type")
    private String taskType;

    /**
     * 任务内容类型
     */
    @TableField("content_type")
    private String contentType;

    /**
     * 积分类型(积分值还是次数)
     */
    @TableField("credit_type")
    private String creditType;

    /**
     * 积分数值
     */
    @TableField("credit_value")
    private Integer creditValue;
}

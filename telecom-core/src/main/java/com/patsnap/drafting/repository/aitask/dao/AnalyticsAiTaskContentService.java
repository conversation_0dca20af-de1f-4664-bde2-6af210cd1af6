package com.patsnap.drafting.repository.aitask.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.patsnap.drafting.enums.task.AiTaskContentTypeEnum;
import com.patsnap.drafting.repository.aitask.entity.AnalyticsAiTaskContentPO;

import java.util.List;

/**
 * AI任务文本内容 Service 接口
 *
 * <AUTHOR>
 * @date 2024/01/24
 */
public interface AnalyticsAiTaskContentService extends IService<AnalyticsAiTaskContentPO> {
    
    /**
     * 根据任务ID和内容类型获取内容
     *
     * @param taskId 任务ID
     * @param contentType 内容类型
     * @return 内容对象
     */
    AnalyticsAiTaskContentPO getContent(String taskId, AiTaskContentTypeEnum contentType);
    
    /**
     * 根据任务ID获取内容列表
     *
     * @param taskId 任务ID
     * @return 内容列表
     */
    List<AnalyticsAiTaskContentPO> getAllContentsByTaskId(String taskId);
    
    /**
     * 保存或更新内容
     *
     * @param content 内容对象
     */
    void saveOrUpdateContent(AnalyticsAiTaskContentPO content);
    
    /**
     * 删除内容
     *
     * @param taskId  任务ID
     * @param contentTypes  内容列表
     */
    void deleteContent(String taskId, List<AiTaskContentTypeEnum> contentTypes);

    /**
     * 删除任务所有内容
     *
     * @param taskId  任务ID
     */
    void deleteAllContent(String taskId);
} 
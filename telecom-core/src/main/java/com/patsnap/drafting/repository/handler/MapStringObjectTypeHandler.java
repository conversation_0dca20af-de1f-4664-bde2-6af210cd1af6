package com.patsnap.drafting.repository.handler;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.postgresql.util.PGobject;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * <AUTHOR>
 * @date 2024/07/04
 */
public class MapStringObjectTypeHandler extends BaseTypeHandler<Map<String, Object>> {

    private static final ObjectMapper mapper = new ObjectMapper();
    private static final JavaType type = mapper.getTypeFactory()
            .constructParametricType(HashMap.class, String.class, Object.class);

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, Map<String, Object> parameter, JdbcType jdbcType)
            throws SQLException {
        try {
            PGobject pGobject = new PGobject();
            pGobject.setType("jsonb");
            pGobject.setValue(mapper.writeValueAsString(parameter));
            ps.setObject(i, pGobject);
        } catch (JsonProcessingException e) {
            throw new SQLException(e);
        }
    }

    @Override
    public Map<String, Object> getNullableResult(ResultSet rs, String columnName) throws SQLException {
        try {
            String json = rs.getString(columnName);
            if (json != null) {
                return mapper.readValue(json, type);
            }
        } catch (JsonProcessingException e) {
            throw new SQLException(e);
        }
        return null;
    }

    @Override
    public Map<String, Object> getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        try {
            String json = rs.getString(columnIndex);
            if (json != null) {
                return mapper.readValue(json, type);
            }
        } catch (JsonProcessingException e) {
            throw new SQLException(e);
        }
        return null;
    }

    @Override
    public Map<String, Object> getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        try {
            String json = cs.getString(columnIndex);
            if (json != null) {
                return mapper.readValue(json, type);
            }
        } catch (JsonProcessingException e) {
            throw new SQLException(e);
        }
        return null;
    }
}
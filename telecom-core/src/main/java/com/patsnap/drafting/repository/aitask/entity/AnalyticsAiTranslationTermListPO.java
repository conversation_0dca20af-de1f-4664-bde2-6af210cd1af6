package com.patsnap.drafting.repository.aitask.entity;

import com.patsnap.common.mybatis.entity.AbstractVersionEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * AI翻译术语
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "analytics_ai_translation_term_list", autoResultMap = true)
public class AnalyticsAiTranslationTermListPO extends AbstractVersionEntity<String> {
    /**
     * 术语表唯一标识符
     */
    private String id;
    
    /**
     * 标题
     */
    private String title;
    
    /**
     * 可见范围，仅对用户自己可见，其他用户不可见
     */
    private String scope;

    /**
     * 删除标记位
     */
    private Boolean deleted = false;

    /**
     * 术语对
     */
    private String data;

    /**
     * 所属人
     */
    private String owner;
} 
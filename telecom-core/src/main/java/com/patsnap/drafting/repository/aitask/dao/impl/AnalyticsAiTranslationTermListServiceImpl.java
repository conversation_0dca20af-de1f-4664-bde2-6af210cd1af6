package com.patsnap.drafting.repository.aitask.dao.impl;

import com.patsnap.drafting.repository.aitask.dao.AnalyticsAiTranslationTermListService;
import com.patsnap.drafting.repository.aitask.dao.mapper.AnalyticsAiTranslationTermListMapper;
import com.patsnap.drafting.repository.aitask.entity.AnalyticsAiTranslationTermListPO;

import java.util.List;

import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


@Service
public class AnalyticsAiTranslationTermListServiceImpl extends
        ServiceImpl<AnalyticsAiTranslationTermListMapper, AnalyticsAiTranslationTermListPO> implements
        AnalyticsAiTranslationTermListService {
    
    @Override
    public List<AnalyticsAiTranslationTermListPO> getTermByUser(String creator) {
        LambdaQueryWrapper<AnalyticsAiTranslationTermListPO> queryWrapper = new LambdaQueryWrapper<AnalyticsAiTranslationTermListPO>()
                .eq(AnalyticsAiTranslationTermListPO::getOwner, creator);
        return list(queryWrapper);
    }
}

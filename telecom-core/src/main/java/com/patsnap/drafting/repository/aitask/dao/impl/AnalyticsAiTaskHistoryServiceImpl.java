package com.patsnap.drafting.repository.aitask.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.patsnap.drafting.repository.aitask.dao.AnalyticsAiTaskHistoryService;
import com.patsnap.drafting.repository.aitask.dao.mapper.AnalyticsAiTaskHistoryMapper;
import com.patsnap.drafting.repository.aitask.entity.AnalyticsAiTaskHistoryPO;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/07/04
 */
@Service
public class AnalyticsAiTaskHistoryServiceImpl extends
        ServiceImpl<AnalyticsAiTaskHistoryMapper, AnalyticsAiTaskHistoryPO> implements AnalyticsAiTaskHistoryService {
    
    @Override
    public AnalyticsAiTaskHistoryPO getTaskByTaskId(String taskId) {
        LambdaQueryWrapper<AnalyticsAiTaskHistoryPO> queryWrapper = new LambdaQueryWrapper<AnalyticsAiTaskHistoryPO>()
                .eq(AnalyticsAiTaskHistoryPO::getId, taskId);
        return getOne(queryWrapper, false);
    }
    
    @Override
    public List<AnalyticsAiTaskHistoryPO> getTasksByType(String type) {
        LambdaQueryWrapper<AnalyticsAiTaskHistoryPO> queryWrapper = new LambdaQueryWrapper<AnalyticsAiTaskHistoryPO>()
                .eq(AnalyticsAiTaskHistoryPO::getType, type);
        return list(queryWrapper);
    }
}

package com.patsnap.drafting.repository.share.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.patsnap.drafting.repository.aitask.entity.AnalyticsAiTaskHistoryPO;
import com.patsnap.drafting.repository.share.dao.mapper.AiTaskShareLinkMapper;
import com.patsnap.drafting.repository.share.entity.AiTaskShareLinkPO;
import com.patsnap.drafting.repository.share.service.AiTaskShareLinkService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Objects;

/**
 * AI任务分享链接服务实现类
 */
@Slf4j
@Service
public class AiTaskShareLinkServiceImpl extends ServiceImpl<AiTaskShareLinkMapper, AiTaskShareLinkPO>
        implements AiTaskShareLinkService {

    @Override
    public AiTaskShareLinkPO createShareLink(AiTaskShareLinkPO aiTaskShareLinkPO) {
        // 设置默认值
        if (Objects.isNull(aiTaskShareLinkPO.getPublicShare())) {
            aiTaskShareLinkPO.setPublicShare(true);
        }
        if (Objects.isNull(aiTaskShareLinkPO.getActive())) {
            aiTaskShareLinkPO.setActive(true);
        }
        // 保存实体
        save(aiTaskShareLinkPO);
        return aiTaskShareLinkPO;
    }

    @Override
    public boolean validateShareLink(String shareId, String password) {
        AiTaskShareLinkPO po = getById(shareId);
        if (Objects.isNull(po)) {
            return false;
        }
        
        // 检查链接是否激活
        if (!Boolean.TRUE.equals(po.getActive())) {
            return false;
        }
        
        // 检查是否过期
        if (isExpired(shareId)) {
            return false;
        }
        
        // 如果是公开分享，直接返回true
        if (Boolean.TRUE.equals(po.getPublicShare())) {
            return true;
        }
        
        // 检查密码
        return StringUtils.hasText(password) && password.equals(po.getPassword());
    }

    @Override
    public AiTaskShareLinkPO getShareLinkByShareId(String shareId) {
        if (!StringUtils.hasText(shareId)) {
            return null;
        }
        return getById(shareId);
    }
    
    @Override
    public AiTaskShareLinkPO getShareLinkByTaskId(String taskId) {
        if (!StringUtils.hasText(taskId)) {
            return null;
        }
        LambdaQueryWrapper<AiTaskShareLinkPO> queryWrapper = new LambdaQueryWrapper<AiTaskShareLinkPO>()
                .eq(AiTaskShareLinkPO::getResourceId, taskId);
        return getOne(queryWrapper, false);
    }

    @Override
    public boolean updateShareLinkStatus(String shareId, boolean active) {
        AiTaskShareLinkPO po = getById(shareId);
        if (Objects.isNull(po)) {
            return false;
        }
        
        po.setActive(active);
        return updateById(po);
    }

    @Override
    public boolean isExpired(String shareId) {
        AiTaskShareLinkPO po = getById(shareId);
        if (Objects.isNull(po) || Objects.isNull(po.getExpiresAt())) {
            return true;
        }
        return po.getExpiresAt().isBeforeNow();
    }
} 
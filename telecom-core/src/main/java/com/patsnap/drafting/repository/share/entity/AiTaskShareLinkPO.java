package com.patsnap.drafting.repository.share.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.patsnap.common.mybatis.entity.AbstractVersionEntity;
import com.patsnap.common.mybatis.handler.LongToDateTimeTypeHandler;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.joda.time.DateTime;

/**
 * AI任务分享链接实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("ai_task_share_link")
public class AiTaskShareLinkPO extends AbstractVersionEntity<String> {

    /**
     * 分享ID
     */
    @TableId(type = IdType.ASSIGN_UUID)
    @TableField("share_id")
    private String shareId;

    /**
     * 资源类型
     */
    @TableField("resource_type")
    private String resourceType;

    /**
     * 资源ID
     */
    @TableField("resource_id")
    private String resourceId;

    /**
     * 是否公开分享
     */
    @TableField("public_share")
    private Boolean publicShare;

    /**
     * 过期时间
     */
    @TableField(value = "expires_at", typeHandler = LongToDateTimeTypeHandler.class)
    private DateTime expiresAt;

    /**
     * 访问密码
     */
    @TableField("password")
    private String password;

    /**
     * 角色
     */
    @TableField("role")
    private String role;

    /**
     * 是否激活
     */
    @TableField("active")
    private Boolean active;
} 

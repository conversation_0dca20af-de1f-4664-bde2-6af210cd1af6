package com.patsnap.drafting.repository.aitask.entity;


import com.patsnap.analytics.repository.BaseEntityPO;

import java.time.LocalDateTime;

import org.apache.ibatis.type.LocalDateTimeTypeHandler;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/1/8
 */
@TableName("analytics_copilot_history")
@Data
public class CopilotHistoryPO {

    private String product;
    private String module;
    private String question;
    private String answer;
    /**
     * 识别出来的问题意图
     */
    private String intent;
    private String companyId;
    /**
     * 识别出来的问题意图
     */
    private String pageParam;
    /**
     * 页面真实的检索参数
     */
    private String query;
    /**
     * LIKE:赞，DISLIKE:踩
     */
    private String type;
    /**
     * 评论
     */
    private String comment;

    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    private String createdBy;
    private String updatedBy;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
}

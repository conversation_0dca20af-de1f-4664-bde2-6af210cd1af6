package com.patsnap.drafting.repository.creditusage.dao.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.patsnap.drafting.enums.task.AiTaskContentTypeEnum;
import com.patsnap.drafting.repository.creditusage.dao.mapper.AnalyticsCreditUsageDetailMapper;
import com.patsnap.drafting.repository.creditusage.service.AnalyticsCreditUsageDetailService;
import com.patsnap.drafting.repository.creditusage.entity.AnalyticsCreditUsageDetailPO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.UUID;

@Slf4j
@Service
public class AnalyticsCreditUsageDetailServiceImpl extends ServiceImpl<AnalyticsCreditUsageDetailMapper, AnalyticsCreditUsageDetailPO>
        implements AnalyticsCreditUsageDetailService {

    @Override
    public void createCreditUsageDetail(AnalyticsCreditUsageDetailPO creditUsageDetailPO) {
        // 保存实体
        save(creditUsageDetailPO);
    }

    @Override
    public void createCreditUsageDetail(String taskId, String taskType, String userId, boolean ifSystem, String creditType, int creditValue, AiTaskContentTypeEnum contentTypeEnum) {
        // 创建新的实体对象
        AnalyticsCreditUsageDetailPO creditUsageDetailPO = new AnalyticsCreditUsageDetailPO();
        creditUsageDetailPO.setPk(UUID.randomUUID().toString());
        creditUsageDetailPO.setUserId(userId);
        creditUsageDetailPO.setIfSystem(ifSystem);
        creditUsageDetailPO.setTaskId(taskId);
        creditUsageDetailPO.setTaskType(taskType);
        if(Objects.nonNull(contentTypeEnum)) {
            creditUsageDetailPO.setContentType(contentTypeEnum.getType());
        }
        creditUsageDetailPO.setCreditType(creditType);
        creditUsageDetailPO.setCreditValue(creditValue);

        // 保存实体
        save(creditUsageDetailPO);
    }
}

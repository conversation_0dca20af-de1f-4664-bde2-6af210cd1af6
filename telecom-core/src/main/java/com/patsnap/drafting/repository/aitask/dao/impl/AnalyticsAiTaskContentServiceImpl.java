package com.patsnap.drafting.repository.aitask.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.patsnap.drafting.enums.task.AiTaskContentTypeEnum;
import com.patsnap.drafting.enums.task.TaskContentDirectionEnum;
import com.patsnap.drafting.repository.aitask.dao.AnalyticsAiTaskContentService;
import com.patsnap.drafting.repository.aitask.dao.mapper.AnalyticsAiTaskContentMapper;
import com.patsnap.drafting.repository.aitask.entity.AnalyticsAiTaskContentPO;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * AI任务文本内容 Service 实现类
 *
 * <AUTHOR>
 * @date 2024/01/24
 */
@Service
public class AnalyticsAiTaskContentServiceImpl 
    extends ServiceImpl<AnalyticsAiTaskContentMapper, AnalyticsAiTaskContentPO>
    implements AnalyticsAiTaskContentService {
    
    @Override
    public AnalyticsAiTaskContentPO getContent(String taskId, AiTaskContentTypeEnum contentType) {
        LambdaQueryWrapper<AnalyticsAiTaskContentPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AnalyticsAiTaskContentPO::getTaskId, taskId)
               .eq(AnalyticsAiTaskContentPO::getContentType, contentType.getType());
        return getOne(wrapper);
    }
    
    @Override
    public List<AnalyticsAiTaskContentPO> getAllContentsByTaskId(String taskId) {
        LambdaQueryWrapper<AnalyticsAiTaskContentPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AnalyticsAiTaskContentPO::getTaskId, taskId);
        return list(wrapper);
    }
    
    @Override
    public void saveOrUpdateContent(AnalyticsAiTaskContentPO content) {
        if (content == null) {
            return;
        }
        AnalyticsAiTaskContentPO contentPO = getContent(content.getTaskId(), AiTaskContentTypeEnum.fromType(content.getContentType()));
        if (contentPO != null) {
            update(content);
        } else {
            save(content);
        }
    }
    
    private void update(AnalyticsAiTaskContentPO content) {
        LambdaUpdateWrapper<AnalyticsAiTaskContentPO> updateWrapper = Wrappers.<AnalyticsAiTaskContentPO>lambdaUpdate()
                .eq(AnalyticsAiTaskContentPO::getTaskId, content.getTaskId())
                .eq(AnalyticsAiTaskContentPO::getContentType, content.getContentType());
        update(content, updateWrapper);
    }
    
    public void deleteContent(String taskId, List<AiTaskContentTypeEnum> contentTypes) {
        LambdaUpdateWrapper<AnalyticsAiTaskContentPO> updateWrapper = Wrappers.<AnalyticsAiTaskContentPO>lambdaUpdate()
                .eq(AnalyticsAiTaskContentPO::getTaskId, taskId)
                .eq(AnalyticsAiTaskContentPO::getDirection, TaskContentDirectionEnum.OUTPUT.getValue());
        if (CollectionUtils.isEmpty(contentTypes)) {
            remove(updateWrapper);
            return;
        }
        updateWrapper
                .in(AnalyticsAiTaskContentPO::getContentType, contentTypes.stream().map(AiTaskContentTypeEnum::getType).toArray());
        remove(updateWrapper);
    }
    public void deleteAllContent(String taskId) {
        LambdaUpdateWrapper<AnalyticsAiTaskContentPO> updateWrapper = Wrappers.<AnalyticsAiTaskContentPO>lambdaUpdate()
                .eq(AnalyticsAiTaskContentPO::getTaskId, taskId)
                .eq(AnalyticsAiTaskContentPO::getDirection, TaskContentDirectionEnum.OUTPUT.getValue());
        remove(updateWrapper);
        return;
    }

} 
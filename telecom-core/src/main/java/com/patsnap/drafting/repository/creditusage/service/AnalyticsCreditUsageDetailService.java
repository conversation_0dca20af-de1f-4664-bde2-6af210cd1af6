package com.patsnap.drafting.repository.creditusage.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.patsnap.drafting.enums.task.AiTaskContentTypeEnum;
import com.patsnap.drafting.repository.creditusage.entity.AnalyticsCreditUsageDetailPO;

public interface AnalyticsCreditUsageDetailService extends IService<AnalyticsCreditUsageDetailPO> {

    /**
     * 创建积分使用明细数据
     *
     * @param creditUsageDetailPO 积分使用明细数据对象
     */
    void createCreditUsageDetail(AnalyticsCreditUsageDetailPO creditUsageDetailPO);

    void createCreditUsageDetail(String taskId, String taskType, String userId, boolean ifSystem, String creditType, int creditValue, AiTaskContentTypeEnum contentTypeEnum);
}

package com.patsnap.drafting.repository.share.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.patsnap.drafting.model.share.AiTaskShareLinkDTO;
import com.patsnap.drafting.repository.share.entity.AiTaskShareLinkPO;

/**
 * AI任务分享链接服务接口
 */
public interface AiTaskShareLinkService extends IService<AiTaskShareLinkPO> {

    /**
     * 创建分享链接
     *
     * @param po 分享链接信息
     * @return 创建后的分享链接信息
     */
    AiTaskShareLinkPO createShareLink(AiTaskShareLinkPO po);

    /**
     * 验证分享链接
     *
     * @param shareId  分享ID
     * @param password 访问密码
     * @return 验证结果
     */
    boolean validateShareLink(String shareId, String password);

    /**
     * 获取分享链接信息
     *
     * @param shareId 分享ID
     * @return 分享链接信息
     */
    AiTaskShareLinkPO getShareLinkByShareId(String shareId);
    
    /**
     * 获取分享链接信息
     *
     * @param taskId 任务ID
     * @return 分享链接信息
     */
    AiTaskShareLinkPO getShareLinkByTaskId(String taskId);

    /**
     * 更新分享链接状态
     *
     * @param shareId 分享ID
     * @param active  是否激活
     * @return 更新结果
     */
    boolean updateShareLinkStatus(String shareId, boolean active);

    /**
     * 检查链接是否过期
     *
     * @param shareId 分享ID
     * @return 是否过期
     */
    boolean isExpired(String shareId);
} 
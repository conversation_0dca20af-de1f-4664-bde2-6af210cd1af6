package com.patsnap.drafting.repository.aitask.dao;

import com.patsnap.drafting.repository.aitask.entity.AnalyticsAiTranslationTermListPO;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <AUTHOR>
 * @date 2024/07/04
 */
public interface AnalyticsAiTranslationTermListService extends IService<AnalyticsAiTranslationTermListPO> {

    /**
     * 获取术语表
     *
     * @param creator 创建者
     * @return 术语表
     */
    List<AnalyticsAiTranslationTermListPO> getTermByUser(String creator);
}

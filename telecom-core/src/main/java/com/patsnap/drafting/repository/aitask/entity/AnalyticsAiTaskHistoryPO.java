package com.patsnap.drafting.repository.aitask.entity;

import com.patsnap.common.mybatis.entity.AbstractVersionEntity;
import com.patsnap.common.mybatis.handler.IntToBooleanTypeHandler;
import com.patsnap.drafting.repository.handler.MapStringObjectTypeHandler;

import java.util.Map;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/07/04
 */
@TableName(value = "analytics_ai_task_history", autoResultMap = true)
@Data
public class AnalyticsAiTaskHistoryPO extends AbstractVersionEntity<String> {

    /**
     * 任务唯一标识符
     */
    private String id;
    /**
     * 任务标题
     */
    private String title;
    /**
     * 任务描述
     */
    @TableField("\"desc\"")
    private String desc;
    /**
     * 任务类型: AI_SEARCH
     */
    private String type;
    /**
     * 数据版本：解决功能升级后识别出无法还原的数据
     */
    private Integer dataVersion;
    /**
     * 任务类型: ASYNC|SYNC
     */
    private String taskType;
    /**
     * 异步任务执行状态: Ready|Running|Complete
     */
    private String asyncStatus;
    /**
     * 已读状态: 1-已读, 0-未读
     */
    @TableField(typeHandler = IntToBooleanTypeHandler.class)
    private Boolean readStatus;
    
    /**
     * 任务详细的历史记录内容
     * 这部分信息已经不再使用，新的任务详细信息存储在analytics_ai_task_content表中
     */
    @Deprecated
    @TableField(value = "detail", typeHandler = MapStringObjectTypeHandler.class)
    private Map<String, Object> detail;

    /**
     * 数据来源：s-core-eureka
     */
    @TableField("data_from")
    private String dataFrom;
}

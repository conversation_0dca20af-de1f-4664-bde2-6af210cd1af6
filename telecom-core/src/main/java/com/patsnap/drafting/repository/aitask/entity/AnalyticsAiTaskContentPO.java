package com.patsnap.drafting.repository.aitask.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.patsnap.common.mybatis.entity.AbstractVersionEntity;

import com.patsnap.core.common.bizsecurity.annotation.EncryptColumn;
import com.patsnap.core.common.bizsecurity.annotation.EncryptTable;
import com.patsnap.core.common.bizsecurity.handler.EncryptTypeHandler;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * AI任务中的文本信息
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "analytics_ai_task_content", autoResultMap = true)
@EncryptTable(
    tableName = "analytics_ai_task_content",
    uniqueFields = {"task_id", "content_type"},
    versionField = "updated_at",
    description = "AI任务内容表"
)
public class AnalyticsAiTaskContentPO extends AbstractVersionEntity<String> {
    
    /**
     * 任务ID
     */
    private String taskId;
    
    /**
     * 内容标识
     */
    private String contentType;
    
    /**
     * 段落内容
     */
    @EncryptColumn("用户内容数据")
    @TableField(typeHandler = EncryptTypeHandler.class)
    private String content;
    
    /**
     * 隐藏标识
     */
    private Boolean hide;
    
    /**
     * 方向标识（用户输入或内容输出）
     */
    private String direction;
} 
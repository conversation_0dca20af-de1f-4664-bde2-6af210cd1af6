package com.patsnap.drafting.repository.aitask.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.patsnap.drafting.repository.aitask.entity.AnalyticsAiTaskHistoryPO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/07/04
 */
public interface AnalyticsAiTaskHistoryService extends IService<AnalyticsAiTaskHistoryPO> {
    
    /**
     * 获取任务
     *
     * @param taskId 任务 ID
     * @return 任务
     */
    AnalyticsAiTaskHistoryPO getTaskByTaskId(String taskId);
    
    /**
     * 根据任务类型查询所有任务
     *
     * @param type 任务类型
     * @return 任务列表
     */
    List<AnalyticsAiTaskHistoryPO> getTasksByType(String type);
}

package com.patsnap.drafting.transfer.export.handler.impl;

import cn.hutool.core.io.file.FileNameUtil;
import cn.hutool.core.text.StrPool;
import com.alibaba.fastjson.JSONObject;
import com.patsnap.common.exception.InternalServerErrorException;
import com.patsnap.common.request.UserIdHolder;
import com.patsnap.core.common.utils.MapUtils;
import com.patsnap.core.spring.boot.autoconfigure.web.entity.CommonResponse;
import com.patsnap.drafting.client.model.PatentClaimTreeResponse;
import com.patsnap.drafting.client.model.PatentLegalStatusResponse;
import com.patsnap.drafting.enums.common.RiskLevelEnum;
import com.patsnap.drafting.enums.task.AiTaskContentTypeEnum;
import com.patsnap.drafting.enums.task.AiTaskTypeEnum;
import com.patsnap.drafting.manager.FileManager;
import com.patsnap.drafting.manager.aitask.AiTaskManager;
import com.patsnap.drafting.manager.patent.PatentInfoManager;
import com.patsnap.drafting.request.ainoveltysearch.*;
import com.patsnap.drafting.request.patentinfo.PatentInfoRequestDTO;
import com.patsnap.drafting.response.aiftosearch.FtoSearchReportTitleResponseDTO;
import com.patsnap.drafting.response.ainoveltysearch.FeatureComparisonFinalResClaim;
import com.patsnap.drafting.response.ainoveltysearch.FeatureExactionResponseDTO;
import com.patsnap.drafting.transfer.export.enitty.ExportRequest;
import com.patsnap.drafting.transfer.export.enitty.ExportResult;
import com.patsnap.drafting.transfer.export.handler.AbstractExportHandler;
import com.patsnap.drafting.util.DateUtils;
import com.patsnap.drafting.util.NoveltySearchExportUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.entity.ContentType;
import org.apache.poi.xwpf.usermodel.*;
import org.joda.time.DateTime;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Component;
import retrofit2.Response;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.*;
import java.util.stream.Collectors;

import static com.patsnap.drafting.constants.ExportConstant.*;
import static com.patsnap.drafting.util.FtoSearchExportUtils.*;
import static com.patsnap.drafting.util.NoveltySearchExportUtils.setColumnWidth;

/**
 * fto search导出
 *
 * <AUTHOR>
 * @Date 2025/4/14 15:00
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AiFtoSearchExportHandler extends AbstractExportHandler {

    private final AiTaskManager aiTaskManager;
    private final FileManager fileManager;
    private final PatentInfoManager patentInfoManager;
    private final ResourceLoader resourceLoader;

    // ==================== 报告基本信息 ====================
    public static final String REPORT_TITLE = "REPORT_TITLE";
    public static final String REPORTER_LABEL = "REPORTER_LABEL";
    public static final String REPORTER_NAME_PLACEHOLDER = "REPORTER_NAME_PLACEHOLDER";
    public static final String REPORT_TIME_LABEL = "REPORT_TIME_LABEL";
    public static final String REPORT_DATE_PLACEHOLDER = "REPORT_DATE_PLACEHOLDER";
    public static final String SEARCH_SUMMARY = "SEARCH_SUMMARY";
    // ==================== 检索相关 ====================
    public static final String SEARCH_SCOPE_TITLE = "SEARCH_SCOPE_TITLE";
    public static final String SEARCH_SCOPE_TEXT = "SEARCH_SCOPE_TEXT";
    public static final String MAIN_POINT_TITLE = "MAIN_POINT_TITLE";
    public static final String MAIN_POINT_TEXT = "MAIN_POINT_TEXT";
    public static final String TECHNICAL_FEATURE_KEYWORD_TITLE = "TECHNICAL_FEATURE_KEYWORD_TITLE";
    public static final String TECHNICAL_FEATURE_KEYWORD_TABLE = "TECHNICAL_FEATURE_KEYWORD_TABLE";
    // ==================== 技术方案相关 ====================
    public static final String TECHNICAL_FEATURE_COMPARISON_TITLE = "TECHNICAL_FEATURE_COMPARISON_TITLE";
    public static final String TECHNICAL_FEATURE_COMPARISON_TABLE = "TECHNICAL_FEATURE_COMPARISON_TABLE";
    // ==================== 附录相关 ====================
    public static final String APPENDIX_SEARCH_SYNONYM_TITLE = "APPENDIX_SEARCH_SYNONYM_TITLE";
    public static final String APPENDIX_SEARCH_SYNONYM_TABLE = "APPENDIX_SEARCH_SYNONYM_TABLE";
    public static final String APPENDIX_SEARCH_STRATEGY_TITLE = "APPENDIX_SEARCH_STRATEGY_TITLE";
    public static final String APPENDIX_SEARCH_STRATEGY_TABLE = "APPENDIX_SEARCH_STRATEGY_TABLE";
    // ==================== 免责声明 ====================
    public static final String DISCLAIMER_TITLE = "DISCLAIMER_TITLE";
    public static final String DISCLAIMER_CONTENT = "DISCLAIMER_CONTENT";

    private static final String LANG_CN = "CN";

    @Override
    public AiTaskTypeEnum getAiTaskTypeEnum() {
        return AiTaskTypeEnum.AI_FTO_SEARCH;
    }

    @Override
    public void preExport(ExportRequest exportRequest) {
        Map<String, String> placeholderData = exportRequest.getPlaceholderData();
        // 封面
        handleTaskTitleInfo(placeholderData, exportRequest.getTaskId(), exportRequest.getLang());
        // 检索范围
        handleSearchScopeInfo(placeholderData, exportRequest.getTaskId(), exportRequest.getLang());
        // 发明要点
        handleMainPointInfo(placeholderData, exportRequest.getTaskId(), exportRequest.getLang());
        // 各个表格的固定标题
        handleTableTitle(placeholderData, exportRequest.getLang());
        // 免责声明
        handleDisclaimer(placeholderData, exportRequest.getLang());
    }

    /**
     * 获取导出模板名称（模板可能根据国家、受理局等分类，业务方提供模板后缀区分，否则使用无后缀默认模板）
     *
     * @param exportRequest 导出请求
     * @return 导出模板名称
     */
    private String getExportTemplateNameWithSuffix(ExportRequest exportRequest) {
        String templateNameSuffix = exportRequest.getTemplateNameSuffix();
        if (StringUtils.isBlank(templateNameSuffix)) {
            return exportRequest.getTemplateName();
        }
        String templateName = exportRequest.getTemplateName();
        String name = FileNameUtil.mainName(templateName);
        String templateFileType = FileNameUtil.extName(templateName);
        return name + StrPool.UNDERLINE + StringUtils.lowerCase(templateNameSuffix) + StrPool.DOT + templateFileType;
    }

    @Override
    public ExportResult customExport(ExportRequest exportRequest) {
        try {
            // 1. 先获取模板并替换占位符，生成初始的Word文档字节数组
            Resource resource = resourceLoader.getResource(EXPORT_TEMPLATE_PATH + this.getExportTemplateNameWithSuffix(exportRequest));
            if (!resource.exists()) {
                resource = resourceLoader.getResource(EXPORT_TEMPLATE_PATH + exportRequest.getTemplateName());
            }
            byte[] resultBytes = NoveltySearchExportUtils.replaceInWord(resource, exportRequest.getPlaceholderData());

            // 2. 将字节数组转换为新的Word文档，并在指定标记位添加表格
            byte[] finalBytes = addTablesToDocument(resultBytes, exportRequest.getTaskId(), exportRequest.getLang());

            // 3. 上传到S3
            String s3key = String.format(EXPORT_S3_PATH, UserIdHolder.get(), System.currentTimeMillis(), exportRequest.getFileName());
            String url = fileManager.uploadFile2AmazonS3(finalBytes, s3key, ContentType.MULTIPART_FORM_DATA);
            return ExportResult.builder().fileName(exportRequest.getFileName()).url(url).build();
        } catch (Exception e) {
            log.error("ExportManager export failed", e);
            throw new InternalServerErrorException();
        }
    }

    /**
     * 在Word文档中的指定标记位添加表格
     *
     * @param documentBytes 原始文档字节数组
     * @param taskId 任务ID
     * @param lang 语言
     * @return 添加表格后的文档字节数组
     */
    private byte[] addTablesToDocument(byte[] documentBytes, String taskId, String lang) throws Exception {
        try (ByteArrayInputStream bis = new ByteArrayInputStream(documentBytes);
             XWPFDocument document = new XWPFDocument(bis)) {

            // 查找并替换标记位，添加相应的表格
            addTablesAtMarkers(document, taskId, lang);

            // 将修改后的文档写入字节数组
            try (ByteArrayOutputStream out = new ByteArrayOutputStream()) {
                document.write(out);
                return out.toByteArray();
            }
        }
    }

    /**
     * 在文档的标记位添加表格
     *
     * @param document Word文档
     * @param taskId 任务ID
     * @param lang 语言
     */
    private void addTablesAtMarkers(XWPFDocument document, String taskId, String lang) {
        // 遍历所有段落，查找标记位
        List<XWPFParagraph> paragraphs = document.getParagraphs();
        for (int i = 0; i < paragraphs.size(); i++) {
            XWPFParagraph paragraph = paragraphs.get(i);
            String text = paragraph.getText();

            if (text == null) {
                continue;
            }

            // 根据不同的标记位添加相应的表格
            if (text.contains("${"+TECHNICAL_FEATURE_KEYWORD_TABLE+"}")) {
                XWPFParagraph titleParagraph = document.insertNewParagraph(document.getParagraphs().get(i).getCTP().newCursor());
                titleParagraph.setAlignment(ParagraphAlignment.LEFT);
                clearParagraph(paragraph);
                addTechnicalFeaturesAndKeywordsTable(document, titleParagraph, taskId, lang);
            } else if (text.contains("${"+TECHNICAL_FEATURE_COMPARISON_TABLE+"}")) {
                XWPFParagraph titleParagraph = document.insertNewParagraph(document.getParagraphs().get(i).getCTP().newCursor());
                titleParagraph.setAlignment(ParagraphAlignment.LEFT);
                clearParagraph(paragraph);
                handleTechnicalFeaturesComparisonTable(document, titleParagraph, taskId, lang);
            } else if (text.contains("${"+APPENDIX_SEARCH_SYNONYM_TABLE+"}")) {
                XWPFParagraph titleParagraph = document.insertNewParagraph(document.getParagraphs().get(i).getCTP().newCursor());
                titleParagraph.setAlignment(ParagraphAlignment.LEFT);
                clearParagraph(paragraph);
                handleAppendix1(document, titleParagraph, taskId, lang);
            } else if (text.contains("${"+APPENDIX_SEARCH_STRATEGY_TABLE+"}")) {
                XWPFParagraph titleParagraph = document.insertNewParagraph(document.getParagraphs().get(i).getCTP().newCursor());
                titleParagraph.setAlignment(ParagraphAlignment.LEFT);
                clearParagraph(paragraph);
                handleAppendix2(document, titleParagraph, taskId, lang);
            }
        }
    }

    /**
     * 清空段落内容
     */
    private void clearParagraph(XWPFParagraph paragraph) {
        // 清除所有runs
        for (int j = paragraph.getRuns().size() - 1; j >= 0; j--) {
            paragraph.removeRun(j);
        }
    }

    private void addTechnicalFeaturesAndKeywordsTable(XWPFDocument document, XWPFParagraph afterParagraph, String taskId, String lang) {
        FeatureExactionResponseDTO techFeatureInfo = aiTaskManager.getTaskContent(taskId, AiTaskContentTypeEnum.FTO_SEARCH_TECH_FEATURE);
        if(Objects.isNull(techFeatureInfo) || CollectionUtils.isEmpty(techFeatureInfo.getFeatures())) {
            return;
        }

        List<XWPFParagraph> paragraphs = document.getParagraphs();
        int titleIndex = paragraphs.indexOf(afterParagraph);

        Long rowSize = techFeatureInfo.getFeatures().stream().filter(AiSearchAgentFeature::getSelect).count() + 1L;
        Long columnSize = 2L;

        XWPFTable table = document.insertNewTbl(document.getParagraphs().get(titleIndex).getCTP().newCursor());
        table.setWidth("100%");

        // 设置行高
        for (int i = 1; i < rowSize; i++) {
            table.createRow().setHeight(TABLE_ROW_HEIGHT);
        }

        // 确保每行都有3列
        for (int i = 0; i < rowSize; i++) {
            XWPFTableRow row = table.getRow(i);
            while (row.getTableCells().size() < columnSize) {
                row.createCell();
            }
        }

        // 设置表格边框
        table.setInsideHBorder(XWPFTable.XWPFBorderType.SINGLE, 4, 0, TABLE_BORDER_COLOR_BLUE);
        table.setInsideVBorder(XWPFTable.XWPFBorderType.SINGLE, 4, 0, TABLE_BORDER_COLOR_BLUE);
        table.setTopBorder(XWPFTable.XWPFBorderType.SINGLE, 4, 0, TABLE_BORDER_COLOR_BLUE);
        table.setBottomBorder(XWPFTable.XWPFBorderType.SINGLE, 4, 0, TABLE_BORDER_COLOR_BLUE);
        table.setLeftBorder(XWPFTable.XWPFBorderType.SINGLE, 4, 0, TABLE_BORDER_COLOR_BLUE);
        table.setRightBorder(XWPFTable.XWPFBorderType.SINGLE, 4, 0, TABLE_BORDER_COLOR_BLUE);

        // 设置表头(第一行)
        String tableHeader1 = "CN".equalsIgnoreCase(lang) ? "序号" : "Number";
        String tableHeader2 = "CN".equalsIgnoreCase(lang) ? "技术特征" : "Technical features";
        setCellText(table, 0, 0, tableHeader1, false, ParagraphAlignment.LEFT, TABLE_CELL_COLOR_BLUE, "CN".equalsIgnoreCase(lang) ? "等线" :"Arial");
        setCellText(table, 0, 1, tableHeader2, false, ParagraphAlignment.LEFT, TABLE_CELL_COLOR_BLUE, "CN".equalsIgnoreCase(lang) ? "等线" :"Arial");

        // 设置表格数据内容
        int currentRowIndex = 1;
        for(AiSearchAgentFeature featureItem : techFeatureInfo.getFeatures()) {
            boolean selected = featureItem.getSelect();
            if(selected) {
                setCellText(table, currentRowIndex, 0, String.valueOf(currentRowIndex), false, ParagraphAlignment.LEFT, "", "CN".equalsIgnoreCase(lang) ? "等线" :"Arial");
                setCellText(table, currentRowIndex, 1, featureItem.getTechFeature(), false, ParagraphAlignment.LEFT, "", "CN".equalsIgnoreCase(lang) ? "等线" :"Arial");

                currentRowIndex = currentRowIndex + 1;
            }
        }

        // 调整单元格宽度
        setColumnWidth(table, 0, 20);
        setColumnWidth(table, 1, 80);

        document.createParagraph();
    }

    private void handleTechnicalFeaturesComparisonTable(XWPFDocument document, XWPFParagraph afterParagraph, String taskId, String lang) {
        // 查新agent结果final_result变量里的值（作为表格第二列以及后面的数据）
        AiSearchResultDTO aiSearchResultInfo = aiTaskManager.getTaskContent(taskId, AiTaskContentTypeEnum.FTO_SEARCH_AGENT_RESULT);
        if (Objects.isNull(aiSearchResultInfo)) {
            return;
        }

        // 每个专利(selected=true)是一个表格，每个专利的各个权利要求，只导出是否侵权=是的权利要求数据
        List<AiSearchFinalResult> finalResult = aiSearchResultInfo.getFinalResult();
        if(CollectionUtils.isEmpty(finalResult)) {
            return;
        }
        finalResult = finalResult.stream().filter(AiSearchFinalResult::isSelected).toList();
        if(CollectionUtils.isEmpty(finalResult)) {
            return;
        }
        // 批量查询专利信息
        List<String> patentIds = finalResult.stream().map(AiSearchFinalResult::getPatentId).distinct().toList();
        PatentInfoRequestDTO patentRequestDTO = new PatentInfoRequestDTO();
        patentRequestDTO.setPatentIds(patentIds);
        JSONObject patentInfoList = patentInfoManager.batchFetchPatentBasicInfo(patentRequestDTO);
        Response<PatentLegalStatusResponse> legalStatusResponse = patentInfoManager.fetchLegalStatusConfig();

        // 每个专利都是一个表格
        // 行数 = 1(专利标题) + 1(专利信息) + 2(结论标题，结论信息，只有cn有) + 1(固定标题) + 权利要求侵权特征数(如果没有侵权就显示一行文案)
        // 列数 = 4
        for(int i = 0; i < finalResult.size(); i++) {
            AiSearchFinalResult finalResultItem = finalResult.get(i);

            // 先计算出行数
            Long columnSize = 4L;
            Long rowSize = 2L + ("CN".equalsIgnoreCase(lang) ? 2L : 0L);
            boolean hasInfringement = false;
            List<ClaimEquivalentItem> claimEquivalentList = finalResultItem.getClaimEquivalentInfo().stream().filter(ClaimEquivalentItem::getInfringement).toList();
            if(CollectionUtils.isEmpty(claimEquivalentList)) {
                // 展示无侵权的固定文案，不需要展示任何的权利要求数据
                rowSize = rowSize + 1L;
            } else {
                hasInfringement = true;
                // 展示权利要求数据
                List<Integer> claimNumList = claimEquivalentList.stream().map(ClaimEquivalentItem::getClaimNum).distinct().toList();
                List<AiSearchFinalResultFeature> features = finalResultItem.getFeatures().stream()
                        .filter(item -> claimNumList.contains(item.getClaimNum()))
                        .filter(AiSearchFinalResultFeature::isSimilar)
                        .toList();
                // 1行固定标题 + 所有侵权特征个数
                rowSize = rowSize + 1L + features.size();
            }

            // 填充行列数据
            // 重新获取段落列表，因为之前的插入操作可能已经改变了文档结构
            List<XWPFParagraph> paragraphs = document.getParagraphs();
            int titleIndex = paragraphs.indexOf(afterParagraph);
            XWPFTable table = document.insertNewTbl(document.getParagraphs().get(titleIndex).getCTP().newCursor());
            table.setWidth("100%");
            for (int a = 1; a < rowSize; a++) {
                table.createRow().setHeight(TABLE_ROW_HEIGHT);;
            }
            for (int b = 0; b < rowSize; b++) {
                XWPFTableRow row = table.getRow(b);
                while (row.getTableCells().size() < columnSize) {
                    row.createCell();
                }
            }
            // 设置表格边框
            table.setInsideHBorder(XWPFTable.XWPFBorderType.SINGLE, 4, 0, TABLE_BORDER_COLOR_BLUE);
            table.setInsideVBorder(XWPFTable.XWPFBorderType.SINGLE, 4, 0, TABLE_BORDER_COLOR_BLUE);
            table.setTopBorder(XWPFTable.XWPFBorderType.SINGLE, 4, 0, TABLE_BORDER_COLOR_BLUE);
            table.setBottomBorder(XWPFTable.XWPFBorderType.SINGLE, 4, 0, TABLE_BORDER_COLOR_BLUE);
            table.setLeftBorder(XWPFTable.XWPFBorderType.SINGLE, 4, 0, TABLE_BORDER_COLOR_BLUE);
            table.setRightBorder(XWPFTable.XWPFBorderType.SINGLE, 4, 0, TABLE_BORDER_COLOR_BLUE);

            // 专利标题行
            // 专利基本信息行
            String patentId = finalResultItem.getPatentId();
            CommonResponse<Object> claimTreeResponse = patentInfoManager.fetchPatentClmsTree(patentId);
            JSONObject patentInfo = patentInfoList.getJSONObject(patentId);
            String relatedPatentTitle = "CN".equalsIgnoreCase(lang) ? "相关专利" + (i+1) : "Related Patent " + (i+1);
            String riskLevelTitle = "CN".equalsIgnoreCase(lang) ? "风险等级" : "Risk Level";
            String pn = ("CN".equalsIgnoreCase(lang) ? "公开号:" : "Patent Number:") + patentInfo.getString("PN");
            String title = patentInfo.getString("TITLE");
            Integer legalStatus = patentInfo.getInteger("SIMPLE_LEGAL_STATUS");
            String legalStatusText = ("CN".equalsIgnoreCase(lang) ? "法律状态:" : "Legal Status:") + patentInfoManager.fetchSimpleLegalStatusText(lang, legalStatus, legalStatusResponse);
            List<Integer> legalStatusList = patentInfo.getJSONArray("LEGAL_STATUS").toJavaList(Integer.class);
            String legalEventText = ("CN".equalsIgnoreCase(lang) ? "法律事件:" : "Legal Event:") + patentInfoManager.fetchLegalEventText(lang, legalStatusList, legalStatusResponse);
            String publicationDate = ("CN".equalsIgnoreCase(lang) ? "申请日:" : "Application Date:") + patentInfo.getString("APD");
            String estimatedDate = ("CN".equalsIgnoreCase(lang) ? "预估到期日:" : "Estimated Expiry Date:") + patentInfo.getString("EXDT");
            RiskLevelEnum riskLevelEnum = RiskLevelEnum.fromValue(finalResultItem.getRiskLevel());
            String riskLevel = LANG_CN.equalsIgnoreCase(lang) ? riskLevelEnum.getExplain() : riskLevelEnum.getValue();

            setCellText(table, 0, 0, relatedPatentTitle, false, ParagraphAlignment.LEFT, TABLE_CELL_COLOR_BLUE, "CN".equalsIgnoreCase(lang) ? "等线" :"Arial");
            setCellText(table, 0, 3, riskLevelTitle, false, ParagraphAlignment.LEFT, TABLE_CELL_COLOR_BLUE, "CN".equalsIgnoreCase(lang) ? "等线" :"Arial");
            setCellTextWithBreak(table, 1, 0, Arrays.asList(title, pn, publicationDate, estimatedDate, legalStatusText, legalEventText), false, ParagraphAlignment.LEFT, "", "CN".equalsIgnoreCase(lang) ? "等线" :"Arial");
            setCellText(table, 1, 3, riskLevel, false, ParagraphAlignment.LEFT, "", "CN".equalsIgnoreCase(lang) ? "等线" :"Arial");
            mergeCellsHorizontally(table, 0, 0, 2);
            mergeCellsHorizontally(table, 1, 0, 2);

            // 结论
            if("CN".equalsIgnoreCase(lang)) {
                String conclusionPrefix = "CN".equalsIgnoreCase(lang) ? "对比结论" : "Summary of Findings";
                String conclusionText = finalResultItem.getComparisonConclusion().get("zh");
                setCellText(table, 2, 0, conclusionPrefix, false, ParagraphAlignment.LEFT, TABLE_CELL_COLOR_BLUE, "CN".equalsIgnoreCase(lang) ? "等线" :"Arial");
                setCellTextWithBreak(table, 3, 0, Arrays.asList(conclusionText), false, ParagraphAlignment.LEFT, "", "CN".equalsIgnoreCase(lang) ? "等线" :"Arial");
                mergeCellsHorizontally(table, 2, 0, 3);
                mergeCellsHorizontally(table, 3, 0, 3);
            }

            // 权利要求
            if(!hasInfringement) {
                String fixedText = "CN".equalsIgnoreCase(lang) ? "经过本次技术特征比对，Hiro 未发现该专利与您提交的技术方案存在等同技术特征。因此，当前未发现您的技术方案对该专利构成侵权风险。"
                        : "Based on the claim charting analysis, Hiro did not identify any equivalent technical features between this patent and your submitted solution. As the match level is low, no issues have been flagged at this stage.";
                Integer rowIndex = "CN".equalsIgnoreCase(lang) ? 4 :2;
                setCellText(table, rowIndex, 0, fixedText, false, ParagraphAlignment.LEFT, "", "CN".equalsIgnoreCase(lang) ? "等线" :"Arial");
                mergeCellsHorizontally(table, rowIndex, 0, 3);
            } else {
                // 固定标题(cn第五行，en第三行)
                int row = "CN".equalsIgnoreCase(lang) ? 4 : 2;
                String tableHeader1 = "CN".equalsIgnoreCase(lang) ? "权利要求" : "Claims";
                String tableHeader2 = "CN".equalsIgnoreCase(lang) ? "技术特征" : "Technical feature";
                String tableHeader3 = "CN".equalsIgnoreCase(lang) ? "标的技术特征" : "Technical feature of target";
                String tableHeader4 = "CN".equalsIgnoreCase(lang) ? "是否等同" : "Equivalent";
                setCellText(table, row, 0, tableHeader1, false, ParagraphAlignment.LEFT, TABLE_CELL_COLOR_BLUE, "CN".equalsIgnoreCase(lang) ? "等线" :"Arial");
                setCellText(table, row, 1, tableHeader2, false, ParagraphAlignment.LEFT, TABLE_CELL_COLOR_BLUE, "CN".equalsIgnoreCase(lang) ? "等线" :"Arial");
                setCellText(table, row, 2, tableHeader3, false, ParagraphAlignment.LEFT, TABLE_CELL_COLOR_BLUE, "CN".equalsIgnoreCase(lang) ? "等线" :"Arial");
                setCellText(table, row, 3, tableHeader4, false, ParagraphAlignment.LEFT, TABLE_CELL_COLOR_BLUE, "CN".equalsIgnoreCase(lang) ? "等线" :"Arial");

                // 权利要求聚合数据
                List<ClaimEquivalentItem> claimList = finalResultItem.getClaimEquivalentInfo().stream().filter(ClaimEquivalentItem::getInfringement).collect(Collectors.toList());
                // 权利要求原始文本数据
                List<FeatureComparisonFinalResClaim> claimTextList = finalResultItem.getClaims();
                if(CollectionUtils.isEmpty(claimTextList)) {
                    claimTextList = new ArrayList<>();
                }
                // 按照claim_num正序排序，从小到大
                claimList.sort(Comparator.comparing(ClaimEquivalentItem::getClaimNum));
                int currentRowIndex = "CN".equalsIgnoreCase(lang) ? 5 : 3;
                for(ClaimEquivalentItem claimItem : claimList) {
                    Integer claimNum = claimItem.getClaimNum();
                    PatentClaimTreeResponse.Claims claimTree = JSONObject.parseObject(JSONObject.toJSONString(((LinkedHashMap) claimTreeResponse.getData()).get("claims")), PatentClaimTreeResponse.Claims.class);
                    String claimNumText = ("CN".equalsIgnoreCase(lang) ? "权利要求":"Claim") + claimNum;
                    // 权利要求原始文本先从算法接口对应字段取值，如果取不到，再从专利要求树中取值
                    String claimOriginText = claimTextList.stream()
                            .filter(item -> claimNum.equals(item.getClaimNum()))
                            .map(FeatureComparisonFinalResClaim::getClaimText)
                            .findFirst()
                            .orElse("");
                    if(StringUtils.isBlank(claimOriginText)) {
                        claimOriginText = claimTree.getClmsTree().stream()
                                .filter(item -> claimNum.equals(item.getIntegerNum()))
                                .map(PatentClaimTreeResponse.ClaimTree::getBody)
                                .findFirst()
                                .map(PatentClaimTreeResponse.Body::getText)
                                .orElse("");
                    }
                    setCellTextWithBreak(table, currentRowIndex, 0, Arrays.asList(claimNumText, claimOriginText), false, ParagraphAlignment.LEFT, "", "CN".equalsIgnoreCase(lang) ? "等线" :"Arial");

                    // 展示权利要求编号里所有侵权的特征数据
                    List<AiSearchFinalResultFeature> featureList = finalResultItem.getFeatures().stream()
                            .filter(item -> claimNum.equals(item.getClaimNum()))
                            .filter(AiSearchFinalResultFeature::isSimilar)
                            .toList();
                    for (int n = 0; n < featureList.size(); n++) {
                        AiSearchFinalResultFeature featureInfo = featureList.get(n);
                        // 第一列不需要设置，第二，第三，第四列需要填充数据
                        setCellText(table, currentRowIndex, 0, "", false, ParagraphAlignment.LEFT, "", "CN".equalsIgnoreCase(lang) ? "等线" :"Arial");
                        setCellText(table, currentRowIndex, 1, featureInfo.getTechFeature(), false, ParagraphAlignment.LEFT, "", "CN".equalsIgnoreCase(lang) ? "等线" :"Arial");
                        setCellText(table, currentRowIndex, 2, featureInfo.getComparisonFeature(), false, ParagraphAlignment.LEFT, "", "CN".equalsIgnoreCase(lang) ? "等线" :"Arial");
                        setCellText(table, currentRowIndex, 3, "CN".equalsIgnoreCase(lang) ? "是":"Yes", false, ParagraphAlignment.LEFT, "", "CN".equalsIgnoreCase(lang) ? "等线" :"Arial");
                        currentRowIndex = currentRowIndex + 1;
                    }
                    // 合并第一列(权要编号聚合)
                    mergeCellsVertically(table, 0, currentRowIndex - featureList.size(), currentRowIndex-1);
                }
            }


            // 调整单元格宽度
             setColumnWidth(table, 0, 30);
             setColumnWidth(table, 1, 30);
            setColumnWidth(table, 2, 30);
            setColumnWidth(table, 3, 10);
            
            // 每个专利的表格之间空一行
            // 在当前表格后面插入一个空白段落，用于分隔表格
            // 重新获取段落列表，找到表格后面的位置
            List<XWPFParagraph> updatedParagraphs = document.getParagraphs();
            int currentTitleIndex = updatedParagraphs.indexOf(afterParagraph);
            
            // 在表格后面插入空白段落
            if (currentTitleIndex != -1 && currentTitleIndex < updatedParagraphs.size() - 1) {
                XWPFParagraph blankParagraph = document.insertNewParagraph(updatedParagraphs.get(currentTitleIndex + 1).getCTP().newCursor());
                blankParagraph.setAlignment(ParagraphAlignment.LEFT);
                // 更新afterParagraph为新创建的空白段落，作为下一次循环的插入点
                afterParagraph = blankParagraph;
            } else {
                // 如果找不到合适的位置，就在文档末尾添加空白段落
                XWPFParagraph blankParagraph = document.createParagraph();
                blankParagraph.setAlignment(ParagraphAlignment.LEFT);
                afterParagraph = blankParagraph;
            }

        }
        document.createParagraph();
    }

    private void handleAppendix1(XWPFDocument document, XWPFParagraph afterParagraph, String taskId, String lang) {
        AiSearchResultDTO aiSearchResultInfo = aiTaskManager.getTaskContent(taskId, AiTaskContentTypeEnum.FTO_SEARCH_AGENT_RESULT);
        if(Objects.isNull(aiSearchResultInfo)) {
            return;
        }
        // 过滤掉完全为空的对象，防止产生表格里的空白行
        List<AiSearchFeatureKeyword> featureKeywordList = aiSearchResultInfo.getFeatureKeywords().stream().filter(item -> StringUtils.isNotBlank(item.getTechFeature())).toList();
        if(CollectionUtils.isEmpty(featureKeywordList)) {
            return;
        }

        List<XWPFParagraph> paragraphs = document.getParagraphs();
        int titleIndex = paragraphs.indexOf(afterParagraph);
        int rowSize = featureKeywordList.size() + 1;
        int columnSize = 3;
        XWPFTable table = document.insertNewTbl(document.getParagraphs().get(titleIndex).getCTP().newCursor());
        table.setWidth("100%");
        for (int i = 1; i < rowSize; i++) {
            table.createRow().setHeight(TABLE_ROW_HEIGHT);;
        }
        for (int i = 0; i < rowSize; i++) {
            XWPFTableRow row = table.getRow(i);
            while (row.getTableCells().size() < columnSize) {
                row.createCell();
            }
        }

        // 设置表格边框
        table.setInsideHBorder(XWPFTable.XWPFBorderType.SINGLE, 4, 0, TABLE_BORDER_COLOR_BLUE);
        table.setInsideVBorder(XWPFTable.XWPFBorderType.SINGLE, 4, 0, TABLE_BORDER_COLOR_BLUE);
        table.setTopBorder(XWPFTable.XWPFBorderType.SINGLE, 4, 0, TABLE_BORDER_COLOR_BLUE);
        table.setBottomBorder(XWPFTable.XWPFBorderType.SINGLE, 4, 0, TABLE_BORDER_COLOR_BLUE);
        table.setLeftBorder(XWPFTable.XWPFBorderType.SINGLE, 4, 0, TABLE_BORDER_COLOR_BLUE);
        table.setRightBorder(XWPFTable.XWPFBorderType.SINGLE, 4, 0, TABLE_BORDER_COLOR_BLUE);

        setCellText(table, 0, 0, "CN".equalsIgnoreCase(lang) ? "序号" : "Number", false, ParagraphAlignment.LEFT, TABLE_CELL_COLOR_BLUE, "CN".equalsIgnoreCase(lang) ? "等线" :"Arial");
        setCellText(table, 0, 1, "CN".equalsIgnoreCase(lang) ? "检索要素" : "Search Elements", false, ParagraphAlignment.LEFT, TABLE_CELL_COLOR_BLUE, "CN".equalsIgnoreCase(lang) ? "等线" :"Arial");
        setCellText(table, 0, 2, "CN".equalsIgnoreCase(lang) ? "同义词" : "Synonyms", false, ParagraphAlignment.LEFT, TABLE_CELL_COLOR_BLUE, "CN".equalsIgnoreCase(lang) ? "等线" :"Arial");

        for (int i = 1; i <= featureKeywordList.size(); i++) {
            AiSearchFeatureKeyword featureKeyword = featureKeywordList.get(i - 1);
            setCellText(table, i, 0, String.valueOf(i), false, ParagraphAlignment.LEFT, "", "CN".equalsIgnoreCase(lang) ? "等线" :"Arial");
            setCellText(table, i, 1, StringUtils.isNotBlank(featureKeyword.getTechFeature()) ? featureKeyword.getTechFeature() : "", false, ParagraphAlignment.LEFT, "", "CN".equalsIgnoreCase(lang) ? "等线" :"Arial");
            setCellText(table, i, 2, CollectionUtils.isNotEmpty(featureKeyword.getSynonym()) ? StringUtils.join(featureKeyword.getSynonym(), " ") : "", false, ParagraphAlignment.LEFT, "", "CN".equalsIgnoreCase(lang) ? "等线" :"Arial");
        }

        // 调整单元格宽度
        setColumnWidth(table, 0, 10);
        setColumnWidth(table, 1, 35);

        document.createParagraph();
    }

    private void handleAppendix2(XWPFDocument document, XWPFParagraph afterParagraph, String taskId, String lang) {
        AiSearchResultDTO aiSearchResultInfo = aiTaskManager.getTaskContent(taskId, AiTaskContentTypeEnum.FTO_SEARCH_AGENT_RESULT);
        String userInput = aiTaskManager.getTaskContent(taskId, AiTaskContentTypeEnum.USER_INPUT);
        if(Objects.isNull(aiSearchResultInfo)) {
            return;
        }
        AiSearchProcessDict processDict = aiSearchResultInfo.getProcessDict();
        if(Objects.isNull(processDict)) {
            return;
        }


        List<XWPFParagraph> paragraphs = document.getParagraphs();
        int titleIndex = paragraphs.indexOf(afterParagraph);
        int rowSize = 8;
        int columnSize = 3;
        XWPFTable table = document.insertNewTbl(document.getParagraphs().get(titleIndex).getCTP().newCursor());
        table.setWidth("100%");
        for (int i = 1; i < rowSize; i++) {
            table.createRow().setHeight(TABLE_ROW_HEIGHT);;
        }
        for (int i = 0; i < rowSize; i++) {
            XWPFTableRow row = table.getRow(i);
            while (row.getTableCells().size() < columnSize) {
                row.createCell();
            }
        }

        // 设置表格边框
        table.setInsideHBorder(XWPFTable.XWPFBorderType.SINGLE, 4, 0, TABLE_BORDER_COLOR_BLUE);
        table.setInsideVBorder(XWPFTable.XWPFBorderType.SINGLE, 4, 0, TABLE_BORDER_COLOR_BLUE);
        table.setTopBorder(XWPFTable.XWPFBorderType.SINGLE, 4, 0, TABLE_BORDER_COLOR_BLUE);
        table.setBottomBorder(XWPFTable.XWPFBorderType.SINGLE, 4, 0, TABLE_BORDER_COLOR_BLUE);
        table.setLeftBorder(XWPFTable.XWPFBorderType.SINGLE, 4, 0, TABLE_BORDER_COLOR_BLUE);
        table.setRightBorder(XWPFTable.XWPFBorderType.SINGLE, 4, 0, TABLE_BORDER_COLOR_BLUE);

        setCellText(table, 0, 0, "CN".equalsIgnoreCase(lang) ? "序号" : "Number", false, ParagraphAlignment.LEFT, TABLE_CELL_COLOR_BLUE, "CN".equalsIgnoreCase(lang) ? "等线" :"Arial");
        setCellText(table, 0, 1, "CN".equalsIgnoreCase(lang) ? "检索步骤" : "Search Step", false, ParagraphAlignment.LEFT, TABLE_CELL_COLOR_BLUE, "CN".equalsIgnoreCase(lang) ? "等线" :"Arial");
        setCellText(table, 0, 2, "CN".equalsIgnoreCase(lang) ? "检索策略" : "Search String", false, ParagraphAlignment.LEFT, TABLE_CELL_COLOR_BLUE, "CN".equalsIgnoreCase(lang) ? "等线" :"Arial");

        for (int i = 1; i <= 7; i++) {
            String stepText = "";
            String strategyText = "";
            switch(i) {
                case 1: {
                    stepText = "CN".equalsIgnoreCase(lang) ? "语义检索" : "Semantic Search";
                    // 前端页面语义检索表格里的值其实是SEMANTIC:用户原始输入文本
                    strategyText = "SEMANTIC:" + userInput;
                    break;
                }
                case 2: {
                    stepText = "CN".equalsIgnoreCase(lang) ? "分类号检索" : "Classification Search";
                    strategyText = Objects.nonNull(processDict.getIpcCpcSearch()) ? processDict.getIpcCpcSearch().getStrategy().getQuery() : "";
                    break;
                }
                case 3: {
                    stepText = "CN".equalsIgnoreCase(lang) ? "第 1 次块检索和渐进式检索" : "Keyword search 1";
                    strategyText = Objects.nonNull(processDict.getBlockProgressiveSearch1()) ? processDict.getBlockProgressiveSearch1().getStrategy().getQuery() : "";
                    break;
                }
                case 4: {
                    stepText = "CN".equalsIgnoreCase(lang) ? "第 2 次块检索和渐进式检索" : "Keyword search 2";
                    strategyText = Objects.nonNull(processDict.getBlockProgressiveSearch2()) ? processDict.getBlockProgressiveSearch2().getStrategy().getQuery() : "";
                    break;
                }
                case 5: {
                    stepText = "CN".equalsIgnoreCase(lang) ? "第 3 次块检索和渐进式检索" : "Keyword search 3";
                    strategyText = Objects.nonNull(processDict.getBlockProgressiveSearch3()) ? processDict.getBlockProgressiveSearch3().getStrategy().getQuery() : "";
                    break;
                }
                case 6: {
                    stepText = "CN".equalsIgnoreCase(lang) ? "第 4 次块检索和渐进式检索" : "Keyword search 4";
                    strategyText = Objects.nonNull(processDict.getBlockProgressiveSearch4()) ? processDict.getBlockProgressiveSearch4().getStrategy().getQuery() : "";
                    break;
                }
                case 7: {
                    stepText = "CN".equalsIgnoreCase(lang) ? "高度相关追踪检索" : "Family & Citation Expansion Search ";
                    strategyText = Objects.nonNull(processDict.getRelevantTrackingSearch()) ? processDict.getRelevantTrackingSearch().getStrategy().getQuery() : "";
                    break;
                }
                default:{
                    // do nothing
                }
            }

            setCellText(table, i, 0, String.valueOf(i), false, ParagraphAlignment.LEFT, "", "CN".equalsIgnoreCase(lang) ? "等线" :"Arial");
            setCellText(table, i, 1, stepText, false, ParagraphAlignment.LEFT, "", "CN".equalsIgnoreCase(lang) ? "等线" :"Arial");
            setCellText(table, i, 2, strategyText, false, ParagraphAlignment.LEFT, "", "CN".equalsIgnoreCase(lang) ? "等线" :"Arial");
        }

        // 调整单元格宽度
        setColumnWidth(table, 0, 10);
        setColumnWidth(table, 1, 35);

        document.createParagraph();
    }

    // --------------------------------------------------------------------------------------
    private void handleTaskTitleInfo(Map<String, String> placeholderData, String taskId, String lang) {
        // 查新任务相关的数据
        FtoSearchReportTitleResponseDTO taskTitle = aiTaskManager.getTaskContent(taskId, AiTaskContentTypeEnum.FTO_SEARCH_REPORT_TITLE);
        if(Objects.isNull(taskTitle)) {
            return;
        }

        placeholderData.put(REPORT_TITLE, taskTitle.getTitle() + (LANG_CN.equalsIgnoreCase(lang) ? " - 防侵权检索报告" : " - FTO Search Report"));
        placeholderData.put(REPORTER_LABEL, LANG_CN.equalsIgnoreCase(lang) ? "报告人" : "Lecturer");
        placeholderData.put(REPORTER_NAME_PLACEHOLDER, taskTitle.getCreator());
        placeholderData.put(REPORT_TIME_LABEL, LANG_CN.equalsIgnoreCase(lang) ? "报告时间" : "Time");
        String formatTime = DateUtils.formatDateByFormat(new DateTime(taskTitle.getGeneratedTime()), LANG_CN.equalsIgnoreCase(lang) ? "yyyy/MM/dd" : "dd MM yyyy");
        placeholderData.put(REPORT_DATE_PLACEHOLDER, formatTime);
        String subTitleKey = LANG_CN.equalsIgnoreCase(lang) ? "zh" : "en";
        String subTitleText = Objects.nonNull(taskTitle.getHeader()) ? taskTitle.getHeader().get(subTitleKey) : "";
        placeholderData.put(SEARCH_SUMMARY, subTitleText);
    }

    private void handleSearchScopeInfo(Map<String, String> placeholderData, String taskId, String lang) {
        FtoSearchReportTitleResponseDTO taskTitle = aiTaskManager.getTaskContent(taskId, AiTaskContentTypeEnum.FTO_SEARCH_REPORT_TITLE);
        if(Objects.isNull(taskTitle) || MapUtils.isEmpty(taskTitle.getSearchScope())) {
            return;
        }

        placeholderData.put(SEARCH_SCOPE_TITLE, LANG_CN.equalsIgnoreCase(lang) ? "检索范围" : "Search Scope");

        // 发明要点内容
        String searchScopeKey = "";
        if("CN".equalsIgnoreCase(lang)) {
            searchScopeKey = "zh";
        } else if("EN".equalsIgnoreCase(lang)) {
            searchScopeKey = "en";
        } else if("JP".equalsIgnoreCase(lang)) {
            searchScopeKey = "jp";
        }
        placeholderData.put(SEARCH_SCOPE_TEXT, taskTitle.getSearchScope().getOrDefault(searchScopeKey, ""));
    }

    private void handleMainPointInfo(Map<String, String> placeholderData, String taskId, String lang) {
        FeatureExactionResponseDTO techFeatureInfo = aiTaskManager.getTaskContent(taskId, AiTaskContentTypeEnum.FTO_SEARCH_TECH_FEATURE);
        if(Objects.isNull(techFeatureInfo) || StringUtils.isBlank(techFeatureInfo.getText())) {
            return;
        }

        placeholderData.put(MAIN_POINT_TITLE, LANG_CN.equalsIgnoreCase(lang) ? "本技术方案的发明要点" : "Technical Solution");
        placeholderData.put(MAIN_POINT_TEXT, techFeatureInfo.getText());
    }

    private void handleTableTitle(Map<String, String> placeholderData, String lang) {
        String technicalFeaturesKeywordsTableTitle = LANG_CN.equalsIgnoreCase(lang) ? "检索技术特征" : "Technical Features";
        placeholderData.put(TECHNICAL_FEATURE_KEYWORD_TITLE, technicalFeaturesKeywordsTableTitle);

        String technicalFeaturesComparisonTableTitle = LANG_CN.equalsIgnoreCase(lang) ? "技术特征对比表" : "Technical Feature Comparison Table";
        placeholderData.put(TECHNICAL_FEATURE_COMPARISON_TITLE, technicalFeaturesComparisonTableTitle);

        String firstAppendixTableTitle = LANG_CN.equalsIgnoreCase(lang) ? "附录1：检索要素及其同义词" : "Appendix 1: Technical Elements & Synonyms";
        placeholderData.put(APPENDIX_SEARCH_SYNONYM_TITLE, firstAppendixTableTitle);

        String secondAppendixTableTitle = LANG_CN.equalsIgnoreCase(lang) ? "附录2：检索策略" : "Appendix 2: Search Strategy";
        placeholderData.put(APPENDIX_SEARCH_STRATEGY_TITLE, secondAppendixTableTitle);
    }

    private void handleDisclaimer(Map<String, String> placeholderData, String lang) {
        String disclaimerCn = "本报告部分内容由AI自动生成，仅供参考。本报告仅作为初步专利检索的参考性材料使用，未经专业人士确认前，不可作为法律意见使用，智慧芽不承担因使用、误用或依赖本报告导致的任何直接、间接或特殊损失。报告中的技术分析框架、算法模型及相关数据资产均受著作权法及商业秘密保护，未经授权方书面许可禁止反向工程、商业性传播或用于衍生开发。";
        String disclaimerEn = "Part of this report is automatically generated by AI and is for reference only. This report is only used as a reference material for preliminary patent search and cannot be used as legal advice without confirmation by professionals. patsnap does not assume any direct, indirect or special losses caused by the use, misuse or reliance on this report. The technical analysis framework, algorithm models and related data assets in the report are protected by copyright law and trade secrets. Reverse engineering, commercial dissemination or use for derivative development is prohibited without the written permission of the authorized party.";

        String titleContent = LANG_CN.equalsIgnoreCase(lang) ? "免责声明" : "Disclaimer";

        placeholderData.put(DISCLAIMER_TITLE, titleContent);
        placeholderData.put(DISCLAIMER_CONTENT, LANG_CN.equalsIgnoreCase(lang) ? disclaimerCn : disclaimerEn);
    }
}

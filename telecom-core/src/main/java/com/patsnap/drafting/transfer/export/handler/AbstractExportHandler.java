package com.patsnap.drafting.transfer.export.handler;

import cn.hutool.core.io.file.FileNameUtil;
import cn.hutool.json.JSONConfig;
import cn.hutool.json.JSONUtil;
import com.patsnap.core.common.request.SiteLangHolder;
import com.patsnap.drafting.constants.Constant;
import com.patsnap.drafting.manager.content.TaskContentManager;
import com.patsnap.drafting.manager.refrence.PatentReferenceManager;
import com.patsnap.drafting.model.patent.PatentSolutionInfo;
import com.patsnap.drafting.repository.aitask.entity.AnalyticsAiTaskContentPO;
import com.patsnap.drafting.response.aidisclosure.ReferencesReqDTO;
import com.patsnap.drafting.transfer.export.enitty.ExportRequest;
import com.patsnap.drafting.util.ParagraphUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 导出处理器抽象类
 *
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractExportHandler implements ExportHandler {

    protected static final String TITLE = "title";

    protected static final String REFERENCES = "references";

    private static final Pattern REGEX_REFERENCE = Pattern.compile("\\[(\\w+)]");

    @Autowired
    protected PatentReferenceManager patentReferenceManager;

    @Autowired
    protected TaskContentManager taskContentManager;

    /**
     * 导出通用指定字段内容
     *
     * @param exportRequest 导出请求
     * @param contentFields 指定字段
     */
    protected void exportContent(ExportRequest exportRequest, List<String> contentFields) {
        // 设置默认标题
        exportRequest.getPlaceholderData().put(TITLE, FileNameUtil.mainName(exportRequest.getFileName()));
        // 设置指定字段内容
        Map<String, String> allContentMap = taskContentManager.getAllContentsByTaskId(exportRequest.getTaskId()).stream()
                .filter(item -> StringUtils.isNotBlank(item.getContentType()) && contentFields.contains(item.getContentType()))
                .collect(Collectors.toMap(AnalyticsAiTaskContentPO::getContentType, AnalyticsAiTaskContentPO::getContent, (v1, v2) -> v2));
        for (String contentFiled : contentFields) {
            exportRequest.getPlaceholderData().put(contentFiled, ParagraphUtils.replaceMultipleCarriageReturn(allContentMap.getOrDefault(contentFiled, StringUtils.EMPTY)));
        }
    }

    /**
     * 导出引用文献
     *
     * @param exportRequest 导出请求
     */
    protected void exportReferences(ExportRequest exportRequest) {
        // 1.处理全文中的引用，将引用的显示由专利号结构[WO2012118600A1]替换为[1]序列结构，并得到两种结构的引用映射 WO2012118600A1 <-> 1
        Map<String, String> referenceMap = new LinkedHashMap<>();
        AtomicInteger count = new AtomicInteger(0);
        exportRequest.getPlaceholderData().forEach((key, value) -> {
            Matcher matcher = REGEX_REFERENCE.matcher(value);
            StringBuilder referenceContentBuilder = new StringBuilder();
            while (matcher.find()) {
                String patentPn = matcher.group(1);
                referenceMap.computeIfAbsent(patentPn, item -> String.valueOf(count.incrementAndGet()));
                matcher.appendReplacement(referenceContentBuilder, String.format("[%s]", referenceMap.get(patentPn)));
            }
            matcher.appendTail(referenceContentBuilder);
            exportRequest.getPlaceholderData().put(key, referenceContentBuilder.toString());
        });
        if (MapUtils.isEmpty(referenceMap)) {
            exportRequest.getPlaceholderData().put(REFERENCES, StringUtils.EMPTY);
            return;
        }

        boolean isEn = Constant.EN.equalsIgnoreCase(SiteLangHolder.get());
        String comma = isEn ? ", " : "，";
        // 2.根据引用映射查询出真实引用数据（专利号、专利名称、申请人），生成引用内容
        ReferencesReqDTO referencesReqDTO = ReferencesReqDTO.builder().pns(referenceMap.keySet().stream().toList()).build();
        Collection<Map<String, Object>> analyticsReference = patentReferenceManager.getReference(referencesReqDTO);
        Map<String, PatentSolutionInfo> patentSolutionInfoMap = JSONUtil.toList(
                JSONUtil.toJsonStr(analyticsReference, JSONConfig.create().setIgnoreCase(true)), PatentSolutionInfo.class
        ).stream().collect(Collectors.toMap(PatentSolutionInfo::getPn, Function.identity()));

        StringBuilder referenceContentBuilder = new StringBuilder();
        referenceMap.forEach((patentPn, referenceIndex) -> {
            PatentSolutionInfo patentSolutionInfo = patentSolutionInfoMap.get(patentPn);
            referenceContentBuilder.append("[").append(referenceIndex).append("]").append(" ")
                    .append(StringUtils.trimToEmpty(patentPn));
            if (Objects.nonNull(patentSolutionInfo)) {
                // 专利数据库可以找到专利
                referenceContentBuilder.append(comma)
                        .append(StringUtils.trimToEmpty(patentSolutionInfo.getTitle()));
                int size = patentSolutionInfo.getOrgInfo().size();
                if (size > 0) {
                    referenceContentBuilder.append(comma)
                            .append(StringUtils.trimToEmpty(patentSolutionInfo.getOrgInfo().get(0).getNormalizedName()));
                    if (size > 1) {
                        referenceContentBuilder.append(comma).append("+").append((size - 1));
                    }
                }
            } else {
                // 未识别
                referenceContentBuilder.append(comma).append(isEn ? "Unrecognizable" : "未识别");
            }
            referenceContentBuilder.append("\n");
        });
        exportRequest.getPlaceholderData().put(REFERENCES, referenceContentBuilder.toString());
    }

}
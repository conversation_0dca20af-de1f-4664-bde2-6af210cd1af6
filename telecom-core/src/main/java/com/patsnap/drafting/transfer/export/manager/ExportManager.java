package com.patsnap.drafting.transfer.export.manager;

import cn.hutool.core.io.file.FileNameUtil;
import cn.hutool.core.text.StrPool;
import com.google.common.collect.Maps;
import com.patsnap.common.exception.InternalServerErrorException;
import com.patsnap.common.request.UserIdHolder;
import com.patsnap.core.common.request.SiteLangHolder;
import com.patsnap.drafting.enums.task.AiTaskContentTypeEnum;
import com.patsnap.drafting.enums.task.AiTaskTypeEnum;
import com.patsnap.drafting.manager.FileManager;
import com.patsnap.drafting.manager.aitask.AiTaskManager;
import com.patsnap.drafting.repository.aitask.entity.AnalyticsAiTaskHistoryPO;
import com.patsnap.drafting.transfer.export.enitty.ExportRequest;
import com.patsnap.drafting.transfer.export.enitty.ExportResult;
import com.patsnap.drafting.transfer.export.factory.ExportHandlerFactory;
import com.patsnap.drafting.transfer.export.handler.ExportHandler;
import com.patsnap.drafting.util.ParagraphUtils;
import com.patsnap.drafting.util.WordUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.entity.ContentType;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Service;

/**
 * 导出业务类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ExportManager {

    /** 导出模板路径 */
    private static final String EXPORT_TEMPLATE_PATH = "classpath:template/export/";

    /** 导出S3路径 */
    private static final String EXPORT_S3_PATH = "ai_drafting/%s/%s/%s";

    /** 导出文件类类型 */
    private static final String EXPORT_DEFAULT_FILE_TYPE = ".docx";

    private final ResourceLoader resourceLoader;

    private final ExportHandlerFactory exportHandlerFactory;

    private final FileManager fileManager;

    private final AiTaskManager aiTaskManager;

    public ExportResult export(String taskId) {
        log.info("ExportManager export taskId: {}", taskId);

        // 1.构建默认导出参数
        String lang = SiteLangHolder.get();
        // FTO模块的lang需要取用户输入原始文本的lang，而不是网站lang
        ExportRequest exportRequest = this.buildExportRequest(taskId, lang);

        // 2.获取导出业务处理器
        ExportHandler exportHandler = exportHandlerFactory.getHandler(exportRequest.getAiTaskTypeEnum());

        // 3.业务前置处理
        exportHandler.preExport(exportRequest);

        // 4.导出逻辑：优先尝试自定义导出，如果返回null则使用通用导出
        ExportResult exportResult = exportHandler.customExport(exportRequest);
        if (exportResult == null) {
            exportResult = this.export(exportRequest);
        }

        // 5.业务后置处理
        exportHandler.postExport(exportRequest, exportResult);

        return exportResult;
    }

    private ExportRequest buildExportRequest(String taskId, String lang) {

        AnalyticsAiTaskHistoryPO analyticsAiTaskHistoryPO = aiTaskManager.checkPermission(taskId);
        AiTaskTypeEnum aiTaskTypeEnum = AiTaskTypeEnum.fromType(analyticsAiTaskHistoryPO.getType());
        // FTO模块的lang需要取用户输入原始文本的lang，而不是网站lang
        if(AiTaskTypeEnum.AI_FTO_SEARCH.equals(aiTaskTypeEnum)) {
            lang = aiTaskManager.getTaskContent(taskId, AiTaskContentTypeEnum.FTO_SEARCH_INPUT_LANG);
        }

        // 导出模板、文件名等默认数据，业务可以再preExport中修改覆盖
        return ExportRequest.builder()
                .taskId(taskId)
                .lang(lang)
                .aiTaskTypeEnum(aiTaskTypeEnum)
                .templateName(aiTaskTypeEnum.getType().toLowerCase() + EXPORT_DEFAULT_FILE_TYPE)
                .templateNameSuffix("CN".equalsIgnoreCase(lang) ? "cn" : "en")
                .fileName(ParagraphUtils.replaceCarriageReturn(analyticsAiTaskHistoryPO.getTitle()) + EXPORT_DEFAULT_FILE_TYPE)
                .placeholderData(Maps.newLinkedHashMap())
                .build();
    }

    private ExportResult export(ExportRequest exportRequest) {
        try {
            Resource resource = resourceLoader.getResource(EXPORT_TEMPLATE_PATH + this.getExportTemplateNameWithSuffix(exportRequest));
            if (!resource.exists()) {
                resource = resourceLoader.getResource(EXPORT_TEMPLATE_PATH + exportRequest.getTemplateName());
            }
            byte[] resultBytes = WordUtils.replaceInWord(resource, exportRequest.getPlaceholderData());
            String s3key = String.format(EXPORT_S3_PATH, UserIdHolder.get(), System.currentTimeMillis(), exportRequest.getFileName());
            String url = fileManager.uploadFile2AmazonS3(resultBytes, s3key, ContentType.MULTIPART_FORM_DATA);
            return ExportResult.builder().fileName(exportRequest.getFileName()).url(url).build();
        } catch (Exception e) {
            log.error("ExportManager export failed", e);
            throw new InternalServerErrorException();
        }
    }

    /**
     * 获取导出模板名称（模板可能根据国家、受理局等分类，业务方提供模板后缀区分，否则使用无后缀默认模板）
     *
     * @param exportRequest 导出请求
     * @return 导出模板名称
     */
    private String getExportTemplateNameWithSuffix(ExportRequest exportRequest) {
        String templateNameSuffix = exportRequest.getTemplateNameSuffix();
        if (StringUtils.isBlank(templateNameSuffix)) {
            return exportRequest.getTemplateName();
        }
        String templateName = exportRequest.getTemplateName();
        String name = FileNameUtil.mainName(templateName);
        String templateFileType = FileNameUtil.extName(templateName);
        return name + StrPool.UNDERLINE + StringUtils.lowerCase(templateNameSuffix) + StrPool.DOT + templateFileType;
    }

}
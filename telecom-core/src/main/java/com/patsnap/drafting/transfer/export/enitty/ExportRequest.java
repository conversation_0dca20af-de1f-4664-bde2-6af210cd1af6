package com.patsnap.drafting.transfer.export.enitty;

import com.patsnap.drafting.enums.task.AiTaskTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * 文件导出参数
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExportRequest {

    /** 任务ID */
    private String taskId;

    /** 任务类型 */
    private AiTaskTypeEnum aiTaskTypeEnum;

    /** 导出模板名 */
    private String templateName;

    /** 导出模板名后缀（根据业务支持多模板场景，由业务自己传值） */
    private String templateNameSuffix;

    /** 导出文件名 */
    private String fileName;

    /** 替换数据 */
    private Map<String, String> placeholderData;

    /** 导出语言 */
    private String lang;

}
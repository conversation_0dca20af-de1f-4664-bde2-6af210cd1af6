package com.patsnap.drafting.transfer.export.handler.impl;

import cn.hutool.core.io.file.FileNameUtil;
import com.patsnap.drafting.enums.task.AiTaskContentTypeEnum;
import com.patsnap.drafting.enums.task.AiTaskTypeEnum;
import com.patsnap.drafting.transfer.export.enitty.ExportRequest;
import com.patsnap.drafting.transfer.export.handler.AbstractExportHandler;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * AI翻译导出处理器
 *
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class AiTranslationExportHandler extends AbstractExportHandler {

    private static final String SRC_TEXT = "src_text";

    private static final String TRANSLATED_TEXT = "translated_text";

    private static final String GRAPH_INDEX = "graph_index";

    @Override
    public AiTaskTypeEnum getAiTaskTypeEnum() {
        return AiTaskTypeEnum.AI_TRANSLATION;
    }

    @Override
    public void preExport(ExportRequest exportRequest) {
        String taskId = exportRequest.getTaskId();
        Map<String, String> placeholderData = exportRequest.getPlaceholderData();

        // 1.翻译标题
        placeholderData.put(TITLE, FileNameUtil.mainName(exportRequest.getFileName()));

        // 2.翻译原文
        String srcText = taskContentManager.getTaskContent(taskId, AiTaskContentTypeEnum.USER_INPUT);
        placeholderData.put(SRC_TEXT, StringUtils.trimToEmpty(srcText));

        // 3.翻译译文
        List<LinkedHashMap<String, Object>> contentList = taskContentManager.getTaskContent(taskId, AiTaskContentTypeEnum.TRANSLATION_RESULT);
        StringBuilder translatedTextBuilder = new StringBuilder();
        int tempIndex = 0;
        for (LinkedHashMap<String, Object> translateMap : contentList) {
            int nowIndex = Integer.valueOf(Objects.toString(translateMap.get(GRAPH_INDEX), StringUtils.EMPTY));
            String text = Objects.toString(translateMap.get(TRANSLATED_TEXT), StringUtils.EMPTY);
            if (tempIndex != nowIndex && !text.endsWith("\n")) {
                translatedTextBuilder.append("\n");
            }
            translatedTextBuilder.append(text);
            tempIndex = nowIndex;
        }
        placeholderData.put(TRANSLATED_TEXT, translatedTextBuilder.toString());
    }

}

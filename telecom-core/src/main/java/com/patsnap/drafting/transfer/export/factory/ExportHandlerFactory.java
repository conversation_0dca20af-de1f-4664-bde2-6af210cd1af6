package com.patsnap.drafting.transfer.export.factory;

import com.patsnap.drafting.enums.task.AiTaskTypeEnum;
import com.patsnap.drafting.exception.BizException;
import com.patsnap.drafting.exception.errorcode.TaskErrorCodeEnum;
import com.patsnap.drafting.transfer.export.handler.ExportHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 导出处理器工厂
 *
 * <AUTHOR>
 */
@Component
public class ExportHandlerFactory {

    private final Map<AiTaskTypeEnum, ExportHandler> processorMap = new ConcurrentHashMap<>();

    @Autowired
    public ExportHandlerFactory(List<ExportHandler> processors) {
        processors.forEach(processor -> processorMap.put(processor.getAiTaskTypeEnum(), processor));
    }

    public ExportHandler getHandler(AiTaskTypeEnum aiTaskTypeEnum) {
        return Optional.ofNullable(processorMap.get(aiTaskTypeEnum))
                .orElseThrow(() -> new BizException(TaskErrorCodeEnum.TASK_TYPE_NOT_EXIST));
    }

}
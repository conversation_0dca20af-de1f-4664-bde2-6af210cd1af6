package com.patsnap.drafting.transfer.export.handler.impl;

import com.patsnap.common.request.UserIdHolder;
import com.patsnap.core.common.identity.AccountInfo;
import com.patsnap.core.common.request.SiteLangHolder;
import com.patsnap.drafting.enums.common.Lang;
import com.patsnap.drafting.enums.task.AiTaskContentTypeEnum;
import com.patsnap.drafting.enums.task.AiTaskTypeEnum;
import com.patsnap.drafting.manager.IdentityAccountManager;
import com.patsnap.drafting.transfer.export.enitty.ExportRequest;
import com.patsnap.drafting.transfer.export.handler.AbstractExportHandler;
import com.patsnap.drafting.util.DateUtils;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 专利技术交底书导出处理器
 *
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class PatentDisclosureExportHandler extends AbstractExportHandler {

    private static final List<String> CONTENT_FIELDS = List.of(
            AiTaskContentTypeEnum.TECH_FIELD.getType(),
            AiTaskContentTypeEnum.BACKGROUND.getType(),
            AiTaskContentTypeEnum.REFERENCE.getType(),
            AiTaskContentTypeEnum.DRAWBACKS.getType(),
            AiTaskContentTypeEnum.TECH_PROBLEM.getType(),
            AiTaskContentTypeEnum.TECH_EFFECT.getType(),
            AiTaskContentTypeEnum.TECH_MEANS.getType(),
            AiTaskContentTypeEnum.EMBODIMENT.getType()
    );

    private static final String AUTHOR = "author";

    private static final String EMAIL = "email";

    private static final String PHONE = "phone";

    private static final String INVENTOR = "inventor";

    private static final String TIME = "time";

    private final IdentityAccountManager identityAccountManager;

    @Override
    public AiTaskTypeEnum getAiTaskTypeEnum() {
        return AiTaskTypeEnum.AI_PATENT_DISCLOSURE;
    }

    @Override
    public void preExport(ExportRequest exportRequest) {
        // 1.设置文档用户信息
        this.exportAccountInfo(exportRequest.getPlaceholderData());

        // 2.设置文档正文指定字段
        super.exportContent(exportRequest, CONTENT_FIELDS);

        // 3.设置文档底部引用列表
        super.exportReferences(exportRequest);

        // 4.根据不同语言获取导出模板
        exportRequest.setTemplateNameSuffix(SiteLangHolder.get());
    }

    private void exportAccountInfo(Map<String, String> placeholderData) {
        Optional<AccountInfo> accountInfo = Optional.ofNullable(identityAccountManager.getAccountInfoByUserId(UserIdHolder.get()));
        placeholderData.put(AUTHOR, accountInfo.map(AccountInfo::getNickname).orElse(StringUtils.EMPTY));
        placeholderData.put(EMAIL, accountInfo.map(AccountInfo::getEmail).orElse(StringUtils.EMPTY));
        placeholderData.put(PHONE, accountInfo.map(AccountInfo::getMobileNumber).orElse(StringUtils.EMPTY));
        placeholderData.put(INVENTOR, accountInfo.map(AccountInfo::getNickname).orElse(StringUtils.EMPTY));
        placeholderData.put(TIME, DateUtils.formatDateTimeByLang(DateTime.now(), Lang.parseLang(SiteLangHolder.get())));
    }

}
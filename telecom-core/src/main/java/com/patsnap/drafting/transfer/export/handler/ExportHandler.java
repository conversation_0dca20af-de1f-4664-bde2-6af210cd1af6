package com.patsnap.drafting.transfer.export.handler;

import com.patsnap.drafting.enums.task.AiTaskTypeEnum;
import com.patsnap.drafting.transfer.export.enitty.ExportRequest;
import com.patsnap.drafting.transfer.export.enitty.ExportResult;

/**
 * 导出处理器接口
 *
 * <AUTHOR>
 */
public interface ExportHandler {

    /**
     * 获取AI任务类型
     *
     * @return AI任务类型
     */
    AiTaskTypeEnum getAiTaskTypeEnum();

    /**
     * 导出前置处理
     *
     * @param exportRequest 导出业务参数
     */
    void preExport(ExportRequest exportRequest);

    /**
     * 导出后置处理
     *
     * @param exportRequest 导出业务参数
     * @param exportResult  导出结果
     */
    default void postExport(ExportRequest exportRequest, ExportResult exportResult) {
        // default implementation do noting
    }

    /**
     * 自定义导出处理
     *
     * @param exportRequest 导出业务参数
     * @return 导出结果
     */
    default ExportResult customExport(ExportRequest exportRequest) {
        // 自定义导出逻辑，各个handler各自实现
        return null;
    }

}
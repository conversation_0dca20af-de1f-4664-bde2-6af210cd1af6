package com.patsnap.drafting.transfer.export.handler.impl;

import cn.hutool.core.io.file.FileNameUtil;
import cn.hutool.core.text.StrPool;
import com.patsnap.common.exception.InternalServerErrorException;
import com.patsnap.common.request.UserIdHolder;
import com.patsnap.drafting.client.model.AiSpecificationComputeData;
import com.patsnap.drafting.enums.aispecification.JurisdictionEnum;
import com.patsnap.drafting.enums.aispecification.JurisdictionFigureFormatEnum;
import com.patsnap.drafting.enums.task.AiTaskContentTypeEnum;
import com.patsnap.drafting.enums.task.AiTaskTypeEnum;
import com.patsnap.drafting.manager.FileManager;
import com.patsnap.drafting.manager.aispecification.ClaimParseManager;
import com.patsnap.drafting.manager.aitask.AiTaskManager;
import com.patsnap.drafting.model.aispecification.FigureContentBO;
import com.patsnap.drafting.model.aispecification.SpecificationEmbodimentGenerateBO;
import com.patsnap.drafting.model.aispecification.SpecificationEmbodimentOutlineBO;
import com.patsnap.drafting.model.aispecification.SpecificationInitializationBO;
import com.patsnap.drafting.request.aispecification.EmbodimentGenerateItem;
import com.patsnap.drafting.request.aispecification.Figure;
import com.patsnap.drafting.transfer.export.enitty.ExportRequest;
import com.patsnap.drafting.transfer.export.enitty.ExportResult;
import com.patsnap.drafting.transfer.export.handler.AbstractExportHandler;
import com.patsnap.drafting.util.ExportUtils;
import com.patsnap.drafting.util.ParagraphUtils;
import com.patsnap.drafting.util.WordUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.entity.ContentType;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 专利说明书导出处理器
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AiSpecificationExportHandler extends AbstractExportHandler {

    private final AiTaskManager aiTaskManager;
    private final ClaimParseManager claimParseManager;
    private final FileManager fileManager;
    private final ResourceLoader resourceLoader;

    /** 导出模板路径 */
    private static final String EXPORT_TEMPLATE_PATH = "classpath:template/export/";
    
    /** 导出S3路径 */
    private static final String EXPORT_S3_PATH = "ai_drafting/%s/%s/%s";

    private static final List<String> CONTENT_FIELDS = List.of(
            AiTaskContentTypeEnum.CLAIM_FORMAT.getType(),
            AiTaskContentTypeEnum.SPECIFICATION_TECH_FIELD.getType(),
            AiTaskContentTypeEnum.SPECIFICATION_BACKGROUND.getType(),
            AiTaskContentTypeEnum.SUMMARY.getType(),
            AiTaskContentTypeEnum.SPECIFICATION_EMBODIMENT.getType(),
            AiTaskContentTypeEnum.ABSTRACT.getType(),
            AiTaskContentTypeEnum.FIGURES_DESCRIPTION.getType()
    );

    @Override
    public AiTaskTypeEnum getAiTaskTypeEnum() {
        return AiTaskTypeEnum.AI_SPECIFICATION;
    }

    @Override
    public void preExport(ExportRequest exportRequest) {
        // 1.设置文档正文指定字段
        super.exportContent(exportRequest, CONTENT_FIELDS);

        // 2.设置权利要求书
        String taskId = exportRequest.getTaskId();
        String claimText = Optional.ofNullable(claimParseManager.parseSimpleFormattedClmByTaskId(taskId))
                .map(AiSpecificationComputeData::getClaimText).orElse(StringUtils.EMPTY);
        exportRequest.getPlaceholderData().put(AiTaskContentTypeEnum.CLAIM_FORMAT.getType(), ParagraphUtils.replaceMultipleCarriageReturn(claimText));
        SpecificationInitializationBO initializationBO = aiTaskManager.getTaskContent(taskId, AiTaskContentTypeEnum.INITIALIZATION);
        String jurisdiction = initializationBO.getJurisdiction();

        // 2.1 ⭐新增⭐ 兼容新老实施例数据格式
        handleEmbodimentCompatibility(exportRequest, jurisdiction);
        
        // 3.设置附图相关内容 ⭐新增⭐
        addImageContent(exportRequest);

        // 4.设置文档底部引用列表
        super.exportReferences(exportRequest);
        // 5.根据不同受理局获取导出模板
        SpecificationInitializationBO taskContent = aiTaskManager.getTaskContent(exportRequest.getTaskId(), AiTaskContentTypeEnum.INITIALIZATION);
        Optional.ofNullable(taskContent)
                .map(SpecificationInitializationBO::getJurisdiction)
                .ifPresent(exportRequest::setTemplateNameSuffix);
        
    }
    
    private String getExportTemplateNameWithSuffix(ExportRequest exportRequest) {
        String templateNameSuffix = exportRequest.getTemplateNameSuffix();
        if (StringUtils.isBlank(templateNameSuffix)) {
            return exportRequest.getTemplateName();
        }
        String templateName = exportRequest.getTemplateName();
        String name = FileNameUtil.mainName(templateName);
        String templateFileType = FileNameUtil.extName(templateName);
        return name + StrPool.UNDERLINE + StringUtils.lowerCase(templateNameSuffix) + StrPool.DOT + templateFileType;
    }

    @Override
    public ExportResult customExport(ExportRequest exportRequest) {
        try {
            // 构建模板资源路径
            Resource templateResource = resourceLoader.getResource(EXPORT_TEMPLATE_PATH + this.getExportTemplateNameWithSuffix(exportRequest));
            if (!templateResource.exists()) {
                templateResource = resourceLoader.getResource(EXPORT_TEMPLATE_PATH + exportRequest.getTemplateName());
            }
            
            // 使用支持图片的Word处理方法
            Map<String, String> textReplacements = exportRequest.getPlaceholderData();
            Map<String, List<WordUtils.ImageData>> imageReplacements = buildImageReplacements(exportRequest);
            
            byte[] documentBytes = WordUtils.replaceInWordWithImages(templateResource, textReplacements, imageReplacements);
            
            // 上传到S3
            String s3key = String.format(EXPORT_S3_PATH, UserIdHolder.get(), System.currentTimeMillis(), exportRequest.getFileName());
            String url = fileManager.uploadFile2AmazonS3(documentBytes, s3key, ContentType.MULTIPART_FORM_DATA);
            
            return ExportResult.builder().fileName(exportRequest.getFileName()).url(url).build();
            
        } catch (Exception e) {
            log.error("说明书导出失败, taskId: {}", exportRequest.getTaskId(), e);
            throw new InternalServerErrorException("说明书导出失败");
        }
    }

    /**
     * 添加图片内容到导出请求中
     */
    private void addImageContent(ExportRequest exportRequest) {
        // 附图说明已经在CONTENT_FIELDS中处理，这里不需要额外处理
        
        // 为图片占位符设置空字符串，实际图片通过imageReplacements处理
        exportRequest.getPlaceholderData().put("figures", "");
        exportRequest.getPlaceholderData().put("abstract_figure", "");
    }

    /**
     * 构建图片替换数据
     */
    private Map<String, List<WordUtils.ImageData>> buildImageReplacements(ExportRequest exportRequest) {
        Map<String, List<WordUtils.ImageData>> imageReplacements = new HashMap<>();
        String taskId = exportRequest.getTaskId();
        String lang = exportRequest.getLang();

        log.info("开始构建图片替换数据, taskId: {}, lang: {}", taskId, lang);

        // 构建附图数据
        List<WordUtils.ImageData> figuresData = buildFiguresImageData(taskId);
        if (!figuresData.isEmpty()) {
            imageReplacements.put("figures", figuresData);
            log.info("添加附图数据, 数量: {}", figuresData.size());
        } else {
            log.warn("附图数据为空");
        }

        // 构建摘要附图数据
        List<WordUtils.ImageData> abstractFigureData = buildAbstractFigureImageData(taskId);
        if (!abstractFigureData.isEmpty()) {
            imageReplacements.put("abstract_figure", abstractFigureData);
            log.info("添加摘要附图数据, 数量: {}", abstractFigureData.size());
        } else {
            log.warn("摘要附图数据为空");
        }

        log.info("图片替换数据构建完成, 总占位符数量: {}", imageReplacements.size());
        return imageReplacements;
    }

    /**
     * 构建附图图片数据 - 多张图片，根据受理局设置不同格式
     */
    private List<WordUtils.ImageData> buildFiguresImageData(String taskId) {
        List<WordUtils.ImageData> imageDataList = new ArrayList<>();
        
        try {
            log.info("开始构建附图数据, taskId: {}", taskId);
            FigureContentBO figureContentBO = aiTaskManager.getTaskContent(taskId, AiTaskContentTypeEnum.FIGURES);
            if (figureContentBO == null || figureContentBO.getFigures() == null || figureContentBO.getFigures().isEmpty()) {
                log.warn("未找到附图数据, taskId: {}", taskId);
                return imageDataList;
            }
            
            log.info("找到附图数量: {}, taskId: {}", figureContentBO.getFigures().size(), taskId);

            // 获取受理局信息
            SpecificationInitializationBO taskContent = aiTaskManager.getTaskContent(taskId, AiTaskContentTypeEnum.INITIALIZATION);
            String jurisdiction = Optional.ofNullable(taskContent)
                    .map(SpecificationInitializationBO::getJurisdiction)
                    .orElse(null);
            
            // 根据受理局获取格式规则
            JurisdictionFigureFormatEnum formatEnum = JurisdictionFigureFormatEnum.fromJurisdiction(jurisdiction);
            
            for (int i = 0; i < figureContentBO.getFigures().size(); i++) {
                Figure figure = figureContentBO.getFigures().get(i);
                log.debug("处理第{}张图片, figure: {}", i + 1, figure != null ? figure.getUniqueId() : "null");
                
                if (figure != null && figure.getImage() != null) {
                    String base64Data = getImageBase64Data(figure);
                    log.debug("第{}张图片base64数据长度: {}", i + 1, base64Data != null ? base64Data.length() : 0);
                    
                    if (StringUtils.isNotBlank(base64Data)) {
                        // 根据受理局格式生成标题
                        String title = formatEnum.generateTitle(i + 1);
                        log.info("生成图片数据: 标题={}, 受理局={}", title, jurisdiction);
                        
                        // 创建图片数据对象，使用默认构造函数，不固定宽高
                        WordUtils.ImageData imageData = new WordUtils.ImageData(title, base64Data);

                        // 设置等比例缩放的最大尺寸（让一页能放2张图）
                        // A4纸宽度约595磅，去掉页边距约500磅，高度约842磅，去掉页边距约700磅
                        // 为了让一页放2张图（上下排列），每张图最大高度约320磅，最大宽度约450磅
                        int[] scaledDimensions = calculateScaledDimensions(base64Data, 450, 320);
                        imageData.setWidth(scaledDimensions[0]);
                        imageData.setHeight(scaledDimensions[1]);

                        log.info("图片缩放尺寸: 宽度={}, 高度={}, 标题={}", scaledDimensions[0], scaledDimensions[1], title);
                        
                        // 设置标题格式
                        imageData.setTitleBold(false);
                        
                        // 根据受理局设置图片和标题的对齐方式
                        configureImageAlignment(imageData, formatEnum);
                        
                        imageDataList.add(imageData);
                    } else {
                        log.warn("第{}张图片base64数据为空", i + 1);
                    }
                } else {
                    log.warn("第{}张图片数据无效: figure={}, image={}", i + 1, 
                            figure != null, figure != null ? figure.getImage() : null);
                }
            }
            
            log.info("构建附图数据完成, 共{}张图片, taskId: {}", imageDataList.size(), taskId);
            
        } catch (Exception e) {
            log.error("构建附图数据失败, taskId: {}", taskId, e);
        }
        
        return imageDataList;
    }

    /**
     * 根据受理局格式配置图片对齐方式
     */
    private void configureImageAlignment(WordUtils.ImageData imageData, JurisdictionFigureFormatEnum formatEnum) {
        // 设置图片对齐方式
        WordUtils.ImageAlignment imageAlignment = convertToImageAlignment(formatEnum.getFigureAlignment());
        imageData.setImageAlignment(imageAlignment);
        
        // 设置标题位置
        WordUtils.TitlePosition titlePosition = convertToTitlePosition(formatEnum.getTitlePosition());
        imageData.setTitlePosition(titlePosition);
        
        // 设置标题对齐方式
        WordUtils.TitleAlignment titleAlignment = convertToTitleAlignment(formatEnum.getTitleAlignment());
        imageData.setTitleAlignment(titleAlignment);
    }
    
    /**
     * 转换图片对齐方式枚举
     */
    private WordUtils.ImageAlignment convertToImageAlignment(JurisdictionFigureFormatEnum.FigureAlignment figureAlignment) {
        switch (figureAlignment) {
            case LEFT:
                return WordUtils.ImageAlignment.LEFT;
            case RIGHT:
                return WordUtils.ImageAlignment.RIGHT;
            case CENTER:
            default:
                return WordUtils.ImageAlignment.CENTER;
        }
    }
    
    /**
     * 转换标题位置枚举
     */
    private WordUtils.TitlePosition convertToTitlePosition(JurisdictionFigureFormatEnum.TitlePosition titlePosition) {
        switch (titlePosition) {
            case ABOVE:
                return WordUtils.TitlePosition.ABOVE;
            case BELOW:
            default:
                return WordUtils.TitlePosition.BELOW;
        }
    }
    
    /**
     * 转换标题对齐方式枚举
     */
    private WordUtils.TitleAlignment convertToTitleAlignment(JurisdictionFigureFormatEnum.TitleAlignment titleAlignment) {
        switch (titleAlignment) {
            case LEFT:
                return WordUtils.TitleAlignment.LEFT;
            case RIGHT:
                return WordUtils.TitleAlignment.RIGHT;
            case CENTER:
            default:
                return WordUtils.TitleAlignment.CENTER;
        }
    }

    /**
     * 构建摘要附图图片数据 - 单张图片，根据地区显示不同样式的标题
     */
    private List<WordUtils.ImageData> buildAbstractFigureImageData(String taskId) {
        List<WordUtils.ImageData> imageDataList = new ArrayList<>();
        
        try {
            FigureContentBO abstractFigureContentBO = aiTaskManager.getTaskContent(taskId, AiTaskContentTypeEnum.ABSTRACT_FIGURE);
            if (abstractFigureContentBO == null || abstractFigureContentBO.getFigures() == null || abstractFigureContentBO.getFigures().isEmpty()) {
                return imageDataList;
            }

            // 获取第一张摘要附图（通常只有一张）
            Figure abstractFigure = abstractFigureContentBO.getFigures().get(0);
            if (abstractFigure != null && abstractFigure.getImage() != null) {
                String base64Data = getImageBase64Data(abstractFigure);
                
                if (StringUtils.isNotBlank(base64Data)) {
                    // 创建摘要附图数据对象，使用等比例缩放
                    WordUtils.ImageData imageData = new WordUtils.ImageData(null, base64Data);

                    // 摘要附图通常单独占用一页的部分空间，可以设置稍大的尺寸
                    // 最大宽度450磅，最大高度400磅
                    int[] scaledDimensions = calculateScaledDimensions(base64Data, 450, 400);
                    imageData.setWidth(scaledDimensions[0]);
                    imageData.setHeight(scaledDimensions[1]);

                    log.info("摘要附图缩放尺寸: 宽度={}, 高度={}", scaledDimensions[0], scaledDimensions[1]);

                    imageDataList.add(imageData);
                }
            }
            
        } catch (Exception e) {
            log.error("构建摘要附图数据失败, taskId: {}", taskId, e);
        }
        
        return imageDataList;
    }

    /**
     * 计算等比例缩放后的图片尺寸
     *
     * @param base64Data 图片的base64数据
     * @param maxWidth 最大宽度（磅）
     * @param maxHeight 最大高度（磅）
     * @return 缩放后的尺寸数组 [宽度, 高度]
     */
    private int[] calculateScaledDimensions(String base64Data, int maxWidth, int maxHeight) {
        try {
            // 解码base64获取图片信息
            String cleanBase64Data = WordUtils.cleanBase64Data(base64Data);
            byte[] imageBytes = Base64.getDecoder().decode(cleanBase64Data);

            // 使用ImageIO读取图片尺寸
            try (java.io.ByteArrayInputStream bis = new java.io.ByteArrayInputStream(imageBytes)) {
                java.awt.image.BufferedImage image = javax.imageio.ImageIO.read(bis);
                if (image != null) {
                    int originalWidth = image.getWidth();
                    int originalHeight = image.getHeight();

                    // 计算缩放比例，保持原始宽高比
                    double widthRatio = (double) maxWidth / originalWidth;
                    double heightRatio = (double) maxHeight / originalHeight;
                    double scalingRatio = Math.min(widthRatio, heightRatio);

                    // 如果原图尺寸已经小于最大尺寸，则不需要缩放
                    if (scalingRatio >= 1.0) {
                        return new int[]{originalWidth, originalHeight};
                    }

                    int scaledWidth = (int) Math.round(originalWidth * scalingRatio);
                    int scaledHeight = (int) Math.round(originalHeight * scalingRatio);

                    log.debug("图片等比例缩放: 原始尺寸={}x{}, 缩放比例={}, 缩放后尺寸={}x{}",
                            originalWidth, originalHeight, scalingRatio, scaledWidth, scaledHeight);

                    return new int[]{scaledWidth, scaledHeight};
                }
            }

        } catch (Exception e) {
            log.warn("计算图片缩放尺寸失败，使用默认尺寸", e);
        }

        // 如果无法获取原始尺寸，返回默认最大尺寸
        return new int[]{maxWidth, maxHeight};
    }

    /**
     * 获取图片的base64数据
     */
    private String getImageBase64Data(Figure figure) {
        try {
            // 优先使用标注图片
            if (StringUtils.isNotBlank(figure.getAnnotationImageBase64())) {
                return figure.getAnnotationImageBase64();
            }
            
            // 最后尝试通过S3Key获取签名URL并下载
            if (figure.getImage() != null && StringUtils.isNotBlank(figure.getImage().getS3Key())) {
                String signedUrl = fileManager.signFile(figure.getImage().getS3Key());
                if (StringUtils.isNotBlank(signedUrl)) {
                    return WordUtils.downloadImageToBase64(signedUrl);
                }
            }
            
        } catch (Exception e) {
            log.error("获取图片base64数据失败: {}", figure.getUniqueId(), e);
        }
        
        return null;
    }

    /**
     * 处理实施例数据兼容性
     * 优先使用新格式(SPECIFICATION_EMBODIMENT_CONTENT)，如果不存在则使用老格式(SPECIFICATION_EMBODIMENT)
     */
    private void handleEmbodimentCompatibility(ExportRequest exportRequest, String jurisdiction) {
        String taskId = exportRequest.getTaskId();
        String embodimentContent = StringUtils.EMPTY;

        try {
            // 1. 优先尝试获取新格式的实施例数据
            SpecificationEmbodimentGenerateBO embodimentGenerateBO = aiTaskManager.getTaskContent(taskId, AiTaskContentTypeEnum.SPECIFICATION_EMBODIMENT_CONTENT);
            if (embodimentGenerateBO != null && CollectionUtils.isNotEmpty(embodimentGenerateBO.getEmbodimentGenerateItems())) {
                // 将新格式的结构化数据转换为字符串格式，用于导出
                embodimentContent = convertEmbodimentGenerateBOToString(embodimentGenerateBO, jurisdiction);
                log.info("使用新格式实施例数据进行导出, taskId: {}, 实施例数量: {}", taskId, embodimentGenerateBO.getEmbodimentGenerateItems().size());
                // 再查询一下实施例大纲的数据，大纲里的头尾数据也需要拼接在实施例内容前后
                SpecificationEmbodimentOutlineBO outlineContent = aiTaskManager.getTaskContent(taskId, AiTaskContentTypeEnum.SPECIFICATION_EMBODIMENT_OUTLINE);
                if (Objects.nonNull(outlineContent) && CollectionUtils.isNotEmpty(outlineContent.getOutlines())) {
                    StringBuilder outlineContentBuilder = new StringBuilder();
                    // 拼接大纲头部
                    outlineContentBuilder.append(outlineContent.getOutlines().get(0).getText()).append("\n\n");
                    // 拼接实施例内容
                    outlineContentBuilder.append(embodimentContent).append("\n\n");
                    // 拼接大纲尾部
                    outlineContentBuilder.append(outlineContent.getOutlines().get(outlineContent.getOutlines().size() - 1).getText());
                    embodimentContent = outlineContentBuilder.toString();
                } else {
                    log.info("未找到实施例大纲数据, taskId: {}", taskId);
                }
            } else {
                // 2. 如果新格式不存在，使用老格式的实施例数据
                String oldEmbodimentContent = aiTaskManager.getTaskContent(taskId, AiTaskContentTypeEnum.SPECIFICATION_EMBODIMENT);
                if (StringUtils.isNotBlank(oldEmbodimentContent)) {
                    embodimentContent = oldEmbodimentContent;
                    log.info("使用老格式实施例数据进行导出, taskId: {}", taskId);
                } else {
                    log.warn("未找到实施例数据, taskId: {}", taskId);
                }
            }
        } catch (Exception e) {
            log.error("处理实施例数据兼容性时发生错误, taskId: {}", taskId, e);
            // 如果处理新格式失败，尝试使用老格式作为备用方案
            try {
                String oldEmbodimentContent = aiTaskManager.getTaskContent(taskId, AiTaskContentTypeEnum.SPECIFICATION_EMBODIMENT);
                if (StringUtils.isNotBlank(oldEmbodimentContent)) {
                    embodimentContent = oldEmbodimentContent;
                    log.info("使用老格式实施例数据作为备用方案, taskId: {}", taskId);
                }
            } catch (Exception fallbackException) {
                log.error("备用方案也失败, taskId: {}", taskId, fallbackException);
            }
        }

        // 设置实施例内容到导出占位符
        exportRequest.getPlaceholderData().put(AiTaskContentTypeEnum.SPECIFICATION_EMBODIMENT.getType(),
                ParagraphUtils.replaceMultipleCarriageReturn(embodimentContent));
    }

    /**
     * 将新格式的实施例数据转换为字符串格式
     */
    private String convertEmbodimentGenerateBOToString(SpecificationEmbodimentGenerateBO embodimentGenerateBO, String jurisdiction) {
        if (embodimentGenerateBO == null || CollectionUtils.isEmpty(embodimentGenerateBO.getEmbodimentGenerateItems())) {
            return StringUtils.EMPTY;
        }

        StringBuilder sb = new StringBuilder();
        List<EmbodimentGenerateItem> items = embodimentGenerateBO.getEmbodimentGenerateItems();
        // items按照内部的embodimentNumber排序一下，从小到大
        items.sort(Comparator.comparingInt(EmbodimentGenerateItem::getEmbodimentNumber));

        for (int i = 0; i < items.size(); i++) {
            EmbodimentGenerateItem item = items.get(i);
            if (item != null && StringUtils.isNotBlank(item.getText())) {
                // 添加标题：实施例一，实施例二等（只有CNIPA需要）
                if (StringUtils.equals(JurisdictionEnum.CNIPA.name(), jurisdiction)) {
                    String title = String.format("实施例%s\n\n", ExportUtils.convertNumberToChinese(item.getEmbodimentNumber()));
                    sb.append(title);
                }

                // 添加实施例内容
                sb.append(item.getText());

                // 如果不是最后一个实施例，添加分段符
                if (i < items.size() - 1) {
                    sb.append("\n\n");
                }
            }
        }

        return sb.toString();
    }
}

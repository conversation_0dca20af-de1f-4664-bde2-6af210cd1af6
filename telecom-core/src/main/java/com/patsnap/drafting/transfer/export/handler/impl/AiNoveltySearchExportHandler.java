package com.patsnap.drafting.transfer.export.handler.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.file.FileNameUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.patsnap.common.exception.InternalServerErrorException;
import com.patsnap.common.request.UserIdHolder;
import com.patsnap.drafting.client.model.NoveltySearchFeatureExtractDataDTO;
import com.patsnap.drafting.client.model.NoveltySearchFeatureExtractResDTO;
import com.patsnap.drafting.client.model.NoveltySearchSummaryResDTO;
import com.patsnap.drafting.client.model.ainovelty.AiNoveltySearchResponse;
import com.patsnap.drafting.client.model.ainovelty.ProcessDetail;
import com.patsnap.drafting.client.model.ainovelty.QueryInfo;
import com.patsnap.drafting.enums.ainoveltysearch.NoveltySearchPatentTypeEnum;
import com.patsnap.drafting.enums.task.AiTaskContentTypeEnum;
import com.patsnap.drafting.enums.task.AiTaskTypeEnum;
import com.patsnap.drafting.manager.FileManager;
import com.patsnap.drafting.manager.ainoveltysearch.NoveltySearchManager;
import com.patsnap.drafting.manager.aitask.AiTaskManager;
import com.patsnap.drafting.manager.patent.PatentInfoManager;
import com.patsnap.drafting.request.ainoveltysearch.*;
import com.patsnap.drafting.request.aitask.AiTaskReqDTO;
import com.patsnap.drafting.request.patentinfo.PatentInfoRequestDTO;
import com.patsnap.drafting.response.ainoveltysearch.NoveltySearchComparativeLiteratureResDTO;
import com.patsnap.drafting.response.ainoveltysearch.NoveltySearchComputeDTO;
import com.patsnap.drafting.response.ainoveltysearch.NoveltySearchReportTitleResponseDTO;
import com.patsnap.drafting.transfer.export.enitty.ExportRequest;
import com.patsnap.drafting.transfer.export.enitty.ExportResult;
import com.patsnap.drafting.transfer.export.handler.AbstractExportHandler;
import com.patsnap.drafting.util.DateUtils;
import com.patsnap.drafting.util.NoveltySearchExportUtils;
import static com.patsnap.drafting.constants.ExportConstant.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.entity.ContentType;
import org.apache.poi.xwpf.usermodel.*;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Component;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static com.patsnap.drafting.constants.ExportConstant.*;
import static com.patsnap.drafting.util.NoveltySearchExportUtils.*;

/**
* <AUTHOR> chendepeng
* @Date 2025/3/24 14:45
*/
@Slf4j
@Component
@RequiredArgsConstructor
public class AiNoveltySearchExportHandler extends AbstractExportHandler {
    private final AiTaskManager aiTaskManager;
    private final FileManager fileManager;
    private final PatentInfoManager patentInfoManager;
    private final NoveltySearchManager noveltySearchManager;
    private final ResourceLoader resourceLoader;
    private final ObjectMapper objectMapper;

    @Value("${configs.com.patsnap.domain.eureka:eureka.zhihuiya.com}")
    private String eurekaDomain;

    // Map结构，存放技术特征和技术特征分类编号的对应关系，用于在原文中高亮展示
    private final Map<String, String> TECHNICAL_FEATURE_CATEGORY_MAP = new HashMap<>();
    private final Map<String, String> TECHNICAL_FEATURE_CATEGORY_COLOR_MAP = new HashMap<>();
    public static final String FONT_FAMILY = "等线";
    public static final int TABLE_FONT_SIZE = 9;

    private static final String LANG_CN = "CN";

    // ==================== 报告基本信息 ====================

    public static final String CONCLUSION_AND_SUGGESTIONS_TITLE = "CONCLUSION_AND_SUGGESTIONS_TITLE";

    public static final String DISCLAIMER_TITLE = "DISCLAIMER_TITLE";

    public static final String FEATURE_COMPARISON_TABLE_TITLE = "FEATURE_COMPARISON_TABLE_TITLE";

    public static final String FEATURE_VS_LITERATURE_TITLE = "FEATURE_VS_LITERATURE_TITLE";

    public static final String INVENTION_POINTS_TITLE = "INVENTION_POINTS_TITLE";

    public static final String NOVELTY_EVALUATION_TITLE = "NOVELTY_EVALUATION_TITLE";

    public static final String REPORTER_LABEL = "REPORTER_LABEL";

    public static final String REPORTER_NAME_PLACEHOLDER = "REPORTER_NAME_PLACEHOLDER";

    public static final String REPORT_DATE_PLACEHOLDER = "REPORT_DATE_PLACEHOLDER";

    public static final String REPORT_TIME_LABEL = "REPORT_TIME_LABEL";

    public static final String REPORT_TITLE = "REPORT_TITLE";

    public static final String SEARCH_FEATURES_TITLE = "SEARCH_FEATURES_TITLE";

    // ==================== 检索相关 ====================

    public static final String APPENDIX_SEARCH_STRATEGY = "APPENDIX_SEARCH_STRATEGY";

    public static final String APPENDIX_SEARCH_STRATEGY_TABLE = "APPENDIX_SEARCH_STRATEGY_TABLE";

    public static final String APPENDIX_SEARCH_RESULT = "APPENDIX_SEARCH_RESULT";

    public static final String APPENDIX_SEARCH_RESULT_TABLE = "APPENDIX_SEARCH_RESULT_TABLE";

    public static final String SEARCH_FEATURES_TABLE = "SEARCH_FEATURES_TABLE";

    public static final String SEARCH_SUMMARY = "SEARCH_SUMMARY";

    // ==================== 技术方案相关 ====================

    public static final String FEATURE_COMPARISON_TABLE = "FEATURE_COMPARISON_TABLE";

    public static final String FEATURE_VS_LITERATURE_TABLE = "FEATURE_VS_LITERATURE_TABLE";

    public static final String TECHNICAL_KEY_ORIGINAL_TEXT = "TECHNICAL_KEY_ORIGINAL_TEXT";

    // ==================== 文献相关 ====================

    public static final String LITERATURE_INTERPRETATION = "LITERATURE_INTERPRETATION";

    // ==================== 评述相关 ====================

    public static final String NOVELTY_EVALUATION_TEXT = "NOVELTY_EVALUATION_TEXT";

    // ==================== 结论建议 ====================

    public static final String CONCLUSION_RECOMMENDATION = "CONCLUSION_RECOMMENDATION";

    // ==================== 免责声明 ====================

    public static final String DISCLAIMER_CONTENT = "DISCLAIMER_CONTENT";

    @Override
    public AiTaskTypeEnum getAiTaskTypeEnum() {
        return AiTaskTypeEnum.AI_NOVELTY_SEARCH;
    }

    @Override
    public void preExport(ExportRequest exportRequest) {
        Map<String, String> placeholderData = exportRequest.getPlaceholderData();
        // 封面
        handleTaskTitleInfo(placeholderData, exportRequest.getTaskId(), exportRequest.getLang());
        // 发明要点
        handleMainPointInfo(placeholderData, exportRequest.getTaskId(), exportRequest.getLang());
        // 表格标题
        handleTableTitle(placeholderData, exportRequest.getLang());
        // 新颖性评述
        handleReviewReport(placeholderData, exportRequest.getTaskId(), exportRequest.getLang());
        // 结论和建议
        handleConclusionAdvice(placeholderData, exportRequest.getTaskId(), exportRequest.getLang());
        // 免责声明
        handleDisclaimer(placeholderData, exportRequest.getLang());
    }

    private void handleDisclaimer(Map<String, String> placeholderData, String lang) {
        String disclaimerCn = "本报告部分内容由AI自动生成，仅供参考。本报告仅作为初步专利检索的参考性材料使用，未经专业人士确认前，不可作为法律意见使用，智慧芽不承担因使用、误用或依赖本报告导致的任何直接、间接或特殊损失。报告中的技术分析框架、算法模型及相关数据资产均受著作权法及商业秘密保护，未经授权方书面许可禁止反向工程、商业性传播或用于衍生开发。";
        String disclaimerEn = "Part of this report is automatically generated by AI and is for reference only. This report is only used as a reference material for preliminary patent search and cannot be used as legal advice without confirmation by professionals. patsnap does not assume any direct, indirect or special losses caused by the use, misuse or reliance on this report. The technical analysis framework, algorithm models and related data assets in the report are protected by copyright law and trade secrets. Reverse engineering, commercial dissemination or use for derivative development is prohibited without the written permission of the authorized party.";

        String titleContent = LANG_CN.equalsIgnoreCase(lang) ? "免责声明" : "Disclaimer";

        placeholderData.put(DISCLAIMER_TITLE, titleContent);
        placeholderData.put(DISCLAIMER_CONTENT, LANG_CN.equalsIgnoreCase(lang) ? disclaimerCn : disclaimerEn);
    }

    private void handleConclusionAdvice(Map<String, String> placeholderData, String taskId, String lang) {
        AiNoveltySearchResponse aiSearchResultInfo = aiTaskManager.getTaskContent(taskId, AiTaskContentTypeEnum.NOVELTY_SEARCH_AGENT_RESULT);
        if(Objects.isNull(aiSearchResultInfo)) {
            return;
        }
        List<AiSearchFinalResult> finalResult = aiSearchResultInfo.getFinalResult();
        if(CollectionUtils.isEmpty(finalResult)) {
            return;
        }

        String conclusionCn = "";
        String conclusionEn = "";
        String conclusionJp = "";

        // 根据对比文献X，Y，A种类的情况固定设置结论和建议
        boolean hasX = finalResult.stream().anyMatch(item -> NoveltySearchPatentTypeEnum.X.getValue().equals(item.getComparativeLiteratureType()));
        boolean hasY = finalResult.stream().anyMatch(item -> NoveltySearchPatentTypeEnum.Y.getValue().equals(item.getComparativeLiteratureType()));
        boolean hasA = finalResult.stream().anyMatch(item -> NoveltySearchPatentTypeEnum.A.getValue().equals(item.getComparativeLiteratureType()));
        if(hasX) {
            // 有X
            conclusionCn = "您的技术方案相对于检索到的现有技术不具备新颖性，建议优化技术方案。";
            conclusionEn = "Your technical solution lacks novelty compared to the retrieved prior art. It is recommended to optimize the technical solution.";
            conclusionJp = "お客様の技術案は、検索された既存技術と比較して新規性を有しません。技術案の最適化を推奨します。";
        }
        if(!hasX && hasY) {
            // 有Y无X
            conclusionCn = "您的技术方案相对于检索到的现有技术不具备创造性，建议优化技术方案。";
            conclusionEn = "Your technical solution lacks inventiveness compared to the retrieved prior art. It is recommended to optimize the technical solution.";
            conclusionJp = "お客様の技術案は、検索された既存技術と比較して創造性を有しません。技術案の最適化を推奨します。";
        }
        if(!hasX && !hasY && hasA) {
            // 仅有A
            conclusionCn = "您的技术方案相对于检索到的现有技术具备新颖性和创造性，建议申请专利。";
            conclusionEn = "Your technical solution demonstrates both novelty and inventiveness compared to the retrieved prior art. It is recommended to file a patent application.";
            conclusionJp = "お客様の技術案は、検索された既存技術と比較して新規性および創造性を有しており、特許出願を推奨します。";
        }

        String titleContent = LANG_CN.equalsIgnoreCase(lang) ? "结论和建议" : "Summary";
        placeholderData.put(CONCLUSION_AND_SUGGESTIONS_TITLE, titleContent);
        placeholderData.put(CONCLUSION_RECOMMENDATION, LANG_CN.equalsIgnoreCase(lang) ? conclusionCn : conclusionEn);
    }

    private void handleReviewReport(Map<String, String> placeholderData, String taskId, String lang) {
        // 如果任务是英文的，则不需要导出新颖性评述
        String taskLanguage = aiTaskManager.getTaskContent(taskId, AiTaskContentTypeEnum.NOVELTY_SEARCH_INPUT_LANG);


        AiTaskReqDTO request = new AiTaskReqDTO();
        request.setTaskId(taskId);
        String result = null;
        String titleContent = "";
        String type = noveltySearchManager.getReportType(taskId);
        if (type.equals(NoveltySearchPatentTypeEnum.X.getValue())) {
            result = noveltySearchManager.getReportReviewOfNovelty(request).getReport();
            titleContent = LANG_CN.equalsIgnoreCase(lang) ? "新颖性评述" : "Novelty Assessment";
        } else if (type.equals(NoveltySearchPatentTypeEnum.Y.getValue())) {
            result = noveltySearchManager.getReportReviewOfCreative(request).getReport();
            titleContent = LANG_CN.equalsIgnoreCase(lang) ? "创造性评述" : "Inventive Step Assessment";
        } else if (type.equals(NoveltySearchPatentTypeEnum.A.getValue())) {
            titleContent = LANG_CN.equalsIgnoreCase(lang) ? "新颖性与创造性评述" : "Novelty and Inventiveness Assessment";
            if (LANG_CN.equals(lang)) {
                result = "检索到的对比文件中未发现影响该技术方案新颖性和创造性的文件，该技术方案具有新颖性和创造性。";
            }else {
                result = "";
            }
        }
        if("EN".equalsIgnoreCase(taskLanguage)) {
            result = "";
        }


        placeholderData.put(NOVELTY_EVALUATION_TITLE, titleContent);
        placeholderData.put(NOVELTY_EVALUATION_TEXT, result);
    }

    private void handleTableTitle(Map<String, String> placeholderData, String lang) {
        String firstTableTitle = LANG_CN.equalsIgnoreCase(lang) ? "检索要素" : "Search Elements";
        placeholderData.put(SEARCH_FEATURES_TITLE, firstTableTitle);
        String secondTableTile = LANG_CN.equalsIgnoreCase(lang) ? "技术特征对比表" : "Technical Feature Comparison Table";
        placeholderData.put(FEATURE_COMPARISON_TABLE_TITLE, secondTableTile);
        String thirdTableTile = LANG_CN.equalsIgnoreCase(lang) ? "技术特征与对比文献" : "Overall Reference Comparison & Similarity Score";
        placeholderData.put(FEATURE_VS_LITERATURE_TITLE, thirdTableTile);
        String fourthTableTile = LANG_CN.equalsIgnoreCase(lang) ? "附录1：检索策略" : "Appendix 1: Search Strategy";
        placeholderData.put(APPENDIX_SEARCH_STRATEGY, fourthTableTile);
        String fiveTableTitle = LANG_CN.equalsIgnoreCase(lang) ? "附录2：专利列表" : "Appendix 2: Patent List";
        placeholderData.put(APPENDIX_SEARCH_RESULT, fiveTableTitle);

        String tips = LANG_CN.equalsIgnoreCase(lang) ? "* 说明：引用文件的专用类型：\n" +
                "(仅针对权利要求或主要技术方案进行对比)\n" +
                "“X” 单独影响权利要求的新颖性或创造性的文件；\n" +
                "“Y” 与该检索报告中其它Y类文件组合影响权利要求创造性的文件；\n" +
                "“A” 背景技术文件，即反映权利要求的部分技术特征或者现有技术的一部分的文件；\n" : "* Description: Special type of reference file：\n" +
                "(Compare only the claims or main technical solutions)\n" +
                "“X” A document that can independently negate the novelty of the target technical solution.\n" +
                "“Y” A document requiring combination with other references to demonstrate lack of inventive step.\n" +
                "“A” Document describing the state of the art, for background information.\n";
        placeholderData.put(LITERATURE_INTERPRETATION, tips);
    }

    private void handleTaskTitleInfo(Map<String, String> placeholderData, String taskId, String lang) {
        // 查新任务相关的数据
        NoveltySearchReportTitleResponseDTO taskTitle = aiTaskManager.getTaskContent(taskId, AiTaskContentTypeEnum.NOVELTY_SEARCH_REPORT_TITLE);
        if(Objects.isNull(taskTitle)) {
            return;
        }
        placeholderData.put(REPORT_TITLE, taskTitle.getTitle() + (LANG_CN.equalsIgnoreCase(lang) ? "查新检索报告" : " Novelty Search Report"));
        placeholderData.put(REPORTER_LABEL, LANG_CN.equalsIgnoreCase(lang) ? "报告人" : "Lecturer");
        placeholderData.put(REPORTER_NAME_PLACEHOLDER, taskTitle.getCreator());
        placeholderData.put(REPORT_TIME_LABEL, LANG_CN.equalsIgnoreCase(lang) ? "报告时间" : "Time");
        String formatTime = DateUtils.formatDateByFormat(new DateTime(taskTitle.getGeneratedTime()), LANG_CN.equalsIgnoreCase(lang) ? "yyyy/MM/dd" : "dd MM yyyy");
        placeholderData.put(REPORT_DATE_PLACEHOLDER, formatTime);
        String subTitleTemplate = LANG_CN.equalsIgnoreCase(lang) ? SUB_TITLE_TEMPLATE_CN : SUB_TITLE_TEMPLATE_EN;
        String subTitleText = String.format(subTitleTemplate, taskTitle.getSearchQty(), taskTitle.getComparativeQty(), taskTitle.getNearestQty(), taskTitle.getSecondaryApproachQty());
        placeholderData.put(SEARCH_SUMMARY, subTitleText);
    }

    private void handleMainPointInfo(Map<String, String> placeholderData, String taskId, String lang) {
        NoveltySearchSummaryResDTO.Data techFeatureInfo = aiTaskManager.getTaskContent(taskId, AiTaskContentTypeEnum.NOVELTY_SEARCH_INPUT_SUMMARY);
        if(Objects.isNull(techFeatureInfo) || StringUtils.isBlank(techFeatureInfo.getTechSolution())) {
            return;
        }

        String titleContent = LANG_CN.equalsIgnoreCase(lang) ? "本技术方案的发明要点" : "Inventive Features of This Solution";
        placeholderData.put(INVENTION_POINTS_TITLE, titleContent);
        placeholderData.put(TECHNICAL_KEY_ORIGINAL_TEXT, techFeatureInfo.getTechSolution());
    }
    

    /**
     * 获取导出模板名称（模板可能根据国家、受理局等分类，业务方提供模板后缀区分，否则使用无后缀默认模板）
     *
     * @param exportRequest 导出请求
     * @return 导出模板名称
     */
    private String getExportTemplateNameWithSuffix(ExportRequest exportRequest) {
        String templateNameSuffix = exportRequest.getTemplateNameSuffix();
        if (StringUtils.isBlank(templateNameSuffix)) {
            return exportRequest.getTemplateName();
        }
        String templateName = exportRequest.getTemplateName();
        String name = FileNameUtil.mainName(templateName);
        String templateFileType = FileNameUtil.extName(templateName);
        return name + StrPool.UNDERLINE + StringUtils.lowerCase(templateNameSuffix) + StrPool.DOT + templateFileType;
    }

    @Override
    public ExportResult customExport(ExportRequest exportRequest) {
        try {
            // 1. 先获取模板并替换占位符，生成初始的Word文档字节数组
            Resource resource = resourceLoader.getResource(EXPORT_TEMPLATE_PATH + this.getExportTemplateNameWithSuffix(exportRequest));
            if (!resource.exists()) {
                resource = resourceLoader.getResource(EXPORT_TEMPLATE_PATH + exportRequest.getTemplateName());
            }
            byte[] resultBytes = NoveltySearchExportUtils.replaceInWord(resource, exportRequest.getPlaceholderData());
            
            // 2. 将字节数组转换为新的Word文档，并在指定标记位添加表格
            byte[] finalBytes = addTablesToDocument(resultBytes, exportRequest.getTaskId(), exportRequest.getLang());
            
            // 3. 上传到S3
            String s3key = String.format(EXPORT_S3_PATH, UserIdHolder.get(), System.currentTimeMillis(), exportRequest.getFileName());
            String url = fileManager.uploadFile2AmazonS3(finalBytes, s3key, ContentType.MULTIPART_FORM_DATA);
            return ExportResult.builder().fileName(exportRequest.getFileName()).url(url).build();
        } catch (Exception e) {
            log.error("ExportManager export failed", e);
            throw new InternalServerErrorException();
        }
    }

    /**
     * 在Word文档中的指定标记位添加表格
     * 
     * @param documentBytes 原始文档字节数组
     * @param taskId 任务ID
     * @param lang 语言
     * @return 添加表格后的文档字节数组
     */
    private byte[] addTablesToDocument(byte[] documentBytes, String taskId, String lang) throws Exception {
        try (ByteArrayInputStream bis = new ByteArrayInputStream(documentBytes);
             XWPFDocument document = new XWPFDocument(bis)) {
            
            // 查找并替换标记位，添加相应的表格
            addTablesAtMarkers(document, taskId, lang);
            
            // 将修改后的文档写入字节数组
            try (ByteArrayOutputStream out = new ByteArrayOutputStream()) {
                document.write(out);
                return out.toByteArray();
            }
        }
    }

    /**
     * 在文档的标记位添加表格
     * 
     * @param document Word文档
     * @param taskId 任务ID
     * @param lang 语言
     */
    private void addTablesAtMarkers(XWPFDocument document, String taskId, String lang) {
        // 遍历所有段落，查找标记位
        List<XWPFParagraph> paragraphs = document.getParagraphs();
        for (int i = 0; i < paragraphs.size(); i++) {
            XWPFParagraph paragraph = paragraphs.get(i);
            String text = paragraph.getText();
            
            if (text == null) {
                continue;
            }

            // 根据不同的标记位添加相应的表格
            if (text.contains("${"+SEARCH_FEATURES_TABLE+"}")) {
                XWPFParagraph titleParagraph = document.insertNewParagraph(document.getParagraphs().get(i).getCTP().newCursor());
                titleParagraph.setAlignment(ParagraphAlignment.LEFT);
                clearParagraph(paragraph);
                addTechnicalFeaturesAndKeywordsTable(document, titleParagraph, taskId, lang);
            } else if (text.contains("${"+FEATURE_COMPARISON_TABLE+"}")) {
                XWPFParagraph titleParagraph = document.insertNewParagraph(document.getParagraphs().get(i).getCTP().newCursor());
                titleParagraph.setAlignment(ParagraphAlignment.LEFT);
                clearParagraph(paragraph);
                handleTechnicalFeaturesComparisonTable(document, titleParagraph, taskId, lang);
            } else if (text.contains("${"+FEATURE_VS_LITERATURE_TABLE+"}")) {
                XWPFParagraph titleParagraph = document.insertNewParagraph(document.getParagraphs().get(i).getCTP().newCursor());
                titleParagraph.setAlignment(ParagraphAlignment.LEFT);
                clearParagraph(paragraph);
                handleTechnicalFeaturesComparisonLiterature(document, titleParagraph, taskId, lang);
            } else if (text.contains("${"+APPENDIX_SEARCH_STRATEGY_TABLE+"}")) {
                XWPFParagraph titleParagraph = document.insertNewParagraph(document.getParagraphs().get(i).getCTP().newCursor());
                titleParagraph.setAlignment(ParagraphAlignment.LEFT);
                clearParagraph(paragraph);
                handleAppendix1(document, titleParagraph, taskId, lang);
            } else if (text.contains("${"+APPENDIX_SEARCH_RESULT_TABLE+"}")) {
                XWPFParagraph titleParagraph = document.insertNewParagraph(document.getParagraphs().get(i).getCTP().newCursor());
                titleParagraph.setAlignment(ParagraphAlignment.LEFT);
                clearParagraph(paragraph);
                handleAppendix2(document, titleParagraph, taskId, lang);
            }
        }
    }

    /**
     * 清空段落内容
     */
    private void clearParagraph(XWPFParagraph paragraph) {
        // 清除所有runs
        for (int j = paragraph.getRuns().size() - 1; j >= 0; j--) {
            paragraph.removeRun(j);
        }
    }

    private void addTechnicalFeaturesAndKeywordsTable(XWPFDocument document, XWPFParagraph afterParagraph, String taskId, String lang) {

        NoveltySearchElementResDTO searchElementResDTO = aiTaskManager.getTaskContent(taskId, AiTaskContentTypeEnum.NOVELTY_SEARCH_RETRIEVAL_ELEMENTS);
        NoveltySearchFeatureExtractResDTO noveltySearchFeatureExtractResDTO = aiTaskManager.getTaskContent(taskId, AiTaskContentTypeEnum.NOVELTY_SEARCH_TECH_FEATURE);

        if(Objects.isNull(searchElementResDTO)) {
            return;
        }
        int index = -1;
        for (NoveltySearchFeatureExtractDataDTO featureItem : noveltySearchFeatureExtractResDTO.getData()) {
            if(!featureItem.getSelected()) {
                continue;
            }
            index++;
            String categoryText = TECHNICAL_FEATURE_CATEGORY.get(index);
            String categoryColor = TECHNICAL_FEATURE_CATEGORY_COLOR.get(index);
            TECHNICAL_FEATURE_CATEGORY_MAP.put(featureItem.getFeatureTextOriginal(), categoryText);
            TECHNICAL_FEATURE_CATEGORY_COLOR_MAP.put(featureItem.getFeatureTextOriginal(), categoryColor);
        }
        List<XWPFParagraph> paragraphs = document.getParagraphs();
        int titleIndex = paragraphs.indexOf(afterParagraph);
        Long columnSize = 3L;
        Long rowSize = 1L; // 表头行
        
        // 计算总行数：表头 + 所有选中的检索要素的express数量
        for (NoveltySearchElementDataDTO featureItem : searchElementResDTO.getData()) {
            if(!featureItem.getSelected()) {
                continue;
            }
            for (NoveltySearchElementDataDTO.Element element : featureItem.getExpress()) {
                rowSize++;
            }
        }
        
        XWPFTable table = document.insertNewTbl(document.getParagraphs().get(titleIndex).getCTP().newCursor());
        table.setWidth("100%");
        
        // 创建足够的行
        for (int i = 1; i < rowSize; i++) {
            table.createRow().setHeight(TABLE_ROW_HEIGHT);
        }
        
        // 确保每行都有3列
        for (int i = 0; i < rowSize; i++) {
            XWPFTableRow row = table.getRow(i);
            while (row.getTableCells().size() < columnSize) {
                row.createCell();
            }
        }

        // 设置表格边框
        table.setInsideHBorder(XWPFTable.XWPFBorderType.SINGLE, 4, 0, TABLE_BORDER_COLOR_BLUE);
        table.setInsideVBorder(XWPFTable.XWPFBorderType.SINGLE, 4, 0, TABLE_BORDER_COLOR_BLUE);
        table.setTopBorder(XWPFTable.XWPFBorderType.SINGLE, 4, 0, TABLE_BORDER_COLOR_BLUE);
        table.setBottomBorder(XWPFTable.XWPFBorderType.SINGLE, 4, 0, TABLE_BORDER_COLOR_BLUE);
        table.setLeftBorder(XWPFTable.XWPFBorderType.SINGLE, 4, 0, TABLE_BORDER_COLOR_BLUE);
        table.setRightBorder(XWPFTable.XWPFBorderType.SINGLE, 4, 0, TABLE_BORDER_COLOR_BLUE);

        // 设置表头
        setCellText(table, 0, 0, LANG_CN.equalsIgnoreCase(lang) ? "检索要素" : "Search Features", false, ParagraphAlignment.CENTER, TABLE_CELL_COLOR_BLUE);
        setCellText(table, 0, 1, LANG_CN.equalsIgnoreCase(lang) ? "技术特征" : "Technical Features", false, ParagraphAlignment.CENTER, TABLE_CELL_COLOR_BLUE);
        setCellText(table, 0, 2, LANG_CN.equalsIgnoreCase(lang) ? "相关IPC分类号" : "Related IPC Classification", false, ParagraphAlignment.CENTER, TABLE_CELL_COLOR_BLUE);

        // 填充表格数据
        int currentRowIndex = 1;
        for (NoveltySearchElementDataDTO featureItem : searchElementResDTO.getData()) {
            if(!featureItem.getSelected()) {
                continue;
            }
            // 记录当前检索要素开始的行索引，用于后续合并单元格
            int mergeStartRow = currentRowIndex;
            // 遍历该检索要素下的所有express元素
            for (NoveltySearchElementDataDTO.Element element : featureItem.getExpress()) {
                // 只在第一行设置检索要素文本，其他行留空（后续会合并）
                if (currentRowIndex == mergeStartRow) {
                    String techCategory = (LANG_CN.equalsIgnoreCase(lang) ? "技术特征 " : "Technical Feature ") + TECHNICAL_FEATURE_CATEGORY_MAP.get(featureItem.getFeatureTextOriginal());
                    setCellTextWithLineBreaks(table, currentRowIndex, 0, techCategory + "\n" + featureItem.getFeatureTextOriginal(), false, ParagraphAlignment.LEFT, "");
                } else {
                    setCellTextWithLineBreaks(table, currentRowIndex, 0, "", false, ParagraphAlignment.LEFT, "");
                }
                
                // 设置技术特征（格式：${keyword}的拓展词\n keyword1、keyword2）
                setCellTextWithBoldKeyword(table, currentRowIndex, 1, element, ParagraphAlignment.LEFT, "", lang);
                
                // 设置IPC分类号（从element的ipc列表中提取text并用逗号连接）
                String ipcClassifications = "";
                if (CollectionUtils.isNotEmpty(element.getIpc())) {
                    ipcClassifications = element.getIpc().stream()
                            .filter(Objects::nonNull)
                            .map(ipc -> ipc.getKey())
                            .filter(StringUtils::isNotBlank)
                            .collect(java.util.stream.Collectors.joining(",\n"));
                }
                setCellTextWithLineBreaks(table, currentRowIndex, 2, ipcClassifications, false, ParagraphAlignment.LEFT, "");
                
                currentRowIndex++;
            }
            
            // 合并第一列的单元格（检索要素列）
            int mergeEndRow = currentRowIndex - 1;
            if (mergeEndRow > mergeStartRow) {
                mergeCellsVertically(table, 0, mergeStartRow, mergeEndRow);
            }
        }

        // 调整列宽
        setColumnWidth(table, 0, 20); // 检索要素列
        setColumnWidth(table, 1, 50); // 技术特征列
        setColumnWidth(table, 2, 30); // IPC分类号列

        document.createParagraph();
    }

    /**
     * 设置单元格文本，其中关键词部分加粗显示
     * 格式：${keyword}的拓展词\n keyword1、keyword2
     * 
     * @param table 表格
     * @param row 行号
     * @param col 列号
     * @param element 检索要素元素
     * @param alignment 对齐方式
     * @param cellColor 单元格背景色
     */
    private void setCellTextWithBoldKeyword(XWPFTable table, int row, int col, NoveltySearchElementDataDTO.Element element, ParagraphAlignment alignment, String cellColor, String lang) {
        // 由于继承关系可能导致编译问题，使用反射获取text字段
        String keyword = "";
        try {
            keyword = element.getWord().getKey();
        } catch (Exception e) {
            // 如果反射失败，返回空字符串
            keyword = "";
        }
        
        if (element == null || StringUtils.isBlank(keyword)) {
            setCellText(table, row, col, "", false, alignment, cellColor);
            return;
        }
        
        XWPFTableCell cell = table.getRow(row).getCell(col);
        // 设置垂直对齐
        cell.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
        if (StringUtils.isNotBlank(cellColor)) {
            cell.setColor(cellColor);
        }

        XWPFParagraph paragraph = cell.getParagraphs().get(0);
        paragraph.setAlignment(alignment);
        paragraph.setSpacingBefore(0);
        paragraph.setSpacingAfter(0);
        
        // 添加加粗的关键词
        XWPFRun boldRun = paragraph.createRun();
        // 添加普通文本"的拓展词"
        XWPFRun normalRun = paragraph.createRun();

        if (LANG_CN.equalsIgnoreCase(lang)) {
            setText(boldRun, keyword, TABLE_FONT_SIZE, true);
            setText(normalRun, "的同类/扩展词", TABLE_FONT_SIZE, false);
        } else {
            setText(normalRun, "Equivalent and extended keywords of ", TABLE_FONT_SIZE, false);
            setText(boldRun, keyword , TABLE_FONT_SIZE, true);
        }

        // 尝试通过反射获取扩展词列表
        try {

            if (CollectionUtils.isNotEmpty(element.getExtend())) {
                // 添加换行
                XWPFRun breakRun = paragraph.createRun();
                breakRun.addBreak();
                
                // 添加扩展词列表，保持原有的换行格式
                List<String> keywordTexts = element.getExtend().stream()
                        .filter(Objects::nonNull)
                        .map(keywordItem -> {
                            return keywordItem.getKey();
                        })
                        .filter(StringUtils::isNotBlank)
                        .collect(java.util.stream.Collectors.toList());
                
                // 逐个添加关键词，保持换行格式
                for (int i = 0; i < keywordTexts.size(); i++) {
                    String keywordText = keywordTexts.get(i);
                    
                    // 处理包含换行符的文本
                    if (keywordText.contains("\n")) {
                        String[] lines = keywordText.split("\n");
                        for (int j = 0; j < lines.length; j++) {
                            XWPFRun keywordRun = paragraph.createRun();
                            setText(keywordRun, lines[j], TABLE_FONT_SIZE, false);
                            if (j < lines.length - 1) {
                                keywordRun.addBreak();
                            }
                        }
                    } else {
                        XWPFRun keywordRun = paragraph.createRun();
                        setText(keywordRun, keywordText, TABLE_FONT_SIZE, false);
                    }
                    
                    // 在关键词之间添加顿号分隔符（除了最后一个）
                    if (i < keywordTexts.size() - 1) {
                        XWPFRun separatorRun = paragraph.createRun();
                        setText(separatorRun, "、", TABLE_FONT_SIZE, false);
                    }
                }
            }
        } catch (Exception e) {
            // 如果反射失败，只显示主关键词
            // log.warn("Failed to access keyword field via reflection", e);
        }
    }

    /**
     * 构建技术特征文本，格式：${keyword}的拓展词\n keyword1、keyword2
     * 
     * @param element 检索要素元素
     * @return 格式化的技术特征文本
     */
    private String buildTechnicalFeatureText(NoveltySearchElementDataDTO.Element element) {
        // 由于继承关系可能导致编译问题，使用反射获取text字段
        String keyword = "";
        try {
            java.lang.reflect.Field textField = com.patsnap.drafting.request.ainoveltysearch.element.BasicElement.class.getDeclaredField("text");
            textField.setAccessible(true);
            keyword = (String) textField.get(element);
        } catch (Exception e) {
            // 如果反射失败，返回空字符串
            keyword = "";
        }
        
        if (element == null || StringUtils.isBlank(keyword)) {
            return "";
        }
        
        StringBuilder result = new StringBuilder();
        
        // 添加主关键词和说明
        result.append(keyword).append("的拓展词");
        
        // 尝试通过反射获取扩展词列表
        try {
            java.lang.reflect.Field keywordField = element.getClass().getDeclaredField("keyword");
            keywordField.setAccessible(true);
            @SuppressWarnings("unchecked")
            List<com.patsnap.drafting.request.ainoveltysearch.element.BasicElement> keywordList = 
                (List<com.patsnap.drafting.request.ainoveltysearch.element.BasicElement>) keywordField.get(element);
            
            if (CollectionUtils.isNotEmpty(keywordList)) {
                result.append("\n");
                List<String> keywordTexts = keywordList.stream()
                        .filter(Objects::nonNull)
                        .map(keywordItem -> {
                            try {
                                java.lang.reflect.Field textField = com.patsnap.drafting.request.ainoveltysearch.element.BasicElement.class.getDeclaredField("text");
                                textField.setAccessible(true);
                                return (String) textField.get(keywordItem);
                            } catch (Exception e) {
                                return "";
                            }
                        })
                        .filter(StringUtils::isNotBlank)
                        .collect(java.util.stream.Collectors.toList());
                
                // 保持原有的换行格式，用顿号连接
                String keywords = String.join("、", keywordTexts);
                result.append(keywords);
            }
        } catch (Exception e) {
            // 如果反射失败，只显示主关键词
            // log.warn("Failed to access keyword field via reflection", e);
        }
        
        return result.toString();
    }

    private void handleTechnicalFeaturesComparisonTable(XWPFDocument document, XWPFParagraph afterParagraph, String taskId, String lang) {
        // 检索技术特征和关键词（作为表格第一列值）
        NoveltySearchFeatureExtractResDTO techFeatureInfo = aiTaskManager.getTaskContent(taskId, AiTaskContentTypeEnum.NOVELTY_SEARCH_TECH_FEATURE);
        if(Objects.isNull(techFeatureInfo)) {
            return;
        }

        // 查新agent结果final_result变量里的值（作为表格第二列以及后面的数据）
        AiNoveltySearchResponse aiSearchResultInfo = aiTaskManager.getTaskContent(taskId, AiTaskContentTypeEnum.NOVELTY_SEARCH_AGENT_RESULT);
        if(Objects.isNull(aiSearchResultInfo)) {
            return;
        }
        List<XWPFParagraph> paragraphs = document.getParagraphs();
        int titleIndex = paragraphs.indexOf(afterParagraph);
        Long columnSize = aiSearchResultInfo.getFinalResult().stream().filter(AiSearchFinalResult::isSelected).count() + 2;
        Long rowSize = techFeatureInfo.getData().stream().filter(NoveltySearchFeatureExtractDataDTO::getSelected).count() + 6;
        XWPFTable table = document.insertNewTbl(document.getParagraphs().get(titleIndex).getCTP().newCursor());
        table.setWidth("100%");
        for (int i = 1; i < rowSize; i++) {
            table.createRow().setHeight(TABLE_ROW_HEIGHT);;
        }
        for (int i = 0; i < rowSize; i++) {
            XWPFTableRow row = table.getRow(i);
            while (row.getTableCells().size() < columnSize) {
                row.createCell();
            }
        }

        // 设置表格边框
        table.setInsideHBorder(XWPFTable.XWPFBorderType.SINGLE, 4, 0, TABLE_BORDER_COLOR_BLUE);
        table.setInsideVBorder(XWPFTable.XWPFBorderType.SINGLE, 4, 0, TABLE_BORDER_COLOR_BLUE);
        table.setTopBorder(XWPFTable.XWPFBorderType.SINGLE, 4, 0, TABLE_BORDER_COLOR_BLUE);
        table.setBottomBorder(XWPFTable.XWPFBorderType.SINGLE, 4, 0, TABLE_BORDER_COLOR_BLUE);
        table.setLeftBorder(XWPFTable.XWPFBorderType.SINGLE, 4, 0, TABLE_BORDER_COLOR_BLUE);
        table.setRightBorder(XWPFTable.XWPFBorderType.SINGLE, 4, 0, TABLE_BORDER_COLOR_BLUE);

        // 先设置一些固定行和列内容(有背景色)
        setCellText(table, 0, 0, "", false, ParagraphAlignment.LEFT, TABLE_CELL_COLOR_BLUE);
        setCellText(table, 1, 0, LANG_CN.equalsIgnoreCase(lang) ? "公开号" : "Publication Number", false, ParagraphAlignment.LEFT, TABLE_CELL_COLOR_BLUE);
        setCellText(table, 2, 0, LANG_CN.equalsIgnoreCase(lang) ? "标题" : "Title", false, ParagraphAlignment.LEFT, TABLE_CELL_COLOR_BLUE);
        setCellText(table, 3, 0, LANG_CN.equalsIgnoreCase(lang) ? "申请人" : "Applicant", false, ParagraphAlignment.LEFT, TABLE_CELL_COLOR_BLUE);
        setCellText(table, 4, 0, LANG_CN.equalsIgnoreCase(lang) ? "公开日" : "Publication Date", false, ParagraphAlignment.LEFT, TABLE_CELL_COLOR_BLUE);
        setCellText(table, 5, 0, LANG_CN.equalsIgnoreCase(lang) ? "文献类型" : "Document Type", false, ParagraphAlignment.LEFT, TABLE_CELL_COLOR_BLUE);

        setCellText(table, 0, 1, LANG_CN.equalsIgnoreCase(lang) ? "本发明特征" : "Technical Features of This Invention", false, ParagraphAlignment.LEFT, TABLE_CELL_COLOR_BLUE);
        setCellText(table, 1, 1, "", false, ParagraphAlignment.LEFT, TABLE_CELL_COLOR_BLUE);
        setCellText(table, 2, 1, "", false, ParagraphAlignment.LEFT, TABLE_CELL_COLOR_BLUE);
        setCellText(table, 3, 1, "", false, ParagraphAlignment.LEFT, TABLE_CELL_COLOR_BLUE);
        setCellText(table, 4, 1, "", false, ParagraphAlignment.LEFT, TABLE_CELL_COLOR_BLUE);
        setCellText(table, 5, 1, "", false, ParagraphAlignment.LEFT, TABLE_CELL_COLOR_BLUE);

        // 设置对比文献1-N的行和列内容(公开号，标题，申请人，公开日，文献类型)
        List<String> selectedPatentIds = aiSearchResultInfo.getFinalResult().stream().filter(AiSearchFinalResult::isSelected).map(AiSearchFinalResult::getPatentId).distinct().toList();
        if (CollectionUtils.isEmpty(selectedPatentIds)) {
            return;
        }

        PatentInfoRequestDTO patentInfoRequestDTO = new PatentInfoRequestDTO();
        patentInfoRequestDTO.setPatentIds(selectedPatentIds);
        patentInfoRequestDTO.setLang(lang);
        JSONObject patentInfoResult = patentInfoManager.batchFetchPatentBasicInfo(patentInfoRequestDTO);
        int currentColumnIndex = 2;
        for(AiSearchFinalResult finalResultItem : aiSearchResultInfo.getFinalResult()) {
            boolean selected = finalResultItem.isSelected();
            if(selected) {
                String patentId = finalResultItem.getPatentId();
                JSONObject patentInfo = patentInfoResult.getJSONObject(patentId);
                String pn = patentInfo.getString("PN");
                String title = patentInfo.getString("TITLE");
                String assignee = StringUtils.join(patentInfo.getJSONArray("ANCS").toArray(), ",");
                String publicationDate = patentInfo.getString("PBD");

                // 设置专利信息
                setCellText(table, 0, currentColumnIndex, (LANG_CN.equalsIgnoreCase(lang) ? "对比文献" : "Compared Reference ") + (currentColumnIndex-1), false, ParagraphAlignment.LEFT, TABLE_CELL_COLOR_BLUE);
                setCellText(table, 1, currentColumnIndex, pn, false, ParagraphAlignment.LEFT, TABLE_CELL_COLOR_BLUE);
                setCellText(table, 2, currentColumnIndex, title, false, ParagraphAlignment.LEFT, TABLE_CELL_COLOR_BLUE);
                setCellText(table, 3, currentColumnIndex, assignee, false, ParagraphAlignment.LEFT, TABLE_CELL_COLOR_BLUE);
                setCellText(table, 4, currentColumnIndex, publicationDate, false, ParagraphAlignment.LEFT, TABLE_CELL_COLOR_BLUE);
                setCellText(table, 5, currentColumnIndex, finalResultItem.getComparativeLiteratureType() + (LANG_CN.equalsIgnoreCase(lang) ? " 文献" : " Citation"), false, ParagraphAlignment.LEFT, TABLE_CELL_COLOR_BLUE);

                // 设置技术特征信息
                int currentRowIndex = 6;
                for(AiSearchFinalResultFeature featureItem : finalResultItem.getFeatures()) {
                    setCellText(table, currentRowIndex, currentColumnIndex, featureItem.isSimilar() ? featureItem.getComparisonFeature() : "-", false, ParagraphAlignment.LEFT, "");
                    currentRowIndex = currentRowIndex + 1;
                }

                currentColumnIndex = currentColumnIndex + 1;
            }
        }

        // 设置技术特征1-N
        int currentRowIndex = 6;
        for(NoveltySearchFeatureExtractDataDTO featureItem : techFeatureInfo.getData()) {
            boolean selected = featureItem.getSelected();
            if(selected) {
                String categoryText = TECHNICAL_FEATURE_CATEGORY_MAP.get(featureItem.getFeatureTextOriginal());
                String categoryColor = TECHNICAL_FEATURE_CATEGORY_COLOR_MAP.get(featureItem.getFeatureTextOriginal());
                String techCategory = (LANG_CN.equalsIgnoreCase(lang) ? "技术特征 " : "Technical Feature ") + categoryText;
                setCellText(table, currentRowIndex, 0, techCategory, false, ParagraphAlignment.LEFT, "");
                setCellText(table, currentRowIndex, 1, featureItem.getFeatureTextOriginal(), false, ParagraphAlignment.LEFT, "");
                currentRowIndex = currentRowIndex + 1;
            }
        }

        // 调整单元格宽度
        setColumnWidth(table, 0, 10);
        setColumnWidth(table, 1, 15);
        document.createParagraph();
    }

    private void handleTechnicalFeaturesComparisonLiterature(XWPFDocument document, XWPFParagraph afterParagraph, String taskId, String lang) {
        // 算法提取出来的技术特征
        NoveltySearchSummaryResDTO.Data summary = aiTaskManager.getTaskContent(taskId, AiTaskContentTypeEnum.NOVELTY_SEARCH_INPUT_SUMMARY);
        if(Objects.isNull(summary)) {
            return;
        }
        // 再取技术特征对应的对比文献特征
        List<LinkedHashMap<String, Object>> confirmMapList = aiTaskManager.getTaskContent(taskId, AiTaskContentTypeEnum.NOVELTY_SEARCH_FEATURE_CONFIRM);
        if(CollectionUtils.isEmpty(confirmMapList)) {
            return;
        }
        List<AiSearchFinalResult> confirmTechFeatures = confirmMapList.stream().map(map -> objectMapper.convertValue(map, AiSearchFinalResult.class)).toList();
        NoveltySearchFeatureExtractResDTO noveltySearchFeatureExtractResDTO = aiTaskManager.getTaskContent(taskId, AiTaskContentTypeEnum.NOVELTY_SEARCH_TECH_FEATURE);
        List<String> techFeatureList = new ArrayList<>();
        for (NoveltySearchFeatureExtractDataDTO featureItem : noveltySearchFeatureExtractResDTO.getData()) {
            if(!featureItem.getSelected()) {
                continue;
            }
            String categoryText = TECHNICAL_FEATURE_CATEGORY_MAP.get(featureItem.getFeatureTextOriginal());
            techFeatureList.add(categoryText + " " + featureItem.getFeatureTextOriginal());
        }
        // 对比文献信息
        AiTaskReqDTO literatureRequest = new AiTaskReqDTO();
        literatureRequest.setTaskId(taskId);
        List<NoveltySearchComparativeLiteratureResDTO> literatureList = new ArrayList<>();
        NoveltySearchComputeDTO reqA = noveltySearchManager.getReportCompute(literatureRequest);
        NoveltySearchComputeDTO reqX = noveltySearchManager.getReportReviewOfNovelty(literatureRequest);
        NoveltySearchComputeDTO reqY = noveltySearchManager.getReportReviewOfCreative(literatureRequest);
        if (CollUtil.isNotEmpty(reqX.getComparativeLiteratures())) {
            literatureList.addAll(reqX.getComparativeLiteratures());
        }
        if (CollUtil.isNotEmpty(reqY.getComparativeLiteratures())) {
            literatureList.addAll(reqY.getComparativeLiteratures());
        }
        if (CollUtil.isNotEmpty(reqA.getComparativeLiteratures())) {
            literatureList.addAll(reqA.getComparativeLiteratures());
        }

        if(CollectionUtils.isEmpty(literatureList)) {
            return;
        }

        // 获取标题段落在文档中的位置
        List<XWPFParagraph> paragraphs = document.getParagraphs();
        int titleIndex = paragraphs.indexOf(afterParagraph);

        // 行是动态变化，列是固定3列
        int rowSize = literatureList.size() + 1;
        // 在标题段落后插入表格
        XWPFTable table = document.insertNewTbl(document.getParagraphs().get(titleIndex).getCTP().newCursor());
        table.setWidth("100%");

        // 先确保表格有足够的行和列
        // 默认创建的表格只有1行1列，需要添加更多行和列
        for (int i = 1; i < rowSize; i++) {
            table.createRow().setHeight(TABLE_ROW_HEIGHT);;
        }
        // 确保每行都有3列
        for (int i = 0; i < rowSize; i++) {
            XWPFTableRow row = table.getRow(i);
            while (row.getTableCells().size() < 3) {
                row.createCell();
            }
        }

        // 设置表格边框
        table.setInsideHBorder(XWPFTable.XWPFBorderType.SINGLE, 4, 0, TABLE_BORDER_COLOR_BLUE);
        table.setInsideVBorder(XWPFTable.XWPFBorderType.SINGLE, 4, 0, TABLE_BORDER_COLOR_BLUE);
        table.setTopBorder(XWPFTable.XWPFBorderType.SINGLE, 4, 0, TABLE_BORDER_COLOR_BLUE);
        table.setBottomBorder(XWPFTable.XWPFBorderType.SINGLE, 4, 0, TABLE_BORDER_COLOR_BLUE);
        table.setLeftBorder(XWPFTable.XWPFBorderType.SINGLE, 4, 0, TABLE_BORDER_COLOR_BLUE);
        table.setRightBorder(XWPFTable.XWPFBorderType.SINGLE, 4, 0, TABLE_BORDER_COLOR_BLUE);

        setCellText(table, 0, 0, LANG_CN.equalsIgnoreCase(lang) ? "本发明技术要点" : "Technical Features of This Invention", false, ParagraphAlignment.LEFT, TABLE_CELL_COLOR_BLUE);
        setCellText(table, 0, 1, LANG_CN.equalsIgnoreCase(lang) ? "对比文献公开号及公开情况" : "Relevant Content from Compared References", false, ParagraphAlignment.LEFT, TABLE_CELL_COLOR_BLUE);
        setCellText(table, 0, 2, LANG_CN.equalsIgnoreCase(lang) ? "公开度" : "Similarity Score", false, ParagraphAlignment.LEFT, TABLE_CELL_COLOR_BLUE);

        int currentRowIndex = 1;
        for(NoveltySearchComparativeLiteratureResDTO literatureItem : literatureList) {
            // 将0.981转换成98.1%的格式
            BigDecimal bd = new BigDecimal(String.valueOf(literatureItem.getPublicScore())).setScale(2, RoundingMode.HALF_UP);
            String formatPublicScore = String.format("%.2f%%", bd.doubleValue() * 100);
            setCellTextWithLineBreaks(table, currentRowIndex, 0, CollUtil.join(techFeatureList, "\n"), false, ParagraphAlignment.LEFT, "");
            // 找到对比文献(patentId)里的技术特征点和文献特征点的对应关系
             List<AiSearchFinalResultFeature> patentFeatures = confirmTechFeatures.stream().filter(item -> literatureItem.getPatentId().equals(item.getPatentId())).toList().get(0).getFeatures();
             Map<String, String> patentFeatureColorMap = new HashMap<>();
             for(AiSearchFinalResultFeature patentFeatureItem : patentFeatures) {
                 String categoryColor = TECHNICAL_FEATURE_CATEGORY_COLOR_MAP.get(patentFeatureItem.getTechFeature());
                 patentFeatureColorMap.put(patentFeatureItem.getComparisonFeature(), categoryColor);
             }
            // 高亮用此行代码
            setCellTextWithBreakAndHighlight(table, currentRowIndex, 1, literatureItem.getPn() + " " + formatPublicScore, literatureItem.getRelatedPara(), false, ParagraphAlignment.LEFT, "", patentFeatureColorMap);
            setCellText(table, currentRowIndex, 2, formatPublicScore, false, ParagraphAlignment.LEFT, "");

            currentRowIndex = currentRowIndex + 1;
        }

        // 调整单元格宽度
        setColumnWidth(table, 0, 45);
        setColumnWidth(table, 1, 45);
        setColumnWidth(table, 2, 10);

        document.createParagraph();
    }

    private void handleAppendix1(XWPFDocument document, XWPFParagraph afterParagraph, String taskId, String lang) {
        AiNoveltySearchResponse aiSearchResultInfo = aiTaskManager.getTaskContent(taskId, AiTaskContentTypeEnum.NOVELTY_SEARCH_AGENT_RESULT);
        if(Objects.isNull(aiSearchResultInfo)) {
            return;
        }
        List<String> processDictKeys = aiSearchResultInfo.getProcessDictKeys();
        if(CollUtil.isEmpty(processDictKeys)) {
            return;
        }

        List<XWPFParagraph> paragraphs = document.getParagraphs();
        int titleIndex = paragraphs.indexOf(afterParagraph);
        Long columnSize = 3L;
        Long rowSize = processDictKeys.size() + 1L;
        XWPFTable table = document.insertNewTbl(document.getParagraphs().get(titleIndex).getCTP().newCursor());
        table.setWidth("100%");
        for (int i = 1; i < rowSize; i++) {
            table.createRow().setHeight(TABLE_ROW_HEIGHT);;
        }
        for (int i = 0; i < rowSize; i++) {
            XWPFTableRow row = table.getRow(i);
            while (row.getTableCells().size() < columnSize) {
                row.createCell();
            }
        }

        // 设置表格边框
        table.setInsideHBorder(XWPFTable.XWPFBorderType.SINGLE, 4, 0, TABLE_BORDER_COLOR_BLUE);
        table.setInsideVBorder(XWPFTable.XWPFBorderType.SINGLE, 4, 0, TABLE_BORDER_COLOR_BLUE);
        table.setTopBorder(XWPFTable.XWPFBorderType.SINGLE, 4, 0, TABLE_BORDER_COLOR_BLUE);
        table.setBottomBorder(XWPFTable.XWPFBorderType.SINGLE, 4, 0, TABLE_BORDER_COLOR_BLUE);
        table.setLeftBorder(XWPFTable.XWPFBorderType.SINGLE, 4, 0, TABLE_BORDER_COLOR_BLUE);
        table.setRightBorder(XWPFTable.XWPFBorderType.SINGLE, 4, 0, TABLE_BORDER_COLOR_BLUE);

        setCellText(table, 0, 0, LANG_CN.equalsIgnoreCase(lang) ? "序号" : "Number", false, ParagraphAlignment.LEFT, TABLE_CELL_COLOR_BLUE);
        setCellText(table, 0, 1, LANG_CN.equalsIgnoreCase(lang) ? "检索步骤" : "Search Step", false, ParagraphAlignment.LEFT, TABLE_CELL_COLOR_BLUE);
        setCellText(table, 0, 2, LANG_CN.equalsIgnoreCase(lang) ? "检索策略" : "Search String", false, ParagraphAlignment.LEFT, TABLE_CELL_COLOR_BLUE);
        Map<String, ProcessDetail> processDict = aiSearchResultInfo.getProcessDict();
        for (int i = 1; i <= processDictKeys.size(); i++) {
            String searchStep = processDictKeys.get(i - 1);
            ProcessDetail processDetail = processDict.get(searchStep);
            String stepText = processDetail.getTitle().get(LANG_CN.equalsIgnoreCase(lang) ? "zh" : "en");
            String strategyText = "-";
            if (CollUtil.isNotEmpty(processDetail.getQueries())) {
                if ("semantic_search".equals(searchStep)) {
                    strategyText = processDetail.getQueries().get(0).getAliasQuery();
                }else {
                    List<String> queries = processDetail.getQueries().stream()
                            .map(QueryInfo::getQuery)
                            .toList();
                    strategyText = String.join("\n", queries);
                }
            }
            setCellText(table, i, 0, String.valueOf(i), false, ParagraphAlignment.LEFT, "");
            setCellText(table, i, 1, stepText, false, ParagraphAlignment.LEFT, "");
            setCellText(table, i, 2, strategyText, false, ParagraphAlignment.LEFT, "");
        }

        // 调整单元格宽度
        setColumnWidth(table, 0, 10);
        setColumnWidth(table, 1, 35);

        document.createParagraph();
    }

    private void handleAppendix2(XWPFDocument document, XWPFParagraph afterParagraph, String taskId, String lang) {
        AiNoveltySearchResponse aiSearchResultInfo = aiTaskManager.getTaskContent(taskId, AiTaskContentTypeEnum.NOVELTY_SEARCH_AGENT_RESULT);
        if(Objects.isNull(aiSearchResultInfo)) {
            return;
        }
        List<AiSearchFinalResult> finalResultList = aiSearchResultInfo.getFinalResult();
        if(CollUtil.isEmpty(finalResultList)) {
            return;
        }

        // 获取所有专利ID列表（100）
        List<String> patentIds = finalResultList.stream()
                .map(AiSearchFinalResult::getPatentId)
                .distinct()
                .collect(Collectors.toList());

        if(CollUtil.isEmpty(patentIds)) {
            return;
        }

        // 批量获取专利基本信息
        PatentInfoRequestDTO patentInfoRequestDTO = new PatentInfoRequestDTO();
        patentInfoRequestDTO.setPatentIds(patentIds);
        patentInfoRequestDTO.setLang(lang);
        JSONObject patentInfoResult = patentInfoManager.batchFetchPatentInfoByField(patentInfoRequestDTO, Arrays.asList("PN","ANCS","PBD","TITLE"));


        // 创建表格 (行数 = 选中专利数量 + 1个表头行)
        int rowCount = patentIds.size() + 1;
        int columnCount = 2; // 序号、专利信息（标题+公开号+公开日+申请人）
        List<XWPFParagraph> paragraphs = document.getParagraphs();
        int titleIndex = paragraphs.indexOf(afterParagraph);
        XWPFTable table = document.insertNewTbl(document.getParagraphs().get(titleIndex).getCTP().newCursor());
        table.setWidth("100%");

        // 创建所需的行
        for (int i = 1; i < rowCount; i++) {
            table.createRow().setHeight(TABLE_ROW_HEIGHT * 2); // 增加行高以容纳两行内容
        }

        // 确保每行都有足够的列
        for (int i = 0; i < rowCount; i++) {
            XWPFTableRow row = table.getRow(i);
            while (row.getTableCells().size() < columnCount) {
                row.createCell();
            }
        }

        // 设置表格边框
        table.setInsideHBorder(XWPFTable.XWPFBorderType.SINGLE, 4, 0, TABLE_BORDER_COLOR_BLUE);
        table.setInsideVBorder(XWPFTable.XWPFBorderType.SINGLE, 4, 0, TABLE_BORDER_COLOR_BLUE);
        table.setTopBorder(XWPFTable.XWPFBorderType.SINGLE, 4, 0, TABLE_BORDER_COLOR_BLUE);
        table.setBottomBorder(XWPFTable.XWPFBorderType.SINGLE, 4, 0, TABLE_BORDER_COLOR_BLUE);
        table.setLeftBorder(XWPFTable.XWPFBorderType.SINGLE, 4, 0, TABLE_BORDER_COLOR_BLUE);
        table.setRightBorder(XWPFTable.XWPFBorderType.SINGLE, 4, 0, TABLE_BORDER_COLOR_BLUE);

        // 设置表头
        String[] headers = LANG_CN.equalsIgnoreCase(lang)
            ? new String[]{"序号", "标题/公开号/公开日/当前申请人"}
            : new String[]{"Number", "Title/Publication Number/Publication Date/Current Assignee"};

        for (int col = 0; col < headers.length; col++) {
            NoveltySearchExportUtils.setCellText(table, 0, col, headers[col], false, ParagraphAlignment.LEFT, TABLE_CELL_COLOR_BLUE);
        }

        // 填充专利数据
        int rowIndex = 1;
        for (int i = 0; i < patentIds.size(); i++) {
            String patentId = patentIds.get(i);
            JSONObject patentInfo = patentInfoResult.getJSONObject(patentId);

            if (Objects.isNull(patentInfo)) {
                continue;
            }
            // 序号
            NoveltySearchExportUtils.setCellText(table, rowIndex, 0, String.valueOf(i + 1), false, ParagraphAlignment.LEFT, null);

            // 专利信息单元格 - 包含标题和详细信息
            XWPFTableCell patentInfoCell = table.getRow(rowIndex).getCell(1);
            patentInfoCell.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.TOP);

            // 清空默认段落
            patentInfoCell.removeParagraph(0);

            // 第一行：标题
            String title = patentInfo.getString("TITLE");
            XWPFParagraph titleParagraph = patentInfoCell.addParagraph();
            titleParagraph.setAlignment(ParagraphAlignment.LEFT);
            titleParagraph.setSpacingBefore(0);
            titleParagraph.setSpacingAfter(0);
            XWPFRun titleRun = titleParagraph.createRun();
            NoveltySearchExportUtils.setText(titleRun, StringUtils.defaultString(title, ""), TABLE_FONT_SIZE, false, FONT_FAMILY);

            // 第二行：公开号（带超链接） + 公开日 + 申请人
            XWPFParagraph detailParagraph = patentInfoCell.addParagraph();
            detailParagraph.setAlignment(ParagraphAlignment.LEFT);
            detailParagraph.setSpacingBefore(0);
            detailParagraph.setSpacingAfter(0);

            // 公开号（带超链接）

            // 使用注入的域名配置
            String domain = eurekaDomain;
            if (!domain.startsWith("http://") && !domain.startsWith("https://")) {
                domain = "https://" + domain;
            }
            String pn = patentInfo.getString("PN");
            if (StringUtils.isNotBlank(pn)) {
                String hyperlink = domain + "/view/#?patentId=" + patentId;
                String rId = detailParagraph.getDocument().getPackagePart().addExternalRelationship(
                    hyperlink, "http://schemas.openxmlformats.org/officeDocument/2006/relationships/hyperlink").getId();

                // 创建超链接
                org.openxmlformats.schemas.wordprocessingml.x2006.main.CTHyperlink cthyperLink =
                    detailParagraph.getCTP().addNewHyperlink();
                cthyperLink.setId(rId);

                org.openxmlformats.schemas.wordprocessingml.x2006.main.CTText ctText =
                    org.openxmlformats.schemas.wordprocessingml.x2006.main.CTText.Factory.newInstance();
                ctText.setStringValue(pn);

                org.openxmlformats.schemas.wordprocessingml.x2006.main.CTR ctr =
                    org.openxmlformats.schemas.wordprocessingml.x2006.main.CTR.Factory.newInstance();
                ctr.setTArray(new org.openxmlformats.schemas.wordprocessingml.x2006.main.CTText[]{ctText});

                // 设置超链接样式
                org.openxmlformats.schemas.wordprocessingml.x2006.main.CTRPr rPr = ctr.addNewRPr();
                org.openxmlformats.schemas.wordprocessingml.x2006.main.CTColor color = rPr.addNewColor();
                color.setVal("0456D1"); // 蓝色
                org.openxmlformats.schemas.wordprocessingml.x2006.main.CTUnderline underline = rPr.addNewU();
                underline.setVal(org.openxmlformats.schemas.wordprocessingml.x2006.main.STUnderline.SINGLE);

                cthyperLink.setRArray(new org.openxmlformats.schemas.wordprocessingml.x2006.main.CTR[]{ctr});
            }

            // 添加4个空格 + 公开日
            String publicationDate = patentInfo.getString("PBD");
            if (StringUtils.isNotBlank(publicationDate)) {
                XWPFRun spacerRun1 = detailParagraph.createRun();
                NoveltySearchExportUtils.setText(spacerRun1, "    " + publicationDate, TABLE_FONT_SIZE, false, FONT_FAMILY);
            }

            // 添加4个空格 + 申请人
            String assignee = "";
            if (patentInfo.containsKey("ANCS") && Objects.nonNull(patentInfo.get("ANCS"))) {
                Object ancsObj = patentInfo.get("ANCS");
                if (ancsObj instanceof List) {
                    assignee = ((List<?>) ancsObj).stream()
                            .filter(Objects::nonNull)
                            .map(Object::toString)
                            .collect(Collectors.joining(", "));
                } else if (ancsObj instanceof String) {
                    assignee = (String) ancsObj;
                }
            }
            if (StringUtils.isNotBlank(assignee)) {
                XWPFRun spacerRun2 = detailParagraph.createRun();
                NoveltySearchExportUtils.setText(spacerRun2, "    " + assignee, TABLE_FONT_SIZE, false, FONT_FAMILY);
            }

            rowIndex++;
        }

        // 设置列宽
        NoveltySearchExportUtils.setColumnWidth(table, 0, 10);  // 序号列
        NoveltySearchExportUtils.setColumnWidth(table, 1, 90);  // 专利信息列

        // 在表格后添加空行
        document.createParagraph();
    }
}

package com.patsnap.drafting.request.company;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotBlank;

/**
 * 公司树查询请求DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CompanyTreeRequestDTO {
    
    /**
     * 搜索关键词
     */
    @NotBlank(message = "搜索关键词不能为空")
    private String q;
    
    /**
     * 返回行数
     */
    private Integer rows = 50;
    
    /**
     * 时间戳
     */
    private Long timestamp;
    
    /**
     * 是否激活
     */
    private Boolean active = false;
    
    /**
     * 是否精确匹配
     */
    private Boolean exactMatch = true;

    private String ua;
} 
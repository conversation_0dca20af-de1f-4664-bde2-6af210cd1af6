package com.patsnap.drafting.request.company;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 公司名称搜索请求DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CompanyNameRequestDTO {
    
    /**
     * 搜索关键词
     */
    @NotBlank(message = "搜索关键词不能为空")
    private String q;
    
    /**
     * 返回结果数量
     */
    @NotNull(message = "返回结果数量不能为空")
    private Integer n;
    
    /**
     * 搜索模式
     */
    private String searchMode;
} 
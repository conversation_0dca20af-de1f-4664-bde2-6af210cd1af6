package com.patsnap.drafting.aspect;

import com.patsnap.drafting.annotation.FluxConvert;
import com.patsnap.drafting.manager.JsonMapperManager;
import com.patsnap.spring.boot.autoconfigure.web.entity.CommonResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

import java.util.Objects;

/**
 * Flux 转换切面
 */
@Aspect
@Component
@Slf4j
@RequiredArgsConstructor
public class FluxConvertAspect {

    private final JsonMapperManager jsonMapperManager;

    @Around("@annotation(fluxConvert)")
    public Object around(ProceedingJoinPoint point, FluxConvert fluxConvert) throws Throwable {
        try {
            Object result = point.proceed();
            if (result == null) {
                log.warn("Result is null, method: {}", point.getSignature().getName());
                return null;
            }
            if (result instanceof Flux<?> flux) {
                return flux.map(item -> {
                            if (item instanceof CommonResponse<?> response) {
                                // 将 CommonResponse 序列化为字符串
                                return jsonMapperManager.convertToCompactString(response);
                            }
                            return item;
                        }).filter(Objects::nonNull);
            }
            return result;
        } catch (Throwable e) {
            log.error("Flux convert error", e);
            throw e;
        }
    }
}
package com.patsnap.drafting.aspect;

import com.patsnap.common.request.CorrelationIdHolder;
import com.patsnap.common.request.UserIdHolder;
import com.patsnap.core.common.copilot.constant.GPTStatus;
import com.patsnap.core.common.copilot.streaming.model.GptResponseDTO;
import com.patsnap.drafting.annotation.CreditCheckLimit;
import com.patsnap.drafting.enums.common.OperateTypeEnum;
import com.patsnap.drafting.enums.task.AiTaskContentTypeEnum;
import com.patsnap.drafting.manager.JsonMapperManager;
import com.patsnap.drafting.request.aitask.AiTaskReqDTO;
import com.patsnap.spring.boot.autoconfigure.web.entity.CommonResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.jetbrains.annotations.NotNull;
import org.springframework.core.DefaultParameterNameDiscoverer;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Stream;

/**
 * 积分校验切面
 * 实现
 * 1. 积分的校验，适用于操作类型（OperateTypeEnum）为 生成内容（GENERATE）与 AI 优化的场景
 * @see OperateTypeEnum
 * @see AiTaskContentTypeEnum
 * @see AiTaskReqDTO
 */
@Aspect
@Component
@Slf4j
@RequiredArgsConstructor
public class CreditCheckLimitAspect {
    private final JsonMapperManager jsonMapperManager;
    private final ExpressionParser parser = new SpelExpressionParser();
    private final DefaultParameterNameDiscoverer discoverer = new DefaultParameterNameDiscoverer();

    @Around("@annotation(creditCheckLimit)")
    public Object around(ProceedingJoinPoint point, CreditCheckLimit creditCheckLimit) throws Throwable {
        AiTaskContentTypeEnum contentType = resolveContentType(point, creditCheckLimit);
        String taskId = extractTaskId(point.getArgs());
        return checkCreditAndExecute(point, taskId, contentType);
    }


    private Object checkCreditAndExecute(ProceedingJoinPoint point, String taskId, AiTaskContentTypeEnum contentType) throws Throwable {
        try {
            Object result = point.proceed();
            if (result == null) {
                log.warn("No result returned for taskId: {}", taskId);
                return null;
            }
            // 如果不是生成操作，则不缓存结果
            if (result instanceof Flux<?> flux) {
                return handleFluxError(taskId, flux);
            }
            return result;
        } catch (Throwable e) {
            log.error("Error executing method for taskId: {}", taskId, e);
            handleError(taskId);
            throw e;
        }
    }

    /**
     * 处理Flux类型的错误,产生错误时候，退回用户积分
     */
    private @NotNull Flux<?> handleFluxError(String taskId, Flux<?> flux) {
        AtomicReference<GPTStatus> status = new AtomicReference<>();
        String userId = UserIdHolder.get();
        String correlationId = CorrelationIdHolder.get();

        return flux.map(item -> {
                    if (item instanceof CommonResponse<?> response) {
                        extractStatus(response, status);
                        // 将 CommonResponse 序列化为字符串
                        return jsonMapperManager.convertToCompactString(response);
                    }
                    return item;
                }).filter(Objects::nonNull)
                // 完成时的处理
                .doOnComplete(() -> handleComplete(taskId, status.get(), userId, correlationId))
                // 异常处理
                .doOnError(error -> handleError(taskId, userId, correlationId, error))
                // 确保在所有情况下都清理 UserIdHolder CorrelationIdHolder
                .doFinally(signal -> {
                    UserIdHolder.remove();
                    CorrelationIdHolder.remove();
                });
    }


    private void handleError(String taskId) {
        try {
        } catch (Exception ex) {
            log.error("Failed to decrement credit usages for taskId: {}", taskId, ex);
        }
    }

    private void extractStatus(Object item, AtomicReference<GPTStatus> status) {
        if (item instanceof CommonResponse<?> response) {
            Optional.ofNullable(response.getData())
                    .filter(data -> data instanceof GptResponseDTO<?>)
                    .map(data -> ((GptResponseDTO<?>) data).getStatus())
                    .ifPresent(status::set);
        }
    }

    private void handleComplete(String taskId, GPTStatus status, String userId, String correlationId) {
        try {
            UserIdHolder.set(userId);
            CorrelationIdHolder.set(correlationId);
            if (GPTStatus.FAILED == status) {
            }
            UserIdHolder.remove();
            CorrelationIdHolder.remove();
        } catch (Exception e) {
            log.error("Error handling completion for taskId: {}", taskId, e);
            throw e;
        }
    }


    private void handleError(String taskId, String userId, String correlationId, Throwable error) {
        try {
            UserIdHolder.set(userId);
            CorrelationIdHolder.set(correlationId);
            UserIdHolder.remove();
            CorrelationIdHolder.remove();
            log.error("Error processing flux for taskId: {}", taskId, error);
        } catch (Exception e) {
            log.error("Error handling error for taskId: {}", taskId, e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 从方法参数中提取taskId
     */
    private String extractTaskId(Object[] args) {
        if (args == null || args.length == 0) {
            throw new IllegalArgumentException("No arguments provided");
        }

        return Stream.of(args)
                .filter(Objects::nonNull)
                .map(arg -> {
                    if (arg instanceof AiTaskReqDTO) {
                        return ((AiTaskReqDTO) arg).getTaskId();
                    }
                    return getTaskIdByReflection(arg);
                })
                .filter(Objects::nonNull)
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("No taskId found in method arguments"));
    }


    private String getTaskIdByReflection(Object arg) {
        try {
            Field taskIdField = arg.getClass().getDeclaredField("taskId");
            taskIdField.setAccessible(true);
            Object value = taskIdField.get(arg);
            return value != null ? value.toString() : null;
        } catch (Exception ignored) {
            return null;
        }
    }

    /**
     * 解析内容类型
     * 1. 优先使用固定值方式
     * 2. 如果固定值为空，则使用表达式方式
     */
    private AiTaskContentTypeEnum resolveContentType(ProceedingJoinPoint point, CreditCheckLimit creditCheckLimit) {
        // 如果表达式为空，使用原有方式
        if (StringUtils.isBlank(creditCheckLimit.contentTypeExpression())) {
            return creditCheckLimit.contentType();
        }

        try {
            MethodSignature signature = (MethodSignature) point.getSignature();
            Method method = signature.getMethod();

            // 获取方法参数名
            String[] parameterNames = discoverer.getParameterNames(method);
            if (parameterNames == null) {
                log.warn("无法获取方法参数名，降级使用固定值方式");
                return creditCheckLimit.contentType();
            }

            // 创建表达式上下文
            EvaluationContext context = new StandardEvaluationContext();
            Object[] args = point.getArgs();
            for (int i = 0; i < parameterNames.length; i++) {
                context.setVariable(parameterNames[i], args[i]);
            }

            // 解析SpEL表达式
            Expression expression = parser.parseExpression(creditCheckLimit.contentTypeExpression());
            String contentTypeStr = expression.getValue(context, String.class);
            return AiTaskContentTypeEnum.fromType(contentTypeStr);
        } catch (Exception e) {
            return creditCheckLimit.contentType();
        }
    }
}
package com.patsnap.drafting.aspect;

import com.patsnap.drafting.annotation.DistributedLock;
import com.patsnap.drafting.exception.BizException;
import com.patsnap.drafting.exception.errorcode.ContentErrorCodeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.core.DefaultParameterNameDiscoverer;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

/**
 * 分布式锁切面
 */
@Slf4j
@Aspect
@Component
@RequiredArgsConstructor
public class DistributedLockAspect {

    private final RedissonClient redissonClient;
    private final ExpressionParser parser = new SpelExpressionParser();
    private final DefaultParameterNameDiscoverer discoverer = new DefaultParameterNameDiscoverer();

    @Around("@annotation(distributedLock)")
    public Object around(ProceedingJoinPoint point, DistributedLock distributedLock) throws Throwable {
        String lockKey = getLockKey(point, distributedLock);
        RLock lock = redissonClient.getLock(lockKey);
        
        try {
            boolean locked = lock.tryLock(distributedLock.waitTime(), distributedLock.timeUnit());
            if (!locked) {
                log.warn("获取分布式锁失败. lockKey: {}", lockKey);
                throw new BizException(ContentErrorCodeEnum.OPERATION_TOO_FREQUENT);
            }
            
            return point.proceed();
            
        } catch (InterruptedException e) {
            log.error("获取分布式锁被中断. lockKey: {}", lockKey, e);
            Thread.currentThread().interrupt();
            throw new BizException(ContentErrorCodeEnum.SYSTEM_BUSY);
        } catch (BizException e) {
            throw e;
        } catch (Exception e) {
            log.error("执行被分布式锁保护的方法时发生异常. lockKey: {}", lockKey, e);
            throw new BizException(ContentErrorCodeEnum.SYSTEM_BUSY);
        } finally {
            try {
                if (lock.isHeldByCurrentThread()) {
                    lock.unlock();
                }
            } catch (Exception e) {
                log.error("释放分布式锁异常. lockKey: {}", lockKey, e);
            }
        }
    }
    
    /**
     * 获取锁的key
     */
    private String getLockKey(ProceedingJoinPoint point, DistributedLock distributedLock) {
        try {
            MethodSignature signature = (MethodSignature) point.getSignature();
            Method method = signature.getMethod();
            
            // 获取方法参数名
            String[] parameterNames = discoverer.getParameterNames(method);
            if (parameterNames == null) {
                throw new IllegalArgumentException("无法获取方法参数名");
            }
            
            // 创建表达式上下文
            EvaluationContext context = new StandardEvaluationContext();
            Object[] args = point.getArgs();
            for (int i = 0; i < parameterNames.length; i++) {
                context.setVariable(parameterNames[i], args[i]);
            }
            
            // 解析SpEL表达式
            Expression expression = parser.parseExpression(distributedLock.key());
            String key = expression.getValue(context, String.class);
            
            // 组装完整的锁key
            return distributedLock.prefix() + ":" + key;
            
        } catch (Exception e) {
            log.error("解析分布式锁key异常", e);
            throw new BizException(ContentErrorCodeEnum.SYSTEM_BUSY);
        }
    }
} 
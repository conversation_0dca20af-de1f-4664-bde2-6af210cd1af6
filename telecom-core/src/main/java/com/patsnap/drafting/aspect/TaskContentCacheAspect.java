package com.patsnap.drafting.aspect;

import com.patsnap.common.request.CorrelationIdHolder;
import com.patsnap.common.request.UserIdHolder;
import com.patsnap.core.common.copilot.constant.GPTStatus;
import com.patsnap.core.common.copilot.streaming.model.GptResponseDTO;
import com.patsnap.drafting.annotation.TaskContentCache;
import com.patsnap.drafting.enums.common.OperateTypeEnum;
import com.patsnap.drafting.enums.task.AiTaskContentTypeEnum;
import com.patsnap.drafting.enums.task.AiTaskTypeEnum;
import com.patsnap.drafting.enums.task.AsyncTaskStatusEnum;
import com.patsnap.drafting.manager.JsonMapperManager;
import com.patsnap.drafting.manager.aitask.AiTaskManager;
import com.patsnap.drafting.manager.content.logic.ContentCacheLogic;
import com.patsnap.drafting.repository.aitask.entity.AnalyticsAiTaskHistoryPO;
import com.patsnap.drafting.request.aitask.AiTaskReqDTO;
import com.patsnap.drafting.util.TaskIdHolder;
import com.patsnap.spring.boot.autoconfigure.web.entity.CommonResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.ApplicationContext;
import org.springframework.core.DefaultParameterNameDiscoverer;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Stream;

/**
 * 任务内容缓存切面 实现 1. 积分的校验，适用于操作类型（OperateTypeEnum）为 生成内容（GENERATE）与 AI 优化的场景 2. 基于taskId和contentType的内容缓存机制，只适用于
 * 操作类型（OperateTypeEnum）为 生成内容（GENERATE）
 *
 * @see OperateTypeEnum
 * @see AiTaskContentTypeEnum
 * @see AiTaskReqDTO
 */
@Aspect
@Component
@Slf4j
@RequiredArgsConstructor
public class TaskContentCacheAspect {

    private final AiTaskManager aiTaskManager;
    private final JsonMapperManager jsonMapperManager;
    private final ExpressionParser parser = new SpelExpressionParser();
    private final DefaultParameterNameDiscoverer discoverer = new DefaultParameterNameDiscoverer();
    private final ApplicationContext applicationContext;

    @Around("@annotation(taskContentCache)")
    public Object around(ProceedingJoinPoint point, TaskContentCache taskContentCache) throws Throwable {
        AiTaskContentTypeEnum contentType = resolveContentType(point, taskContentCache);
        String taskId = extractTaskId(point.getArgs());
        try {
            // 把任务ID放入线程变量，后面生成用户积分使用明细时需要用到
            TaskIdHolder.set(taskId);
            OperateTypeEnum operateType = OperateTypeEnum.fromValue(extractOperateType(point.getArgs()));
            AnalyticsAiTaskHistoryPO task = aiTaskManager.getTaskById(taskId);
            if (needGenerateWithoutCache(taskId, operateType, task)) {
                return executeAndCacheResult(point, taskId, contentType, operateType, point.getArgs(), taskContentCache.logicClass());
            }
            // 优先从表中获取数据
            Object cachedContent = aiTaskManager.getTaskContent(taskId, contentType);
            if (cachedContent != null || AsyncTaskStatusEnum.Interrupt.getValue().equals(task.getAsyncStatus())
                    || AsyncTaskStatusEnum.Failed.getValue().equals(task.getAsyncStatus())
                    || AsyncTaskStatusEnum.Complete.getValue().equals(task.getAsyncStatus()) ) {
                log.info("Cached content found for taskId: {}, task status: {}", taskId, task.getAsyncStatus());
                return buildCachedContent(point, cachedContent);
            }

            return executeAndCacheResult(point, taskId, contentType, operateType, point.getArgs(), taskContentCache.logicClass());
        } catch (Exception e) {
            throw e;
            // do nothing
        } finally {
            // 清除线程变量
            TaskIdHolder.remove();
        }
    }

    /**
     * 判断是否需要执行原方法
     *
     * @param taskId      任务ID
     * @param operateType 操作类型
     * @return 是否需要执行原方法
     */
    private boolean needGenerateWithoutCache(String taskId, OperateTypeEnum operateType, AnalyticsAiTaskHistoryPO task) {
        return StringUtils.isBlank(taskId) || task == null
                || !OperateTypeEnum.GENERATE.equals(operateType);
    }

    private Object buildCachedContent(ProceedingJoinPoint point, Object cachedContent) {
        MethodSignature signature = (MethodSignature) point.getSignature();
        Method method = signature.getMethod();
        if (method.getReturnType().isAssignableFrom(Flux.class)) {
            GptResponseDTO<Object> gptResponse = createGptResponse(cachedContent);
            CommonResponse<GptResponseDTO<Object>> commonResponse = createCommonResponse(gptResponse);
            return Flux.just(jsonMapperManager.convertToCompactString(commonResponse));
        }
        return cachedContent;
    }

    private static <T> GptResponseDTO<T> createGptResponse(T content) {
        return GptResponseDTO.<T>builder().content(content).status(GPTStatus.FINISH).build();
    }

    private static <T> CommonResponse<GptResponseDTO<T>> createCommonResponse(GptResponseDTO<T> gptResponse) {
        return CommonResponse.<GptResponseDTO<T>>builder().withStatus(true).withData(gptResponse).build();
    }


    private Object executeAndCacheResult(ProceedingJoinPoint point, String taskId, AiTaskContentTypeEnum contentType,
            OperateTypeEnum operateType, Object[] args, Class logicClass) throws Throwable {
        log.info("No cached content found, executing original method for taskId: {}, contentType: {}", taskId,
                contentType);

        try {
            Object result = point.proceed();
            if (result == null) {
                log.warn("No result returned for taskId: {}, contentType: {}", taskId, contentType);
                return null;
            }
            // 如果不是生成操作，则不缓存结果
            if (!(OperateTypeEnum.GENERATE.equals(operateType) || OperateTypeEnum.REGENERATE.equals(operateType))) {
                return handleFluxError(taskId, contentType, result, args, logicClass);
            }
            return cacheResultWithReturn(taskId, contentType, result, args, logicClass);
        } catch (Throwable e) {
            log.error("Error executing method for taskId: {}, contentType: {}", taskId, contentType, e);
            handleError(taskId);
            throw e;
        }
    }

    /**
     * 处理Flux类型的错误,产生错误时候，退回用户积分
     */
    private Object handleFluxError(String taskId, AiTaskContentTypeEnum contentType, Object result, Object[] args, Class logicClass) {
        if (result instanceof Flux<?> flux) {
            AtomicReference<Object> content = new AtomicReference<>();
            AtomicReference<GPTStatus> status = new AtomicReference<>();
            String userId = UserIdHolder.get();
            String correlationId = CorrelationIdHolder.get();

            return flux.map(item -> {
                        if (item instanceof CommonResponse<?> response) {
                            extractContent(response, content, status);
                            // 将 CommonResponse 序列化为字符串
                            return jsonMapperManager.convertToCompactString(response);
                        }
                        return item;
                    }).filter(Objects::nonNull)
                    // 完成时的处理
                    .doOnComplete(
                            () -> handleComplete(taskId, contentType, null, status.get(), userId,
                                    correlationId, args, logicClass))
                    // 异常处理
                    .doOnError(error -> handleError(taskId, userId, correlationId, error))
                    // 确保在所有情况下都清理 UserIdHolder CorrelationIdHolder
                    .doFinally(signal -> {
                        UserIdHolder.remove();
                        CorrelationIdHolder.remove();
                    });
        }
        return result;
    }

    /**
     * 缓存结果
     */
    private Object cacheResultWithReturn(String taskId, AiTaskContentTypeEnum contentType, Object result, Object[] args, Class logicClass) {
        if (result instanceof Flux<?> flux) {
            return getFluxAndCacheResult(taskId, contentType, flux, args, logicClass);
        }
        cacheResult(taskId, contentType, result, args, logicClass);
        return result;
    }

    private void handleError(String taskId) {
        try {
        } catch (Exception ex) {
            log.error("Failed to decrement credit usages for taskId: {}", taskId, ex);
        }
    }

    private @NotNull Flux<?> getFluxAndCacheResult(String taskId, AiTaskContentTypeEnum contentType, Flux<?> flux, Object[] args, Class logicClass) {
        AtomicReference<Object> content = new AtomicReference<>();
        AtomicReference<GPTStatus> status = new AtomicReference<>();
        String userId = UserIdHolder.get();
        String correlationId = CorrelationIdHolder.get();

        return flux.map(item -> {
                    if (item instanceof CommonResponse<?> response) {
                        extractContent(response, content, status);
                        // 将 CommonResponse 序列化为字符串
                        return jsonMapperManager.convertToCompactString(response);
                    }
                    return item;
                }).filter(Objects::nonNull)
                // 完成时的处理
                .doOnComplete(
                        () -> handleComplete(taskId, contentType, content.get(), status.get(), userId,
                                correlationId, args, logicClass))
                // 取消时的处理
                .doOnCancel(() -> handleCancel(taskId, contentType, content.get(), userId, correlationId, args, logicClass))
                // 异常处理
                .doOnError(error -> handleError(taskId, userId, correlationId, error))
                // 确保在所有情况下都清理 UserIdHolder CorrelationIdHolder
                .doFinally(signal -> {
                    UserIdHolder.remove();
                    CorrelationIdHolder.remove();
                });
    }

    private void extractContent(Object item, AtomicReference<Object> content, AtomicReference<GPTStatus> status) {
        if (item instanceof CommonResponse<?> response) {
            Optional.ofNullable(response.getData()).filter(data -> data instanceof GptResponseDTO<?>)
                    .map(data -> ((GptResponseDTO<?>) data).getContent()).ifPresent(content::set);
            Optional.ofNullable(response.getData()).filter(data -> data instanceof GptResponseDTO<?>)
                    .map(data -> ((GptResponseDTO<?>) data).getStatus()).ifPresent(status::set);
        }
    }

    private void handleComplete(String taskId, AiTaskContentTypeEnum contentType, Object content, GPTStatus status, String userId, String correlationId, Object[] args, Class logicClass) {
        try {
            UserIdHolder.set(userId);
            CorrelationIdHolder.set(correlationId);
            if (content != null) {
                cacheResult(taskId, contentType, content, args, logicClass);
            }
            if (GPTStatus.FAILED == status) {
            }
            UserIdHolder.remove();
            CorrelationIdHolder.remove();
        } catch (Exception e) {
            log.error("Error handling completion for taskId: {}", taskId, e);
            throw e;
        }
    }

    private void handleCancel(String taskId, AiTaskContentTypeEnum contentType, Object content, String userId,
            String correlationId, Object[] args, Class logicClass) {
        try {
            UserIdHolder.set(userId);
            CorrelationIdHolder.set(correlationId);
            if (content != null) {
                cacheResult(taskId, contentType, content, args, logicClass);
            }
            aiTaskManager.updateTaskStatus(taskId, AsyncTaskStatusEnum.Interrupt);
            UserIdHolder.remove();
            CorrelationIdHolder.remove();
        } catch (Exception e) {
            log.error("Error handling cancellation for taskId: {}", taskId, e);
            throw e;
        }
    }

    private void handleError(String taskId, String userId, String correlationId, Throwable error) {
        try {
            UserIdHolder.set(userId);
            CorrelationIdHolder.set(correlationId);
            UserIdHolder.remove();
            CorrelationIdHolder.remove();
            log.error("Error processing flux for taskId: {}", taskId, error);
        } catch (Exception e) {
            log.error("Error handling error for taskId: {}", taskId, e);
            throw new RuntimeException(e);
        }
    }

    private void cacheResult(String taskId, AiTaskContentTypeEnum contentType, Object result,
            Object[] args, Class logicClass) {
        try {
            Object logicInstanceObject = applicationContext.getBean(logicClass);
            // 1. 保存内容到 task_content 表
            if (logicInstanceObject instanceof ContentCacheLogic logicInstance) {
                logicInstance.updateContent(taskId, contentType, result, args);
            }

            // 2. 如果是标题，则更新任务标题
            if (AiTaskContentTypeEnum.TITLE.getType().equals(contentType.getType())) {
                aiTaskManager.updateTaskTitle(taskId, jsonMapperManager.convertToString(result));
            }
            // 3. 如果是最后一步，则更新任务状态为 Complete
            AnalyticsAiTaskHistoryPO task = aiTaskManager.getTaskById(taskId);
            if (isTaskRunning(task)) {
                updateTaskStatusIfLastStep(task, taskId, contentType);
            }
            log.info("Successfully saved content for taskId: {}, contentType: {}", taskId, contentType);
        } catch (Exception e) {
            log.error("Failed to save task content. taskId: {}, contentType: {}", taskId, contentType, e);
        }
    }

    private boolean isTaskRunning(AnalyticsAiTaskHistoryPO task) {
        return task != null && (AsyncTaskStatusEnum.Running.getValue().equals(task.getAsyncStatus()) ||
                AsyncTaskStatusEnum.Interrupt.getValue().equals(task.getAsyncStatus()));
    }

    private void updateTaskStatusIfLastStep(AnalyticsAiTaskHistoryPO task, String taskId,
            AiTaskContentTypeEnum contentType) {
        Map<String, List<String>> stepDetailMap = AiTaskTypeEnum.fromType(task.getType()).getStepDetailMap();
        String lastStep = stepDetailMap.values().stream().map(list -> list.get(list.size() - 1))
                .reduce((first, second) -> second).orElse(null);

        if (lastStep != null && contentType == AiTaskContentTypeEnum.fromType(lastStep)) {
            aiTaskManager.updateTaskStatus(taskId, AsyncTaskStatusEnum.Complete);
        }
    }

    private String extractOperateType(Object[] args) {
        if (args == null || args.length == 0) {
            throw new IllegalArgumentException("No arguments provided");
        }

        return Stream.of(args).filter(Objects::nonNull).map(arg -> {
                    if (arg instanceof AiTaskReqDTO aiTaskReqDTO) {
                        return aiTaskReqDTO.getOperateType();
                    }
                    return null;
                }).filter(Objects::nonNull).findFirst()
                .orElseThrow(() -> new IllegalArgumentException("No OperateType found in method arguments"));
    }

    /**
     * 从方法参数中提取taskId
     */
    private String extractTaskId(Object[] args) {
        if (args == null || args.length == 0) {
            throw new IllegalArgumentException("No arguments provided");
        }

        return Stream.of(args).filter(Objects::nonNull).map(arg -> {
                    if (arg instanceof AiTaskReqDTO aiTaskReqDTO) {
                        return aiTaskReqDTO.getTaskId();
                    }
                    return getTaskIdByReflection(arg);
                }).filter(Objects::nonNull).findFirst()
                .orElseThrow(() -> new IllegalArgumentException("No taskId found in method arguments"));
    }


    private String getTaskIdByReflection(Object arg) {
        try {
            Field taskIdField = arg.getClass().getDeclaredField("taskId");
            taskIdField.setAccessible(true);
            Object value = taskIdField.get(arg);
            return value != null ? value.toString() : null;
        } catch (Exception ignored) {
            return null;
        }
    }

    /**
     * 解析内容类型 1. 优先使用固定值方式 2. 如果固定值为空，则使用表达式方式
     */
    private AiTaskContentTypeEnum resolveContentType(ProceedingJoinPoint point, TaskContentCache taskContentCache) {
        // 如果表达式为空，使用原有方式
        if (StringUtils.isBlank(taskContentCache.contentTypeExpression())) {
            return taskContentCache.contentType();
        }

        try {
            MethodSignature signature = (MethodSignature) point.getSignature();
            Method method = signature.getMethod();

            // 获取方法参数名
            String[] parameterNames = discoverer.getParameterNames(method);
            if (parameterNames == null) {
                log.warn("无法获取方法参数名，降级使用固定值方式");
                return taskContentCache.contentType();
            }

            // 创建表达式上下文
            EvaluationContext context = new StandardEvaluationContext();
            Object[] args = point.getArgs();
            for (int i = 0; i < parameterNames.length; i++) {
                context.setVariable(parameterNames[i], args[i]);
            }

            // 解析SpEL表达式
            Expression expression = parser.parseExpression(taskContentCache.contentTypeExpression());
            String contentTypeStr = expression.getValue(context, String.class);

            return AiTaskContentTypeEnum.fromType(contentTypeStr);

        } catch (Exception e) {
            log.error("解析contentType表达式异常，降级使用固定值方式", e);
            return taskContentCache.contentType();
        }
    }
} 
package com.patsnap.drafting.enums.task;

import lombok.Getter;

import java.util.List;

import static com.patsnap.drafting.enums.task.AiTaskContentTypeEnum.SPECIFICATION_EMBODIMENT_EXTRACT;

/**
 * AI 说明书步骤类型枚举
 */
@Getter
public enum AiSpecificationStepEnum implements TaskStep {

    // 子步骤的枚举顺序需要和页面生成顺序一致
    SPECIFICATION_INPUT("SPECIFICATION_INPUT",
            List.of(
                    AiTaskContentTypeEnum.INITIALIZATION.getType()  //用户输入
            )
    ),

    // 子步骤的枚举顺序需要和页面生成顺序一致
    SPECIFICATION_DISCLOSURE_RESULT(
            "SPECIFICATION_DISCLOSURE_RESULT",
            List.of(
                    AiTaskContentTypeEnum.CLASSIFICATION.getType(),  //分类号
                    AiTaskContentTypeEnum.CATEGORY.getType(),        //类型
                    AiTaskContentTypeEnum.TECH_WRAPPER.getType(),    //技术领域
                    SPECIFICATION_EMBODIMENT_EXTRACT.getType()       //实施例提取
            )
    ),

    // 子步骤的枚举顺序需要和页面生成顺序一致
    CLAIM_PARSER("CLAIM_PARSER",
            List.of(
                    AiTaskContentTypeEnum.CLAIM_FORMAT.getType(),    //权利要求格式化
                    AiTaskContentTypeEnum.FEATURE_TREE.getType(),    //特征树
                    AiTaskContentTypeEnum.FIGURE_CONFIG.getType(),   //用户输入的附图列表
                    AiTaskContentTypeEnum.TERMS.getType(),           //术语列表
                    AiTaskContentTypeEnum.FIGURE_CONFIRM.getType()   //图片提交确认
            )
    ),

    // 子步骤的枚举顺序需要和页面生成顺序一致
    SPECIFICATION_RESULT("SPECIFICATION_RESULT",
            List.of(
                    AiTaskContentTypeEnum.TITLE.getType(),                             //标题
                    AiTaskContentTypeEnum.TECH_FIELD.getType(),                        //技术领域
                    AiTaskContentTypeEnum.BACKGROUND.getType(),                        //背景技术
                    AiTaskContentTypeEnum.SUMMARY.getType(),                           //发明内容
                    AiTaskContentTypeEnum.FIGURES.getType(),                           //附图列表
                    AiTaskContentTypeEnum.FIGURES_DESCRIPTION.getType(),               //附图说明
                    AiTaskContentTypeEnum.SPECIFICATION_EMBODIMENT.getType(),          //实施例(老数据)
                    AiTaskContentTypeEnum.SPECIFICATION_EMBODIMENT_OUTLINE.getType(),  //实施例大纲(新数据)
                    AiTaskContentTypeEnum.SPECIFICATION_EMBODIMENT_CONTENT.getType(),  //实施例内容(新数据)
                    AiTaskContentTypeEnum.ABSTRACT.getType(),                          //摘要
                    AiTaskContentTypeEnum.ABSTRACT_FIGURE.getType()                    //摘要附图
            )
    );
    
    
    private final String step;
    private final List<String> subStepList;
    
    AiSpecificationStepEnum(String step, List<String> subStepList) {
        this.step = step;
        this.subStepList = subStepList;
    }
}
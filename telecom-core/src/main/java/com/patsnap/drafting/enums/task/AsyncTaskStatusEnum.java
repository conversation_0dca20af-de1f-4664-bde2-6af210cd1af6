package com.patsnap.drafting.enums.task;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/07/23
 */
@Getter
public enum AsyncTaskStatusEnum {
    /**
     * 任务提交成功
     */
    Ready("Ready"),
    /**
     * 任务处理中
     */
    Running("Running"),
    /**
     * 任务处理完成
     */
    Complete("Complete"),
    /**
     * 任务中断
     */
    Interrupt("Interrupt"),
    /**
     * 任务处理失败
     */
    Failed("Failed");

    AsyncTaskStatusEnum(String value) {
        this.value = value;
    }

    private final String value;
}

package com.patsnap.drafting.enums.prompt;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/07/11
 */
@Getter
public enum ScenarioEnum {
    /**
     * AI Search: 文本转检索式
     */
    TEXT2QUERY("ANALYTICS_TEXT_TO_QUERY"),
    /**
     * AI Image: 图像相似检索
     */
    IMAGE_SIMILAR_SEARCH("IMAGE_SIMILAR_SEARCH"),
    /**
     * AI Claim: 专利权利要求流程图
     */
    TEXT2FLOW("ANALYTICS_TEXT_TO_FLOW"),
    /**
     * AI Translation: 翻译
     */
    AI_TRANSLATION("ANALYTICS_AI_TRANSLATION"),
    /**
     * 智能交底书撰写
     */
    PATENT_DISCLOSURE("ANALYTICS_PATENT_DISCLOSURE"),
    /**
     * 权利要求对比
     */
    ClAIM_COMPARE("ClAIM_COMPARE"),
    /**
     * 技术简报
     */
    TECH_REPORT_SCENARIO("TECH_REPORT"),

    AI_SPECIFICATION("ANALYTICS_AI_SPECIFICATION"),
    ;

    ScenarioEnum(String value) {
        this.value = value;
    }

    private final String value;

}

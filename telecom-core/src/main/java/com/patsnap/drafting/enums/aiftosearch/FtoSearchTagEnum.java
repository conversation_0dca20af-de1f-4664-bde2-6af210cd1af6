package com.patsnap.drafting.enums.aiftosearch;

import lombok.Getter;

/**
 * 调用算法接口时的操作类型
 */
@Getter
public enum FtoSearchTagEnum {

    KEYWORDS_EXTRACT("keywords_extract", "技术特征提取"),
    SUMMARY("summary", "总结"),
    TITLE_GENERATE("title_generate", "生成标题"),
    FEATURE_COMPARISON("feature_comparison", "获取技术特征对比及得分"),
    POINT_EXTRACT("point_extract", "特征抽取要素"),
    ;


    private final String value;
    private final String explain;

    FtoSearchTagEnum(String value, String explain) {
        this.value = value;
        this.explain = explain;
    }
}

package com.patsnap.drafting.enums.packageinfo;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 此类主要是自动赋权以及套餐展示时候使用，主要功能是：
 * 1、根据当前用户的套餐决定是否自动赋权
 * 2、套餐页面展示用户当前所拥有的套餐
 *
 * @author: <PERSON><PERSON>
 * @date: 2023/5/25 13:26
 */
@Getter
@AllArgsConstructor
public enum MainPackageEnum {
    //范域套餐
    IP_FREE(ProductEnum.IP, PayTypeEnum.FREE, FeatureEnum.IP_FREE_PACKAGE, 3,
            "ip_free", "configs.com.patsnap.eureka.package.ip-free"),
    IP_TRIAL(ProductEnum.IP, PayTypeEnum.TRIAL, FeatureEnum.IP_TRIAL_PACKAGE, 2,
            "ip_trial", "configs.com.patsnap.eureka.package.ip-trial"),
    IP_COMMERCIAL_OLD(ProductEnum.IP, PayTypeEnum.COMMERCIAL, FeatureEnum.IP_COMMERCIAL_PACKAGE, 1,
            "ip_commercial_old", "configs.com.patsnap.eureka.package.ip-commercial-old"),
    IP_COMMERCIAL(ProductEnum.IP, PayTypeEnum.COMMERCIAL, FeatureEnum.IP_COMMERCIAL_PACKAGE, 1,
            "ip_commercial", "configs.com.patsnap.eureka.package.ip-commercial"),
    //材料套餐
    MATERIAL_TRIAL(ProductEnum.MATERIAL, PayTypeEnum.TRIAL, FeatureEnum.MATERIAL_TRIAL_PACKAGE, 2,
            "material_trial", "configs.com.patsnap.eureka.package.material-trial"),
    MATERIAL_COMMERCIAL_OLD(ProductEnum.MATERIAL, PayTypeEnum.COMMERCIAL, FeatureEnum.MATERIAL_COMMERCIAL_PACKAGE, 1,
            "material_commercial_old", "configs.com.patsnap.eureka.package.material-commercial-old"),
    MATERIAL_COMMERCIAL(ProductEnum.MATERIAL, PayTypeEnum.COMMERCIAL, FeatureEnum.MATERIAL_COMMERCIAL_PACKAGE, 1,
            "material_commercial", "configs.com.patsnap.eureka.package.material-commercial"),
    //Air套餐
    AIR_FREE(ProductEnum.AIR, PayTypeEnum.FREE, FeatureEnum.AIR_FREE_PACKAGE, 3,
            "air_free", "configs.com.patsnap.eureka.package.air-free"),
    AIR_TRIAL(ProductEnum.AIR, PayTypeEnum.TRIAL, FeatureEnum.AIR_TRIAL_PACKAGE, 2,
            "air_trial", "configs.com.patsnap.eureka.package.air-trial"),
    AIR_COMMERCIAL(ProductEnum.AIR, PayTypeEnum.COMMERCIAL, FeatureEnum.AIR_COMMERCIAL_PACKAGE, 1,
            "air_commercial", "configs.com.patsnap.eureka.package.air-commercial"),
    //Insights套餐
    INSIGHTS_TRIAL(ProductEnum.INSIGHTS, PayTypeEnum.TRIAL, FeatureEnum.INSIGHTS_TRIAL_PACKAGE, 2,
            "insights_trial", "configs.com.patsnap.eureka.package.insights-trial"),
    INSIGHTS_COMMERCIAL(ProductEnum.INSIGHTS, PayTypeEnum.COMMERCIAL, FeatureEnum.INSIGHTS_COMMERCIAL_PACKAGE, 1,
            "insights_commercial", "configs.com.patsnap.eureka.package.insights-commercial"),
    ;

    /**
     * 套餐id
     */
    @Setter
    private String packageId;
    /**
     * 产品业务类型
     */
    private final ProductEnum product;
    /**
     * 套餐付费类型
     */
    private final PayTypeEnum payTypeEnum;
    /**
     * 产品套餐对应feature id
     */
    private final String featureId;
    /**
     * 套餐展示优先级 当用户没有勾选产品时，按照这个优先级赋默认值
     */
    private final Integer displayPriority;
    /**
     * 产品套餐展示名称
     */
    private final String display;
    /**
     * 产品套餐环境变量配置ID
     */
    private final String property;

    private static final Map<MainPackageEnum, MainPackageEnum> COMMERCIAL_TO_TRIAL_MAP;

    static {
        Map<MainPackageEnum, MainPackageEnum> map = new EnumMap<>(MainPackageEnum.class);
        map.put(AIR_COMMERCIAL, AIR_TRIAL);
        map.put(IP_COMMERCIAL, IP_TRIAL);
        map.put(IP_COMMERCIAL_OLD, IP_TRIAL);
        map.put(MATERIAL_COMMERCIAL, MATERIAL_TRIAL);
        map.put(MATERIAL_COMMERCIAL_OLD, MATERIAL_TRIAL);
        map.put(INSIGHTS_COMMERCIAL, INSIGHTS_TRIAL);
        COMMERCIAL_TO_TRIAL_MAP = Collections.unmodifiableMap(map);
    }


    MainPackageEnum(ProductEnum product, PayTypeEnum payTypeEnum, FeatureEnum featureEnum,
                    Integer displayPriority, String display, String property) {
        this.product = product;
        this.payTypeEnum = payTypeEnum;
        this.featureId = Optional.ofNullable(featureEnum).map(FeatureEnum::getFeatureId).orElse("");
        this.displayPriority = displayPriority;
        this.display = display;
        this.property = property;
    }

    public static List<String> getMainProductPackageIds(ProductEnum product) {
        return Arrays.stream(MainPackageEnum.values())
                        .filter(mainPackageEnum -> mainPackageEnum.getProduct() == product && StringUtils.isNotBlank(
                        mainPackageEnum.getPackageId())).map(MainPackageEnum::getPackageId).toList();
    }
    
    public static List<String> getAllMainPackageId() {
        return Arrays.stream(MainPackageEnum.values()).map(MainPackageEnum::getPackageId)
                .filter(StringUtils::isNotBlank).collect(Collectors.toList());
    }

    public static List<String> getAllMainPackageFeatureIds() {
        return Arrays.stream(MainPackageEnum.values())
                .map(MainPackageEnum::getFeatureId)
                .filter(StringUtils::isNotBlank).collect(Collectors.toList());
    }

    public static List<String> getAllTrialAndCommercialFeatureIds() {
        return Arrays.stream(MainPackageEnum.values())
                        .filter(mainPackageEnum -> PayTypeEnum.FREE != mainPackageEnum.getPayTypeEnum())
                        .map(MainPackageEnum::getFeatureId).filter(StringUtils::isNotBlank)
                        .collect(Collectors.toList());
    }

    public static List<String> getPackageIdsBy(PayTypeEnum payTypeEnum) {
        return Arrays.stream(MainPackageEnum.values())
                        .filter(mainPackageEnum -> payTypeEnum == mainPackageEnum.getPayTypeEnum())
                        .map(MainPackageEnum::getPackageId).filter(StringUtils::isNotBlank)
                        .collect(Collectors.toList());
    }

    public static MainPackageEnum getByPackageId(String packageId) {
        return Arrays.stream(MainPackageEnum.values())
                .filter(mainPackageEnum -> packageId.equals(mainPackageEnum.getPackageId()))
                .findFirst().orElse(null);
    }
    
    public static List<String> getFeatureIdBy(PayTypeEnum payTypeEnum) {
        return Arrays.stream(MainPackageEnum.values())
                .filter(mainPackageEnum -> payTypeEnum.equals(mainPackageEnum.getPayTypeEnum()))
                .map(MainPackageEnum::getFeatureId).filter(StringUtils::isNotBlank).collect(Collectors.toList());
    }
    
    public static List<String> getFeatureIdBy(ProductEnum productEnum) {
        return Arrays.stream(MainPackageEnum.values())
                .filter(mainPackageEnum -> productEnum.equals(mainPackageEnum.getProduct()))
                .map(MainPackageEnum::getFeatureId).filter(StringUtils::isNotBlank).collect(Collectors.toList());
    }
    

    public static Map<String, MainPackageEnum> getPackageId2MainPackageMap() {
        return Arrays.stream(MainPackageEnum.values())
                .filter(mainPackageEnum -> StringUtils.isNotBlank(mainPackageEnum.getPackageId()))
                .collect(Collectors.toMap(
                        MainPackageEnum::getPackageId,
                        mainPackageEnum -> mainPackageEnum,
                        (existing, replacement) -> existing
                ));
    }

    /**
     * 根据付费套餐id获取试用套餐id （scene: 用户同时拥有免费套餐和试用套餐，则不展示升级弹框 ）
     *
     * @param commercialPackageId 付费套餐id
     * @return 试用套餐id
     */
    public static String getTrialPackageIdByCommercialPackageId(String commercialPackageId) {
        MainPackageEnum mainPackageEnum = MainPackageEnum.getByPackageId(commercialPackageId);
        if (mainPackageEnum == null) {
            return null;
        }
        MainPackageEnum trialPackageEnum = COMMERCIAL_TO_TRIAL_MAP.get(mainPackageEnum);
        return trialPackageEnum != null ? trialPackageEnum.getPackageId() : null;
    }
}
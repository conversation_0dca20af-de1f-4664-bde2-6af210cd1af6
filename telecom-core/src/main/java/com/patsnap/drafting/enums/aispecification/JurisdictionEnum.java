package com.patsnap.drafting.enums.aispecification;

public enum JurisdictionEnum {
    CNIPA("CN", "CNIPA"),
    USPTO("EN", "USPTO"),
    EPO("EN", "EPO-EN")
    ;

    private final String lang;
    private final String authority;

    JurisdictionEnum(String lang, String authority) {
        this.lang = lang;
        this.authority = authority;
    }

    public String getValue() {
        return lang;
    }
    
    public String getAuthority() {
        return authority;
    }

    public static JurisdictionEnum fromLang(String lang) {
        for (JurisdictionEnum jurisdiction : values()) {
            if (jurisdiction.lang.equals(lang)) {
                return jurisdiction;
            }
        }
        throw new IllegalArgumentException("No JurisdictionEnum found for lang: " + lang);
    }

    public static JurisdictionEnum fromName(String name) {
        try {
            return JurisdictionEnum.valueOf(name.toUpperCase().trim());
        } catch (IllegalArgumentException e) {
            throw new IllegalArgumentException("No jurisdiction found for: " + name);
        }
    }


}

package com.patsnap.drafting.enums.common;

/**
 * @description 风险等级
 */
@lombok.Getter
public enum RiskLevelEnum {
    
    HIGH("High", "高风险"),
    MEDIUM("Medium", "中风险"),
    LOW("Low", "低风险"),
    ;
    
    
    private final String value;
    private final String explain;
    
    RiskLevelEnum(String value, String explain) {
        this.value = value;
        this.explain = explain;
    }
    
    public static RiskLevelEnum fromValue(String value) {
        for (RiskLevelEnum riskLevel : RiskLevelEnum.values()) {
            if (riskLevel.getValue().equals(value)) {
                return riskLevel;
            }
        }
        return null;
    }
}

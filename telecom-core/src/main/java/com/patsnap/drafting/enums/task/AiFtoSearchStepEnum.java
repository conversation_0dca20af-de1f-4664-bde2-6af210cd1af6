package com.patsnap.drafting.enums.task;

import lombok.Getter;

import java.util.List;

/**
 * AI Fto侵权查询步骤类型枚举
 * <AUTHOR> chendepeng
 * @Date 2025/4/3 15:33
 */
@Getter
public enum AiFtoSearchStepEnum implements TaskStep {

    // 用户输入阶段
    FTO_SEARCH_INPUT("FTO_SEARCH_INPUT",
            List.of(
                    AiTaskContentTypeEnum.FTO_SEARCH_RECEIVING_OFFICES.getType(),
                    AiTaskContentTypeEnum.FTO_SEARCH_EXCLUDE_COMPANY.getType(),
                    AiTaskContentTypeEnum.FTO_SEARCH_INCLUDE_COMPANY.getType(),
                    AiTaskContentTypeEnum.FTO_SEARCH_LEGAL_STATUS.getType(),
                    AiTaskContentTypeEnum.FTO_SEARCH_APD_START.getType(),
                    AiTaskContentTypeEnum.FTO_SEARCH_APD_END.getType(),
                    AiTaskContentTypeEnum.USER_INPUT.getType(),
                    AiTaskContentTypeEnum.TITLE.getType(),
                    AiTaskContentTypeEnum.FTO_SEARCH_INPUT_SUMMARY.getType())),

    // 特征关键词提取阶段
    FTO_SEARCH_TECH_FEATURE("FTO_SEARCH_TECH_FEATURE",
            List.of(AiTaskContentTypeEnum.FTO_SEARCH_TECH_FEATURE.getType())),

    // AI 侵权查询特征对比确认阶段
    FTO_SEARCH_CONFIRM("CONFIRM",
            List.of(
                    AiTaskContentTypeEnum.FTO_SEARCH_AGENT_RESULT.getType(),
                    AiTaskContentTypeEnum.FTO_SEARCH_FEATURE_CONFIRM.getType())),

    // AI 侵权查询报告阶段
    FTO_SEARCH_RESULT("RESULT",
            List.of(
                    AiTaskContentTypeEnum.FTO_SEARCH_REPORT_TITLE.getType()));

    private final String step;
    private final List<String> subStepList;

    AiFtoSearchStepEnum(String step, List<String> subStepList) {
        this.step = step;
        this.subStepList = subStepList;
    }
}

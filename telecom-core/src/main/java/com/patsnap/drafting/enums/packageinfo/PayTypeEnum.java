package com.patsnap.drafting.enums.packageinfo;

import lombok.Getter;

/**
 * 套餐付费类型
 *
 * <AUTHOR>
 * @Date 2024/11/13 下午2:15
 */
@Getter
public enum PayTypeEnum {
    FREE("free", 2), TRIAL("trial", 1), COMMERCIAL("commercial", 0);

    private final String display;

    private final int priority;

    PayTypeEnum(String display, int priority) {
        this.display = display;
        this.priority = priority;
    }
}

package com.patsnap.drafting.enums.aidisclosure;

import com.patsnap.drafting.enums.common.OperateTypeEnum;
import com.patsnap.drafting.enums.task.AiTaskContentTypeEnum;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

/**
 * SA交底书接口操作模式
 *
 * <AUTHOR>
 * @date 2024/08/22
 */
@Getter
public enum DisclosureModeEnum {
    /**
     * 抽取信息，结构化用户输入
     */
    EXTRACT("extract", "extract", false),
    /**
     * 推荐发明主体，应用领域
     */
    RECOMMEND("recommend", "recommend", false),
    /**
     * 对用户输入进行分类
     */
    CLASSIFY("classify", "classify", false),
    /**
     * 生成标题
     */
    TITLE("title", "title", false),
    /**
     * 生成技术领域
     */
    TECH_FIELD(AiTaskContentTypeEnum.TECH_FIELD.getType(), "tech_field",  true),
    /**
     * 生成背景技术描述
     */
    BACKGROUND(AiTaskContentTypeEnum.BACKGROUND.getType(), "background_ref", true),
    /**
     * 生成引用文献介绍
     */
    REFERENCE(AiTaskContentTypeEnum.REFERENCE.getType(), "reference", true),
    /**
     * 生成现有技术的缺点
     */
    DRAWBACKS(AiTaskContentTypeEnum.DRAWBACKS.getType(), "drawbacks_ref", true),
    /**
     * 生成发明目的的问题描述
     */
    TECH_PROBLEM(AiTaskContentTypeEnum.TECH_PROBLEM.getType(), "tech_problem", true),
    /**
     * 生成发明目的的有益效果
     */
    TECH_EFFECT(AiTaskContentTypeEnum.TECH_EFFECT.getType(), "tech_effect", true),
    /**
     * 生成技术方案
     */
    TECH_MEANS(AiTaskContentTypeEnum.TECH_MEANS.getType(), "tech_means", true),
    /**
     * 生成实施例
     */
    EMBODIMENT(AiTaskContentTypeEnum.EMBODIMENT.getType(), "embodiment", true),
    /**
     * 简写
     */
    SIMPLIFY(OperateTypeEnum.SIMPLIFY.getValue(), "simplify", false),
    /**
     * 扩写
     */
    EXPAND(OperateTypeEnum.EXPAND.getValue(), "expand", false),
    /**
     * 改写
     */
    POLISH(OperateTypeEnum.POLISH.getValue(), "polish", false),
    /**
     * 新增背景技术的描述
     */
    ADD_BACKGROUND(OperateTypeEnum.ADD_BACKGROUND.getValue(), "add_background_ref", false),
    /**
     * 新增一篇引用专利
     */
    ADD_REFERENCE(OperateTypeEnum.ADD_REFERENCE.getValue(), "add_reference", false),
    /**
     * 新增一个现有技术缺点
     */
    ADD_DRAWBACKS(OperateTypeEnum.ADD_DRAWBACKS.getValue(), "add_drawbacks_ref", false),
    /**
     * 新增一个问题
     */
    ADD_PROBLEM(OperateTypeEnum.ADD_PROBLEM.getValue(), "add_problem", false),
    /**
     * 新增一个有益效果
     */
    ADD_EFFECT(OperateTypeEnum.ADD_EFFECT.getValue(), "add_effect", false),
    /**
     * 新增一个实施例
     */
    ADD_EMBODIMENT(OperateTypeEnum.ADD_EMBODIMENT.getValue(), "add_embodiment", false),
    /**
     * 用户修改others修改重新抽取
     */
    OTHER_EXTRACT("other_extract", "other_extract", false),
    ;

    private final String value;
    
    private final String mode;
    /**
     * 是否为生成正文内容
     */
    private final boolean isContent;

    DisclosureModeEnum(String value, String mode, boolean isContent) {
        this.value = value;
        this.mode = mode;
        this.isContent = isContent;
    }

    public static DisclosureModeEnum fromValue(String value) {
        for (DisclosureModeEnum mode : DisclosureModeEnum.values()) {
            if (mode.getValue().equals(value)) {
                return mode;
            }
        }
        throw new IllegalArgumentException("Unknown DisclosureModeEnum value: " + value);
    }

    /**
     * 获取所有生成正文内容字段
     *
     * @return 生成正文内容字段列表
     */
    public static List<String> listContentField() {
        return Arrays.stream(DisclosureModeEnum.values())
                .filter(DisclosureModeEnum::isContent)
                .map(DisclosureModeEnum::getValue)
                .toList();
    }
}

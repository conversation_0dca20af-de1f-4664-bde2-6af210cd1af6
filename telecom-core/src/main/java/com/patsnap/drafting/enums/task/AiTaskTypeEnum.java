package com.patsnap.drafting.enums.task;

import com.patsnap.drafting.exception.BizException;
import com.patsnap.drafting.exception.errorcode.TaskErrorCodeEnum;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * 任务类型枚举
 */
@Slf4j
@Getter
public enum AiTaskTypeEnum {
    AI_TRANSLATION("AI_TRANSLATION", 202501, AiTranslationStepEnum.values(), new Integer[]{202501}),
    AI_PATENT_DISCLOSURE("AI_PATENT_DISCLOSURE", 202501, AiDisclosureStepEnum.values(), new Integer[]{202501}),
    AI_SPECIFICATION("AI_SPECIFICATION", 202501, AiSpecificationStepEnum.values(), new Integer[]{202501}),
    AI_NOVELTY_SEARCH("AI_NOVELTY_SEARCH", 202506, AiNoveltySearchStepEnum.values(), new Integer[]{202501, 202505, 202506}),
    AI_FTO_SEARCH("AI_FTO_SEARCH", 202501, AiFtoSearchStepEnum.values(), new Integer[]{202501});

    private final String type;
    private final Integer dataVersion;
    private final TaskStep[] steps;
    private final Integer[] historyVersions;
    // 缓存步骤详情Map，避免重复计算
    private final Map<String, List<String>> stepDetailMap;

    AiTaskTypeEnum(String type, Integer dataVersion, TaskStep[] steps, Integer[] historyVersions) {
        this.type = type;
        this.dataVersion = dataVersion;
        this.steps = steps;
        // 在构造时就初始化Map，并使用LinkedHashMap保证顺序
        this.stepDetailMap = Arrays.stream(steps)
            .collect(LinkedHashMap::new,
                    (map, step) -> map.put(step.getStep(), step.getSubStepList()),
                    Map::putAll);
        this.historyVersions = historyVersions;
    }

    public static AiTaskTypeEnum fromType(String type) {
        return Arrays.stream(values())
                .filter(e -> e.type.equals(type))
                .findFirst()
                .orElseThrow(() -> new BizException(TaskErrorCodeEnum.TASK_TYPE_NOT_EXIST));
    }
}

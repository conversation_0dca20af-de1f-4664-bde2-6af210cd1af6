package com.patsnap.drafting.enums.task;

import lombok.Getter;

import java.util.List;

/**
 * AI 查新步骤类型枚举
 */
@Getter
public enum AiNoveltySearchStepEnum implements TaskStep {
    
    // 用户输入阶段
    NOVELTY_SEARCH_INPUT("NOVELTY_SEARCH_INPUT",
            List.of(
                    AiTaskContentTypeEnum.USER_INPUT.getType(),
                    AiTaskContentTypeEnum.TITLE.getType(),
                    AiTaskContentTypeEnum.NOVELTY_SEARCH_INPUT_SUMMARY.getType()
            )),
    
    // 特征关键词提取阶段
    NOVELTY_SEARCH_TECH_FEATURE("NOVELTY_SEARCH_TECH_FEATURE",
            List.of(AiTaskContentTypeEnum.NOVELTY_SEARCH_TECH_FEATURE.getType()
    )),
    
    // 检索要素确认阶段
    NOVELTY_SEARCH_RETRIEVAL_ELEMENTS("NOVELTY_SEARCH_RETRIEVAL_ELEMENTS",
            List.of(AiTaskContentTypeEnum.NOVELTY_SEARCH_RETRIEVAL_ELEMENTS.getType()
    )),

    // AI 初筛阶段
    NOVELTY_SEARCH_PATENT_CONFIRM("PATENT_CONFIRM", List.of(
            AiTaskContentTypeEnum.NOVELTY_SEARCH_AGENT_RESULT.getType(),
            AiTaskContentTypeEnum.NOVELTY_SEARCH_PATENT_CONFIRM_FEATURE.getType()
    )),

    // AI 查新特征对比确认阶段
    NOVELTY_SEARCH_CONFIRM("GENERATE_FEATURE_COMPARE_TABLE", List.of(
            AiTaskContentTypeEnum.NOVELTY_SEARCH_FEATURE_CONFIRM.getType()
    )),
    
    // AI 查新报告阶段
    NOVELTY_SEARCH_RESULT("RESULT", List.of(
            AiTaskContentTypeEnum.NOVELTY_SEARCH_REPORT_TITLE.getType(),
            AiTaskContentTypeEnum.NOVELTY_SEARCH_REPORT_COMPARATIVE_LITERATURE.getType(),
            AiTaskContentTypeEnum.NOVELTY_SEARCH_REPORT_REVIEW_OF_NOVELTY.getType(),
            AiTaskContentTypeEnum.NOVELTY_SEARCH_REPORT_REVIEW_OF_CREATIVE.getType()
    ));
    
    private final String step;
    private final List<String> subStepList;
    
    AiNoveltySearchStepEnum(String step, List<String> subStepList) {
        this.step = step;
        this.subStepList = subStepList;
    }
}

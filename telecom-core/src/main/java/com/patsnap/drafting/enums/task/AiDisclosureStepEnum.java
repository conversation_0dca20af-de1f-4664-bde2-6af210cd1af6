package com.patsnap.drafting.enums.task;

import lombok.Getter;

import java.util.List;

/**
 * AI 交底书步骤类型枚举
 */
@Getter
public enum AiDisclosureStepEnum implements TaskStep {
    
    // AI 交底书
    DISCLOSURE_RESULT("DISCLOSURE_RESULT", List.of(AiTaskContentTypeEnum.SETTING.getType(),
            AiTaskContentTypeEnum.TITLE.getType(),
            AiTaskContentTypeEnum.TECH_FIELD.getType(),
            AiTaskContentTypeEnum.BACKGROUND.getType(),
            AiTaskContentTypeEnum.REFERENCE.getType(),
            AiTaskContentTypeEnum.DRAWBACKS.getType(),
            AiTaskContentTypeEnum.TECH_PROBLEM.getType(),
            AiTaskContentTypeEnum.TECH_EFFECT.getType(),
            AiTaskContentTypeEnum.TECH_MEANS.getType(),
            AiTaskContentTypeEnum.EMBODIMENT.getType())),
    ;
    
    private final String step;
    private final List<String> subStepList;
    
    AiDisclosureStepEnum(String step, List<String> subStepList) {
        this.step = step;
        this.subStepList = subStepList;
    }
}
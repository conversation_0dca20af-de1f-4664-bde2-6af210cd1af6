package com.patsnap.drafting.enums.packageinfo;

import com.google.common.collect.Lists;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 各个功能点在不同付费类型的配置
 *
 * <AUTHOR>
 * @Date 2024/11/13 下午4:29
 */
@Getter
public enum FeaturePayTypeEnum {

    SEARCH("3c8a", "35ri"),

    NEWS_LETTER("35u0", "3508"),

    NOVELTY_CHECK("3i7e", "350m");

    /**
     * ip业务功能点id
     */
    private final String trail;

    /**
     * 材料业务功能点id
     */
    private final String commercial;

    FeaturePayTypeEnum(String trail, String commercial) {
        this.trail = trail;
        this.commercial = commercial;
    }

    /**
     * 获取当前feature配置的付费类型，过滤掉为空的
     *
     * @return
     */
    public List<String> getConfigFeatureIds() {
        List<String> list = Lists.newArrayList(trail, commercial);
        return list.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
    }
}

package com.patsnap.drafting.enums.packageinfo;

import com.google.common.collect.Lists;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 各个功能模块针对不同业务线配置
 *
 * <AUTHOR>
 * @Date 2024/11/13 下午4:23
 */
@Getter
public enum FeatureBusinessTypeEnum {

    SEEK("3chr", "3b19", "32bi", "3baz"),

    RESEARCH("", "3f5y", "3bx7", ""),

    SEARCH("32bh", "3i2z", "", ""),

    PATENT_VIEW("3bce", "3f5w", "", ""),

    PAPER_VIEW("3bc6", "3f5o", "", ""),

    COPILOT("32b5", "3i2n", "", ""),
    ;

    /**
     * ip业务功能点id
     */
    private final String ip;

    /**
     * 材料业务功能点id
     */
    private final String material;

    /**
     * air业务功能点id
     */
    private final String air;

    /**
     * discovery业务功能点id
     */
    private final String insights;

    FeatureBusinessTypeEnum(String ip, String material, String air, String insights) {
        this.ip = ip;
        this.material = material;
        this.air = air;
        this.insights = insights;
    }

    /**
     * 获取当前feature配置的业务类型，过滤掉为空的
     *
     * @return
     */
    public List<String> getConfigFeatureIds() {
        List<String> list = Lists.newArrayList(ip, material, air, insights);
        return list.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
    }
}

package com.patsnap.drafting.enums.prompt;

import lombok.Getter;

/**
 * prompt管理平台的key
 *
 * <AUTHOR>
 * @date 2024/07/22
 * @see SpecificationPromptKeyEnum 说明书相关的prompt key
 */
@Getter
public enum PromptKeyEnum {
    /**
     * 图片转文字
     */
    IMAGE2TEXT_PROMPT_KEY("analytics.ai_image.recognition"),
    /**
     * 权利要求转流程图
     */
    TEXT2MERMAID_PROMPT_KEY("analytics.flow_chart.visualization"),
    /**
     * AI翻译: 逐句翻译
     */
    SENTENCE_TRANSLATION("analytics.ai_translation.sentence_translation"),
    /**
     * AI翻译: 翻译校对
     */
    PROOFREADING_TRANSLATION("analytics.ai_translation.proofreading"),
    /**
     * AI翻译: 提取技术主题
     */
    TOPIC_EXTRACTION("analytics.ai_translation.topic_extraction"),
    /**
     * AI翻译: 提取技术术语
     */
    TERMINOLOGY_EXTRACTION("analytics.ai_translation.terminology_extraction"),
    /**
     * AI翻译: 句子重写后翻译
     */
    SENTENCE_REWRITING("analytics.ai_translation.sentence.rewriting"),
    /**
     * AI翻译: 调用自研模型翻译, 英译中
     */
    SELF_MODEL_TRANSLATION_EN2CN("analytics.ai_translation.self_model_translation_en2cn"),

    /**
     * AI翻译: 调用自研模型翻译, 中译英
     */
    SELF_MODEL_TRANSLATION_CN2EN("analytics.ai_translation.self_model_translation_cn2en"),
    /**
     * AI翻译: 调用自研模型句子重写后翻译
     */
    SELF_MODEL_SENTENCE_REWRITING("analytics.ai_translation.self_model_translation_rewriting"),

    /**
     * 图片相似度比较
     */
    IMAGE_SIMILARITY_COMPARISON("analytics.ai_image.similarity_comparison"),
    /**
     * 图片相似性报告评估
     */
    IMAGE_SIMILARITY_REPORT("analytics.ai_image.similarity_comparison_report"),
    
    /**
     * 短语分词
     */
    SIMPLE_WORD_SEGMENTATION("analytics.tech_report.simple_word_segmentation"),
    
    /**
     * 二次检查独特技术特征
     */
    SECONDARY_CHECK_UNIQUE_TECH_FEATURES("analytics.tech_report.secondary_check_unique_tech_features"),
    
    
    /**
     * 分析特征 - 关键词
     */
    ANALYSIS_FEATURE_KEYWORDS("analytics.tech_report.analysis_feature_keywords"),
    
    /**
     * 关联度分析
     */
    CORRELATION_ANALYSIS("analytics.tech_report.correlation_analysis"),
    
    /**
     * 推荐子领域 - 公司
     */
    RECOMMEND_SUBDOMAIN_COMPANY("analytics.tech_report.recommend_subdomain_company"),
    
    /**
     * 推荐子领域 - 公司&技术
     */
    RECOMMEND_SUBDOMAIN_COMPANY_TECH("analytics.tech_report.recommend_subdomain_company_tech"),
    
    /**
     * 推荐子领域 - 关键词
     */
    RECOMMEND_SUBDOMAIN_KEYWORDS("analytics.tech_report.recommend_subdomain_keywords"),
    
    /**
     * 要素拆解 - 默认
     */
    ELEMENT_ANALYSIS_DEFAULT("analytics.tech_report.element_analysis_default"),
    
    /**
     * 意图识别
     */
    INTENT_RECOGNITION("analytics.tech_report.intent_recognition"),

    /**
     * 扩词
     */
    EXPAND_KEYWORDS("analytics.tech_report.expand_keyword"),



    /**
     * 总领域总结
     */
    GENERAL_DOMAIN_SUMMARY("analytics.tech_report.general_domain_summary"),
    ;


    PromptKeyEnum(String value) {
        this.value = value;
    }

    private final String value;
}

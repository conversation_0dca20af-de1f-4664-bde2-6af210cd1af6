package com.patsnap.drafting.enums.ainoveltysearch;

import lombok.Getter;

/**
 * 调用算法接口时的操作类型
 */
@Getter
public enum NoveltySearchTagEnum {
    
    KEYWORDS_EXTRACT("keywords_extract", "检索要素提取"),
    FEATURE_EXTRACT("feature_extract", "技术特征提取"),
    SUMMARY("summary", "总结"),
    KEYWORDS_EXTEND("keywords_extend", "增加检索要素，单独扩词"),
    TITLE_GENERATE("title_generate", "生成标题"),
    FEATURE_COMPARISON("feature_comparison", "技术特征对比及得分"),
    POINT_EXTRACT("point_extract", "特征抽取要素"),
    REPORT_GENERATE("report_generate", "生成报告")
    ;
    
    
    private final String value;
    private final String explain;
    
    NoveltySearchTagEnum(String value, String explain) {
        this.value = value;
        this.explain = explain;
    }
}

package com.patsnap.drafting.enums.task;

import lombok.Getter;

/**
 * 内容方向标识（用户输入或内容输出）
 */
@Getter
public enum TaskContentDirectionEnum {
    /**
     * 用户输入
     */
    INPUT("input"),

    /**
     * 任务创建时的额外信息
     */
    CONFIG("config"),

    /**
     * 内容输出
     */
    OUTPUT("output");

    TaskContentDirectionEnum(String value) {
        this.value = value;
    }

    private final String value;
}

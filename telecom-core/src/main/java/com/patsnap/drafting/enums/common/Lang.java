package com.patsnap.drafting.enums.common;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 02/12/2016.
 */
public enum Lang {
    CN, TW, EN, JP, DE;

    public static Lang parseLang(String language) {
        try {
            return Lang.valueOf(language.toUpperCase());
        } catch (Exception e) {
            return EN;
        }
    }

    public static List<String> getValues() {
        return Arrays.asList(Lang.values()).stream().map(Lang::toString).collect(Collectors.toList());
    }
}

package com.patsnap.drafting.enums.common;

import lombok.Getter;

/**
 * 内容生成的操作类型
 */
@Getter
public enum OperateTypeEnum {
    
    GENERATE("generate", "生成内容"),
    REGENERATE("regenerate", "重新生成内容"),
    EXPAND("expand", "扩写"),
    POLISH("polish", "润色"),
    SIMPLIFY("simplify", "简写"),
    CONTINUE("continue", "续写"),
    ADD_EMBODIMENT("add_embodiment", "增加实施例"),
    ADD_BACKGROUND("add_background", "增加背景技术描述"),
    ADD_REFERENCE("add_reference", "新增一篇引用专利"),
    ADD_DRAWBACKS("add_drawbacks", "新增一个现有技术缺点"),
    ADD_PROBLEM("add_problem", "新增一个问题"),
    ADD_EFFECT("add_effect", "新增一个有益效果"),
    ;
    
    
    private final String value;
    private final String explain;
    
    OperateTypeEnum(String value, String explain) {
        this.value = value;
        this.explain = explain;
    }
    
    public static OperateTypeEnum fromValue(String value) {
        for (OperateTypeEnum operateType : OperateTypeEnum.values()) {
            if (operateType.getValue().equals(value)) {
                return operateType;
            }
        }
        return null;
    }
}

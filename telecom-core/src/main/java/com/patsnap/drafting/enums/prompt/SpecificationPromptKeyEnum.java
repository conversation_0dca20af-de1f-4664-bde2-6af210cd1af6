package com.patsnap.drafting.enums.prompt;

import lombok.Getter;

/**
 * 说明书相关的prompt管理平台key
 *
 * <AUTHOR>
 * @date 2024/07/22
 */
@Getter
public enum SpecificationPromptKeyEnum {
    /**
     * 说明书撰写: 获取类别
     */
    CATEGORY("analytics.ai_specification_category"),

    /**
     * 说明书撰写: 获取技术三要素
     */
    TECH_WRAPPER("analytics.ai_specification_tech_wrapper"),

    /**
     * 说明书撰写: 提权技术特征树
     */
    FEATURE_TREE("analytics.ai_specification_feature_tree"),
    
    /**
     * 说明书撰写: 改写
     */
    IMPROVE_CONTENT("analytics.ai_specification_improve_content"),
    
    /**
     * 说明书撰写: 简写
     */
    SHORTER_CONTENT("analytics.ai_specification_shorter_content"),
    
    /**
     * 说明书撰写: 扩写
     */
    LONGER_CONTENT("analytics.ai_specification_longer_content"),
    
    /**
     * 说明书撰写: 关键词扩展
     */
    KEYWORD_EXPAND("analytics.ai_specification_keyword_expand"),
    
    /**
     * 说明书撰写: 续写背景技术
     */
    BACKGROUND_CONTINUE_WRITING("analytics.ai_specification_background_continue_writing"),
    
    /**
     * 说明书撰写: 续写发明内容
     */
    SUMMARY_CONTINUE_WRITING("analytics.ai_specification_summary_continue_writing"),
    
    /**
     * 说明书撰写: 增加实施例
     */
    ADD_EMBODIMENT("analytics.ai_specification_add_embodiment"),
    /**
     * EPO 说明书撰写: 增加实施例
     */
    ADD_EMBODIMENT_EPO("analytics.ai_specification_add_embodiment_epo"),
    ;

    SpecificationPromptKeyEnum(String value) {
        this.value = value;
    }

    private final String value;
} 
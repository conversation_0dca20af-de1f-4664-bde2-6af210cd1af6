package com.patsnap.drafting.enums.aispecification;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 受理局附图格式枚举
 * 定义不同受理局的附图展示格式规则
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum JurisdictionFigureFormatEnum {

    /**
     * 中国受理局：图片居中，标题在图片下方居中，格式"图 1"、"图 2"
     */
    CNIPA("cnipa", "图 %d", FigureAlignment.CENTER, TitlePosition.BELOW, TitleAlignment.CENTER),

    /**
     * 欧洲专利局：图片靠左，标题在图片上方靠左，格式"[Fig. 1]"、"[Fig. 2]"
     */
    EPO("epo", "[Fig. %d]", FigureAlignment.LEFT, TitlePosition.ABOVE, TitleAlignment.LEFT),

    /**
     * 美国专利局：图片靠左，标题在图片下方靠左，格式"Figure 1"、"Figure 2"
     */
    USPTO("uspto", "Figure %d", FigureAlignment.LEFT, TitlePosition.BELOW, TitleAlignment.LEFT),

    /**
     * 默认格式（中国格式）
     */
    DEFAULT("default", "图 %d", FigureAlignment.CENTER, TitlePosition.BELOW, TitleAlignment.CENTER);

    /** 受理局标识 */
    private final String jurisdiction;

    /** 标题格式模板 */
    private final String titleFormat;

    /** 图片对齐方式 */
    private final FigureAlignment figureAlignment;

    /** 标题位置 */
    private final TitlePosition titlePosition;

    /** 标题对齐方式 */
    private final TitleAlignment titleAlignment;

    /**
     * 根据受理局标识获取格式枚举
     *
     * @param jurisdiction 受理局标识
     * @return 格式枚举
     */
    public static JurisdictionFigureFormatEnum fromJurisdiction(String jurisdiction) {
        if (jurisdiction == null) {
            return DEFAULT;
        }

        String lowerJurisdiction = jurisdiction.toLowerCase();
        for (JurisdictionFigureFormatEnum format : values()) {
            if (format.getJurisdiction().equalsIgnoreCase(lowerJurisdiction)) {
                return format;
            }
        }
        return DEFAULT;
    }

    /**
     * 生成图片标题
     *
     * @param figureIndex 图片索引（从1开始）
     * @return 格式化的标题
     */
    public String generateTitle(int figureIndex) {
        return String.format(titleFormat, figureIndex);
    }

    /**
     * 图片对齐方式枚举
     */
    public enum FigureAlignment {
        /** 居中 */
        CENTER,
        /** 靠左 */
        LEFT,
        /** 靠右 */
        RIGHT
    }

    /**
     * 标题位置枚举
     */
    public enum TitlePosition {
        /** 在图片上方 */
        ABOVE,
        /** 在图片下方 */
        BELOW
    }

    /**
     * 标题对齐方式枚举
     */
    public enum TitleAlignment {
        /** 居中 */
        CENTER,
        /** 靠左 */
        LEFT,
        /** 靠右 */
        RIGHT
    }
} 
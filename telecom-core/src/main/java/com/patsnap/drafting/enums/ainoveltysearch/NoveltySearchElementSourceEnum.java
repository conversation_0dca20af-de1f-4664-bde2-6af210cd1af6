package com.patsnap.drafting.enums.ainoveltysearch;

import lombok.Getter;

/**
 * 检索要素来源
 */
@Getter
public enum NoveltySearchElementSourceEnum {

    MANUAL("manual", "手动输入"),
    KEYWORDS_EXTRACT("keywords_extract", "要素提取"),
    ;


    private final String value;
    private final String explain;

    NoveltySearchElementSourceEnum(String value, String explain) {
        this.value = value;
        this.explain = explain;
    }
}

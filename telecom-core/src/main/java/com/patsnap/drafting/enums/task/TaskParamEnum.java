package com.patsnap.drafting.enums.task;

import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.patsnap.drafting.repository.aitask.entity.AnalyticsAiTaskHistoryPO;
import lombok.Getter;

/**
 * 任务表参数枚举
 */
@Getter
public enum TaskParamEnum {
    /**
     * 标题
     */
    TITLE("title", AnalyticsAiTaskHistoryPO::getTitle),
    /**
     * 任务状态
     */
    ASYNC_STATUS("async_status", AnalyticsAiTaskHistoryPO::getAsyncStatus),
    /**
     * 描述
     */
    DESC("desc", AnalyticsAiTaskHistoryPO::getDesc);

    TaskParamEnum(String value, SFunction<AnalyticsAiTaskHistoryPO, String> tableFieldName) {
        this.value = value;
        this.tableFieldName = tableFieldName;
    }

    private final String value;
    private final SFunction<AnalyticsAiTaskHistoryPO, String> tableFieldName;
    
    public static TaskParamEnum fromValue(String value) {
        for (TaskParamEnum taskParamEnum : TaskParamEnum.values()) {
            if (taskParamEnum.getValue().equals(value)) {
                return taskParamEnum;
            }
        }
        return null;
    }
}

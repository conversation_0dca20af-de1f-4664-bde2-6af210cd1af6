package com.patsnap.drafting.enums.ainoveltysearch;

import lombok.Getter;

/**
 * 查新专利类型
 */
@Getter
public enum NoveltySearchPatentTypeEnum {

    X("X", "特征全匹配"),
    Y("Y", "专利组特征全匹配"),
    A("A", "部分匹配")
    ;


    private final String value;
    private final String explain;

    NoveltySearchPatentTypeEnum(String value, String explain) {
        this.value = value;
        this.explain = explain;
    }
}

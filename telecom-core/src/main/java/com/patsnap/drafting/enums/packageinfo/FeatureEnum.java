package com.patsnap.drafting.enums.packageinfo;

import com.google.common.collect.Lists;
import lombok.Getter;
import org.apache.commons.collections.CollectionUtils;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/11/13 上午10:15
 */
@Getter
public enum FeatureEnum {
    COMMON_FEATURE("313d"),

    IP_FREE_PACKAGE("3ppr"),

    IP_TRIAL_PACKAGE("3pph"),

    IP_COMMERCIAL_PACKAGE("3maq"),

    MATERIAL_TRIAL_PACKAGE("3ffn"),

    MATERIAL_COMMERCIAL_PACKAGE("3s0w"),

    AIR_FREE_PACKAGE("3m66"),

    AIR_TRIAL_PACKAGE("3m6w"),

    AIR_COMMERCIAL_PACKAGE("3f95"),

    INSIGHTS_TRIAL_PACKAGE("3fnd"),

    INSIGHTS_COMMERCIAL_PACKAGE("3sjm"),

    <PERSON><PERSON><PERSON><PERSON><PERSON>("7qq0"),

    SEARCH_RESULT_PAGE("3py4", FeaturePayTypeEnum.SEARCH, FeatureBusinessTypeEnum.SEARCH),

    DESIGN_SEARCH("3pw3"),

    PATENT_VIEW("3p8e", FeatureBusinessTypeEnum.PATENT_VIEW),

    PAPER_VIEW("3cdj", FeatureBusinessTypeEnum.PAPER_VIEW),

    COMPANY_PROFILE("3cfz"),

    COPILOT("svtu", FeatureBusinessTypeEnum.COPILOT),

    AI_CHAT("3135"),

    AI_DISCLOSURE("36x9"),
    
    AI_PATENT_DISCLOSURE("1wfu"),
    
    AI_TRANSLATION("1jt0"),
    
    AI_SPECIFICATION("1jby"),

    MINI_PROGRAM("3pw0"),

    ADVANCED_SEARCH("3ceb"),

    BATCH_SAVE("30z8"),

    EXPORT("3152"),

    NOVELTY_CHECK("3g14", FeaturePayTypeEnum.NOVELTY_CHECK),

    ROADMAP("3itb"),

    SEEK("3pr9", FeatureBusinessTypeEnum.SEEK),

    MIND("3cbn"),

    FMEA("38pp"),

    RESEARCH("31gf", FeatureBusinessTypeEnum.RESEARCH),

    MONITOR("3cek", FeaturePayTypeEnum.NEWS_LETTER),

    PDF_DOWNLOAD("3gz8"),

    STRUCTURE_SEARCH("3aco"),

    SUBSTANCE_SEARCH("3avj"),

    PROPERTY_SEARCH("3azo"),

    METAL_SEARCH("33r3"),

    WORKFLOW_SEARCH("3a1e"),

    MATERIAL_MIND("3apt"),

    INSIGHTS_COMPANY_SEARCH("3i7g"),

    INSIGHTS_COMPETITIVE_LANDSCAPE("3ad3"),

    INSIGHTS_MARKETS_ANALYSIS("3i32"),

    WORKFLOW_ASSISTANT("3nq4");

    /**
     * 首页IP场景展示的功能列表
     */
    public static List<FeatureEnum> IP_MODULE_DISPLAY_FEATURES = Arrays.asList(NOVELTY_CHECK, AI_DISCLOSURE, ROADMAP,
            AI_PATENT_DISCLOSURE, AI_TRANSLATION, AI_SPECIFICATION);

    /**
     * 首页材料展示功能列表
     */
    public static List<FeatureEnum> MATERIAL_MODULE_DISPLAY_FEATURES = Arrays.asList(STRUCTURE_SEARCH,
            SUBSTANCE_SEARCH, PROPERTY_SEARCH, METAL_SEARCH, WORKFLOW_SEARCH, MATERIAL_MIND);

    /**
     * 首页AIR场景展示的功能列表
     */
    public static List<FeatureEnum> AIR_MODULE_DISPLAY_FEATURES = Arrays.asList(RESEARCH, MIND, FMEA);

    /**
     * 首页Discovery展示的功能列表
     */
    public static List<FeatureEnum> INSIGHTS_MODULE_DISPLAY_FEATURES = Arrays.asList(INSIGHTS_COMPANY_SEARCH,
            INSIGHTS_COMPETITIVE_LANDSCAPE, INSIGHTS_MARKETS_ANALYSIS);

    private final String featureId;

    private FeatureBusinessTypeEnum featureBusinessTypeEnum;

    private FeaturePayTypeEnum featurePayTypeEnum;

    FeatureEnum(String featureId) {
        this.featureId = featureId;
    }

    FeatureEnum(String featureId, FeatureBusinessTypeEnum featureBusinessTypeEnum) {
        this.featureId = featureId;
        this.featureBusinessTypeEnum = featureBusinessTypeEnum;
    }

    FeatureEnum(String featureId, FeaturePayTypeEnum featurePayTypeEnum) {
        this.featureId = featureId;
        this.featurePayTypeEnum = featurePayTypeEnum;
    }

    FeatureEnum(String featureId, FeaturePayTypeEnum featurePayTypeEnum,
                FeatureBusinessTypeEnum featureBusinessTypeEnum) {
        this.featureId = featureId;
        this.featurePayTypeEnum = featurePayTypeEnum;
        this.featureBusinessTypeEnum = featureBusinessTypeEnum;
    }

    public static List<String> getFeatureIds(List<FeatureEnum> featureEnums) {
        if (CollectionUtils.isEmpty(featureEnums)) {
            return Lists.newArrayList();
        }
        return featureEnums.stream().map(FeatureEnum::getFeatureId).collect(Collectors.toList());
    }
}
package com.patsnap.drafting.enums.task;

import com.patsnap.drafting.client.model.NoveltySearchFeatureExtractResDTO;
import com.patsnap.drafting.client.model.NoveltySearchSummaryResDTO;
import com.patsnap.drafting.client.model.ainovelty.AiNoveltySearchResponse;
import com.patsnap.drafting.exception.BizException;
import com.patsnap.drafting.exception.errorcode.ContentErrorCodeEnum;
import com.patsnap.drafting.model.aispecification.*;
import com.patsnap.drafting.request.ainoveltysearch.AiSearchResultDTO;
import com.patsnap.drafting.request.ainoveltysearch.NoveltySearchElementResDTO;
import com.patsnap.drafting.response.aidisclosure.DisclosureSettingResDTO;
import com.patsnap.drafting.response.aiftosearch.FtoSearchReportTitleResponseDTO;
import com.patsnap.drafting.response.ainoveltysearch.FeatureComparisonResponseDTO;
import com.patsnap.drafting.response.ainoveltysearch.FeatureExactionResponseDTO;
import com.patsnap.drafting.response.ainoveltysearch.NoveltySearchComputeDTO;
import com.patsnap.drafting.response.ainoveltysearch.NoveltySearchReportTitleResponseDTO;
import com.patsnap.drafting.response.aitranslation.TranslationTermDTO;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

import static com.patsnap.drafting.enums.task.TaskContentDirectionEnum.*;

/**
 * AI任务内容类型枚举
 */
@Getter
@AllArgsConstructor
public enum AiTaskContentTypeEnum {
    // 通用任务内容
    USER_INPUT("user_input", "用户输入", INPUT, String.class, 0),
    TITLE("title", "标题", OUTPUT, String.class, 50),

    // AI 翻译
    TRANSLATION_SOURCE_LANG("source_lang", "原文语言", CONFIG, String.class, 0),
    TRANSLATION_TARGET_LANG("target_lang", "译文语言", CONFIG, String.class, 0),
    TRANSLATION_TECH_TOPIC("tech_topic", "技术主题", OUTPUT, String.class, 0),
    TRANSLATION_SUGGESTED_TERM("suggested_term", "AI建议术语", OUTPUT, TranslationTermDTO.class, 0),
    TRANSLATION_CUSTOM_TERM("custom_term", "用户自定义术语", CONFIG, TranslationTermDTO.class, 0),
    TRANSLATION_REWRITE("translation_rewrite", "重新翻译", CONFIG, String.class, 50),
    TRANSLATION_RESULT("translation_result", "翻译结果", OUTPUT, List.class, 1800),

    // AI 交底书
    TYPE_SELECT("type_select", "输入内容的类型", OUTPUT, String.class, 0),
    SETTING("setting", "设置", OUTPUT, DisclosureSettingResDTO.class, 200),
    TECH_FIELD("tech_field", "技术领域", OUTPUT, String.class, 50),
    BACKGROUND("background", "背景技术描述", OUTPUT, String.class, 100),
    REFERENCE("reference", "引用文献介绍", OUTPUT, String.class, 100),
    DRAWBACKS("drawbacks", "现有技术的缺点", OUTPUT, String.class, 100),
    TECH_PROBLEM("tech_problem", "问题描述", OUTPUT, String.class, 100),
    TECH_EFFECT("tech_effect", "技术效果", OUTPUT, String.class, 100),
    TECH_MEANS("tech_means", "技术方案", OUTPUT, String.class, 150),
    EMBODIMENT("embodiment", "实施例", OUTPUT, String.class, 100),

    // AI 说明书
    INITIALIZATION("initialization", "用户输入", INPUT, SpecificationInitializationBO.class, 0),
    CLASSIFICATION("classification", "分类号", OUTPUT, List.class, 0),
    CATEGORY("category", "类型", OUTPUT, String.class, 50),
    TECH_WRAPPER("tech_wrapper", "技术三要素", OUTPUT, SpecificationTechWrapperBO.class, 200),
    CLAIM_FORMAT("claim_format", "权利要求格式化", OUTPUT, String.class, 0),
    FEATURE_TREE("feature_tree", "特征树", OUTPUT, ClaimFeatureContentBO.class, 300),
    FIGURE_CONFIG("figure_config", "用户输入的附图列表", CONFIG, FigureContentBO.class, 0),
    FIGURE_CONFIRM("figure_confirm", "图片提交确认", CONFIG,  String.class, 0),
    FIGURES("figures", "附图列表", CONFIG, FigureContentBO.class, 0),
    ABSTRACT_FIGURE("abstract_figure", "摘要附图", CONFIG, FigureContentBO.class, 0),
    FIGURES_PRE_INFO("figures_pre_info", "附图说明先决信息", CONFIG, String.class, 0),
    FIGURES_DESCRIPTION("figures_description", "附图说明", OUTPUT, String.class, 0),
    TERMS("terms", "术语列表", OUTPUT, TermContentBO.class, 0),
    SPECIFICATION_TITLE("title", "标题", OUTPUT, String.class, 50),
    SPECIFICATION_TECH_FIELD("tech_field", "技术领域", OUTPUT, String.class, 50),
    SPECIFICATION_BACKGROUND("background", "背景技术", OUTPUT, String.class, 100),
    SUMMARY("summary", "发明内容", OUTPUT, String.class, 200),
    SPECIFICATION_EMBODIMENT("embodiment", "实施例", OUTPUT, String.class, 150),
    ABSTRACT("abstract", "摘要", OUTPUT, String.class, 100),
    SPECIFICATION_EMBODIMENT_KEYWORD_EXPAND("embodiment_keyword_expand", "实施例关键词扩展", OUTPUT, String.class, 50),
    SPECIFICATION_EMBODIMENT_ADD("embodiment_add", "扩展实施例", OUTPUT, String.class, 50),
    SPECIFICATION_EMBODIMENT_EXTRACT("embodiment_extract", "抽取实施例", OUTPUT, SpecificationDisclosureExtractEmbodimentBO.class, 0),
    SPECIFICATION_EMBODIMENT_OUTLINE("embodiment_outline", "实施例大纲", TaskContentDirectionEnum.OUTPUT, SpecificationEmbodimentOutlineBO.class, 0),
    SPECIFICATION_EMBODIMENT_CONTENT("embodiment_content", "生成实施例", TaskContentDirectionEnum.OUTPUT, SpecificationEmbodimentGenerateBO.class, 0),
            
    // AI 查新
    NOVELTY_SEARCH_INPUT_LANG("novelty_search_input_lang", "输入语言", OUTPUT, String.class, 0),
    NOVELTY_SEARCH_PBD_START("pbd_start", "公开日开始时间", CONFIG, String.class, 0),
    NOVELTY_SEARCH_PBD_END("pbd_end", "公开日结束时间", CONFIG, String.class, 0),
    NOVELTY_SEARCH_INPUT_SUMMARY("novelty_search_input_summary", "文本总结", OUTPUT, NoveltySearchSummaryResDTO.Data.class, 0),
    NOVELTY_SEARCH_TECH_FEATURE("novelty_search_tech_feature", "技术特征", OUTPUT, NoveltySearchFeatureExtractResDTO.class, 0),
    NOVELTY_SEARCH_RETRIEVAL_ELEMENTS("novelty_search_retrieval_elements", "检索要素", OUTPUT, NoveltySearchElementResDTO.class, 0),
    NOVELTY_SEARCH_TECH_FEATURE_COMPARISON("novelty_search_tech_feature_comparison", "用户输入的专利特征提取", OUTPUT, FeatureComparisonResponseDTO.class, 0),
    NOVELTY_SEARCH_TASK_ID("novelty_search_task_id", "查新搜索任务ID", OUTPUT, String.class, 0),
    NOVELTY_SEARCH_AGENT_RESULT("novelty_search_agent_result", "agent查询结果", OUTPUT, AiNoveltySearchResponse.class, 0),
    NOVELTY_SEARCH_PATENT_CONFIRM_FEATURE("novelty_search_patent_confirm_feature", "专利初筛确认特征", OUTPUT, List.class, 0),
    NOVELTY_SEARCH_FEATURE_CONFIRM("novelty_search_feature_confirm", "文献确认", OUTPUT, List.class, 0),
    NOVELTY_SEARCH_REPORT_TITLE("novelty_search_report_title", "报告-标题", OUTPUT, NoveltySearchReportTitleResponseDTO.class, 0),
    NOVELTY_SEARCH_REPORT_COMPARATIVE_LITERATURE("novelty_search_report_comparative_literature", "报告-对比文献公开情况", OUTPUT, NoveltySearchComputeDTO.class, 0),
    NOVELTY_SEARCH_REPORT_REVIEW_OF_NOVELTY("novelty_search_report_review_of_novelty", "报告-新颖性评述", OUTPUT, NoveltySearchComputeDTO.class, 0),
    NOVELTY_SEARCH_REPORT_REVIEW_OF_CREATIVE("novelty_search_report_review_of_creative", "报告-创造性评述", OUTPUT, NoveltySearchComputeDTO.class, 0),


    // AI fto侵权查询
    FTO_SEARCH_INPUT_LANG("fto_search_input_lang", "输入语言", OUTPUT, String.class, 0),
    FTO_SEARCH_RECEIVING_OFFICES("receiving_offices", "受理局列表", CONFIG, List.class, 0),
    FTO_SEARCH_EXCLUDE_COMPANY("exclude_company", "排除的公司列表", CONFIG, List.class, 0),
    FTO_SEARCH_INCLUDE_COMPANY("include_company", "包含的公司列表", CONFIG, List.class, 0),
    FTO_SEARCH_LEGAL_STATUS("legal_status", "法律状态列表", CONFIG, List.class, 0),
    FTO_SEARCH_APD_START("apd_start", "申请日开始时间", CONFIG, String.class, 0),
    FTO_SEARCH_APD_END("apd_end", "申请日结束时间", CONFIG, String.class, 0),
    FTO_SEARCH_INPUT_SUMMARY("fto_search_input_summary", "文本总结", OUTPUT, String.class, 0),
    FTO_SEARCH_TECH_FEATURE("fto_search_tech_feature", "技术特征", OUTPUT, FeatureExactionResponseDTO.class, 0),
    FTO_SEARCH_TECH_FEATURE_COMPARISON_SCORE("fto_search_tech_feature_comparison_score", "用户输入的专利特征提取", OUTPUT, FeatureComparisonResponseDTO.class, 0),
    FTO_SEARCH_TASK_ID("novelty_search_task_id", "查新搜索任务ID", OUTPUT, String.class, 0),
    FTO_SEARCH_AGENT_RESULT("fto_search_agent_result", "agent查询结果", OUTPUT, AiSearchResultDTO.class, 0),
    FTO_SEARCH_FEATURE_CONFIRM("fto_search_feature_confirm", "文献确认", OUTPUT, List.class, 0),
    FTO_SEARCH_REPORT_TITLE("fto_search_report_title", "报告-标题", OUTPUT, FtoSearchReportTitleResponseDTO.class, 0),

    ;
    private final String type;
    private final String desc;
    /**
     * 用户输入 或 内容输出
     */
    private final TaskContentDirectionEnum direction;
    /**
     * 内容所对应的存储对象
     */
    private final Class<?> contentClass;
    /**
     * 内容所消耗的积分
     */
    private final Integer credit;

    public static AiTaskContentTypeEnum fromType(String type) {
        return Arrays.stream(values())
                .filter(e -> e.type.equals(type))
                .findFirst()
                .orElseThrow(() -> new BizException(ContentErrorCodeEnum.CONTENT_TYPE_INVALID));
    }

}

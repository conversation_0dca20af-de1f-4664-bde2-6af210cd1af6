package com.patsnap.drafting.enums.task;

import lombok.Getter;

import java.util.List;

/**
 * AI 翻译步骤类型枚举
 */
@Getter
public enum AiTranslationStepEnum implements TaskStep {
    
    // AI 翻译
    //TRANSLATION_INPUT("INPUT", List.of(AiTaskContentTypeEnum.USER_INPUT.getType())),
    
    TRANSLATION_RESULT("RESULT", List.of(AiTaskContentTypeEnum.USER_INPUT.getType(),
            AiTaskContentTypeEnum.TRANSLATION_RESULT.getType())),
    
    ;
    
    private final String step;
    private final List<String> subStepList;
    
    AiTranslationStepEnum(String step, List<String> subStepList) {
        this.step = step;
        this.subStepList = subStepList;
    }
}
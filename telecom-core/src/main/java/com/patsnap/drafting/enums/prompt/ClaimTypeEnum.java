package com.patsnap.drafting.enums.prompt;

import com.patsnap.drafting.constants.Constant;

import org.apache.commons.lang3.StringUtils;

import jodd.util.StringUtil;

public enum ClaimTypeEnum {

    DEP_CLAIM("从属权利要求", "dependent claim"),
    INDEP_CLAIM("独立权利要求", "independent claim");

    private String cnDesc;

    private String enDesc;

    ClaimTypeEnum(String cnDesc, String enDesc) {
        this.cnDesc = cnDesc;
        this.enDesc = enDesc;
    }


    public String getDesc(String lang) {
        if (StringUtils.equalsIgnoreCase(Constant.CN, lang)) {
            return this.cnDesc;
        }
        return this.enDesc;
    }

    public String getCnDesc() {
        return cnDesc;
    }

    public void setCnDesc(String cnDesc) {
        this.cnDesc = cnDesc;
    }

    public String getEnDesc() {
        return enDesc;
    }

    public void setEnDesc(String enDesc) {
        this.enDesc = enDesc;
    }
}


package com.patsnap.drafting.enums.ainoveltysearch;

import lombok.Getter;

/**
 * 内容生成的操作类型
 */
@Getter
public enum NoveltySearchResultEnum {
    
    SUCCESS("success", "校验成功"),
    EXCEED_INPUT_LENGTH("exceed_input_length", "超出限制字数"),
    LANGUAGE_ERROR("language_error", "语言错误"),
    ;
    
    
    private final String value;
    private final String explain;
    
    NoveltySearchResultEnum(String value, String explain) {
        this.value = value;
        this.explain = explain;
    }
}

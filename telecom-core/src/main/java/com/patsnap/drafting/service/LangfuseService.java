package com.patsnap.drafting.service;

import com.patsnap.drafting.client.LangfuseJavaSdkClient;
import com.patsnap.drafting.observability.langfuse.tracer.AiOperationContext;
import com.patsnap.drafting.observability.langfuse.tracer.AiOperationResult;
import com.patsnap.drafting.observability.langfuse.tracer.LangfuseTracer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Langfuse 综合服务类
 * 整合 OpenTelemetry 追踪和 Java SDK API 访问功能
 *
 * <AUTHOR> Assistant
 * @date 2025/01/31
 */
@Slf4j
@Service
@RequiredArgsConstructor
@ConditionalOnProperty(prefix = "configs.com.patsnap.drafting.langfuse", name = "enabled", havingValue = "true")
public class LangfuseService {

    private final LangfuseJavaSdkClient langfuseSdkClient;
    private final LangfuseTracer langfuseTracer;

    /**
     * 获取所有提示词模板
     *
     * @return 提示词模板列表
     */
    public Optional<List<Map<String, Object>>> getPrompts() {
        return langfuseSdkClient.getPrompts();
    }

    /**
     * 根据名称获取特定提示词模板
     *
     * @param promptName 提示词名称
     * @param version 版本（可选）
     * @return 提示词模板
     */
    public Optional<Map<String, Object>> getPrompt(String promptName, String version) {
        return langfuseSdkClient.getPrompt(promptName, version);
    }

    /**
     * 获取追踪详情
     *
     * @param traceId 追踪ID
     * @return 追踪详情
     */
    public Optional<Map<String, Object>> getTrace(String traceId) {
        return langfuseSdkClient.getTrace(traceId);
    }

    /**
     * 手动创建追踪上下文
     *
     * @param operationName 操作名称
     * @param system 系统名称
     * @param operationType 操作类型
     * @param input 输入内容
     * @param model 模型名称
     * @param metadata 元数据
     * @param tags 标签
     * @return 追踪上下文
     */
    public AiOperationContext createTrace(String operationName, String system, String operationType,
                                          String input, String model, Map<String, Object> metadata,
                                          String... tags) {
        AiOperationContext.AiOperationContextBuilder builder = AiOperationContext.builder()
                .system(system)
                .operationType(operationType)
                .model(model)
                .prompt(input)
                .metadata(metadata);
        
        if (tags != null && tags.length > 0) {
            builder.tags(java.util.Arrays.asList(tags));
        }
        
        return builder.build();
    }

    /**
     * 执行带追踪的操作并记录结果
     *
     * @param operationName 操作名称
     * @param context 追踪上下文
     * @param operation 要执行的操作
     * @param <T> 返回类型
     * @return 操作结果
     */
    public <T> T executeWithContext(String operationName, AiOperationContext context, TracedOperation<T> operation) {
        try {
            return langfuseTracer.traceAiOperation(operationName, context, () -> {
                T result = operation.execute();
                // 如果结果是字符串，构建一个 AiOperationResult
                if (result instanceof String) {
                    @SuppressWarnings("unchecked")
                    T convertedResult = (T) AiOperationResult.builder()
                            .completion((String) result)
                            .success(true)
                            .build();
                    return convertedResult;
                }
                return result;
            });
        } catch (Exception e) {
            log.error("执行操作时发生错误: {}", operationName, e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 执行带追踪的操作
     * 这是一个便捷方法，自动处理追踪的开始和结束
     *
     * @param operationName 操作名称
     * @param system 系统名称
     * @param operationType 操作类型
     * @param input 输入内容
     * @param model 模型名称
     * @param operation 要执行的操作
     * @param <T> 返回类型
     * @return 操作结果
     */
    public <T> T executeWithTracing(String operationName, String system, String operationType,
                                    String input, String model, TracedOperation<T> operation) {
        AiOperationContext context = createTrace(operationName, system, operationType, input, model, null);
        return executeWithContext(operationName, context, operation);
    }

    /**
     * 检查 Langfuse 服务健康状态
     *
     * @return 是否健康
     */
    public boolean isHealthy() {
        return langfuseSdkClient.isHealthy();
    }

    /**
     * 追踪操作接口
     *
     * @param <T> 返回类型
     */
    @FunctionalInterface
    public interface TracedOperation<T> {
        T execute() throws Exception;
    }
}
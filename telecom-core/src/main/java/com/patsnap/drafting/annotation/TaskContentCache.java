package com.patsnap.drafting.annotation;

import com.patsnap.drafting.enums.task.AiTaskContentTypeEnum;
import com.patsnap.drafting.manager.content.logic.ContentCacheLogic;
import com.patsnap.drafting.manager.content.logic.CommonContentCacheLogic;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 任务内容缓存注解
 * 用于方法级别的内容缓存，通过taskId和contentType作为key
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface TaskContentCache {
    /**
     * 内容类型（固定值方式）
     * 当 contentTypeExpression 为空时,此值必须要有值
     */
    AiTaskContentTypeEnum contentType() default AiTaskContentTypeEnum.TITLE;

    /**
     * 内容类型的SpEL表达式（动态方式）
     * 可以直接使用方法参数名，如：#request.contentType
     * 当该值不为空时，优先使用表达式解析结果
     */
    String contentTypeExpression() default "";

    /**
     * 特殊执行逻辑的类
     */
    Class<? extends ContentCacheLogic> logicClass() default CommonContentCacheLogic.class;
} 
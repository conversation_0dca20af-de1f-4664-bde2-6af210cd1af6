package com.patsnap.drafting.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.util.concurrent.TimeUnit;

/**
 * 分布式锁注解
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface DistributedLock {
    
    /**
     * 锁的key前缀
     */
    String prefix() default "";
    
    /**
     * 锁的key，支持SpEL表达式
     * 可以直接使用方法参数名，如：#taskId
     */
    String key();
    
    /**
     * 等待锁的时间
     */
    long waitTime() default 3;
    
    /**
     * 时间单位
     */
    TimeUnit timeUnit() default TimeUnit.SECONDS;
    
    /**
     * 获取锁失败时的错误消息
     */
    String failMessage() default "操作太频繁，请稍后再试";
} 
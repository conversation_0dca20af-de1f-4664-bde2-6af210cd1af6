package com.patsnap.drafting.annotation;

import com.patsnap.drafting.enums.task.AiTaskContentTypeEnum;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 积分校验注解
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface CreditCheckLimit {
    /**
     * 内容类型（固定值方式）
     * 当 contentTypeExpression 为空时,此值必须要有值
     */
    AiTaskContentTypeEnum contentType() default AiTaskContentTypeEnum.TITLE;

    /**
     * 内容类型的SpEL表达式（动态方式）
     * 可以直接使用方法参数名，如：#request.contentType
     * 当该值不为空时，优先使用表达式解析结果
     */
    String contentTypeExpression() default "";
}
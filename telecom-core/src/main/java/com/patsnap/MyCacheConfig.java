package com.patsnap;


import java.util.HashMap;
import java.util.Map;

import org.redisson.api.RedissonClient;
import org.redisson.spring.cache.CacheConfig;
import org.redisson.spring.cache.RedissonSpringCacheManager;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@EnableCaching
public class MyCacheConfig {

    @Bean
    CacheManager cacheManager(RedissonClient redissonClient) {
        Map<String, CacheConfig> config = new HashMap<>();
        // 创建一个名称为"commonCache"的缓存，过期时间ttl为5分钟，同时最长空闲时maxIdleTime为3分钟。
        config.put("commonCache", new CacheConfig(5 * 60 * 1000, 3 * 60 * 1000));
        return new RedissonSpringCacheManager(redissonClient, config);
    }
}

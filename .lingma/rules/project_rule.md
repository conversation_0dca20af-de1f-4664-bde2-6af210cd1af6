# Telecom Analytics Project Context

## Project Structure
- telecom-api: API definitions and DTOs
- telecom-core: Core business logic and implementations
- telecom-parent: Parent project for dependency management
- telecom-service: Service implementations and configurations

## Key Packages and Classes

### com.patsnap.drafting
- annotation: Custom annotations (e.g., TaskContentCache, Distri<PERSON>Lock)
- aspect: Aspect classes for cross-cutting concerns
- client: API clients for external services
- config: Configuration classes
- constants: Constant definitions
- enums: Enumeration classes
- exception: Custom exceptions and error codes
- manager: Business logic managers
- model: Domain model classes
- repository: Data access layer
- util: Utility classes

## Main Functionalities
1. AI-assisted drafting
2. Patent analysis and disclosure
3. Task management and history tracking
4. Content caching and distributed locking
5. Credit check and limiting

## Key Components
- AiTaskManager: Manages AI-related tasks
- PatentDisclosureManager: Handles patent disclosure processes
- TaskContentManager: Manages task content
- AiHistoryManager: Handles AI task history

## Frameworks and Libraries
- Spring Boot: Main application framework
- MyBatis: ORM framework
- Retrofit: HTTP client for API calls
- Eureka: Service discovery

## Design Patterns and Architectural Concepts
- Microservices architecture (evidenced by Eureka usage)
- Aspect-Oriented Programming (AOP) for cross-cutting concerns
- Repository pattern for data access
- Manager pattern for business logic
- DTO pattern for data transfer
- Builder pattern (e.g., TaskContentBuilder)

## Notes for AI
- This project deals with telecom analytics and AI-assisted patent drafting
- It uses a multi-module structure with clear separation of concerns
- There's heavy use of managers for encapsulating business logic
- The project implements caching and distributed locking for performance and consistency
- Error handling is done through custom exceptions and error code enums
- The project seems to integrate with external services (e.g., patent API, OpenAI)

When working with this project, consider the microservices architecture and the separation of concerns between modules. Pay attention to the manager classes for business logic implementation and the repository classes for data access.

# 项目描述

## 项目概述
本项目是一个基于 AI 的专利撰写和翻译服务，提供专利文档的智能撰写、翻译、分析等功能。项目采用 Java Spring Boot 框架开发，使用响应式编程模型（WebFlux）。

## 技术栈
- Java 17
- Spring Boot
- Spring WebFlux
- MyBatis
- Redis
- PostgreSQL
- Maven

## 项目架构
项目采用多模块 Maven 结构，主要包含以下模块：
- telecom-api: 定义 DTO 和接口
- telecom-core: 核心业务逻辑
- telecom-service: Web 服务和控制器
- telecom-parent: 依赖管理

## 主要功能模块

### AI 撰写 (AI Drafting)
提供 AI 辅助专利撰写功能，包括：
- 专利说明书生成
- 专利权利要求书生成
- 专利公开内容生成
- 敏感词检查
- 专利引用管理

### AI 翻译 (AI Translation)
提供专业的专利文档翻译功能，包括：
- 文本语言检测
- 专利文档翻译
- 自定义术语表管理
- 异步翻译任务处理
- 翻译结果导出

### AI 规范 (AI Specification)
提供专利规范化处理功能，包括：
- 专利分类
- 专利内容规范化
- 实施例管理

### 历史记录 (AI History)
管理用户的操作历史和生成内容历史：
- 历史记录查询
- 历史内容详情

### 积分系统 (Credit System)
管理用户使用 AI 功能的积分：
- 积分查询
- 积分消费
- 积分变更

## 代码结构

### com.patsnap.drafting 包结构
- annotation: 自定义注解
- aspect: AOP 切面
- client: 外部服务客户端
- config: 配置类
- constants: 常量定义
- controller: REST 控制器
- enums: 枚举类型
- exception: 异常处理
- handler: 处理器
- listener: 事件监听器
- manager: 业务管理类
- model: 业务模型
- repository: 数据访问层
- request: 请求 DTO
- response: 响应 DTO
- startup: 启动相关
- transfer: 数据转换
- util: 工具类

## 开发规范
1. 使用 lombok 简化代码
2. 使用 swagger 注解进行 API 文档化
3. 使用 validation 注解进行参数校验
4. 遵循 RESTful API 设计规范
5. 使用响应式编程模型处理异步请求
6. 使用 CommonResponse 封装统一响应格式

## 数据流
1. 客户端请求 -> Controller
2. Controller -> Manager
3. Manager -> Service
4. Service -> Repository
5. Repository -> 数据库/外部服务

## 注意事项
1. 敏感词检查是必要的安全措施
2. 积分系统控制用户对 AI 功能的使用量
3. 异步任务处理需要考虑任务状态管理
4. 翻译和生成内容需要考虑专业术语的准确性
<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>telecom-parent</artifactId>
        <groupId>com.patsnap.analytics</groupId>
        <version>1.1-SNAPSHOT</version>
        <relativePath>../telecom-parent</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <version>1.1-SNAPSHOT</version>
    <artifactId>telecom-api</artifactId>
    <packaging>jar</packaging>
    <name>PatSnap :: Analytics :: Telecom :: API</name>

    <dependencies>

        <dependency>
            <groupId>com.patsnap.common</groupId>
            <artifactId>common-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.patsnap.core.common</groupId>
            <artifactId>common-core</artifactId>
        </dependency>

        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-annotations</artifactId>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.28</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>javax.servlet-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
        </dependency>

        <dependency>
            <groupId>javax.validation</groupId>
            <artifactId>validation-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>3.5.5</version>
        </dependency>

        <dependency>
            <groupId>com.patsnap.core.common</groupId>
            <artifactId>common-commonclient</artifactId>
        </dependency>

    </dependencies>
</project>
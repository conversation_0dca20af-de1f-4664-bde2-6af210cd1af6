package com.patsnap.drafting.response.imagesearch;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("专利信息项")
public class PatentMessagesItem{

	@ApiModelProperty("专利ID")
	@JsonProperty("patent_id")
	private String patentId;

	@ApiModelProperty("分数")
	@JsonProperty("score")
	private Double score;
	
	@ApiModelProperty("风险等级")
	@JsonProperty("risk_level")
	private String riskLevel;

	@ApiModelProperty("LOC匹配")
	@JsonProperty("loc_match")
	private int locMatch;

	@ApiModelProperty("申请日期")
	@JsonProperty("apdt")
	private Integer apdt;

	@ApiModelProperty("专利号")
	@JsonProperty("patent_pn")
	private String patentPn;

	@ApiModelProperty("公开日期")
	@JsonProperty("pbdt")
	private Integer pbdt;

	@ApiModelProperty("发明人")
	@JsonProperty("inventor")
	private String inventor;

	@ApiModelProperty("原始申请人")
	@JsonProperty("original_assignee")
	private String originalAssignee;

	@ApiModelProperty("标题")
	@JsonProperty("title")
	private String title;

	@ApiModelProperty("申请号")
	@JsonProperty("apno")
	private String apno;

	@ApiModelProperty("当前申请人")
	@JsonProperty("current_assignee")
	private String currentAssignee;

	@ApiModelProperty("图像URL")
	@JsonProperty("url")
	private String url;

	@ApiModelProperty("小尺寸图像URL(120px)")
	@JsonProperty("url_120")
	private String url120;
}
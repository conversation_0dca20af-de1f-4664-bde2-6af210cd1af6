package com.patsnap.drafting.response.aitranslation;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/07/29
 */
@ApiModel("AI翻译句子响应")
@Data
public class SentenceTransDTO {
    @ApiModelProperty("原始句子")
    private String original;
    @ApiModelProperty("翻译句子")
    private String translation;
    @ApiModelProperty("当前句子属于第几段落")
    private Integer paragraph;
}

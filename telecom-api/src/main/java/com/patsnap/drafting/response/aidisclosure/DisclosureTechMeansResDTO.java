package com.patsnap.drafting.response.aidisclosure;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/08/22
 */
@ApiModel("提取技术手段响应")
@Data
public class DisclosureTechMeansResDTO {

    @ApiModelProperty("改进主体")
    @JsonProperty("invention_subject")
    private List<String> inventionSubject;
    @ApiModelProperty("推荐的改进主体候选项")
    @JsonProperty("recommend_invention_subject")
    private List<String> recommendInventionSubject;
    @ApiModelProperty("应用领域")
    @JsonProperty("application_field")
    private List<String> applicationField;
    @ApiModelProperty("推荐的应用领域候选项")
    @JsonProperty("recommend_application_field")
    private List<String> recommendApplicationField;
    @ApiModelProperty("技术手段")
    @JsonProperty("tech_means")
    private String techMeans;
    @ApiModelProperty("技术效果")
    @JsonProperty("tech_effect")
    private String techEffect;
    @ApiModelProperty("技术背景")
    @JsonProperty("tech_background")
    private String techBackground;
    @ApiModelProperty("技术问题")
    @JsonProperty("tech_problem")
    private String techProblem;
    @ApiModelProperty("实施例")
    @JsonProperty("embodiment")
    private String embodiment;
    @ApiModelProperty("将技术背景,技术问题,实施例拼接到一起")
    @JsonProperty("other")
    private String other;
}

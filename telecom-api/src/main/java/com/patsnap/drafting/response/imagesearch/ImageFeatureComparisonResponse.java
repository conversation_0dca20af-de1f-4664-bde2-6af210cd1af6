package com.patsnap.drafting.response.imagesearch;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("图像特征对比响应")
public class ImageFeatureComparisonResponse{
	@ApiModelProperty("用户图像")
	@JsonProperty("user_image")
	private String userImage;

	@ApiModelProperty("对比项列表")
	@JsonProperty("comparisons")
	private List<ComparisonsItem> comparisons;
}
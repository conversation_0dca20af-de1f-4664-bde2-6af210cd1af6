package com.patsnap.drafting.response.techreport;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 技术预览响应
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ApiModel("技术预览响应")
public class TechReportPreviewResDTO {

    @ApiModelProperty("item ID")
    @JsonProperty("item_id")
    private String itemId;

    @ApiModelProperty("item 类型")
    @JsonProperty("item_type")
    private String itemType;

    @ApiModelProperty("专利信息")
    @JsonProperty("patent_info")
    private PatentInfo patentInfo;

    @ApiModelProperty("专利信息列表")
    @JsonProperty("patent_info_list")
    private List<PatentInfo> patentInfoList;

    /**
     * 专利信息
     */
    @Data
    @ApiModel("专利信息")
    public static class PatentInfo {

        @ApiModelProperty("patent ID")
        @JsonProperty("patent_id")
        private String patentId;

        @ApiModelProperty("专利摘要")
        @JsonProperty("abst")
        private String abst;

        @ApiModelProperty("专利标题")
        @JsonProperty("title")
        private String title;

        @ApiModelProperty("相关度")
        @JsonProperty("relevance")
        private RelevanceInfo relevanceInfo;
        
        @ApiModelProperty("专利号")
        @JsonProperty("pn")
        private String pn;
        
        @ApiModelProperty("发明点")
        @JsonProperty("llm_innovation_list")
        private List<String> inventions;
        
        @ApiModelProperty("功效")
        @JsonProperty("llm_benefit")
        private String benefits;
        
        @ApiModelProperty("问题")
        @JsonProperty("llm_tech_problem")
        private String problem;

        @ApiModelProperty("技术标题")
        @JsonProperty("llm_technical_title")
        private String technicalTitle;
        
        @ApiModelProperty("专利技术主题")
        @JsonProperty("tech_field_and_topic_list")
        private List<String> techTopics;
        
        @ApiModelProperty("申请人")
        @JsonProperty("ancs")
        private ANCS ancs;
        
        /**
         * 相关度信息
         */
        @Data
        @ApiModel("相关度信息")
        public static class RelevanceInfo {
            @ApiModelProperty("相关度得分")
            @JsonProperty("score")
            private Integer score;
            
            @ApiModelProperty("相关度说明")
            @JsonProperty("description")
            private String description;
        }
         /**
         * ANCS信息
         */
        @Data
        @ApiModel("ANCS")
        public static class ANCS {
            @ApiModelProperty("申请人")
            @JsonProperty("ans_name")
            private String ansName;

             @ApiModelProperty("entity_id")
             @JsonProperty("entity_id")
             private String entityId;

             @ApiModelProperty("实体类型")
             @JsonProperty("entity_type")
             private String entityType;

             @ApiModelProperty("logo")
             @JsonProperty("logo")
             private String logo;
        }


    }
} 
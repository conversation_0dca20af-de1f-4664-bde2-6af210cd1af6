package com.patsnap.drafting.response.ainoveltysearch;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR> chendepeng
 * @Date 2025/3/17 14:55
 */
@Data
@Accessors(chain = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class FeatureComparisonResponseDTO {

    @ApiModelProperty("最终结果")
    @JsonProperty("final_res")
    private List<FeatureComparisonFinalResItem> finalRes;

    @ApiModelProperty("阈值")
    @JsonProperty("threshold")
    private int threshold;
}

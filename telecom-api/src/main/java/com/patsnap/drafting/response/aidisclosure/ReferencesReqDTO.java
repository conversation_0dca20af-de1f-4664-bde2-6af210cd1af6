package com.patsnap.drafting.response.aidisclosure;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;

@ApiModel("交底书引用列表获取")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReferencesReqDTO {
    
    @NotNull
    @ApiModelProperty("PN 号列表")
    private List<String> pns;
}

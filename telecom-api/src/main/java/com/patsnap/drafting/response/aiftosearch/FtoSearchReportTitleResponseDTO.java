package com.patsnap.drafting.response.aiftosearch;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Map;

/**
 * <AUTHOR> chendepeng
 * @Date 2025/4/3 15:44
 */
@Data
@Accessors(chain = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class FtoSearchReportTitleResponseDTO {

    @ApiModelProperty("标题")
    private String title;

    @ApiModelProperty("创建人")
    private String creator;

    @JsonProperty("generated_time")
    @ApiModelProperty("报告生成时间")
    private Long generatedTime;

    @JsonProperty("header")
    @ApiModelProperty("副标题")
    private Map<String, String> header;

    @JsonProperty("search_scope")
    @ApiModelProperty("检索范围")
    private Map<String, String> searchScope;
}

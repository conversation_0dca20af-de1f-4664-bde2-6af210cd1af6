package com.patsnap.drafting.response.techreport;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Map;

/**
 * 技术简报详情响应
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ApiModel("技术简报详情响应")
public class TechReportDetailResDTO {

    @ApiModelProperty(value = "监控信息")
    private MonitorInfo monitorInfo;

    @ApiModelProperty(value = "摘要")
    private Summary summary;

    @ApiModelProperty(value = "子领域")
    private List<Subfield> subfields;

    @ApiModelProperty(value = "专利")
    private Patents patents;

    @ApiModelProperty(value = "技术分布")
    private TechnologyDistribution technologyDistribution;

    @ApiModelProperty(value = "首次公开技术")
    private List<PatentItem> firstDisclosures;

    @ApiModelProperty(value = "新闻")
    private List<NewsItem> news;

    @Data
    @Accessors(chain = true)
    public static class MonitorInfo {
        
        @ApiModelProperty(value = "ID")
        private String id;
        
        @ApiModelProperty(value = "名称")
        private String name;
        
        @ApiModelProperty(value = "创建时间")
        private String createTime;
        
        @ApiModelProperty(value = "更新时间")
        private String updateTime;
        
        @ApiModelProperty(value = "检索式")
        private SearchQuery searchQuery;
        
        @ApiModelProperty(value = "提取的特征")
        private Map<String, Object> extractedFeatures;
    }

    @Data
    @Accessors(chain = true)
    public static class SearchQuery {
        
        @ApiModelProperty(value = "专利检索式")
        private String patent;
        
        @ApiModelProperty(value = "新闻检索式")
        private String news;
        
        @ApiModelProperty(value = "论文检索式")
        private String paper;
    }

    @Data
    @Accessors(chain = true)
    public static class Summary {
        
        @ApiModelProperty(value = "文本")
        private String text;
        
        @ApiModelProperty(value = "重点概述")
        private List<String> keyHighlights;
    }

    @Data
    @Accessors(chain = true)
    public static class Subfield {
        
        @ApiModelProperty(value = "名称")
        private String name;
        
        @ApiModelProperty(value = "类型", notes = "company 或 technology")
        private String type;
        
        @ApiModelProperty(value = "专利数量")
        private Integer patentCount;
    }

    @Data
    @Accessors(chain = true)
    public static class Patents {
        
        @ApiModelProperty(value = "高相关", notes = "相关度4-5分")
        private List<PatentItem> highRelevance;
        
        @ApiModelProperty(value = "低相关", notes = "相关度2-3分")
        private List<PatentItem> lowRelevance;
    }

    @Data
    @Accessors(chain = true)
    public static class PatentItem {
        
        @ApiModelProperty(value = "ID")
        private String id;
        
        @ApiModelProperty(value = "标题")
        private String title;
        
        @ApiModelProperty(value = "公开号")
        private String publicationNumber;
        
        @ApiModelProperty(value = "申请人")
        private String applicant;
        
        @ApiModelProperty(value = "公开日期")
        private String publicationDate;
        
        @ApiModelProperty(value = "摘要")
        private String abstract_;
        
        @ApiModelProperty(value = "相关度评分", notes = "0-5分")
        private Integer relevanceScore;
        
        @ApiModelProperty(value = "子领域ID")
        private String subfieldId;
    }

    @Data
    @Accessors(chain = true)
    public static class TechnologyDistribution {
        
        @ApiModelProperty(value = "主题")
        private List<TopicWeight> topics;
        
        @ApiModelProperty(value = "功效")
        private List<BenefitCount> benefits;
    }

    @Data
    @Accessors(chain = true)
    public static class TopicWeight {
        
        @ApiModelProperty(value = "名称")
        private String name;
        
        @ApiModelProperty(value = "权重")
        private Integer weight;
    }

    @Data
    @Accessors(chain = true)
    public static class BenefitCount {
        
        @ApiModelProperty(value = "名称")
        private String name;
        
        @ApiModelProperty(value = "数量")
        private Integer count;
    }

    @Data
    @Accessors(chain = true)
    public static class NewsItem {
        
        @ApiModelProperty(value = "ID")
        private String id;
        
        @ApiModelProperty(value = "标题")
        private String title;
        
        @ApiModelProperty(value = "来源")
        private String source;
        
        @ApiModelProperty(value = "发布日期")
        private String publishDate;
        
        @ApiModelProperty(value = "摘要")
        private String summary;
    }
} 
package com.patsnap.drafting.response.init;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

@ApiModel("AI 撰写权限信息")
@Data
@Builder
public class DraftingPermission {
	
	@JsonProperty("ai_translation")
	@ApiModelProperty("AI 翻译权限")
	private boolean aiTranslation;
	
	@JsonProperty("ai_disclosure")
	@ApiModelProperty("AI 交底书权限")
	private boolean aiDisclosure;
	
	@ApiModelProperty("AI 说明书权限(CNIPA)")
	@JsonProperty("ai_specification_cn")
	private boolean aiSpecificationCn;

	@ApiModelProperty("AI 说明书权限(USPTO)")
	@JsonProperty("ai_specification_us")
	private boolean aiSpecificationUs;
	
	@ApiModelProperty("AI 说明书权限(EPO)")
	@JsonProperty("ai_specification_eu")
	private boolean aiSpecificationEu;

	@ApiModelProperty("AI 专利查新权限")
	@JsonProperty("ai_novelty_search")
	private boolean aiNoveltySearch;

	@ApiModelProperty("AI FTO查新权限")
	@JsonProperty("ai_fto_search")
	private boolean aiFtoSearch;

	@ApiModelProperty("AI 说明书权限(统一)")
	@JsonProperty("ai_specification_unified")
	private boolean aiSpecificationUnified;
}

package com.patsnap.drafting.response.aidisclosure;

import com.patsnap.drafting.annotation.HistoryField;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnore;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/08/23
 */
@Data
public class DisclosureSettingResDTO extends DisclosureTechMeansResDTO {

    @ApiModelProperty("语义检索后top20专利号")
    @HistoryField("patent_ids")
    private List<String> patentIds;
}

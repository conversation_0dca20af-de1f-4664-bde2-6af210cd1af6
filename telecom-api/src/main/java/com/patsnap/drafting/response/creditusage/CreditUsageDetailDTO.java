package com.patsnap.drafting.response.creditusage;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel("用户积分明细数据")
@Data
public class CreditUsageDetailDTO {

    @ApiModelProperty("用户ID")
    private String userId;

    @ApiModelProperty("用户名称")
    private String userName;

    @ApiModelProperty("任务ID")
    private String taskId;

    @ApiModelProperty("任务名称")
    private String taskName;

    @ApiModelProperty("任务类型")
    private String taskType;

    @ApiModelProperty("创建时间")
    private long createdAt;

    @ApiModelProperty("积分数值")
    private int creditValue;

    @ApiModelProperty("积分类型")
    private String creditType;
}

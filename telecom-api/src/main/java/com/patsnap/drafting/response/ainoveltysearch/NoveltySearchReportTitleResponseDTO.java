package com.patsnap.drafting.response.ainoveltysearch;

import com.patsnap.drafting.request.ainoveltysearch.AiSearchAgentFeature;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class NoveltySearchReportTitleResponseDTO {

	@ApiModelProperty("标题")
	private String title;
	
	@ApiModelProperty("创建人")
	private String creator;

	@JsonProperty("generated_time")
	@ApiModelProperty("报告生成时间")
	private Long generatedTime;

	@JsonProperty("search_qty")
	@ApiModelProperty("查询次数")
	private Integer searchQty;

	@JsonProperty("comparative_qty")
	@ApiModelProperty("对比数量")
	private Integer comparativeQty;

	@JsonProperty("nearest_qty")
	@ApiModelProperty("最接近数量")
	private Integer nearestQty;

	@JsonProperty("secondary_approach_qty")
	@ApiModelProperty("次接近数量")
	private Integer secondaryApproachQty;
}
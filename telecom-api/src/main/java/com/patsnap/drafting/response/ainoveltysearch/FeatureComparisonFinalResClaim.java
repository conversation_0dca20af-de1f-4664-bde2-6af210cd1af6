package com.patsnap.drafting.response.ainoveltysearch;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 权利要求编号及其原始文本
 *
 * <AUTHOR>
 * @Date 2025/7/16 11:04
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class FeatureComparisonFinalResClaim {

    // 权利要求编号(权1，权2，权3)
    @JsonProperty("claim_num")
    private Integer claimNum;

    // 权利要求原始文本
    @JsonProperty("claim_text")
    private String claimText;
}

package com.patsnap.drafting.response.imagesearch;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 文件签名响应 DTO
 *
 * <AUTHOR> Assistant
 * @date 2024/12/19
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("文件签名响应")
public class FileSignResponseDTO {

    @ApiModelProperty(value = "签名成功的文件列表")
    @JsonProperty("signed_files")
    private List<SignedFileInfo> signedFiles;

    @ApiModelProperty(value = "签名失败的文件列表")
    @JsonProperty("failed_files")
    private List<FailedFileInfo> failedFiles;

    @ApiModelProperty(value = "总文件数")
    @JsonProperty("total_count")
    private Integer totalCount;

    @ApiModelProperty(value = "成功签名数")
    @JsonProperty("success_count")
    private Integer successCount;

    @ApiModelProperty(value = "失败签名数")
    @JsonProperty("failed_count")
    private Integer failedCount;

    /**
     * 签名成功的文件信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel("签名成功的文件信息")
    public static class SignedFileInfo {

        @ApiModelProperty(value = "S3文件键")
        @JsonProperty("s3_key")
        private String s3Key;

        @ApiModelProperty(value = "签名后的访问URL")
        @JsonProperty("signed_url")
        private String signedUrl;

        @ApiModelProperty(value = "URL过期时间（秒）")
        @JsonProperty("expire_seconds")
        private Integer expireSeconds;
    }

    /**
     * 签名失败的文件信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel("签名失败的文件信息")
    public static class FailedFileInfo {

        @ApiModelProperty(value = "S3文件键")
        @JsonProperty("s3_key")
        private String s3Key;

        @ApiModelProperty(value = "失败原因")
        @JsonProperty("error_message")
        private String errorMessage;
    }
} 
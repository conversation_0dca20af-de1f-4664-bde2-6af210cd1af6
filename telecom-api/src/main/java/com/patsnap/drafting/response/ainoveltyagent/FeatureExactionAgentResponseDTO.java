package com.patsnap.drafting.response.ainoveltyagent;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.patsnap.drafting.request.ainoveltysearch.AiSearchAgentFeature;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
@JsonIgnoreProperties(ignoreUnknown = true)
@Builder
public class FeatureExactionAgentResponseDTO {
	@ApiModelProperty("用户输入")
	private String text;
	
	@ApiModelProperty("任务ID")
	private String taskId;
	
	@ApiModelProperty("技术特征")
	private List<AiSearchAgentFeature> features;
	
	@ApiModelProperty("阈值")
	private Integer threshold;
	
	@ApiModelProperty("可访问的URL")
	@JsonProperty("open_url")
	private String url;
}
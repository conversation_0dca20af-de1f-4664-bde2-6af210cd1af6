package com.patsnap.drafting.response.ainoveltysearch;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> chendepeng
 * @Date 2025/3/17 14:59
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class FeatureComparisonFinalResFeature {

    @ApiModelProperty("特征词")
    @JsonProperty("tech_feature")
    private String techFeature;

    @ApiModelProperty("对比特征词")
    @JsonProperty("comparison_feature")
    private String comparisonFeature;

    @ApiModelProperty("打分")
    @JsonProperty("score")
    private int score;

    @ApiModelProperty("公开度打分")
    @JsonProperty("public_score")
    private int public_score;

    // 权利要求编号(权1，权2，权3)
    @JsonProperty("claim_num")
    private Integer claimNum;

    // 权利要求类型(独权，从权)
    @JsonProperty("claim_type")
    private String claimType;
}

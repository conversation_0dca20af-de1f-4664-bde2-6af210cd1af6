package com.patsnap.drafting.response.imagesearch;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("图像相似搜索响应")
public class ImageSimilarSearchResponse {
	@ApiModelProperty("响应数据")
	@JsonProperty("data")
	private ImageSimilarSearchData data;

	@ApiModelProperty("错误码")
	@JsonProperty("error_code")
	private int errorCode;

	@ApiModelProperty("状态")
	@JsonProperty("status")
	private boolean status;
}
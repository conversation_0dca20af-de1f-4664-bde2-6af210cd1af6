package com.patsnap.drafting.response.aiftosearch;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR> chendepeng
 * @Date 2025/4/3 15:21
 */
@Data
@Builder
public class FtoSearchSubmitResponseDTO {

    @ApiModelProperty("任务ID")
    @JsonProperty("task_id")
    String taskId;

    @ApiModelProperty("用户的输入")
    @JsonProperty("text")
    String text;
}

package com.patsnap.drafting.response.aitranslation;

import java.util.List;
import java.util.Map;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/07/29
 */
@ApiModel("AI翻译结果响应")
@NoArgsConstructor
@Data
public class AiTransResultResDTO {

    @ApiModelProperty("任务ID")
    private String taskId;
    @ApiModelProperty("任务状态")
    private String status;
    @ApiModelProperty("翻译结果")
    private List<Map<String,Object>> content;

    public AiTransResultResDTO(String taskId, String status) {
        this.taskId = taskId;
        this.status = status;
    }
}

package com.patsnap.drafting.response.aitranslation;

import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/07/23
 */
@ApiModel("AI翻译获取AI词表响应")
@Data
public class TranslationKeywordResDTO {

    @ApiModelProperty("原始关键词")
    private List<String> original;
    @ApiModelProperty("翻译关键词")
    private String translation;
    @ApiModelProperty("翻译关键词通过使用了全库百分之多少的专利获得")
    // 本版本数据组还未支持该功能, 暂时数值随机生成，区间为[20, 70]，取整数
    private Integer percentage = (int) (Math.random() * 50) + 20;
    @ApiModelProperty("是否为AI推荐")
    private Boolean aIRecommend = true;
}

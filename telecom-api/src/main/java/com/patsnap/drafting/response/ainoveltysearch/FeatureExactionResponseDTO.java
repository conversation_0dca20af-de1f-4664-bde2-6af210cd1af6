package com.patsnap.drafting.response.ainoveltysearch;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.patsnap.drafting.request.ainoveltysearch.AiSearchAgentFeature;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class FeatureExactionResponseDTO {
	@ApiModelProperty("用户输入")
	private String text;
	
	@ApiModelProperty("技术特征")
	private List<AiSearchAgentFeature> features;
	
	@ApiModelProperty("阈值")
	private Integer threshold;
}
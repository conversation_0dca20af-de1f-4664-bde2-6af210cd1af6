package com.patsnap.drafting.response.imagesearch;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 图像位置预测响应
 */
@Data
@ApiModel("图像位置预测响应")
public class ImageLocPredictResponse {
    
    @JsonProperty("status")
    @ApiModelProperty("请求状态")
    private Boolean status;
    
    @JsonProperty("error_code")
    @ApiModelProperty("错误码")
    private Integer errorCode;
    
    @JsonProperty("data")
    @ApiModelProperty("响应数据")
    private ImageLocPredictData data;
    
    @Data
    public static class ImageLocPredictData {
        
        @JsonProperty("keywords")
        @ApiModelProperty("关键词列表")
        private List<String> keywords;
        
        @JsonProperty("loc")
        @ApiModelProperty("位置预测结果")
        private List<LocPrediction> loc;
    }
    
    @Data
    public static class LocPrediction {
        
        @JsonProperty("loc")
        @ApiModelProperty("位置编码")
        private String loc;
        
        @JsonProperty("desc")
        @ApiModelProperty("位置描述")
        private String desc;
        
        @JsonProperty("score")
        @ApiModelProperty("预测分数")
        private Double score;
    }
}

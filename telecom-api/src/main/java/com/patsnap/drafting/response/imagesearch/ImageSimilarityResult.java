package com.patsnap.drafting.response.imagesearch;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description 单个图片的相似度比较结果
 */
@Data
public class ImageSimilarityResult {
    
    @ApiModelProperty("相似度分数")
    @JsonProperty("similar_score")
    private Double similarScore;
    
    @ApiModelProperty("相似度")
    @JsonProperty("similar")
    private String similar;
    
    @ApiModelProperty("风险等级")
    @JsonProperty("risk_level")
    private String riskLevel;
    
    @ApiModelProperty("风险描述")
    @JsonProperty("risk_desc")
    private String riskDesc;
    
    @ApiModelProperty("建议")
    @JsonProperty("suggestion")
    private String suggestion;
    
    @ApiModelProperty("差异")
    @JsonProperty("difference")
    private String difference;
    
    @ApiModelProperty("消费者可能混淆的原因")
    @JsonProperty("confusion")
    private String confusion;
    
    @ApiModelProperty("标题")
    @JsonProperty("title")
    private String title;
    
    @ApiModelProperty("专利号")
    @JsonProperty("pn")
    private String pn;
    
    @ApiModelProperty("图片序号")
    @JsonProperty("id")
    private Integer id;
}

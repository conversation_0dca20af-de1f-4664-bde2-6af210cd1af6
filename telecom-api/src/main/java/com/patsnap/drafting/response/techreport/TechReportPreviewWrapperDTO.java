package com.patsnap.drafting.response.techreport;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 技术预览包装响应，包含主查询和专利列表
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("技术预览包装响应")
public class TechReportPreviewWrapperDTO {

    @ApiModelProperty("主查询")
    @JsonProperty("mainQuery")
    private String mainQuery;

    @ApiModelProperty("专利列表")
    @JsonProperty("patents")
    private List<TechReportPreviewResDTO> patents;

    @ApiModelProperty("意图")
    @JsonProperty("intent")
    String intent;

    @ApiModelProperty("专利时间范围")
    @JsonProperty("timeRange")
    private String timeRange;
} 
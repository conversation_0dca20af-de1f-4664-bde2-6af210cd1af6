package com.patsnap.drafting.response.techreport;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Map;

/**
 * 推荐技术及分支响应
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ApiModel("推荐技术及子领域")
public class TechReportTechRecommendResDTO {

    @ApiModelProperty("推荐重要技术列表")
    @JsonProperty("recommend_tech")
    private List<String> recommendTech;

    @ApiModelProperty("子领域列表")
    @JsonProperty("tech_child")
    private List<TechChild> techChild;

    @ApiModelProperty("推荐公司列表")
    @JsonProperty("recommend_companies")
    private List<String> recommendCompanies;

    @Data
    @Accessors(chain = true)
    @ApiModel("子领域")
    public static class TechChild {
        @ApiModelProperty("子领域名称")
        @JsonProperty("name")
        private String name;

        @ApiModelProperty("综合评分（0-100）")
        @JsonProperty("score")
        private Integer score;
    }
} 
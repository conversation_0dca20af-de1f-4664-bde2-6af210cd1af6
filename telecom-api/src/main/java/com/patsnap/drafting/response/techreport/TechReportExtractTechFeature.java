package com.patsnap.drafting.response.techreport;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Map;

/**
 * 技术特征提取响应DTO
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ApiModel("技术特征提取响应")
public class TechReportExtractTechFeature {

    @ApiModelProperty("关键词")
    @JsonProperty("keyword")
    private String keyword;

    @ApiModelProperty("用户所属行业")
    @JsonProperty("industry")
    private String industry;

    @ApiModelProperty("技术术语分类")
    @JsonProperty("keyword_category")
    private String keywordCategory;

    @ApiModelProperty("定义")
    @JsonProperty("definition")
    private String definition;

    @ApiModelProperty("下位术语列表")
    @JsonProperty("subtypes")
    private List<String> subtypes;

    @ApiModelProperty("上位术语列表")
    @JsonProperty("parent_keywords")
    private List<String> parentKeywords;

    @ApiModelProperty("独属的技术特征列表")
    @JsonProperty("uniqueTechFeatures")
    private List<String> uniqueTechFeatures;

    @ApiModelProperty("子类信息")
    @JsonProperty("IPC_CPC")
    private Map<String, String> subclass;
} 
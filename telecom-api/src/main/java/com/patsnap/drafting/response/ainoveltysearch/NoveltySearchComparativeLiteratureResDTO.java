package com.patsnap.drafting.response.ainoveltysearch;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class NoveltySearchComparativeLiteratureResDTO {

    private String patentId;
    private String pn;
    // 公开度打分
    @JsonProperty("related_para")
    private String relatedPara;
    // 公开度打分
    @JsonProperty("public_score")
    private float publicScore;

    public float getPublicScore() {
        return publicScore > 1.0f ? 1.0f : publicScore;
    }
}
package com.patsnap.drafting.response.ainoveltyagent;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.patsnap.drafting.request.ainoveltysearch.AiSearchResultDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class AiSearchAgentStartResponse {
    
    @ApiModelProperty("可访问的URL")
    @JsonProperty("open_url")
    private String url;

    @ApiModelProperty("搜索结果")
    @JsonProperty("search_result")
    private Object searchResult;
    
    @ApiModelProperty("任务状态")
    private String status;
}

package com.patsnap.drafting.response.aitranslation;


import com.patsnap.drafting.annotation.HistoryField;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/07/22
 */
@ApiModel("AI翻译获取技术主题响应")
@Data
public class AiTransTopicResDTO {

    public AiTransTopicResDTO(String techTopic) {
        this.techTopic = techTopic;
    }

    @ApiModelProperty("技术主题")
    @HistoryField("tech_topic")
    private String techTopic;
}

package com.patsnap.drafting.response.patentinfo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> chendepeng
 * @Date 2025/3/19 10:50
 */
@Data
public class PatentInfo {

    @JsonProperty("TITLE_LANG")
    private String titleLang;

    @JsonProperty("LEGAL_STATUS")
    private List<Integer> legalStatus;

    @JsonProperty("READ_STATUS")
    private Boolean readStatus;

    @JsonProperty("PATENT_ID")
    private String patentId;

    @JsonProperty("TITLE")
    private String title;

    @JsonProperty("PN")
    private String pn;

    @JsonProperty("TITLE_LANGS")
    private List<String> titleLangs;
}

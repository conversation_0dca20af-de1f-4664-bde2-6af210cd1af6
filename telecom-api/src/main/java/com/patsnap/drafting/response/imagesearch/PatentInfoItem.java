package com.patsnap.drafting.response.imagesearch;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class PatentInfoItem{
	@JsonProperty("patent_pn")
	private String patentPn;
	
	@JsonProperty("description")
	private String description;
	
	@JsonProperty("comparison_analysis")
	private String comparisonAnalysis;
	
	@JsonProperty("recommendations")
	private String recommendations;
	
	@JsonProperty("similarity")
	private Double similarity;
	
	@JsonProperty("infringement_risk")
	private String infringementRisk;
	
	@JsonProperty("risk_level")
	private Integer riskLevel;
	
	@JsonProperty("image_url")
	private String imageUrl;
}
package com.patsnap.drafting.response.aihistory;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.logging.log4j.util.Strings;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * AI历史任务已执行的步骤详情
 */
@ApiModel("AI历史详情")
@Data
public class AiHistoryStepDTO {

    @ApiModelProperty("任务详细内容")
    @JsonProperty("task_id")
    private String taskId;
    
    @ApiModelProperty("任务详细内容")
    @JsonProperty("current_step")
    private String currentStep = Strings.EMPTY;
    
    @ApiModelProperty("任务名称")
    @JsonProperty("title")
    private String title = Strings.EMPTY;
    
    @ApiModelProperty("更新时间")
    @JsonProperty("updated_at")
    private long updatedAt;
    
    @ApiModelProperty("创建时间")
    @JsonProperty("created_at")
    private long createdAt;
    
    @ApiModelProperty("用户输入的内容")
    @JsonProperty("user_input")
    private Object userInput;
    
    @ApiModelProperty("用户输入的语言")
    @JsonProperty("input_lang")
    private String inputLang;
    
    @ApiModelProperty("任务状态")
    @JsonProperty("task_status")
    private String taskStatus = Strings.EMPTY;

    @ApiModelProperty("任务执行类型(同步、异步)")
    @JsonProperty("task_execute_type")
    private String taskExecuteType = Strings.EMPTY;
    
    @ApiModelProperty("任务步骤详情")
    @JsonProperty("step_detail")
    // key 对应每个任务的步骤名称，value 对应每个步骤的详细内容列表
    private Map<String, List<ContentInfo>> stepDetail = new HashMap<>();

    // 是否是历史数据(目前只有FTO的任务需要)
    @JsonProperty("is_history_data")
    private boolean isHistoryData;

}

package com.patsnap.drafting.response.imagesearch;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("图像搜索Redis数据响应")
public class ImageSearchRedisDataResponse {
    
    @ApiModelProperty("图像相似搜索结果")
    @JsonProperty("search_image_single")
    private ImageSimilarSearchResponse searchImageSingle;
    
    @ApiModelProperty("图像特征比较结果")
    @JsonProperty("feature_comparison")
    private ImageFeatureComparisonResponse featureComparison;
    
    @ApiModelProperty("输入图片URL")
    @JsonProperty("input_image_urls")
    private String inputImageUrls;
    
    @ApiModelProperty("国家信息")
    @JsonProperty("countries")
    private List<String> countries;
} 
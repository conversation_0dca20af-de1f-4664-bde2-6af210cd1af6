package com.patsnap.drafting.response.init;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

/**
 * 专利说明书使用情况
 *
 * <AUTHOR>
 * @Date 2025/4/15 10:38
 */
@ApiModel("专利说明书使用情况")
@Data
@Builder
public class PatentSpecificationUsage {

    @ApiModelProperty("总次数")
    private Integer total;

    @ApiModelProperty("已使用次数")
    private Integer usage;
}

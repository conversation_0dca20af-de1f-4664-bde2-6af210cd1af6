package com.patsnap.drafting.response.ainoveltysearch;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> chendepeng
 * @Date 2025/3/17 14:56
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class FeatureComparisonFinalResItem {

    @ApiModelProperty("专利ID")
    @JsonProperty("patent_id")
    private String patentId;

    @ApiModelProperty("特征词清单")
    @JsonProperty("features")
    private List<FeatureComparisonFinalResFeature> features;

    @ApiModelProperty("公开度打分")
    @JsonProperty("public_score")
    private float publicScore;

    @ApiModelProperty("文档")
    @JsonProperty("document")
    private String document;

    @ApiModelProperty("语言")
    @JsonProperty("lang")
    private String lang;

    @ApiModelProperty("权利要求原始文本清单")
    @JsonProperty("claims")
    private List<FeatureComparisonFinalResClaim> claims;
}

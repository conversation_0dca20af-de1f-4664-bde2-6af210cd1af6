package com.patsnap.drafting.response.imagesearch;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("图像对比项")
public class ComparisonsItem{
	@ApiModelProperty("风险描述")
	@JsonProperty("risk_desc")
	private String riskDesc;

	@ApiModelProperty("专利ID")
	@JsonProperty("patent_id")
	private String patentId;

	@ApiModelProperty("相似度分数")
	@JsonProperty("score")
	private Double similarScore;

	@ApiModelProperty("相似度")
	@JsonProperty("similar")
	private String similar;

	@ApiModelProperty("风险等级")
	@JsonProperty("risk_level")
	private String riskLevel;

	@ApiModelProperty("建议")
	@JsonProperty("suggestion")
	private String suggestion;

	@ApiModelProperty("差异")
	@JsonProperty("difference")
	private String difference;

	@ApiModelProperty("标题")
	@JsonProperty("title")
	private String title;

	@ApiModelProperty("专利号")
	@JsonProperty("patent_pn")
	private String pn;

	@ApiModelProperty("国家")
	@JsonProperty("country")
	private String country;

	@ApiModelProperty("法律状态")
	@JsonProperty("status")
	private Integer status;
	
	@ApiModelProperty("消费者可能混淆的原因")
	@JsonProperty("confusion")
	private String confusion;
}

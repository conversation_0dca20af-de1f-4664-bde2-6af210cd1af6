package com.patsnap.drafting.response.techreport;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 技术简报创建字段响应DTO
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class TechReportCreateFieldsResDTO {

    /**
     * 标题
     */
    @JsonProperty("title")
    private String title;

    /**
     * 用户输入
     */
    @JsonProperty("user_input")
    private String userInput;

    /**
     * 意图类型
     */
    @JsonProperty("intent_type")
    private String intentType;

    /**
     * 子领域列表
     */
    @JsonProperty("sub_field_list")
    private List<SubField> subFieldList;

    /**
     * 新闻检索式
     */
    @JsonProperty("news_query")
    private List<String> newsQuery;

    /**
     * 专利检索式
     */
    @JsonProperty("patent_query")
    private String patentQuery;

    /**
     * IPC检索式
     */
    @JsonProperty("ipc_query")
    private String ipcQuery;

    /**
     *监控industry
     */
    @JsonProperty("industry")
    private String industry;

    /**
     *监控主题
     */
    @JsonProperty("subject")
    private String subject;

    /**
     *用户所在公司
     */
    @JsonProperty("user_company")
    private String userCompany;

    /**
     * 自动创建标志
     */
    @JsonProperty("auto_create")
    private Boolean autoCreate;

    /**
     * 提取的公司列表
     */
    @JsonProperty("extract_company")
    private List<String> extractCompany;

    /**
     * 推荐的公司列表
     */
    @JsonProperty("recommend_company")
    private List<String> recommendCompany;
    
    @JsonProperty("source_type")
    private String sourceType;

    /**
     * 会话ID
     */
    @JsonProperty("session_id")
    private String sessionId;

    /**
     * 子领域信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Accessors(chain = true)
    public static class SubField {

        /**
         * 子领域id
         */
        @JsonProperty("id")
        private String id;

        /**
         * 子领域名称
         */
        @JsonProperty("name")
        private String name;

        /**
         * 分类类型
         */
        @JsonProperty("classification_type")
        private String classificationType;

        /**
         * 专利检索式
         */
        @JsonProperty("patent_query")
        private String patentQuery;
    }
} 
package com.patsnap.drafting.response.techreport;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 技术简报用户意图识别响应
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ApiModel("技术简报用户意图识别响应")
public class TechReportIntentResDTO {
    
    @ApiModelProperty(value = "意图类型代号")
    private String intent;
    
    @ApiModelProperty(value = "用户所属公司")
    @JsonProperty("user_company")
    private String userCompany;
    
    @ApiModelProperty(value = "用户输入内容")
    private String userInput;
}
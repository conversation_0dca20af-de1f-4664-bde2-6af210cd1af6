package com.patsnap.drafting.response.techreport;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 推荐公司与技术响应
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ApiModel("推荐公司与技术响应")
public class TechReportRecommendResDTO {

    @ApiModelProperty("头部公司列表")
    @JsonProperty("head_companies")
    private List<CompanyInfo> headCompanies;
    
    @ApiModelProperty("重要技术")
    @JsonProperty("important_technologies")
    private List<String> importantTechnologies;
    
    @ApiModelProperty("核心主题对象")
    @JsonProperty("core_subject")
    private String coreSubject;
} 
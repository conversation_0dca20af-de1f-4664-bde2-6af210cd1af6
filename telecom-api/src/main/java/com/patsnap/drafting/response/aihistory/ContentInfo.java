package com.patsnap.drafting.response.aihistory;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * AI内容详情
 */
@ApiModel("内容详情")
@Data
@AllArgsConstructor
public class ContentInfo {

    @ApiModelProperty("内容 key")
    @JsonProperty("content_type")
    private String contentType;
    
    @ApiModelProperty("是否隐藏")
    @JsonProperty("hide")
    private Boolean hide = Boolean.FALSE;

}

package com.patsnap.drafting.response.aihistory;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/07/29
 */
@ApiModel("AI历史记录")
@Data
public class AiHistoryDTO {
    @ApiModelProperty("任务ID")
    private String taskId;
    @ApiModelProperty("任务标题")
    private String title;
    @ApiModelProperty("是否已读")
    private Boolean read;
    @ApiModelProperty("更新时间")
    private long updatedAt;
    @ApiModelProperty("创建时间")
    private long createdAt;
    @ApiModelProperty("任务执行类型")
    private String taskType;
    @ApiModelProperty("异步任务状态")
    private String taskStatus;
    @ApiModelProperty("任务状态")
    private String type;
}

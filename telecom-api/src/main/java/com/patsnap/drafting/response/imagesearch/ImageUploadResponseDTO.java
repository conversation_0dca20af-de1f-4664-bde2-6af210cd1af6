package com.patsnap.drafting.response.imagesearch;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 图片上传响应 DTO
 *
 * <AUTHOR> Assistant
 * @date 2024/12/19
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("图片上传响应")
public class ImageUploadResponseDTO {

    @ApiModelProperty(value = "上传成功的文件列表")
    @JsonProperty("uploaded_files")
    private List<UploadedFileInfo> uploadedFiles;

    @ApiModelProperty(value = "上传失败的文件列表")
    @JsonProperty("failed_files")
    private List<FailedFileInfo> failedFiles;

    @ApiModelProperty(value = "总文件数")
    @JsonProperty("total_count")
    private Integer totalCount;

    @ApiModelProperty(value = "成功上传数")
    @JsonProperty("success_count")
    private Integer successCount;

    @ApiModelProperty(value = "失败上传数")
    @JsonProperty("failed_count")
    private Integer failedCount;

    /**
     * 上传成功的文件信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel("上传成功的文件信息")
    public static class UploadedFileInfo {

        @ApiModelProperty(value = "原始文件名")
        @JsonProperty("original_name")
        private String originalName;

        @ApiModelProperty(value = "存储的文件名")
        @JsonProperty("stored_name")
        private String storedName;

        @ApiModelProperty(value = "文件访问URL")
        @JsonProperty("file_url")
        private String fileUrl;

        @ApiModelProperty(value = "文件大小（字节）")
        @JsonProperty("file_size")
        private Long fileSize;

        @ApiModelProperty(value = "文件类型")
        @JsonProperty("content_type")
        private String contentType;

        @ApiModelProperty(value = "S3存储键")
        @JsonProperty("s3_key")
        private String s3Key;
        
        @ApiModelProperty(value = "生成的唯一ID")
        @JsonProperty("unique_id")
        private String uniqueId;
    }

    /**
     * 上传失败的文件信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel("上传失败的文件信息")
    public static class FailedFileInfo {

        @ApiModelProperty(value = "原始文件名")
        @JsonProperty("original_name")
        private String originalName;

        @ApiModelProperty(value = "失败原因")
        @JsonProperty("error_message")
        private String errorMessage;
        
        @ApiModelProperty(value = "失败原因错误码")
        @JsonProperty("error_code")
        private Integer errorCode;

        @ApiModelProperty(value = "文件大小（字节）")
        @JsonProperty("file_size")
        private Long fileSize;
    }
} 
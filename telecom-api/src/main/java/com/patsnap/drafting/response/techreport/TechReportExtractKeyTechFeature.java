package com.patsnap.drafting.response.techreport;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Map;

/**
 * 技术特征唯一性检查响应
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ApiModel("技术特征唯一性检查响应")
public class TechReportExtractKeyTechFeature {

    @ApiModelProperty("技术主题")
    @JsonProperty("theme")
    private String theme;

    @ApiModelProperty("技术特征映射")
    @JsonProperty("features")
    private Map<String, TechFeatureInfo> features;

    /**
     * 技术特征信息
     */
    @Data
    @Accessors(chain = true)
    @ApiModel("技术特征信息")
    public static class TechFeatureInfo {
        
        @ApiModelProperty("是否唯一归属")
        @JsonProperty("belongsToThemeUniquely")
        private Boolean belongsToThemeUniquely;
        
        @ApiModelProperty("不属于的原因")
        @JsonProperty("note")
        private String note;
    }
} 
package com.patsnap.drafting.response.aispecification;

import com.patsnap.drafting.request.aitask.AiTaskReqDTO;

import lombok.Data;

@Data
public class SpecificationLangCheckResDTO extends AiTaskReqDTO {

    public static final String RESULT_SUCCESS = "success";
    public static final String RESULT_FAILED = "failed";

    public static final String OPERATION_MODIFY = "modify";
    public static final String OPERATION_UNSUPPORTED = "unsupported";
    public static final String OPERATION_SUGGEST = "suggest";


    //用户提交结果 failed - 根据operation弹框 success- 直接进入下一步
    private String result;

    //modify：修改弹框 suggest：推荐弹框
    private String operation;

    //推荐弹框推荐值
    private String suggestion;

    public static SpecificationLangCheckResDTO instanceOfSuccess() {
        SpecificationLangCheckResDTO specificationLangCheckResDTO = new SpecificationLangCheckResDTO();
        specificationLangCheckResDTO.setResult(RESULT_SUCCESS);
        return specificationLangCheckResDTO;
    }

    public static SpecificationLangCheckResDTO instanceOfUnspported() {
        SpecificationLangCheckResDTO specificationLangCheckResDTO = new SpecificationLangCheckResDTO();
        specificationLangCheckResDTO.setResult(RESULT_FAILED);
        specificationLangCheckResDTO.setOperation(OPERATION_UNSUPPORTED);
        return specificationLangCheckResDTO;
    }


    public static SpecificationLangCheckResDTO instanceOfModify() {
        SpecificationLangCheckResDTO specificationLangCheckResDTO = new SpecificationLangCheckResDTO();
        specificationLangCheckResDTO.setResult(RESULT_FAILED);
        specificationLangCheckResDTO.setOperation(OPERATION_MODIFY);
        return specificationLangCheckResDTO;
    }


    public static SpecificationLangCheckResDTO instanceOfSuggest(String suggestion) {
        SpecificationLangCheckResDTO specificationLangCheckResDTO = new SpecificationLangCheckResDTO();
        specificationLangCheckResDTO.setResult(RESULT_FAILED);
        specificationLangCheckResDTO.setOperation(OPERATION_SUGGEST);
        specificationLangCheckResDTO.setSuggestion(suggestion);
        return specificationLangCheckResDTO;
    }
}

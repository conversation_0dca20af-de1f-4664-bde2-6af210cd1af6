package com.patsnap.drafting.response.aispecification;

import com.patsnap.drafting.request.aispecification.EmbodimentOutlineItem;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class SpecificationRdResDTO {

    private String prompt;

    private String modelName;

    private Map<String, Object> extraData;

    // 实施例大纲数据列表
    private List<EmbodimentOutlineItem> outline;
}

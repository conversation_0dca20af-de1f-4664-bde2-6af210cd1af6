package com.patsnap.drafting.response.imagesearch;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.Set;

@EqualsAndHashCode(callSuper = true)
@Data
public class ImageSimilarReportResponse extends ImageFeatureComparisonResponse{
	
	@JsonProperty("operate_date")
	private Long operateDate;
	
	@JsonProperty("operator")
	private String operator;
	
	@JsonProperty("loc")
	private List<String> loc;

	@ApiModelProperty("出口国家列表")
	@JsonProperty("country")
	private List<String> country;

	@ApiModelProperty("高风险出口国家列表")
	@JsonProperty("high_risk_country")
	private Set<String> highRiskCountry;

	@ApiModelProperty("中风险出口国家列表")
	@JsonProperty("middle_risk_country")
	private Set<String> middleRiskCountry;

	@ApiModelProperty("疑似侵权专利数量")
	@JsonProperty("suspected_infringement_count")
	private Integer suspectedInfringementCount;

	@ApiModelProperty("疑似侵权国家列表")
	@JsonProperty("suspected_infringement_country")
	private Set<String> suspectedInfringementCountry;
}
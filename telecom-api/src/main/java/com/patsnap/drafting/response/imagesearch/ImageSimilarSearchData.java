package com.patsnap.drafting.response.imagesearch;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("图像相似搜索数据")
public class ImageSimilarSearchData {
	@ApiModelProperty("专利信息列表")
	@JsonProperty("patent_messages")
	private List<PatentMessagesItem> patentMessages;

	@ApiModelProperty("搜索结果总数")
	@JsonProperty("total_search_result_count")
	private int totalSearchResultCount;
}
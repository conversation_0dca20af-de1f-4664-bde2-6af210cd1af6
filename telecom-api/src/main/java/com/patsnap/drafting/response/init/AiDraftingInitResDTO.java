package com.patsnap.drafting.response.init;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

@ApiModel("AI撰写 init 响应")
@Data
@Builder
public class AiDraftingInitResDTO {

    @ApiModelProperty("时区信息")
    private TimeZone timeZone;
    
    @ApiModelProperty("权限信息")
    private DraftingPermission permission;
    
    @JsonProperty("novelty_search_usage")
    @ApiModelProperty("查新次数情况")
    private NoveltySearchUsage noveltySearchUsage;

    @JsonProperty("fto_search_usage")
    @ApiModelProperty("FTO次数情况")
    private FtoSearchUsage ftoSearchUsage;

    @JsonProperty("translation_usage")
    @ApiModelProperty("AI翻译字数情况")
    private TranslationUsage translationUsage;

    @JsonProperty("patent_disclosure_usage")
    @ApiModelProperty("专利交底书次数情况")
    private PatentDisclosureUsage patentDisclosureUsage;

    @JsonProperty("cn_patent_specification_usage")
    @ApiModelProperty("专利说明书次数情况(CNIPA)")
    private PatentSpecificationUsage cnPatentSpecificationUsage;

    @JsonProperty("us_patent_specification_usage")
    @ApiModelProperty("专利说明书次数情况(USPTO)")
    private PatentSpecificationUsage usPatentSpecificationUsage;
    
    @JsonProperty("eu_patent_specification_usage")
    @ApiModelProperty("专利说明书次数情况(EPO)")
    private PatentSpecificationUsage euPatentSpecificationUsage;

    @JsonProperty("unified_patent_specification_usage")
    @ApiModelProperty("专利说明书次数情况(统一)")
    private PatentSpecificationUsage unifiedPatentSpecificationUsage;
}

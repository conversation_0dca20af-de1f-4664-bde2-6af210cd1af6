package com.patsnap.drafting.response.techreport;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 提取技术特征响应
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ApiModel("提取技术特征响应")
public class TechReportExtractFeaturesResDTO {
    
    @ApiModelProperty("子术语列表")
    @JsonProperty("child_terms")
    private List<String> childTerms;
    
    @ApiModelProperty("上位术语列表")
    @JsonProperty("upper_terms")
    private List<String> upperTerms;
    
    @ApiModelProperty("关键特征列表")
    @JsonProperty("key_features")
    private List<String> keyFeatures;
    
    /**
     * 核心主题对象
     */
    @ApiModelProperty("核心主题对象")
    @JsonProperty("core_subject")
    private String coreSubject;
    
    @ApiModelProperty("分类号信息")
    @JsonProperty("classification_numbers")
    private List<ClassificationInfo> classificationNumbers;
    
    
    /**
     * 分类号信息
     */
    @Data
    @ApiModel("分类号信息")
    public static class ClassificationInfo {
        @ApiModelProperty("分类号")
        private String code;
        
        @ApiModelProperty("分类号描述")
        private String description;
    }
}
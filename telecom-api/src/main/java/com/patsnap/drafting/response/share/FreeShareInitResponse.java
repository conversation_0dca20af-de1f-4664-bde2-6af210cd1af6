package com.patsnap.drafting.response.share;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class FreeShareInitResponse {
    
    @ApiModelProperty("是否是公开分享")
    private Boolean publicShare;
    
    @ApiModelProperty("任务ID")
    private String taskId;
    
    @ApiModelProperty("任务类型")
    private String taskType;

    @ApiModelProperty("是否是任务的主人")
    private Boolean owner;
    
}

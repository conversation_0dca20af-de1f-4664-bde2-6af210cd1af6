package com.patsnap.drafting.request.aidisclosure;

import javax.validation.constraints.NotBlank;

import com.patsnap.drafting.request.aitask.AiTaskReqDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/08/23
 */
@Data
@ApiModel("交底书技术手段更新请求")
public class DisclosureTechMeansUpdateReqDTO extends AiTaskReqDTO {

    @ApiModelProperty(value = "技术手段", required = true)
    @NotBlank(message = "tech means is blank")
    private String techMeans;
}

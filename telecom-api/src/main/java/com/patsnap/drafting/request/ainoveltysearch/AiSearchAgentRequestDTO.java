package com.patsnap.drafting.request.ainoveltysearch;

import com.patsnap.drafting.request.aitask.AiTaskReqDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 前端执行查新检索的请求体
 *
 * <AUTHOR> chendepeng
 * @Date 2025/3/12 14:00
 */
@Data
public class AiSearchAgentRequestDTO extends AiTaskReqDTO {

    @Size(min = 1)
    @ApiModelProperty("确认过的技术特征")
    private List<AiSearchAgentFeature> features;
}

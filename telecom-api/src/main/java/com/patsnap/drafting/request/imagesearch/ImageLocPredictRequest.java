package com.patsnap.drafting.request.imagesearch;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 图像位置预测请求
 */
@Data
@ApiModel("图像位置预测请求参数")
public class ImageLocPredictRequest {
    
    @NotBlank(message = "图像URL不能为空")
    @JsonProperty("url")
    @ApiModelProperty(value = "图像URL", required = true, example = "https://static-open.zhihuiya.com/sample/common_demo.png")
    private String url;
    
    @JsonProperty("description")
    @ApiModelProperty(value = "图像描述", example = "一款智能触屏手机")
    private String description;
}

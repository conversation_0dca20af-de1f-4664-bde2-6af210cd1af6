package com.patsnap.drafting.request.aispecification;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class EmbodimentGenerateItem {

    @ApiModelProperty("实施例编号")
    @JsonProperty("embodiment_number")
    private Integer embodimentNumber;

    @ApiModelProperty("类型（如：设备、机械装置说明、电路发明等）")
    @JsonProperty("type")
    private String type;

    @ApiModelProperty("权利要求编号列表")
    @JsonProperty("claim_numbers")
    private List<Integer> claimNumbers;

    @ApiModelProperty("文本")
    @JsonProperty("text")
    private String text;
}

package com.patsnap.drafting.request.imagesearch;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 文件签名请求 DTO
 *
 * <AUTHOR> Assistant
 * @date 2024/12/19
 */
@Data
@ApiModel("文件签名请求")
public class FileSignRequestDTO {

    @ApiModelProperty(value = "S3文件键列表", required = true, example = "[\"ai_drafting/images/user123/image_1734567890123_test.jpg\", \"ai_drafting/images/user123/image_1734567890124_test2.png\"]")
    @JsonProperty("s3_keys")
    @NotEmpty(message = "S3文件键列表不能为空")
    private List<String> s3Keys;
} 
package com.patsnap.drafting.request.aidisclosure;


import com.patsnap.drafting.annotation.HistoryField;

import javax.validation.constraints.NotBlank;

import com.patsnap.drafting.request.aitask.AiTaskReqDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/08/22
 */
@ApiModel("提取技术手段请求参数")
@Data
public class DisclosureTechMeansReqDTO extends AiTaskReqDTO {

    @ApiModelProperty("用户输入")
//    @Length(max = 5000, message = "input length should be less than 5000")
    @HistoryField("input")
    private String input;
}

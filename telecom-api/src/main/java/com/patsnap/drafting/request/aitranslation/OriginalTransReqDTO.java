package com.patsnap.drafting.request.aitranslation;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import com.patsnap.drafting.request.aitask.AiTaskReqDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/07/30
 */
@ApiModel("更改原文翻译请求")
@Data
public class OriginalTransReqDTO extends AiTaskReqDTO {

    @ApiModelProperty("原文的在全文中的索引")
    @NotNull(message = "original index is null")
    @Min(value = 0, message = "original index must be greater than or equal to 0")
    private Integer originalIndex;

    @ApiModelProperty("重新输入的原文")
    @NotBlank(message = "original text is blank")
//    @Length(max = 500, message = "original text length must be less than or equal to 500")
    private String originalText;
}

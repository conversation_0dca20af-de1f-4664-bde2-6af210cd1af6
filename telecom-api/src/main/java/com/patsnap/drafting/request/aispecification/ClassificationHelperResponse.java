package com.patsnap.drafting.request.aispecification;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * @Author: z<PERSON><PERSON>i
 * @Date: 2020/7/14 11:24
 */
@Data
public class ClassificationHelperResponse implements Serializable {

    @Serial
    private static final long serialVersionUID = -8531080368048643795L;

    @JsonProperty("responseHeader")
    private ClassificationHelperHeader responseHeader;
    private HelperResponse response;
}

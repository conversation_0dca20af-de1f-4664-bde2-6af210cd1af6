package com.patsnap.drafting.request.aitranslation;

import java.util.Map;

import javax.validation.constraints.NotBlank;

import com.fasterxml.jackson.annotation.JsonProperty;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel("AI翻译模型测试请求")
@Data
public class AiTransTaskTestReqDTO {

    @ApiModelProperty("用户输入")
    @NotBlank(message = "user input is null")
    @JsonProperty("user_input")
    private String userInput;

    @ApiModelProperty("原文语言, CN/EN")
    @NotBlank(message = "source lang is null")
    @JsonProperty("source_lang")
    private String sourceLang;

    @ApiModelProperty("译文语言, CN/EN")
    @NotBlank(message = "target lang is null")
    @JsonProperty("target_lang")
    private String targetLang;

    @ApiModelProperty("词表")
    @JsonProperty("terms")
    private Map<String, String> terms;
}

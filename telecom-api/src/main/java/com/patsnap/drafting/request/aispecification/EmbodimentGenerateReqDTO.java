package com.patsnap.drafting.request.aispecification;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.patsnap.drafting.request.GenerateContentRequestDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

@EqualsAndHashCode(callSuper = true)
@Data
public class EmbodimentGenerateReqDTO extends GenerateContentRequestDTO {

    @ApiModelProperty("实施例编号")
    @NotNull(message = "embodiment_number is null")
    private Integer embodimentNumber;

    @ApiModelProperty("权利要求文本")
    @JsonProperty("claim_text")
    private String claimText;

    @ApiModelProperty("类型（如：设备、机械装置说明、电路发明等）")
    @JsonProperty("type")
    private String type;

    @ApiModelProperty("引用实施例编号")
    @JsonProperty("reference_embodiment_number")
    private Integer referenceEmbodimentNumber;

    @ApiModelProperty("内容类型")
    private String contentType = "embodiment_content";
}

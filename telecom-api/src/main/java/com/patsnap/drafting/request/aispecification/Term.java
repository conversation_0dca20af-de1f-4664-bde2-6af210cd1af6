package com.patsnap.drafting.request.aispecification;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "短语实体")
public class Term {

    @ApiModelProperty(value = "唯一标识")
    @JsonProperty("unique_id")
    @NotNull
    private String uniqueId;
    
    @ApiModelProperty(value = "编号")
    @JsonProperty("number")
    private String number;

    @NotNull
    @ApiModelProperty(value = "名称")
    @JsonProperty("name")
    private String name;

    @ApiModelProperty(value = "是否已标注")
    @JsonProperty("annotated")
    private Boolean annotated;
}

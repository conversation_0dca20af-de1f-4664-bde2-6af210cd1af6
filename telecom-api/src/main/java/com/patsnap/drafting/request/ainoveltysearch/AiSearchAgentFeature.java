package com.patsnap.drafting.request.ainoveltysearch;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> chendepeng
 * @Date 2025/3/12 13:43
 */
@Data
public class AiSearchAgentFeature {
    @ApiModelProperty("抽取的特征词")
    @JsonProperty("tech_feature")
    private String techFeature;

    @ApiModelProperty("关键词清单")
    private List<String> keywords;

    @ApiModelProperty("打分")
    private int score;
    
    @ApiModelProperty("原因")
    private String reason;
    
    @ApiModelProperty("是否选中")
    @JsonProperty("is_select")
    private Boolean select = true;

    @ApiModelProperty("是否为核心字段")
    private Boolean core = false;

    @ApiModelProperty("是否为用户添加")
    @JsonProperty("user_add")
    private Boolean userAdd = false;
}

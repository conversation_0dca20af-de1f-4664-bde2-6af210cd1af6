package com.patsnap.drafting.request.ainoveltysearch;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.patsnap.drafting.response.ainoveltysearch.FeatureComparisonFinalResClaim;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> chendepeng
 * @Date 2025/3/12 19:59
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class AiSearchFinalResult implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    // 专利 id
    @JsonProperty("patent_id")
    private String patentId;

    @JsonProperty("most_similar")
    private boolean mostSimilar = false;

    /**
     * 对比文献种类
     */
    @JsonProperty("comparative_literature_type")
    private String comparativeLiteratureType = "A";

    /**
     * 用户自添加的
     */
    @JsonProperty("user_added")
    private boolean userAdded = false;

    @JsonProperty("selected")
    private boolean selected = false;

    // 公开度打分
    @JsonProperty("public_score")
    private float publicScore;

    // cc 对比的文本段落，demo 使用
    @JsonProperty("document")
    private String document;

    // 特征清单
    @JsonProperty("features")
    private List<AiSearchFinalResultFeature> features;

    // 特征匹配数量
    @JsonProperty("feature_match_num")
    private int featureMatchNum;

    // 语言
    @JsonProperty("lang")
    private String lang;

    // 对比结论
    @JsonProperty("comparison_conclusion")
    private Map<String, String> comparisonConclusion;

    // 等同独权数量
    @JsonProperty("equivalent_independent_claim_num")
    private Integer equivalentIndependentClaimNum;

    // 独权数量
    @JsonProperty("independent_claim_num")
    private Integer independentClaimNum;

    // 等同从权数量
    @JsonProperty("equivalent_dependent_claim_num")
    private Integer equivalentDependentClaimNum;

    // 从权数量
    @JsonProperty("dependent_claim_num")
    private Integer dependentClaimNum;

    // 风险等级
    @JsonProperty("risk_level")
    private String riskLevel;

    // 权利要求等同信息
    @JsonProperty("claim_equivalent_info")
    private List<ClaimEquivalentItem> claimEquivalentInfo;

    @ApiModelProperty("权利要求原始文本清单")
    @JsonProperty("claims")
    private List<FeatureComparisonFinalResClaim> claims;
}

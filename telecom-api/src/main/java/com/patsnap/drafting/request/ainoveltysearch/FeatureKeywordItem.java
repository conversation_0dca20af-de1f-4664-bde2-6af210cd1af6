package com.patsnap.drafting.request.ainoveltysearch;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> chendepeng
 * @Date 2025/3/12 19:57
 */
@Data
public class FeatureKeywordItem {
    // 抽取的特征词
    @JsonProperty("tech_feature")
    private String techFeature;

    // 特征词的翻译
    @JsonProperty("translation")
    private String translation;

    // 特征词的同义词
    @JsonProperty("synonym")
    private List<String> synonym;

    // 同义词打分
    @JsonProperty("synonym_score")
    private List<Integer> synonymScore;

    @JsonProperty("words")
    private List<FeatureKeywordItemWord> words;
}

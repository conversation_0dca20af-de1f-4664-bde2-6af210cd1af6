package com.patsnap.drafting.request.imagesearch;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 图片重排序请求对象
 */
@Data
public class ImageReorderRequest {
    
    @JsonProperty("target_image_urls")
    @ApiModelProperty("目标图片地址")
    private List<String> targetImageUrls;
    
    @JsonProperty("country")
    @ApiModelProperty("商品出口国家列表")
    private List<String> country;
    
    @JsonProperty("image_patent_list")
    @ApiModelProperty("图片专利列表")
    private List<ImagePatentInfo> imagePatentList;
    
    @JsonProperty("merged_image_base64_list")
    @ApiModelProperty("合并的图片URL列表")
    private List<String> mergedImageBase64List;
    
}

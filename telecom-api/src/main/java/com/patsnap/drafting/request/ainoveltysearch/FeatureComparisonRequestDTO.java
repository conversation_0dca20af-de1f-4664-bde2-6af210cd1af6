package com.patsnap.drafting.request.ainoveltysearch;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.patsnap.drafting.request.aitask.AiTaskReqDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.Size;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> chendepeng
 * @Date 2025/3/17 14:28
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel("技术特征对比及得分请求参数")
@Data
public class FeatureComparisonRequestDTO extends AiTaskReqDTO {

    @ApiModelProperty("专利PN号清单")
    @Size(min=1)
    private List<String> patentPns;

    @ApiModelProperty("是否新增专利")
    @JsonProperty("is_add_patent")
    private boolean isAddPatent = true;

    @ApiModelProperty("新增专利是否默认选中进入文献确认")
    @JsonProperty("add_patent_selected")
    private boolean addPatentSelected = false;

    public List<Map<String, Object>> generateFeatureScoreMap(List<AiSearchAgentFeature> featureList) {
        List<Map<String, Object>> featureScoreMapList = new ArrayList<>();
        for (AiSearchAgentFeature feature : featureList) {
            Map<String, Object> featureScoreMap = new HashMap<>();
            featureScoreMap.put("tech_feature", feature.getTechFeature());
            featureScoreMap.put("score", feature.getScore());
            featureScoreMap.put("core", feature.getCore());
            featureScoreMap.put("user_add", feature.getUserAdd());
            featureScoreMapList.add(featureScoreMap);
        }
        return featureScoreMapList;
    }
}

package com.patsnap.drafting.request;

import com.patsnap.drafting.request.aitask.AiTaskReqDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * AI 生成内容的请求参数
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel("AI 生成内容的请求参数")
@Data
public class GenerateContentRequestDTO extends AiTaskReqDTO {
    
    @ApiModelProperty("内容类型")
    //具体值参考 AiTaskContentTypeEnum
    private String contentType;
}

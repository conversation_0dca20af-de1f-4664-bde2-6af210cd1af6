package com.patsnap.drafting.request.aispecification;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 实施例信息
 *
 * <AUTHOR>
 * @Date 2025/4/29 16:59
 */
@Data
public class DisclosureEmbodimentItem {

    @JsonProperty("selected")
    private Boolean selected;

    @JsonProperty("text")
    private String text;

    @JsonProperty("number")
    private Integer number;
}

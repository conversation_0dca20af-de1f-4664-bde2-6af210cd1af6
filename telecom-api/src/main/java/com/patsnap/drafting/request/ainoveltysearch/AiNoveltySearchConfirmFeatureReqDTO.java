package com.patsnap.drafting.request.ainoveltysearch;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.patsnap.drafting.request.aitask.AiTaskReqDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;

/**
 * AI 搜索确认特征请求
 * <AUTHOR>
 */
@Data
public class AiNoveltySearchConfirmFeatureReqDTO extends AiTaskReqDTO {

    @ApiModelProperty("确认使用的特征词列表")
    @JsonProperty("tech_features")
    private List<FeatureDTO> techFeatures = new ArrayList<>();

    @Accessors(chain = true)
    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class FeatureDTO {

        @JsonProperty("feature_num")
        private String featureNum;

        @JsonProperty("feature_text")
        private String featureText;

        @JsonProperty("feature_text_original")
        private String featureTextOriginal;

        @JsonProperty("feature_weight")
        private String featureWeight;

        @JsonProperty("feature_weight_reason")
        private String featureWeightReason;

        @JsonProperty("feature_function")
        private String featureFunction;

        @JsonProperty("feature_elements")
        private String featureElements;

        @JsonProperty("user_add")
        private Boolean userAdd = false;

        @JsonProperty("selected")
        private Boolean selected = true;
    }

}

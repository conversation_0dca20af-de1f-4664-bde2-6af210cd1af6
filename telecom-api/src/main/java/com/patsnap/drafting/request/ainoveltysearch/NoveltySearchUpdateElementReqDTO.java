package com.patsnap.drafting.request.ainoveltysearch;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import java.util.List;

import com.patsnap.drafting.request.aitask.AiTaskReqDTO;

@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class NoveltySearchUpdateElementReqDTO extends AiTaskReqDTO {

    /**
     * 检索要素数据
     */
    private List<NoveltySearchElementDataDTO> data;

    /**
     * 是否启用同族合并检索
     */
    @JsonProperty("family_merge_enabled")
    private Boolean familyMergeEnabled = false;
} 
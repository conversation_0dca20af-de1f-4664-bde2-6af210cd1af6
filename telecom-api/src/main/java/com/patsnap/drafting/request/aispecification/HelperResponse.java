package com.patsnap.drafting.request.aispecification;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * @Author: zhou<PERSON>i
 * @Date: 2020/7/14 11:31
 */
@Data
public class HelperResponse implements Serializable {

    @Serial
    private static final long serialVersionUID = 2506050363677678025L;

    @JsonProperty("query_results")
    private List<HelperResult> queryResults;

    @JsonProperty("h_words")
    private List<String> hWords;
}

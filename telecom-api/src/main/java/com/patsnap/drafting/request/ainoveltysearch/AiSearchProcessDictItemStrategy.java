package com.patsnap.drafting.request.ainoveltysearch;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR> chend<PERSON><PERSON>
 * @Date 2025/3/12 20:07
 */
@Data
public class AiSearchProcessDictItemStrategy {
    // 检索类型
    @JsonProperty("type")
    private String type;

    // 检索类型名称（英文）
    @JsonProperty("description_en")
    private String descriptionEn;

    // 检索类型名称（中文）
    @JsonProperty("description_zh")
    private String descriptionZh;

    // 本次检索类型，尝试的请求次数
    @JsonProperty("try_nth")
    private int tryNth;

    // 检索详细参数，部分检索类型有额外的参数需要前端展示
    @JsonProperty("parameters")
    private AiSearchProcessDictItemStrategyParameter parameters;

    // 检索 query
    @JsonProperty("query")
    private String query;
}

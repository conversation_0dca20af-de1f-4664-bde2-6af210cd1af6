package com.patsnap.drafting.request.aispecification;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 实施例大纲项目
 *
 * <AUTHOR>
 * @Date 2025/1/8 10:00
 */
@Data
public class EmbodimentOutlineItem {

    @ApiModelProperty("权利要求编号列表")
    @JsonProperty("claim_numbers")
    private List<Integer> claimNumbers;

    @ApiModelProperty("权利要求文本")
    @JsonProperty("claim_text")
    private String claimText;

    @ApiModelProperty("实施例编号")
    @JsonProperty("embodiment_number")
    private Integer embodimentNumber;

    @ApiModelProperty("类型（如：设备、机械装置说明、电路发明等）")
    @JsonProperty("type")
    private String type;

    @ApiModelProperty("文本")
    @JsonProperty("text")
    private String text;

    @ApiModelProperty("标签")
    @JsonProperty("label")
    private String label;

    @ApiModelProperty("大纲内容")
    @JsonProperty("outline")
    private String outline;

    @ApiModelProperty("引用权利要求编号列表")
    @JsonProperty("reference_claim_number")
    private List<Integer> referenceClaimNumber;

    @ApiModelProperty("引用实施例编号")
    @JsonProperty("reference_embodiment_number")
    private Integer referenceEmbodimentNumber;
} 
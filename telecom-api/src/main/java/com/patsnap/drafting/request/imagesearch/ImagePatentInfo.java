package com.patsnap.drafting.request.imagesearch;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 图片重排序请求对象
 */
@Data
public class ImagePatentInfo {
    
    @JsonProperty("image_url")
    @ApiModelProperty("图片地址")
    private String imageUrl;
    
    @JsonProperty("patent_id")
    @ApiModelProperty("专利ID")
    private String patentId;
    
    @JsonProperty("patent_pn")
    @ApiModelProperty("专利PN")
    private String patentPn;
    
    @JsonProperty("title")
    @ApiModelProperty("标题")
    private String title;
    
    @JsonProperty("image_url_120")
    @ApiModelProperty("图片小尺寸地址(120px)")
    private String imageUrl120;
    
    @JsonProperty("authority")
    @ApiModelProperty("受理局/国家")
    private String authority;
    
}

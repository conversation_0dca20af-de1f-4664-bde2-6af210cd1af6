package com.patsnap.drafting.request.aispecification;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * @Author: zhou<PERSON>i
 * @Date: 2020/7/14 11:25
 */
@Data
public class ClassificationHelperHeader implements Serializable {

    @Serial
    private static final long serialVersionUID = -1293556545087248967L;

    private String query;
    private Integer status;
    @JsonProperty("numFound")
    private Integer numFound;
    private Integer start;
    private Integer rows;
    private String source;

    public ClassificationHelperHeader() {
    }

    public ClassificationHelperHeader(Integer numFound, Integer start, Integer rows) {
        this.numFound = numFound;
        this.start = start;
        this.rows = rows;
    }
}

package com.patsnap.drafting.request.aispecification;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class ContentModifyReqDTO {

    //任务Id
    private String taskId;

    //分类号
    private List<String> classification;

    //类别
    private String category;

    //技术问题
    private String technicalProblem;

    //技术手段
    private String technicalMethods;

    //技术功效
    private String benefit;

    //技术特征树
    private List<FeatureTree> featureTree;

    // 算法抽取或者用户手动填写的实施例数组
    private List<DisclosureEmbodimentItem> embodiments;

    // 附图信息列表 - 对应 FigureContentBO 结构
    @ApiModelProperty(value = "附图信息列表，包含附图的详细信息如图片URL、标注等")
    private List<Figure> figures;

    // 术语信息列表 - 对应 TermContentBO 结构
    @ApiModelProperty(value = "术语信息列表，包含术语的名称、编号、标注状态等")
    private List<Term> terms;

    // 实施例大纲列表
    private List<EmbodimentOutlineItem> outlines;
}

package com.patsnap.drafting.request.aidisclosure;

import java.util.List;

import javax.validation.constraints.NotBlank;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/09/06
 */
@ApiModel("交底书导出请求")
@Data
public class DisclosureExportReqDTO {

    @ApiModelProperty("任务ID")
    @NotBlank(message = "task id is blank")
    private String taskId;
    @ApiModelProperty("导出标题")
    private String title;
    @ApiModelProperty("导出数据")
    private List<Content> data;
    @ApiModelProperty(value = "发明人", hidden = true)
    private String inventor;
    @ApiModelProperty(value = "邮箱", hidden = true)
    private String email;
    @ApiModelProperty(value = "电话", hidden = true)
    private String phone;
    @ApiModelProperty(value = "作者", hidden = true)
    private String author;
    /**
     * 正文内容,包含标题,副标题,内容
     * 当标题和副标题一致时只需要显示标题
     * 当有多个相同标题,且副标题不一致时,标题只需要显示一次
     */
    @Data
    public static class Content {

        @ApiModelProperty("标题")
        private String parentTitle;
        @ApiModelProperty("副标题")
        private String title;
        @ApiModelProperty("内容")
        private String content;
    }
}

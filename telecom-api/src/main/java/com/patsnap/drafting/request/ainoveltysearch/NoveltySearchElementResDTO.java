package com.patsnap.drafting.request.ainoveltysearch;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.experimental.Accessors;

import java.util.List;

@lombok.Data
@Accessors(chain = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class NoveltySearchElementResDTO {

    @JsonProperty("list")
    private List<NoveltySearchElementDataDTO> data;

    @JsonProperty("family_merge_enabled")
    private Boolean familyMergeEnabled = false;

}

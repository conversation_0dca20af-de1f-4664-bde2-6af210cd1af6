package com.patsnap.drafting.request.aitranslation;

import java.util.ArrayList;
import java.util.List;

import lombok.Data;

/**
 * 术语表DTO
 * <AUTHOR>
 */
@Data
public class TranslationTermDto {


    /**
     * 术语表唯一标识符
     */
    private String id;

    /**
     * 术语表标题
     */
    private String title;

    /**
     * 术语对
     */
    private List<TranslationTermPairDto> termPair = new ArrayList<>();
}

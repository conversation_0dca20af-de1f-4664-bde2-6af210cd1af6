package com.patsnap.drafting.request.aispecification;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * @Author: z<PERSON><PERSON><PERSON>
 * @Date: 2020/7/14 11:43
 */
@Data
public class ClassificationDescription implements Serializable {

    @Serial
    private static final long serialVersionUID = -6909387190958470291L;

    private String cn;
    private String en;
    private String jp;
}

package com.patsnap.drafting.request.aitask;

import com.patsnap.drafting.annotation.HistoryId;

import java.io.Serial;
import java.io.Serializable;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * AI任务基础字段
 *
 * <AUTHOR>
 * @date 2024/07/23
 */
@Data
public class AiTaskReqDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 任务ID
     */
    @NotBlank(message = "task id is blank")
    @HistoryId
    private String taskId;
    
    @ApiModelProperty("操作类型（生成内容/扩写/润色/简写）")
    // 具体值参考 OperateTypeEnum
    private String operateType = "generate";
    
    @ApiModelProperty("输入内容，当 operateType 为扩写,缩写，润色时使用")
    private String text;

    @ApiModelProperty("通过这个值控制算法接口消耗token的多少，以此来控制接口成本, 非必传")
    private Integer ccNum;
}

package com.patsnap.drafting.request.aitask;

import com.patsnap.drafting.annotation.NotEmptyMap;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Map;

@ApiModel("AI任务创建请求")
@Data
public class AiTaskCreateReqDTO {

    @ApiModelProperty("任务ID")
    @NotBlank(message = "task id is null")
    private String taskId;
    
    @ApiModelProperty("类型")
    @NotBlank(message = "type is null")
    // type 为 AiTaskTypeEnum 中的值
    private String type;
    
    @ApiModelProperty("用户输入, key 为约定的值，value 为用户输入的值")
    @NotEmptyMap(message = "content中的key和value不能为空")
    // key AiTaskContentTypeEnum 中的值，value 为用户输入的值
    private Map<String, Object> content;
    
    @ApiModelProperty("任务需要消耗的总积分")
    private int credit = -1;
}

package com.patsnap.drafting.request.patentinfo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR> chendepeng
 * @Date 2025/3/19 10:07
 */
@Data
public class PatentSearchRequestDTO {

    @ApiModelProperty("检索文本")
    @NotBlank(message = "query text is blank")
    private String q;

    @ApiModelProperty("是否简单查询")
    private Boolean simple;
}

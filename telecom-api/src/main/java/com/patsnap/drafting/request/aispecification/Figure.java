package com.patsnap.drafting.request.aispecification;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Map;

@Data
@Accessors(chain = true)
@ApiModel(description = "绘图业务对象")
public class Figure {
    
    @ApiModelProperty(value = "附图的唯一标识")
    @JsonProperty("unique_id")
    private String uniqueId;
    
    @ApiModelProperty(value = "是否是摘要附图")
    @JsonProperty("abstract_figure")
    private Boolean abstractFigure;
    
    @ApiModelProperty(value = "图像信息")
    @JsonProperty("image")
    private Image image;
    
    @ApiModelProperty(value = "标注图像信息的base64编码")
    @JsonProperty("annotation_image_base64")
    private String annotationImageBase64;
    
    @ApiModelProperty(value = "标注列表")
    @JsonProperty("annotations")
    private List<AnnotationsBean> annotations;
    
    
    @Data
    @Accessors(chain = true)
    @ApiModel(description = "图像信息")
    public static class Image {
        
        @ApiModelProperty(value = "S3存储键")
        @JsonProperty("s3_key")
        private String s3Key;
        
        @ApiModelProperty(value = "图像URL")
        @JsonProperty("url")
        private String url;
        
        @ApiModelProperty(value = "图像格式")
        @JsonProperty("properties")
        private Map<String, Object> properties;
    }
    
    @Data
    @Accessors(chain = true)
    @ApiModel(description = "标注信息")
    public static class AnnotationsBean {
        
        @ApiModelProperty(value = "术语唯一标识")
        @JsonProperty("term_unique_id")
        private String termUniqueId;
        
        @ApiModelProperty(value = "标注的属性")
        @JsonProperty("properties")
        private Map<String, Object> properties;
    }
}
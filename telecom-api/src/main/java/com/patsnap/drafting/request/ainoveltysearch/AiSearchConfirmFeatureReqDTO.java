package com.patsnap.drafting.request.ainoveltysearch;

import com.patsnap.drafting.request.aitask.AiTaskReqDTO;

import java.util.ArrayList;
import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * AI 搜索确认特征请求
 * <AUTHOR>
 */
@Data
public class AiSearchConfirmFeatureReqDTO extends AiTaskReqDTO {

    @ApiModelProperty("确认使用的特征词列表")
    private List<AiSearchAgentFeature> techFeatures = new ArrayList<>();

}

package com.patsnap.drafting.request.imagesearch;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class ImageSimilarSearchRequest{
	@JsonProperty("loc")
	private String loc;
	@NotNull
	@JsonProperty("country")
	private List<String> country;
	@JsonProperty("is_https")
	private int isHttps;
	@JsonProperty("offset")
	private int offset;
	@JsonProperty("apply_end_time")
	private String applyEndTime;
	@JsonProperty("assignees")
	private String assignees;
	@NotNull
	@JsonProperty("url")
	private String url;
	@JsonProperty("file_key")
	private String fileKey;
	@JsonProperty("public_end_time")
	private String publicEndTime;
	@JsonProperty("main_field")
	private String mainField;
	@JsonProperty("field")
	private String field;
	@JsonProperty("public_start_time")
	private String publicStartTime;
	@JsonProperty("limit")
	private int limit =100;
	@JsonProperty("stemming")
	private int stemming;
	@JsonProperty("apply_start_time")
	private String applyStartTime;
	@JsonProperty("model")
	private int model = 1;
	@JsonProperty("patent_type")
	private String patentType = "D";
	@JsonProperty("legal_status")
	private String legalStatus;
	@JsonProperty("lang")
	private String lang;
	@JsonProperty("simple_legal_status")
	private String simpleLegalStatus;
	@JsonProperty("pre_filter")
	private int preFilter;
	@JsonProperty("order")
	private String order;
}
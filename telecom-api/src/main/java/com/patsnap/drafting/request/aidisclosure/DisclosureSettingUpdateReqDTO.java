package com.patsnap.drafting.request.aidisclosure;

import java.util.List;

import com.patsnap.drafting.request.aitask.AiTaskReqDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/08/23
 */
@ApiModel("交底书设置更新请求")
@Data
public class DisclosureSettingUpdateReqDTO extends AiTaskReqDTO {
    @ApiModelProperty("改进主体")
    private List<String> inventionSubject;
    @ApiModelProperty("应用领域")
    private List<String> applicationField;
    @ApiModelProperty("技术手段")
    private String techMeans;
    @ApiModelProperty("技术效果")
    private String techEffect;
    @ApiModelProperty("将技术背景,技术问题,实施例拼接到一起")
    private String other;
}

package com.patsnap.drafting.request.techreport;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

/**
 * 初始化技术简报对话请求
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ApiModel("初始化技术简报对话请求")
public class TechReportInitReqDTO {

    @ApiModelProperty(value = "用户输入的技术监控需求", required = true)
    @NotBlank(message = "用户输入不能为空")
    private String userInput;
} 
package com.patsnap.drafting.request.patent;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 批量获取专利图片请求DTO
 */
@Data
@ApiModel("批量获取专利图片请求参数")
public class PatentImagesRequestDTO {
    
    @NotEmpty(message = "专利ID列表不能为空")
    @ApiModelProperty(value = "专利ID列表", required = true, example = "[\"24e7ad0b-2c98-45b9-8046-107e224a1336\"]")
    private List<String> patentIds;
}

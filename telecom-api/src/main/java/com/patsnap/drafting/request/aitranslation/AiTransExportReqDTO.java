package com.patsnap.drafting.request.aitranslation;

import javax.validation.constraints.NotBlank;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/08/02
 */
@ApiModel("AI翻译导出请求")
@Data
public class AiTransExportReqDTO {
    @ApiModelProperty("标题")
    @NotBlank(message = "title is blank")
    private String title;
    @ApiModelProperty("原文")
    @NotBlank(message = "original is blank")
    private String original;
    @ApiModelProperty("翻译")
    @NotBlank(message = "translation is blank")
    private String translation;
}

package com.patsnap.drafting.request.imagesearch;


import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


@Data
@ApiModel("专利图像检索-多图请求参数")
public class ImageMultipleSimilarSearchRequest {
    
    @JsonProperty("loc")
    @ApiModelProperty(value = "LOC分类号", notes = "LOC分类(洛迦诺分类号)是针对外观设计专利的通用分类体系，搜索任意层级的分类号，多个分类号可以用逻辑符AND/OR/NOT连接", example = "(14-03 OR 14-02)")
    private String loc;
    
    @JsonProperty("country")
    @ApiModelProperty(value = "专利受理局", notes = "国家/组织/地区代码，可以多选，多个内容项之间用英文,隔开", example = "[\"US\", \"EU\", \"CN\"]")
    private List<String> country;
    
    @JsonProperty("is_https")
    @ApiModelProperty(value = "是否使用HTTPS", hidden = true)
    private int isHttps;
    
    @JsonProperty("offset")
    @ApiModelProperty(value = "偏移量", notes = "0 <= offset <= 1000，默认为0，如需要获取排名在100名之后的专利，可以设置offset=100", example = "0")
    private int offset;
    
    @JsonProperty("apply_end_time")
    @ApiModelProperty(value = "专利申请截止时间", notes = "格式:yyyyMMdd", example = "20200101")
    private String applyEndTime;
    
    @JsonProperty("assignees")
    @ApiModelProperty(value = "申请（专利权）人", example = "huawei")
    private String assignees;
    
    @JsonProperty("urls")
    @ApiModelProperty(value = "图像的URL列表", notes = "单次检索最多支持上传4张图片url", required = true, example = "[\"https://static-open.zhihuiya.com/sample/common_demo.png\", \"https://static-open.zhihuiya.com/sample/common_demo_2.png\"]")
    private List<String> urls;
    
    @JsonProperty("public_end_time")
    @ApiModelProperty(value = "专利公开截止时间", notes = "格式:yyyyMMdd", example = "20200101")
    private String publicEndTime;
    
    @JsonProperty("main_field")
    @ApiModelProperty(value = "专利主要字段", notes = "主要字段包括标题、摘要、权利要求、说明书、公开号、申请号、申请人、发明人和IPC/UPC/LOC分类号", example = "(phones or 手机)")
    private String mainField;
    
    @JsonProperty("field")
    @ApiModelProperty(value = "返回结果排序字段", notes = "支持SCORE,APD,PBD,ISD。SCORE：按照最相关排序；APD：按照申请日排序；PBD：按照公开日排序；ISD：按照授权日排序", example = "SCORE")
    private String field;
    
    @JsonProperty("public_start_time")
    @ApiModelProperty(value = "专利公开起始时间", notes = "格式:yyyyMMdd", example = "20100101")
    private String publicStartTime;
    
    @JsonProperty("limit")
    @ApiModelProperty(value = "返回专利条数", notes = "1 <= limit <= 100，默认为10", example = "10")
    private int limit;
    
    @JsonProperty("stemming")
    @ApiModelProperty(value = "是否开启截词功能", notes = "1：开启；0：关闭。默认关闭。开启截词，在保留原词的同时，并扩展其对应的单复数及时态", example = "0")
    private int stemming;
    
    @JsonProperty("apply_start_time")
    @ApiModelProperty(value = "专利申请起始时间", notes = "格式:yyyyMMdd", example = "20100101")
    private String applyStartTime;
    
    @JsonProperty("model")
    @ApiModelProperty(value = "图像检索模型", notes = "外观专利：1：智能联想【推荐】，2：搜索此图；实用新型专利：3：匹配形状，4：匹配形状/图案/色彩【推荐】", required = true, example = "1")
    private int model;
    
    @JsonProperty("patent_type")
    @ApiModelProperty(value = "专利类型", notes = "D：外观专利，U：实用新型专利", required = true, example = "D")
    private String patentType;
    
    @JsonProperty("legal_status")
    @ApiModelProperty(value = "专利的法律状态", notes = "1:公开 2:实质审查 3:授权 8:避免重复授权 11:撤回 12:撤回-未指定类型 17:撤回-视为撤回 18:撤回-主动撤回 13:驳回 14:全部撤销 15:期限届满 16:未缴年费 21:权利恢复 22:权利终止 23:部分无效 24:申请终止 30:放弃 19:放弃-视为放弃 20:放弃-主动放弃 25:放弃-未指定类型 222:PCT未进入指定国（指定期内） 223:PCT进入指定国（指定期内） 224:PCT进入指定国（指定期满） 225:PCT未进入指定国（指定期满），可以多选，多个内容项之间用英文,隔开", example = "3,2")
    private String legalStatus;
    
    @JsonProperty("lang")
    @ApiModelProperty(value = "标题的语言优先选择", notes = "可以选cn、en、original，默认为original：original：专利原文标题，cn：专利中文翻译标题，en：专利英文翻译标题", example = "original")
    private String lang;
    
    @JsonProperty("simple_legal_status")
    @ApiModelProperty(value = "专利的简单法律状态", notes = "0:失效 1:有效 2:审中 220:PCT指定期满 221:PCT指定期内 999:未确认，可以多选，多个内容项之间用英文,隔开", example = "1,2")
    private String simpleLegalStatus;
    
    @JsonProperty("pre_filter")
    @ApiModelProperty(value = "是否开启前置国家/LOC过滤", notes = "1：开启；0：关闭。默认开启", example = "1")
    private int preFilter;
    
    @JsonProperty("order")
    @ApiModelProperty(value = "排序方式", notes = "当field选择APD,PBD,ISD时有效，order支持desc,asc。默认desc", example = "desc")
    private String order;
    
}

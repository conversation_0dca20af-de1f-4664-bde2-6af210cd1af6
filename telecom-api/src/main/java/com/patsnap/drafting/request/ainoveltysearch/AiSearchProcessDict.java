package com.patsnap.drafting.request.ainoveltysearch;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR> chendepeng
 * @Date 2025/3/12 20:02
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class AiSearchProcessDict {
    // 语义检索
    @JsonProperty("semantic_search")
    private AiSearchProcessDictItem semanticSearch;

    // 分类号检索
    @JsonProperty("ipc_cpc_search")
    private AiSearchProcessDictItem ipcCpcSearch;

    // 块检索和渐进式检索1
    @JsonProperty("block_progressive_search_1")
    private AiSearchProcessDictItem blockProgressiveSearch1;

    // 块检索和渐进式检索2
    @JsonProperty("block_progressive_search_2")
    private AiSearchProcessDictItem blockProgressiveSearch2;

    // 块检索和渐进式检索3
    @JsonProperty("block_progressive_search_3")
    private AiSearchProcessDictItem blockProgressiveSearch3;

    // 块检索和渐进式检索4
    @JsonProperty("block_progressive_search_4")
    private AiSearchProcessDictItem blockProgressiveSearch4;

    // 追踪式检索
    @JsonProperty("relevant_tracking_search")
    private AiSearchProcessDictItem relevantTrackingSearch;
}

package com.patsnap.drafting.request.ainoveltysearch;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.patsnap.drafting.request.ainoveltysearch.element.BasicElement;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;

@lombok.Data
@Accessors(chain = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class NoveltySearchElementDataDTO {

    @JsonProperty("feature_text_original")
    private String featureTextOriginal;

    /**
     * topic_search (主题要素)
     * key_search (关键要素)
     * sup_search (补充要素)
     */
    @JsonProperty("feature_type")
    private String featureType;

    @JsonProperty("selected")
    private Boolean selected = true;

    @JsonProperty("express")
    private List<Element> express = new ArrayList<>();

    @lombok.Data
    @Accessors(chain = true)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Element {

        // 单词信息
        @JsonProperty("word")
        private BasicElement word;

        // 扩展关键词
        @JsonProperty("extend")
        private List<BasicElement> extend = new ArrayList<>();

        // IPC分类号
        @JsonProperty("ipc")
        private List<BasicElement> ipc = new ArrayList<>();
    }

}

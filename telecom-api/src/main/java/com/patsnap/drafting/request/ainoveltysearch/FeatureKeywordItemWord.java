package com.patsnap.drafting.request.ainoveltysearch;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> chendepeng
 * @Date 2025/3/13 10:40
 */
@Data
public class FeatureKeywordItemWord {
    // 抽取的特征词
    @JsonProperty("tech_feature")
    private String techFeature;

    // 特征词的翻译
    @JsonProperty("translation")
    private String translation;

    // 特征词的同义词
    @JsonProperty("synonym")
    private List<String> synonym;

    // 同义词打分
    @JsonProperty("synonym_score")
    private List<Integer> synonymScore;
}

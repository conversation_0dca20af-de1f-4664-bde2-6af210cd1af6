package com.patsnap.drafting.request.ainoveltysearch;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.patsnap.drafting.request.aitask.AiTaskReqDTO;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> chendepeng
 * @Date 2025/3/12 19:49
 */
@Data
public class AiSearchResultDTO extends AiTaskReqDTO {

    // 任务 id
    @JsonProperty("task_id")
    private String taskId;

    // 当前任务执行状态
    @JsonProperty("task_status")
    private String taskStatus;

    // 任务总耗时（s）
    @JsonProperty("task_cost")
    private float taskCost;

    // 总共比对的专利数量
    @JsonProperty("total_comparison_patent_num")
    private int totalComparisonPatentNum;

    // 已经找到疑似相似专利数量
    @JsonProperty("find_patent_num")
    private int findPatentNum;

    // 执行的检索次数
    @JsonProperty("excute_times")
    private int excuteTimes;

    // 是否是异步请求，与请求中 is_async 参数一致
    @JsonProperty("is_async")
    private boolean isAsync;

    // 结果最后更新的时间
    @JsonProperty("update_ts")
    private String updateTs;

    // 特征关键词
    @JsonProperty("feature_keywords")
    private List<AiSearchFeatureKeyword> featureKeywords;

    // 最终结果
    @JsonProperty("final_result")
    private List<AiSearchFinalResult> finalResult = new ArrayList<>();

    // 每次对比的结果
    @JsonProperty("process_dict")
    private AiSearchProcessDict processDict;

    @JsonProperty("cc_threshold")
    private float ccThreshold;
}

package com.patsnap.drafting.request.patentinfo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> chendepeng
 * @Date 2025/3/13 17:10
 */
@Data
public class PatentBasicDetailRequestDTO {

    @JsonProperty("patent_ids")
    private List<String> patentIds;

    @JsonProperty("fields")
    private List<String> fields;

    // 决定返回的时间的格式
    private String dateType;

    @JsonProperty("start")
    private int start = 0;

    @JsonProperty("rows")
    private int rows = 100;
}

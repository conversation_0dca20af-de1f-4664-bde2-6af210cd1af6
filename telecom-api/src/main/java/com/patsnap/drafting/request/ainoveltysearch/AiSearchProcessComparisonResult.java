package com.patsnap.drafting.request.ainoveltysearch;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR> chend<PERSON><PERSON>
 * @Date 2025/3/12 20:10
 */
@Data
public class AiSearchProcessComparisonResult {
    // 专利Id
    @JsonProperty("patent_id")
    private String patentId;

    // 本轮检索找到的疑似相似专利数量
    @JsonProperty("find_patent_num")
    private String findPatentNum;

    // 公开度打分
    @JsonProperty("public_score")
    private float publicScore;

    // 特征匹配数量
    @JsonProperty("feature_match_num")
    private int featureMatchNum;

    @JsonProperty("score")
    private int score;
}

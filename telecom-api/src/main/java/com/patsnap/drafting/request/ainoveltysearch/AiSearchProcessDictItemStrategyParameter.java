package com.patsnap.drafting.request.ainoveltysearch;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> chendepeng
 * @Date 2025/3/12 20:08
 */
@Data
public class AiSearchProcessDictItemStrategyParameter {
    // 特征清单
    @JsonProperty("features")
    private List<String> features;

    @JsonProperty("ipc")
    private List<String> ipc;

    @JsonProperty("cpc")
    private List<String> cpc;
}

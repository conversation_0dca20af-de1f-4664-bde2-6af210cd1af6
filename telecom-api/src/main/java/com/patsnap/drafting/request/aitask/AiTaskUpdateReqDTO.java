package com.patsnap.drafting.request.aitask;

import java.util.Map;

import javax.validation.constraints.NotBlank;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/07/22
 */
@ApiModel("AI应用的历史记录更新请求")
@Data
public class AiTaskUpdateReqDTO {

    @ApiModelProperty("任务ID")
    @NotBlank(message = "task id is null")
    private String taskId;
    @ApiModelProperty("任务标题")
    private String title;
    @ApiModelProperty("任务描述")
    private String desc;
    @ApiModelProperty("任务详情")
    private Map<String, Object> detailKeyValues;
}

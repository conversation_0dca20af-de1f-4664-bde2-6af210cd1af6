package com.patsnap.drafting.request.imagesearch;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 图片上传请求 DTO
 *
 * <AUTHOR> Assistant
 * @date 2024/12/19
 */
@Data
@ApiModel("图片上传请求")
public class ImageUploadRequestDTO {

    @ApiModelProperty(value = "文件夹路径，用于组织文件存储结构", example = "ai_drafting/images")
    @JsonProperty("folder_path")
    private String folderPath = "ai_drafting/images";

    @ApiModelProperty(value = "文件名前缀，用于标识文件用途", example = "patent_image")
    @JsonProperty("file_prefix")
    private String filePrefix = "patent_image";

    @ApiModelProperty(value = "是否生成唯一文件名", example = "true")
    @JsonProperty("generate_unique_name")
    private Boolean generateUniqueName = true;
} 
package com.patsnap.drafting.request.aitranslation;

import com.patsnap.drafting.annotation.HistoryField;
import javax.validation.constraints.NotBlank;
import com.patsnap.drafting.request.aitask.AiTaskReqDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/07/22
 */
@ApiModel("AI翻译获取技术主题请求")
@Data
public class AiTransTopicReqDTO extends AiTaskReqDTO {

    @ApiModelProperty("用户输入文本")
    @NotBlank(message = "input is blank")
    @HistoryField("input")
    private String input;
    @ApiModelProperty("输入文本语言")
    @NotBlank(message = "source lang is blank")
    @HistoryField("source_lang")
    private String sourceLang;
    @ApiModelProperty("翻译语言")
    @NotBlank(message = "target lang is blank")
    @HistoryField("target_lang")
    private String targetLang;
}

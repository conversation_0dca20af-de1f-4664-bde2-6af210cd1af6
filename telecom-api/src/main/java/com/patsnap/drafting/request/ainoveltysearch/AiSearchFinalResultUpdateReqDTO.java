package com.patsnap.drafting.request.ainoveltysearch;

import com.patsnap.drafting.request.aitask.AiTaskReqDTO;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * AI 对比文献更新DTO
 * <AUTHOR>
 */
@Data
public class AiSearchFinalResultUpdateReqDTO extends AiTaskReqDTO {

    @JsonProperty("final_result")
    private List<AiSearchFinalResult> finalResult;

    @JsonProperty("is_patent_confirm")
    private boolean isPatentConfirm = false;
}

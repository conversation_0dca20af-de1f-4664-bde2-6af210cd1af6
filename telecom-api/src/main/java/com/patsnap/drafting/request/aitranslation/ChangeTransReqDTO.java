package com.patsnap.drafting.request.aitranslation;

import com.patsnap.drafting.request.aitask.AiTaskReqDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2024/07/30
 */
@ApiModel("更改翻译结果请求")
@Data
public class ChangeTransReqDTO extends AiTaskReqDTO {

    @ApiModelProperty("原文的在全文中的索引")
    @NotNull(message = "original index is null")
    @Min(value = 0, message = "original index must be greater than or equal to 0")
    private Integer originalIndex;

    @ApiModelProperty("重新输入的翻译内容")
    @NotBlank(message = "translation text is blank")
    private String translationText;
}

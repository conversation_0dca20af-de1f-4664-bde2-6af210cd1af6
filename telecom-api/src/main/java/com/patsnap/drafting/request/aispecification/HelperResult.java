package com.patsnap.drafting.request.aispecification;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * @Author: z<PERSON>rui
 * @Date: 2020/7/14 11:33
 */
@Data
public class HelperResult implements Serializable {

    @Serial
    private static final long serialVersionUID = 1177818097555670194L;

    @JsonProperty("cls_tree")
    private ClassificationTree clsTree;
}

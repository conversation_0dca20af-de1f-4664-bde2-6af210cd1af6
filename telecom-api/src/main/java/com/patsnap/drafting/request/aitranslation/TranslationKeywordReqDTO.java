package com.patsnap.drafting.request.aitranslation;

import com.patsnap.drafting.annotation.HistoryField;

import javax.validation.constraints.NotBlank;

import com.patsnap.drafting.request.aitask.AiTaskReqDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/07/23
 */
@ApiModel("AI翻译获取AI词表请求")
@Data
public class TranslationKeywordReqDTO extends AiTaskReqDTO {
    @ApiModelProperty("用户输入文本")
    @NotBlank(message = "input is blank")
    private String input;
    @ApiModelProperty("技术主题")
    @NotBlank(message = "tech topic is blank")
    @HistoryField("tech_topic")
    private String techTopic;
    @ApiModelProperty("翻译语言")
    @NotBlank(message = "target lang is blank")
    private String targetLang;
    @ApiModelProperty("输入文本语言")
    @NotBlank(message = "source lang is blank")
    private String sourceLang;
}

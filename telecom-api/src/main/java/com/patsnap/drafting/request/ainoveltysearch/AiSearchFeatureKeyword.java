package com.patsnap.drafting.request.ainoveltysearch;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> chendepeng
 * @Date 2025/3/12 19:55
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class AiSearchFeatureKeyword implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    // 抽取的特征词
    @JsonProperty("tech_feature")
    private String techFeature;

    // 特征词的翻译
    @JsonProperty("translation")
    private String translation;

    // 特征词的同义词
    @JsonProperty("synonym")
    private List<String> synonym;

    // 同义词打分
    @JsonProperty("synonym_score")
    private List<Integer> synonymScore;

    @JsonProperty("words")
    private List<FeatureKeywordItem> words;
}

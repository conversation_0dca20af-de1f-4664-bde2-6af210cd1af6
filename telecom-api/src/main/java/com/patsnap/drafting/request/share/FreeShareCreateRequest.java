package com.patsnap.drafting.request.share;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class FreeShareCreateRequest {
    
    @ApiModelProperty("任务ID")
    @NotNull
    @JsonProperty("task_id")
    private String taskId;
    
    @ApiModelProperty("密码（可以不传）")
    @JsonProperty("password")
    private String password;
}

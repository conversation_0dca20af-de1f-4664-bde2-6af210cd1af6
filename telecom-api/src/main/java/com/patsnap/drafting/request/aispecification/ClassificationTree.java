package com.patsnap.drafting.request.aispecification;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * @Author: z<PERSON><PERSON>i
 * @Date: 2020/7/14 11:41
 */
@Data
public class ClassificationTree implements Serializable {

    @Serial
    private static final long serialVersionUID = -310916481565506886L;

    private String id;
    @JsonProperty("lang_desc_map")
    private ClassificationDescription langDescMap;
    private List<ClassificationTree> children;
}

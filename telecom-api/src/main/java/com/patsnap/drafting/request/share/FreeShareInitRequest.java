package com.patsnap.drafting.request.share;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

import javax.validation.constraints.AssertTrue;

@Data
public class FreeShareInitRequest {
    
    @ApiModelProperty("分享ID")
    @JsonProperty("share_id")
    private String shareId;
    
    @ApiModelProperty("任务ID")
    @JsonProperty("task_id")
    private String taskId;
    
    @AssertTrue(message = "share_id and task_id cannot both be empty")
    @ApiModelProperty(hidden = true)
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    public boolean isEitherShareIdOrTaskIdProvided() {
        return !(StringUtils.isEmpty(shareId) && StringUtils.isEmpty(taskId));
    }
}

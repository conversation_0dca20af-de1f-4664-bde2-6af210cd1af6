package com.patsnap.drafting.request.aiftosearch;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.patsnap.drafting.request.aitask.AiTaskReqDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> chendepeng
 * @Date 2025/4/3 15:24
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel("fto提交任务请求参数")
@Data
public class FtoSearchSubmitRequestDTO extends AiTaskReqDTO {

    @ApiModelProperty("用户输入")
    @NotNull
    private String input;

    private String inputLang;

    private List<String> country = new ArrayList<>(); // 受理局列表

    @JsonProperty("exclude_company")
    private List<String> excludeCompany = new ArrayList<>(); // 排除的公司列表

    @JsonProperty("include_company")
    private List<String> includeCompany = new ArrayList<>(); // 包含的公司列表

    @JsonProperty("legal_status")
    private List<Integer> legalStatus = new ArrayList<>(); // 法律状态列表

    @JsonProperty("apd_start")
    private String apdStart = ""; // 申请日开始

    @JsonProperty("apd_end")
    private String apdEnd = ""; // 申请日结束
}

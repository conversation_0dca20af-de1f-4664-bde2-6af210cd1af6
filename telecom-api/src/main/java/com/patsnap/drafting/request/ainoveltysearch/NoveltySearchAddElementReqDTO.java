package com.patsnap.drafting.request.ainoveltysearch;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.patsnap.drafting.request.aitask.AiTaskReqDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 新增检索要素请求DTO
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class NoveltySearchAddElementReqDTO extends AiTaskReqDTO {

    @JsonProperty("add_word")
    private String addWord;
}
package com.patsnap.drafting.request.ainoveltysearch;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.patsnap.drafting.request.aitask.AiTaskReqDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

@EqualsAndHashCode(callSuper = true)
@ApiModel("提取技术特征请求参数")
@Data
public class FeatureExactionRequestDTO extends AiTaskReqDTO {
    
    @ApiModelProperty("用户输入")
    @NotNull
    private String input;

    private String inputLang;

    @JsonProperty("pbd_start")
    private String pbdStart = "";

    @JsonProperty("pbd_end")
    private String pbdEnd = "";
    
}

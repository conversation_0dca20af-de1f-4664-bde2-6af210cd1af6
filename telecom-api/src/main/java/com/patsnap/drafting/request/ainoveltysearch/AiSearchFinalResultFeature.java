package com.patsnap.drafting.request.ainoveltysearch;

import java.io.Serial;
import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.patsnap.core.common.utils.StrUtils;
import lombok.Data;

/**
 * <AUTHOR> chendepeng
 * @Date 2025/3/12 20:00
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class AiSearchFinalResultFeature implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    // 特征词（来自输入文本）
    @JsonProperty("tech_feature")
    private String techFeature;

    // 目标文本找到的特征词
    @JsonProperty("comparison_feature")
    private String comparisonFeature;

    // 目标文本找到的特征词
    @JsonProperty("compare_feature")
    private String compareFeature;

    // tech_feature与comparison_feature的相似度
    @JsonProperty("score")
    private int score;

    // 特征的公开度
    @JsonProperty("public_score")
    private float publicScore;

    @JsonProperty("similar")
    private boolean similar = true;

    // 权利要求编号(权1，权2，权3)
    @JsonProperty("claim_num")
    private Integer claimNum;

    // 权利要求类型(独权，从权)
    @JsonProperty("claim_type")
    private String claimType;

    public String getComparisonFeature() {
        if (comparisonFeature != null) {
            return comparisonFeature;
        }
        return getCompareFeature();
    }
}

package com.patsnap.drafting.request.ainoveltysearch;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import java.util.List;

/**
 * <AUTHOR> chendepeng
 * @Date 2025/3/12 20:02
 */
@Data
public class AiSearchProcessDictItem {
    // 本轮检索的策略，用于前端展示
    @JsonProperty("strategy")
    private AiSearchProcessDictItemStrategy strategy;

    // 本轮检索耗时
    @JsonProperty("cost")
    private float cost;

    // 本轮检索一共比对的专利数量
    @JsonProperty("comparison_patent_num")
    private int comparisonPatentNum;

    // 本轮检索找到的疑似相似专利数量
    @JsonProperty("find_patent_num")
    private int findPatentNum;

    // 疑似专利信息
    @JsonProperty("comparison_result")
    private List<AiSearchProcessComparisonResult> comparisonResult;

    // 状态
    @JsonProperty("status")
    private String status;
}

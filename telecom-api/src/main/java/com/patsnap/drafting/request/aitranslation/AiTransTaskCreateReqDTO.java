package com.patsnap.drafting.request.aitranslation;

import javax.validation.constraints.NotBlank;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel("AI任务创建请求")
@Data
public class AiTransTaskCreateReqDTO {

    @ApiModelProperty("任务ID")
    @NotBlank(message = "task id is null")
    private String taskId;
    
    @ApiModelProperty("类型")
    @NotBlank(message = "type is null")
    private String type;

    @ApiModelProperty("用户输入")
    private AiTransCreateReqDTO content;


}

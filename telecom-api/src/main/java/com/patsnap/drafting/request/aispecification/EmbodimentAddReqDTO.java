package com.patsnap.drafting.request.aispecification;

import com.patsnap.drafting.request.GenerateContentRequestDTO;

import java.util.List;

import javax.validation.constraints.NotEmpty;


import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class EmbodimentAddReqDTO extends GenerateContentRequestDTO {

    @NotEmpty(message = "rules is empty")
    private List<EmbodimentReplaceRule> rules;
    private String contentType = "embodiment_add";
}

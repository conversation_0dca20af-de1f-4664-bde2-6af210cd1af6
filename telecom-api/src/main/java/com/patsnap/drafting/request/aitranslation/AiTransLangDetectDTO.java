package com.patsnap.drafting.request.aitranslation;

import javax.validation.constraints.NotBlank;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/02/19
 */
@ApiModel("AI翻译获取文本语言请求")
@Data
public class AiTransLangDetectDTO {

    @ApiModelProperty("用户输入文本")
    @NotBlank(message = "input is blank")
    private String input;
}

package com.patsnap.drafting.request.noveltyagent;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.patsnap.drafting.request.ainoveltysearch.AiSearchAgentFeature;
import com.patsnap.drafting.request.aitask.AiTaskReqDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Size;
import java.util.List;

@Data
public class AiSearchAgentStartRequest extends AiTaskReqDTO {

    @Size(min = 1)
    @ApiModelProperty("确认过的技术特征")
    private List<AiSearchAgentFeature> features;
    
    @ApiModelProperty("用户输入")
    @JsonProperty("question")
    private String text;
}

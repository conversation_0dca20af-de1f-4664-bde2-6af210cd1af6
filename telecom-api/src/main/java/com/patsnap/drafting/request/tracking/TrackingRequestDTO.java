package com.patsnap.drafting.request.tracking;

import com.patsnap.core.common.track.ModuleType;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
public class TrackingRequestDTO {

    @NotNull
    private ModuleType moduleType;

    @NotBlank
    private String eventType;

    private String sourceType;

    private String otherParam;

    private String eventDetailStr;
}

package com.patsnap.drafting.request.aispecification;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.patsnap.drafting.request.aitask.AiTaskReqDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * desc
 *
 * <AUTHOR>
 * @Date 2025/6/19 15:44
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class EmbodimentOutlineModifyReqDTO extends AiTaskReqDTO {

    @ApiModelProperty("权利要求文本")
    @JsonProperty("claim_text")
    private String claimText;

    @ApiModelProperty("权利要求编号列表")
    @JsonProperty("claim_numbers")
    private List<Integer> claimNumbers;
}

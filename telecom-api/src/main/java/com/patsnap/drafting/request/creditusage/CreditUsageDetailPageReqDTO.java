package com.patsnap.drafting.request.creditusage;

import com.patsnap.drafting.BasePageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@ApiModel("用户积分明细分页请求对象")
@Data
public class CreditUsageDetailPageReqDTO extends BasePageRequest {

    @ApiModelProperty("开始时间")
    private Long startTime;

    @ApiModelProperty("结束时间")
    private Long endTime;
}

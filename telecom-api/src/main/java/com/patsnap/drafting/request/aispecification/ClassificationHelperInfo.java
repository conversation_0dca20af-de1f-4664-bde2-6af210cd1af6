package com.patsnap.drafting.request.aispecification;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * @Author: zhourui
 * @Date: 2020/7/15 10:45
 */
@Data
public class ClassificationHelperInfo implements Serializable {

    @Serial
    private static final long serialVersionUID = 4889035191127645286L;

    private String id;
    private String parent;
    @JsonProperty("lang_desc_map")
    private ClassificationDescription langDescMap;
    private Boolean hasChildren = Boolean.FALSE;
}

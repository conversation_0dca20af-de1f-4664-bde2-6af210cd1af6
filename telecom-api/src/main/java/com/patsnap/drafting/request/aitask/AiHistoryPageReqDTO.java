package com.patsnap.drafting.request.aitask;


import com.patsnap.drafting.BasePageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@ApiModel("AI历史分页请求对象")
@Data
public class AiHistoryPageReqDTO extends BasePageRequest {

    @ApiModelProperty("任务类型(AI_TRANSLATION, PATENT_DISCLOSURE)")
    private String type;
}

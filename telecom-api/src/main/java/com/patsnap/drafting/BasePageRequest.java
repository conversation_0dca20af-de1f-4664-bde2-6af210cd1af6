package com.patsnap.drafting;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 分页信息
 */
@ApiModel("分页信息")
@Data
public class BasePageRequest {

    @ApiModelProperty("当前显示页")
    @NotNull(message = "page is null")
    @Min(value = 1, message = "page must be greater than 0")
    private Integer page;
    
    @ApiModelProperty("每页显示数")
    @NotNull(message = "limit is null")
    @Min(value = 1, message = "limit must be greater than 0")
    private Integer limit;

    public <T> Page<T> page() {
        return new Page<>(page, limit);
    }
}

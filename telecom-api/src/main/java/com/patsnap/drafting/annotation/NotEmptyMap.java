package com.patsnap.drafting.annotation;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

/**
 * 用于校验Map中的key和value都不能为空
 */
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Constraint(validatedBy = NotEmptyMapValidator.class)
public @interface NotEmptyMap {
    /**
     * 校验失败时的提示信息
     */
    String message() default "Map中的key和value不能为空";

    /**
     * 分组校验
     */
    Class<?>[] groups() default {};

    /**
     * 负载
     */
    Class<? extends Payload>[] payload() default {};
} 
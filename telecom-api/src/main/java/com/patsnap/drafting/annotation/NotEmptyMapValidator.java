package com.patsnap.drafting.annotation;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.Map;
import java.util.Optional;

/**
 * NotEmptyMap注解的验证器实现
 * 支持Map<String, Object>类型的校验
 * 校验规则：
 * 1. Map不能为null或空
 * 2. key不能为null或空字符串
 * 3. value不能为null，如果是String类型则不能为空字符串
 */
public class NotEmptyMapValidator implements ConstraintValidator<NotEmptyMap, Map<String, Object>> {

    @Override
    public boolean isValid(final Map<String, Object> map, ConstraintValidatorContext context) {
        if (map == null || map.isEmpty()) {
            return false;
        }
        
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            if (!isValidEntry(entry)) {
                return false;
            }
        }
        return true;
    }
    
    private boolean isValidEntry(Map.Entry<String, Object> entry) {
        return isValidKey(entry.getKey()) && isValidValue(entry.getValue());
    }
    
    private boolean isValidKey(String key) {
        return Optional.ofNullable(key)
                .map(String::trim)
                .filter(k -> !k.isEmpty())
                .isPresent();
    }
    
    private boolean isValidValue(Object value) {
        if (value == null) {
            return false;
        }
        // 如果值是String类型，需要特殊处理
        if (value instanceof String) {
            return !((String) value).trim().isEmpty();
        }
        // 其他类型的Object，只要不为null就认为是有效的
        return true;
    }
} 

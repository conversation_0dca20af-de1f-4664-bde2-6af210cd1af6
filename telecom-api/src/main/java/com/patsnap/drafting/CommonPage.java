package com.patsnap.drafting;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;

import com.baomidou.mybatisplus.core.metadata.IPage;

import lombok.Data;

/**
 * 分页信息
 *
 * <AUTHOR>
 * @date 2024/07/22
 */
@Data
public class CommonPage<T> {

    private Integer page = 1;
    private Integer limit = 20;
    private Integer totalPage = 0;
    private Long total = 0L;
    private List<T> list = new ArrayList<>();

    public static <T, U> CommonPage<U> convertPage(IPage<T> pageInfo, Function<T, U> transformer) {
        CommonPage<U> result = new CommonPage<>();
        result.setTotalPage((int) pageInfo.getPages());
        result.setPage((int) pageInfo.getCurrent());
        result.setLimit((int) pageInfo.getSize());
        result.setTotal(pageInfo.getTotal());
        if (pageInfo.getRecords() == null) {
            return result;
        }
        result.setList(pageInfo.getRecords().stream().map(transformer).toList());
        return result;
    }
}

configs.com.patsnap.app.region=CN


## Redis config

#Postgre SQL config
spring.datasource.dynamic.datasource.telecom.url=***************************************************************************************************

#S3 bucket
configs.com.patsnap.analytics.storage.bucket-domain=patsnap-storage.cdn.zhihuiya.com

xxl.job.admin.addresses=http://w-platform-xxl-job-service.platform/xxl-job-admin
com.patsnap.compute.similar.tdoc-url=http://s-patsnaprd-tdoc-to-tdoc.patsnaprd/compute/tdoc_to_tdoc/
com.patsnap.analytics.service.data-api-url=http://s-platform-patent-api.platform/openapi/

com.patsnap.analytics.service.email.host=https://eureka.zhihuiya.com

configs.com.patsnap.compute.ipc.url=http://s-patsnaprd-ipc-codefull-cn.patsnaprd/compute/ipc_codefull_cn/

configs.com.patsnap.data.sqs.ai-task.queue-name=core_telecom_ai_task_prod
configs.com.patsnap.data.sqs.ai-task.credentials.region=cn-northwest-1
configs.com.patsnap.data.sqs.ai-task.credentials.uri=https://sqs.cn-northwest-1.amazonaws.com.cn
configs.com.patsnap.data.sqs.ai-task.credentials.access-key=
configs.com.patsnap.data.sqs.ai-task.credentials.secret-key=

# fto history data date
configs.com.patsnap.drafting.fto.history-date=1753354800000
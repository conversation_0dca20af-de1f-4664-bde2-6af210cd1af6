configs.com.patsnap.app.region=CN

configs.com.patsnap.resttemplate.logging.max-payload-length=10240

configs.com.patsnap.drafting.ai-translation.text-max-length=100000

## Redis config
#configs.com.patsnap.redis.redisson.cluster-servers-config.node-addresses[0]=redis://rc-core-ci.patsnap.info:6379

#Postgre SQL config
spring.datasource.dynamic.datasource.telecom.url=***************************************************************************************************

#S3 bucket
configs.com.patsnap.analytics.storage.bucket-domain=qa-patsnap-import-workspace.cdn.zhihuiya.com

com.patsnap.compute.similar.tdoc-url=http://s-patsnaprd-tdoc-to-tdoc.patsnaprd/compute/tdoc_to_tdoc/

com.patsnap.analytics.service.patentapi-url=http://qa-s-platform-patent-api.patsnap.info/openapi

com.patsnap.analytics.service.email.host=https://qa-eureka.zhihuiya.com

configs.com.patsnap.compute.ipc.url=http://s-patsnaprd-ipc-codefull-cn.patsnaprd/compute/ipc_codefull_cn/
configs.com.patsnap.claimparser.service.url=http://s-patsnaprd-claimparser.patsnaprd/claimparser-service/claim_parser

#specification drafting
configs.com.patsnap.compute.specification.url=http://s-patsnaprd-patent-drafting.patsnaprd/compute/patent_drafting/

xxl.job.admin.addresses=http://w-platform-xxl-job-service.platform/xxl-job-admin

# AI Task SQS

configs.com.patsnap.data.sqs.ai-task.queue-name=core_telecom_ai_task_qa
configs.com.patsnap.data.sqs.ai-task.credentials.access-key=
configs.com.patsnap.data.sqs.ai-task.credentials.secret-key=
configs.com.patsnap.data.sqs.ai-task.credentials.region=cn-northwest-1
configs.com.patsnap.data.sqs.ai-task.credentials.uri=https://sqs.cn-northwest-1.amazonaws.com.cn

# fto history data date
configs.com.patsnap.drafting.fto.history-date=1752163200000

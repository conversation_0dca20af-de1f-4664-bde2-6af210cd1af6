configs.com.patsnap.app.region=US

#Postgre SQL config
spring.datasource.dynamic.datasource.telecom.url=***************************************************************************************************

#COS bucket
configs.com.patsnap.cos.bucket-name=
configs.com.patsnap.cos.region=na-ashburn

#S3 bucket
configs.com.patsnap.analytics.region=US
configs.com.patsnap.analytics.storage.bucket-domain=storage.cdn.patsnap.com
configs.com.patsnap.data.sqs.ai-task.queue-name=core_telecom_ai_task_prod
configs.com.patsnap.data.sqs.ai-task.credentials.region=us-east-1
configs.com.patsnap.data.sqs.ai-task.credentials.uri=https://sqs.us-east-1.amazonaws.com.cn
configs.com.patsnap.data.sqs.ai-task.credentials.access-key=
configs.com.patsnap.data.sqs.ai-task.credentials.secret-key=


xxl.job.admin.addresses=http://w-platform-xxl-job-service.platform/xxl-job-admin
com.patsnap.compute.similar.tdoc-url=http://s-patsnaprd-tdoc-to-tdoc.patsnaprd/compute/tdoc_to_tdoc/
com.patsnap.analytics.service.data-api-url=http://s-platform-patent-api.platform/openapi/
com.patsnap.analytics.service.email.host=https://eureka.patsnap.com

configs.com.patsnap.account.eu.customize.companies=456ee925e2f14329a9e3a230ab412f08,2d5d3855e6cf4ceea0b9dd68585a6b48
configs.com.patsnap.account.eu.customize.analytics.host=https://analytics-siemens.patsnap.eu

# fto history data date
configs.com.patsnap.drafting.fto.history-date=*************


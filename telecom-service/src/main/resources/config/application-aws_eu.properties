configs.com.patsnap.app.region=EU

#Postgre SQL config
spring.datasource.dynamic.datasource.telecom.url=******************************************************************************************************

#COS bucket
configs.com.patsnap.cos.bucket-name=
configs.com.patsnap.cos.region=na-ashburn

# S3 bucket
configs.com.patsnap.analytics.region=EU
configs.com.patsnap.analytics.storage.bucket-domain=storage.cdn.patsnap.eu

# service url
xxl.job.admin.addresses=http://w-platform-xxl-job-service.platform/xxl-job-admin
com.patsnap.compute.similar.tdoc-url=http://s-patsnaprd-tdoc-to-tdoc.patsnaprd/compute/tdoc_to_tdoc/
com.patsnap.analytics.service.data-api-url=http://s-platform-patent-api.platform/openapi/
configs.com.patsnap.compute.patsnap-check-input.url=http://aws-us-prod-s-patsnaprd-patsnap-check-input.patsnap.private/compute/patsnap_check_input/

com.patsnap.analytics.service.gpt.base-url=http://aws-us-prod-s-patsnaprd-gateway.patsnap.private/v1
com.patsnap.analytics.service.ai-lab-disclosure-url=http://aws-us-prod-s-patsnaprd-ai-lab-disclosure.patsnap.private/compute/ai_lab_disclosure/
com.patsnap.analytics.service.email.host=https://eureka.patsnap.eu

configs.com.patsnap.compute.lang-detect.url=http://aws-us-prod-s-patsnaprd-lang-detect.patsnap.private/compute/lang_detect/
configs.com.patsnap.compute.cpc.url=http://aws-us-prod-s-patsnaprd-cpc-codefull-en.patsnap.private/compute/cpc_codefull_en/
#specification drafting
configs.com.patsnap.compute.specification.url=http://aws-us-prod-s-patsnaprd-patent-drafting.patsnap.private/compute/patent_drafting/
configs.com.patsnap.compute.ipc.url=http://aws-us-prod-s-patsnaprd-ipc-codefull-cn.patsnap.private/compute/ipc_codefull_cn/
configs.com.patsnap.claimparser.service.url=http://aws-us-prod-s-patsnaprd-claimparser.patsnap.private/claimparser-service/claim_parser
configs.com.patsnap.patent_api.url=http://aws-us-prod-s-platform-patent-api.patsnap.private/openapi/3.2.0
com.patsnap.analytics.service.draw-narrator-url=http://aws-us-prod-s-patsnaprd-drawing-narrator.patsnap.private/compute

com.patsnap.analytics.service.ai-search-url2=http://aws-us-prod-s-search-ai-search-v2.patsnap.private
com.patsnap.analytics.service.ai-novelty-search-compute-url-v2=http://aws-us-prod-s-patsnaprd-novelty-ai-search-v2.patsnap.private/compute

# AI Task SQS
configs.com.patsnap.data.sqs.ai-task.queue-name=core_telecom_ai_task_prod
configs.com.patsnap.data.sqs.ai-task.credentials.region=eu-central-1
configs.com.patsnap.data.sqs.ai-task.credentials.uri=https://sqs.eu-central-1.amazonaws.com.cn
configs.com.patsnap.data.sqs.ai-task.credentials.access-key=
configs.com.patsnap.data.sqs.ai-task.credentials.secret-key=

# fto history data date
configs.com.patsnap.drafting.fto.history-date=1753408800000



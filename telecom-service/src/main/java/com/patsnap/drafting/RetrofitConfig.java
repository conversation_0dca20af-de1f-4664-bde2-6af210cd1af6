package com.patsnap.drafting;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.patsnap.common.request.CorrelationIdHolder;
import com.patsnap.common.request.RequestFromHolder;
import com.patsnap.common.request.RoleIdsHolder;
import com.patsnap.common.request.TenantIdHolder;
import com.patsnap.common.request.UserIdHolder;
import com.patsnap.core.common.request.SiteLangHolder;
import com.patsnap.drafting.client.api.AgentApi;
import com.patsnap.drafting.client.api.AiNoveltyComputeApi;
import com.patsnap.drafting.client.api.AiNoveltyComputeV2Api;
import com.patsnap.drafting.client.api.AiSearch2Api;
import com.patsnap.drafting.client.api.AiSearchApi;
import com.patsnap.drafting.client.api.BasicSearchApi;
import com.patsnap.drafting.client.api.DrawNarratorApi;
import com.patsnap.drafting.client.api.EurekaApi;
import com.patsnap.drafting.client.api.OpenApi;
import okhttp3.Interceptor;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.logging.HttpLoggingInterceptor;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import retrofit2.Retrofit;
import retrofit2.converter.jackson.JacksonConverterFactory;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * Retrofit配置类
 *
 * <AUTHOR>
 * @version Id: RetrofitConfig, v 0.1 2024/4/2 10:13 ryan Exp $
 */
@Configuration
public class RetrofitConfig {
    
    public static final String X_API_VERSION = "X-API-Version";
    
    public static final String X_PATSNAP_VERSION = "X-Patsnap-Version";
    
    public static final String SERVICE_NAME = "s-analytics-search";
    
    @Value("${com.patsnap.analytics.service.eureka-url:http://s-core-eureka.core/eureka}")
    private String eurekaUrl;
    
    @Value("${com.patsnap.analytics.service.ai-search-url}")
    private String aiSearchUrl;

    @Value("${com.patsnap.analytics.service.ai-search-url2}")
    private String aiSearch2Url;
    
    @Value("${com.patsnap.analytics.service.ai-agent-url}")
    private String agentUrl;
    
    @Value("${com.patsnap.analytics.service.ai-novelty-search-compute-url}")
    private String aiNoveltySearchComputeUrl;

    @Value("${com.patsnap.analytics.service.ai-novelty-search-compute-url-v2}")
    private String aiNoveltySearchComputeUrlV2;
    
    @Value("${configs.com.patsnap.open-api.url}")
    private String openApiUrl;

    @Value("${com.patsnap.analytics.service.basic-url}")
    private String basicServiceApiUrl;
    
    @Value("${com.patsnap.analytics.service.draw-narrator-url}")
    private String drawNarratorUrl;
    
    @Bean
    public EurekaApi initEurekaAPI() {
        return getRetrofit(eurekaUrl + "/", VersionId.V_1_0).create(EurekaApi.class);
    }
    
    @Bean
    public OpenApi initOpenAPI() {
        return getRetrofit(openApiUrl + "/", VersionId.V_1_0).create(OpenApi.class);
    }
    
    @Bean
    public AiSearchApi initAiSearchAPI() {
        return getRetrofit(aiSearchUrl + "/", VersionId.V_1_0, VersionId.P_V_1).create(AiSearchApi.class);
    }
    @Bean
    public AiSearch2Api initAiSearch2API() {
        return getRetrofit(aiSearch2Url + "/", VersionId.V_1_0, VersionId.P_V_1).create(AiSearch2Api.class);
    }
    
    @Bean
    public AgentApi initAgentAPI() {
        return getRetrofit(agentUrl + "/", VersionId.V_1_0, VersionId.P_V_1).create(AgentApi.class);
    }
    
    @Bean
    public AiNoveltyComputeApi initAiNoveltyComputeAPI() {
        return getRetrofit(aiNoveltySearchComputeUrl + "/", VersionId.V_1_0, VersionId.P_V_1).create(AiNoveltyComputeApi.class);
    }

    @Bean
    public AiNoveltyComputeV2Api initAiNoveltyComputeV2API() {
        return getRetrofit(aiNoveltySearchComputeUrlV2 + "/", VersionId.V_1_0, VersionId.P_V_1).create(AiNoveltyComputeV2Api.class);
    }

    @Bean
    public BasicSearchApi initBasicSearchAPI() {
        return getRetrofit(basicServiceApiUrl + "/", VersionId.V_1_0).create(BasicSearchApi.class);
    }
    
    @Bean
    public DrawNarratorApi initDrawNarratorAPI() {
        return getRetrofit(drawNarratorUrl + "/", VersionId.V_1_0, VersionId.P_V_1).create(DrawNarratorApi.class);
    }

    private Retrofit getRetrofit(String url, String version) {
        return getRetrofit(url, version, null);
    }
    
    private Retrofit getRetrofit(String url, String version, String patsnapVersion) {
        // 创建自定义拦截器以添加Header
        Interceptor headerInterceptor = chain -> {
            Request originalRequest = chain.request();
            List<String> userRoles = Optional.ofNullable(RoleIdsHolder.get()).orElseGet(List::of);
            Request newRequest = originalRequest.newBuilder()
                    .addHeader(UserIdHolder.X_USER_ID, Optional.ofNullable(UserIdHolder.get()).orElse(StringUtils.EMPTY))
                    .addHeader(TenantIdHolder.X_TENANT_ID, Optional.ofNullable(TenantIdHolder.get()).orElse(StringUtils.EMPTY))
                    .addHeader(CorrelationIdHolder.X_CORRELATION_ID, Optional.ofNullable(CorrelationIdHolder.get()).orElse(StringUtils.EMPTY))
                    .addHeader(RoleIdsHolder.X_USER_ROLES, String.join(",", userRoles))
                    .addHeader(SiteLangHolder.X_SITE_LANG, SiteLangHolder.get())
                    .addHeader(X_API_VERSION, version)// 添加自定义Header
                    .addHeader(RequestFromHolder.X_PATSNAP_FROM, SERVICE_NAME)// 添加自定义Header
                    .method(originalRequest.method(), originalRequest.body())
                    .build();
            if (Objects.nonNull(patsnapVersion)) {
                newRequest = newRequest.newBuilder().addHeader(X_PATSNAP_VERSION, patsnapVersion).build();
            }
            return chain.proceed(newRequest);
        };
        
        // 创建日志拦截器并设置级别
        HttpLoggingInterceptor loggingInterceptor = new HttpLoggingInterceptor();
        loggingInterceptor.setLevel(HttpLoggingInterceptor.Level.BODY);
        
        // 创建OkHttpClient并设置拦截器
        OkHttpClient okHttpClient = new OkHttpClient.Builder()
                .addInterceptor(headerInterceptor)
                .addInterceptor(loggingInterceptor)
                .hostnameVerifier((hostname, session) -> true)
                .connectTimeout(300, TimeUnit.SECONDS)
                .readTimeout(300, TimeUnit.SECONDS)
                .build();
        
        // 创建并返回Retrofit实例
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.setPropertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE);
        return new Retrofit.Builder()
                .baseUrl(url)
                .addConverterFactory(JacksonConverterFactory.create(objectMapper))
                .client(okHttpClient)
                .build();
    }
    
    static class VersionId {
        public static final String P_V_1 = "v1";
        public static final String V_1_0 = "1.0";
    }
}


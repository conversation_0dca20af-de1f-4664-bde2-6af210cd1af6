package com.patsnap.drafting.controller;

import com.patsnap.core.spring.boot.autoconfigure.web.entity.CommonResponse;
import com.patsnap.drafting.CommonPage;
import com.patsnap.drafting.manager.creditusage.CreditUsageDetailManager;
import com.patsnap.drafting.request.creditusage.CreditUsageDetailPageReqDTO;
import com.patsnap.drafting.response.creditusage.CreditUsageDetailDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import javax.validation.Valid;

@Api(tags = "用户积分明细数据")
@RestController
@RequiredArgsConstructor
@RequestMapping("/credit-usage-detail")
public class CreditUsageDetailController {

    private final CreditUsageDetailManager creditUsageDetailManager;

    @ApiOperation("分页获取历史记录列表")
    @GetMapping("/list")
    public CommonResponse<CommonPage<CreditUsageDetailDTO>> getCreditUsageDetailList(@Valid CreditUsageDetailPageReqDTO request) {
        return CommonResponse.<CommonPage<CreditUsageDetailDTO>>builder().withData(creditUsageDetailManager.getCreditUsageDetailList(request))
                .build();
    }
}

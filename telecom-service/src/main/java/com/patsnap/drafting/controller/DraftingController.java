package com.patsnap.drafting.controller;

import com.patsnap.core.spring.boot.autoconfigure.web.entity.CommonResponse;
import com.patsnap.drafting.manager.aitask.AiTaskManager;
import com.patsnap.drafting.manager.init.AIDraftingInitManager;
import com.patsnap.drafting.manager.refrence.PatentReferenceManager;
import com.patsnap.drafting.request.aitask.SensitiveReqDTO;
import com.patsnap.drafting.response.aidisclosure.ReferencesReqDTO;
import com.patsnap.drafting.response.init.AiDraftingInitResDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import javax.validation.Valid;
import java.util.Collection;
import java.util.List;
import java.util.Map;

@Api(tags = "AI 撰写 init")
@RestController
@RequestMapping("/ai-drafting")
@RequiredArgsConstructor
public class DraftingController {

    private final AIDraftingInitManager aiDraftingInitManager;
    private final PatentReferenceManager patentReferenceManager;
    private final AiTaskManager aiTaskManager;

    @ApiOperation("初始化接口")
    @GetMapping("/init")
    public CommonResponse<AiDraftingInitResDTO> init() {
        return CommonResponse.<AiDraftingInitResDTO>builder().withData(aiDraftingInitManager.init()).build();
    }
    
    @ApiOperation("专利引用列表获取")
    @PostMapping(value = "/patent/reference")
    public CommonResponse<Collection<Map<String, Object>>> getReference(@Valid @RequestBody ReferencesReqDTO request) {
        return CommonResponse.<Collection<Map<String, Object>>>builder().withData(patentReferenceManager.getReference(request)).build();
    }
    
    @ApiOperation("检查敏感词")
    @PostMapping("/sensitive-word")
    public CommonResponse<Boolean> checkSensitiveWords(@Valid @RequestBody SensitiveReqDTO request) {
        aiTaskManager.checkSensitiveWords(List.of(request.getContent()));
        return CommonResponse.<Boolean>builder().withData(Boolean.TRUE).build();
    }
}

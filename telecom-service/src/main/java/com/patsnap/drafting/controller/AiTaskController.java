package com.patsnap.drafting.controller;

import com.patsnap.core.spring.boot.autoconfigure.web.entity.CommonResponse;
import com.patsnap.drafting.CommonPage;
import com.patsnap.drafting.enums.task.AiTaskContentTypeEnum;
import com.patsnap.drafting.manager.aitask.AiTaskManager;
import com.patsnap.drafting.repository.aitask.entity.AnalyticsAiTaskContentPO;
import com.patsnap.drafting.request.aitask.AiHistoryPageReqDTO;
import com.patsnap.drafting.request.aitask.AiTaskContentHideUpdateReqDTO;
import com.patsnap.drafting.request.aitask.AiTaskContentUpdateReqDTO;
import com.patsnap.drafting.request.aitask.AiTaskCreateReqDTO;
import com.patsnap.drafting.request.aitask.AiTaskExportReqDTO;
import com.patsnap.drafting.request.aitranslation.AiTransLangDetectDTO;
import com.patsnap.drafting.response.aihistory.AiHistoryDTO;
import com.patsnap.drafting.response.aihistory.AiHistoryStepDTO;
import com.patsnap.drafting.transfer.export.enitty.ExportResult;
import com.patsnap.drafting.transfer.export.manager.ExportManager;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

@Api(tags = "AI应用的历史任务")
@RestController
@RequestMapping("/ai/task")
@AllArgsConstructor
public class AiTaskController {

    private final AiTaskManager aiTaskManager;

    private final ExportManager exportManager;

    @ApiOperation("创建任务")
    @PostMapping
    public CommonResponse<String> createTask(@Valid @RequestBody AiTaskCreateReqDTO reqDTO) {
        return CommonResponse.<String>builder().withData(aiTaskManager.checkSensitiveWordsAndCreateTask(reqDTO)).build();
    }

    @ApiOperation("语言检测")
    @PostMapping("/lang-detect")
    public CommonResponse<String> langDetect(
            @Valid @NotNull @RequestBody AiTransLangDetectDTO aidTransLangDetectDTO) {
        return CommonResponse.<String>builder()
                .withData(aiTaskManager.langDetect(aidTransLangDetectDTO.getInput())).build();
    }
    
    @ApiOperation("更新任务主表标题")
    @PutMapping("/{task_id}/title")
    public CommonResponse<Object> updateTaskInfo(@PathVariable("task_id") String taskId,
            @Valid @RequestBody AiTaskContentUpdateReqDTO reqDTO) {
        aiTaskManager.updateTaskTitle(taskId, reqDTO.getContent().toString());
        return CommonResponse.builder().build();
    }
    
    @ApiOperation("更新任务内容表")
    @PutMapping("/{task_id}/content/{content_type}")
    public CommonResponse<Object> updateTaskContent(@PathVariable("task_id") String taskId,
            @PathVariable("content_type") String contentType, @Valid @RequestBody AiTaskContentUpdateReqDTO reqDTO) {
        aiTaskManager.checkAndUpdateTaskContent(taskId, AiTaskContentTypeEnum.fromType(contentType), reqDTO.getContent());
        return CommonResponse.builder().build();
    }
    
    @ApiOperation("隐藏内容")
    @PutMapping("/{task_id}/content/{content_type}/content-hide")
    public CommonResponse<Object> hideTaskContent(@PathVariable("task_id") String taskId,
            @PathVariable("content_type") String contentType, @Valid @RequestBody AiTaskContentHideUpdateReqDTO reqDTO) {
        aiTaskManager.hideTaskContent(taskId, AiTaskContentTypeEnum.fromType(contentType), reqDTO.getHide());
        return CommonResponse.builder().build();
    }

    @ApiOperation("获取历史记录详情")
    @GetMapping("/{task_id}")
    public CommonResponse<AiHistoryStepDTO> getTaskInfo(@PathVariable("task_id") String taskId) {
        return CommonResponse.<AiHistoryStepDTO>builder().withData(aiTaskManager.getTaskInfo(taskId)).build();
    }

    @ApiOperation("获取任务步骤详情")
    @GetMapping("/content/{task_id}/{type}")
    public CommonResponse<Object> getTaskInfo(@PathVariable("task_id") String taskId,
                                                        @PathVariable("type") String type) {
        return CommonResponse.<Object>builder().withData(aiTaskManager.getTaskContent(taskId, AiTaskContentTypeEnum.fromType(type))).build();
    }

    @ApiOperation("分页获取历史记录列表")
    @GetMapping("task-list")
    public CommonResponse<CommonPage<AiHistoryDTO>> getTaskList(@Valid AiHistoryPageReqDTO request) {
        return CommonResponse.<CommonPage<AiHistoryDTO>>builder().withData(aiTaskManager.getTaskList(request))
                .build();
    }

    @ApiOperation("历史记录已读")
    @PutMapping("/{task_id}/read")
    public CommonResponse<Object> readTask(@PathVariable("task_id") String taskId) {
        aiTaskManager.readTask(taskId);
        return CommonResponse.builder().build();
    }

    @ApiOperation("批量获取异步任务执行状态")
    @GetMapping("/async-status")
    public CommonResponse<List<AiHistoryDTO>> getAsyncStatus(@RequestParam("task_ids") List<String> taskIds) {
        return CommonResponse.<List<AiHistoryDTO>>builder().withData(aiTaskManager.getAsyncStatus(taskIds)).build();
    }

    @ApiOperation("获取任务内容,供查问题使用")
    @GetMapping("/{task_id}/contents")
    public CommonResponse<List<AnalyticsAiTaskContentPO>> getAllTaskContents(@PathVariable("task_id") String taskId,
            @RequestParam(value = "content_types", required = false) String contentTypes) {
        return CommonResponse.<List<AnalyticsAiTaskContentPO>>builder()
                .withData(aiTaskManager.getAllTaskContents(taskId, contentTypes))
                .build();
    }

    @ApiOperation("任务导出")
    @PostMapping("/export")
    public CommonResponse<ExportResult> export(@Valid @RequestBody AiTaskExportReqDTO request) {
        return CommonResponse.<ExportResult>builder().withData(exportManager.export(request.getTaskId())).build();
    }

}

package com.patsnap.drafting.controller;

import com.patsnap.core.spring.boot.autoconfigure.web.entity.CommonResponse;
import com.patsnap.drafting.enums.task.AiTaskContentTypeEnum;
import com.patsnap.drafting.manager.aispecification.AlgorithmAsyncProcessor;
import com.patsnap.drafting.manager.aispecification.AlgorithmTaskStatusManager;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * 算法任务状态控制器
 * 提供算法任务状态查询相关的API接口
 * 
 * <AUTHOR> Assistant
 */
@Api(tags = "算法任务状态管理")
@RestController
@RequestMapping("/algorithm-task-status")
@RequiredArgsConstructor
@Slf4j
public class AlgorithmTaskStatusController {

    private final AlgorithmAsyncProcessor algorithmAsyncProcessor;
    private final AlgorithmTaskStatusManager statusManager;

    @ApiOperation("获取任务的所有算法处理状态")
    @GetMapping("/{taskId}/status")
    public CommonResponse<Map<String, String>> getAlgorithmStatus(
            @ApiParam(value = "任务ID", required = true) @PathVariable String taskId) {
        
        log.info("获取算法处理状态，任务ID: {}", taskId);
        
        try {
            Map<String, String> statusMap = algorithmAsyncProcessor.getAlgorithmStatus(taskId);
            
            return CommonResponse.<Map<String, String>>builder()
                    .withData(statusMap)
                    .build();
                    
        } catch (Exception e) {
            log.error("获取算法处理状态失败，任务ID: {}", taskId, e);
            return CommonResponse.<Map<String, String>>builder()
                    .withStatus(false)
                    .withErrorMsg("获取算法处理状态失败")
                    .build();
        }
    }

    @ApiOperation("获取任务的详细算法处理状态信息")
    @GetMapping("/{taskId}/detailed-status")
    public CommonResponse<Map<String, AlgorithmTaskStatusManager.TaskStatusInfo>> getDetailedAlgorithmStatus(
            @ApiParam(value = "任务ID", required = true) @PathVariable String taskId) {
        
        log.info("获取详细算法处理状态，任务ID: {}", taskId);
        
        try {
            Map<String, AlgorithmTaskStatusManager.TaskStatusInfo> statusMap = 
                    algorithmAsyncProcessor.getDetailedAlgorithmStatus(taskId);
            
            return CommonResponse.<Map<String, AlgorithmTaskStatusManager.TaskStatusInfo>>builder()
                    .withData(statusMap)
                    .build();
                    
        } catch (Exception e) {
            log.error("获取详细算法处理状态失败，任务ID: {}", taskId, e);
            return CommonResponse.<Map<String, AlgorithmTaskStatusManager.TaskStatusInfo>>builder()
                    .withStatus(false)
                    .withErrorMsg("获取详细算法处理状态失败")
                    .build();
        }
    }

    @ApiOperation("获取特定内容类型的任务状态")
    @GetMapping("/{taskId}/status/{contentType}")
    public CommonResponse<String> getTaskStatus(
            @ApiParam(value = "任务ID", required = true) @PathVariable String taskId,
            @ApiParam(value = "内容类型", required = true) @PathVariable String contentType) {
        
        log.info("获取任务状态，任务ID: {}, 内容类型: {}", taskId, contentType);
        
        try {
            AiTaskContentTypeEnum contentTypeEnum = AiTaskContentTypeEnum.valueOf(contentType.toUpperCase());
            String status = statusManager.getTaskStatus(taskId, contentTypeEnum);
            
            return CommonResponse.<String>builder()
                    .withData(status)
                    .build();
                    
        } catch (IllegalArgumentException e) {
            log.error("无效的内容类型: {}", contentType, e);
            return CommonResponse.<String>builder()
                    .withStatus(false)
                    .withErrorMsg("无效的内容类型: " + contentType)
                    .build();
        } catch (Exception e) {
            log.error("获取任务状态失败，任务ID: {}, 内容类型: {}", taskId, contentType, e);
            return CommonResponse.<String>builder()
                    .withStatus(false)
                    .withErrorMsg("获取任务状态失败")
                    .build();
        }
    }

    @ApiOperation("获取特定内容类型的算法结果")
    @GetMapping("/{taskId}/result/{contentType}")
    public CommonResponse<Object> getAlgorithmResult(
            @ApiParam(value = "任务ID", required = true) @PathVariable String taskId,
            @ApiParam(value = "内容类型", required = true) @PathVariable String contentType) {
        
        log.info("获取算法结果，任务ID: {}, 内容类型: {}", taskId, contentType);
        
        try {
            AiTaskContentTypeEnum contentTypeEnum = AiTaskContentTypeEnum.valueOf(contentType.toUpperCase());
            Object result = algorithmAsyncProcessor.getAlgorithmResult(taskId, contentTypeEnum);
            
            return CommonResponse.<Object>builder()
                    .withData(result)
                    .build();
                    
        } catch (IllegalArgumentException e) {
            log.error("无效的内容类型: {}", contentType, e);
            return CommonResponse.<Object>builder()
                    .withStatus(false)
                    .withErrorMsg("无效的内容类型: " + contentType)
                    .build();
        } catch (Exception e) {
            log.error("获取算法结果失败，任务ID: {}, 内容类型: {}", taskId, contentType, e);
            return CommonResponse.<Object>builder()
                    .withStatus(false)
                    .withErrorMsg("获取算法结果失败")
                    .build();
        }
    }

    @ApiOperation("获取特定内容类型的错误信息")
    @GetMapping("/{taskId}/error/{contentType}")
    public CommonResponse<Map<String, Object>> getErrorInfo(
            @ApiParam(value = "任务ID", required = true) @PathVariable String taskId,
            @ApiParam(value = "内容类型", required = true) @PathVariable String contentType) {
        
        log.info("获取错误信息，任务ID: {}, 内容类型: {}", taskId, contentType);
        
        try {
            AiTaskContentTypeEnum contentTypeEnum = AiTaskContentTypeEnum.valueOf(contentType.toUpperCase());
            Map<String, Object> errorInfo = statusManager.getErrorInfo(taskId, contentTypeEnum);
            
            return CommonResponse.<Map<String, Object>>builder()
                    .withData(errorInfo)
                    .build();
                    
        } catch (IllegalArgumentException e) {
            log.error("无效的内容类型: {}", contentType, e);
            return CommonResponse.<Map<String, Object>>builder()
                    .withStatus(false)
                    .withErrorMsg("无效的内容类型: " + contentType)
                    .build();
        } catch (Exception e) {
            log.error("获取错误信息失败，任务ID: {}, 内容类型: {}", taskId, contentType, e);
            return CommonResponse.<Map<String, Object>>builder()
                    .withStatus(false)
                    .withErrorMsg("获取错误信息失败")
                    .build();
        }
    }

    @ApiOperation("检查任务是否正在处理中")
    @GetMapping("/{taskId}/is-processing/{contentType}")
    public CommonResponse<Boolean> isTaskProcessing(
            @ApiParam(value = "任务ID", required = true) @PathVariable String taskId,
            @ApiParam(value = "内容类型", required = true) @PathVariable String contentType) {
        
        log.info("检查任务是否正在处理中，任务ID: {}, 内容类型: {}", taskId, contentType);
        
        try {
            AiTaskContentTypeEnum contentTypeEnum = AiTaskContentTypeEnum.valueOf(contentType.toUpperCase());
            boolean isProcessing = statusManager.isTaskProcessing(taskId, contentTypeEnum);
            
            return CommonResponse.<Boolean>builder()
                    .withData(isProcessing)
                    .build();
                    
        } catch (IllegalArgumentException e) {
            log.error("无效的内容类型: {}", contentType, e);
            return CommonResponse.<Boolean>builder()
                    .withStatus(false)
                    .withErrorMsg("无效的内容类型: " + contentType)
                    .build();
        } catch (Exception e) {
            log.error("检查任务处理状态失败，任务ID: {}, 内容类型: {}", taskId, contentType, e);
            return CommonResponse.<Boolean>builder()
                    .withStatus(false)
                    .withErrorMsg("检查任务处理状态失败")
                    .build();
        }
    }

    @ApiOperation("检查任务是否已完成")
    @GetMapping("/{taskId}/is-completed/{contentType}")
    public CommonResponse<Boolean> isTaskCompleted(
            @ApiParam(value = "任务ID", required = true) @PathVariable String taskId,
            @ApiParam(value = "内容类型", required = true) @PathVariable String contentType) {
        
        log.info("检查任务是否已完成，任务ID: {}, 内容类型: {}", taskId, contentType);
        
        try {
            AiTaskContentTypeEnum contentTypeEnum = AiTaskContentTypeEnum.valueOf(contentType.toUpperCase());
            boolean isCompleted = statusManager.isTaskCompleted(taskId, contentTypeEnum);
            
            return CommonResponse.<Boolean>builder()
                    .withData(isCompleted)
                    .build();
                    
        } catch (IllegalArgumentException e) {
            log.error("无效的内容类型: {}", contentType, e);
            return CommonResponse.<Boolean>builder()
                    .withStatus(false)
                    .withErrorMsg("无效的内容类型: " + contentType)
                    .build();
        } catch (Exception e) {
            log.error("检查任务完成状态失败，任务ID: {}, 内容类型: {}", taskId, contentType, e);
            return CommonResponse.<Boolean>builder()
                    .withStatus(false)
                    .withErrorMsg("检查任务完成状态失败")
                    .build();
        }
    }

    @ApiOperation("清理任务相关的Redis数据")
    @DeleteMapping("/{taskId}/clear/{contentType}")
    public CommonResponse<String> clearTaskData(
            @ApiParam(value = "任务ID", required = true) @PathVariable String taskId,
            @ApiParam(value = "内容类型", required = true) @PathVariable String contentType) {
        
        log.info("清理任务数据，任务ID: {}, 内容类型: {}", taskId, contentType);
        
        try {
            AiTaskContentTypeEnum contentTypeEnum = AiTaskContentTypeEnum.valueOf(contentType.toUpperCase());
            statusManager.clearTaskData(taskId, contentTypeEnum);
            
            return CommonResponse.<String>builder()
                    .withData("任务数据清理成功")
                    .build();
                    
        } catch (IllegalArgumentException e) {
            log.error("无效的内容类型: {}", contentType, e);
            return CommonResponse.<String>builder()
                    .withStatus(false)
                    .withErrorMsg("无效的内容类型: " + contentType)
                    .build();
        } catch (Exception e) {
            log.error("清理任务数据失败，任务ID: {}, 内容类型: {}", taskId, contentType, e);
            return CommonResponse.<String>builder()
                    .withStatus(false)
                    .withErrorMsg("清理任务数据失败")
                    .build();
        }
    }
} 
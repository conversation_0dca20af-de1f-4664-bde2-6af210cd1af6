package com.patsnap.drafting.controller;

import com.patsnap.core.spring.boot.autoconfigure.web.entity.CommonResponse;
import com.patsnap.drafting.manager.aidisclosure.PatentDisclosureManager;
import com.patsnap.drafting.request.GenerateContentRequestDTO;
import com.patsnap.drafting.request.aidisclosure.DisclosureExportReqDTO;
import com.patsnap.drafting.request.aidisclosure.DisclosureSettingUpdateReqDTO;
import com.patsnap.drafting.request.aidisclosure.DisclosureTechMeansReqDTO;
import com.patsnap.drafting.request.aidisclosure.DisclosureTechMeansUpdateReqDTO;
import com.patsnap.drafting.request.aitask.AiTaskReqDTO;
import com.patsnap.drafting.response.aidisclosure.DisclosureSettingResDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Flux;

import javax.validation.Valid;

@Api(tags = "AI交底书撰写")
@RestController
@RequestMapping("/patent-disclosure")
public class PatentDisclosureController {

    private final PatentDisclosureManager patentDisclosureManager;

    public PatentDisclosureController(PatentDisclosureManager patentDisclosureManager) {
        this.patentDisclosureManager = patentDisclosureManager;
    }

    @ApiOperation("提取技术手段")
    @PostMapping("/tech-means")
    public CommonResponse<DisclosureSettingResDTO> getTechMeans(@Valid @RequestBody DisclosureTechMeansReqDTO request,
            String modelName) {
        return CommonResponse.<DisclosureSettingResDTO>builder()
                .withData(patentDisclosureManager.generateTechMeans(request, modelName)).build();
    }

    @ApiOperation("更改技术手段")
    @PutMapping("/tech-means")
    public CommonResponse<DisclosureSettingResDTO> updateTechMeans(
            @Valid @RequestBody DisclosureTechMeansUpdateReqDTO request) {
        return CommonResponse.<DisclosureSettingResDTO>builder()
                .withData(patentDisclosureManager.updateTechMeans(request)).build();
    }

    @ApiOperation("更改交底书撰写配置")
    @PutMapping("/setting")
    public CommonResponse<DisclosureSettingResDTO> updateSetting(
            @Valid @RequestBody DisclosureSettingUpdateReqDTO request, String modelName) {
        return CommonResponse.<DisclosureSettingResDTO>builder()
                .withData(patentDisclosureManager.updateSetting(request, modelName)).build();
    }

    @ApiOperation("生成交底书标题")
    @PostMapping("/title")
    public CommonResponse<String> generateTitle(@Valid @RequestBody AiTaskReqDTO request, String modelName) {
        return CommonResponse.<String>builder().withData(patentDisclosureManager.generateTitle(request, modelName))
                .build();
    }

    @ApiOperation("生成交底书正文")
    @PostMapping(value = "/content", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux generateContent(@Valid @RequestBody GenerateContentRequestDTO request) {
        return patentDisclosureManager.generateContent(request);
    }
    
    @ApiOperation("优化内容")
    @PostMapping(value = "/content/opt", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux optimizeContent(@Valid @RequestBody GenerateContentRequestDTO request) {
        return patentDisclosureManager.optimizeContent(request);
    }
    
    @ApiOperation("重新生成")
    @PostMapping(value = "/content/regenerate")
    public CommonResponse<Boolean> regenerate(@Valid @RequestBody AiTaskReqDTO request) {
        return CommonResponse.<Boolean>builder().withData(patentDisclosureManager.regenerate(request)).build();
    }

    @ApiOperation("导出交底书")
//    @PostMapping("/export")
    public CommonResponse<String> exportDisclosure(@Valid @RequestBody DisclosureExportReqDTO request) {
        return CommonResponse.<String>builder().withData(patentDisclosureManager.exportDisclosure(request)).build();
    }

}

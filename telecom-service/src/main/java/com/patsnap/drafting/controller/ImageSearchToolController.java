package com.patsnap.drafting.controller;

import com.patsnap.common.web.entity.CommonResponse;
import com.patsnap.drafting.manager.agent.model.AgentToolResponseDTO;
import com.patsnap.drafting.manager.imagesearch.ImageSearchToolManager;
import com.patsnap.drafting.request.imagesearch.ImageLocPredictRequest;
import com.patsnap.drafting.request.imagesearch.ImageMultipleSimilarSearchRequest;
import com.patsnap.drafting.request.imagesearch.ImageSimilarSearchRequest;
import com.patsnap.drafting.response.imagesearch.ImageLocPredictResponse;
import com.patsnap.drafting.response.imagesearch.ImageSearchRedisDataResponse;
import com.patsnap.drafting.response.imagesearch.ImageSimilarSearchResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@Api(tags = "图像检索,专为 AI Agent 提供的接口")
@RestController
@RequiredArgsConstructor
@RequestMapping("/image-search/tool")
public class ImageSearchToolController {

    private final ImageSearchToolManager imageSearchToolManager;

    @ApiOperation("检索相似图片-单图")
    @PostMapping("search/image-single")
    public AgentToolResponseDTO searchSimilarImageBySingle(@RequestHeader("agent_job_id") String taskId,
            @RequestParam(value = "enable_legal_status_filter", defaultValue = "true") Boolean enableLegalStatusFilter,
            @Valid @RequestBody ImageSimilarSearchRequest request) {
        return imageSearchToolManager.searchSimilarImageBySingle(taskId, request, enableLegalStatusFilter);
    }

    @ApiOperation("检索相似图片-多图")
    @PostMapping("search/image-multiple")
    public ImageSimilarSearchResponse searchSimilarImageByMultiple(@RequestHeader("agent_job_id") String taskId,
            @Valid @RequestBody ImageMultipleSimilarSearchRequest request) {
        return imageSearchToolManager.searchSimilarImageByMultiple(taskId, request);
    }
    
    @ApiOperation("设计特征对比")
    @PostMapping("feature-comparison")
    public AgentToolResponseDTO compareImageFeature(@RequestParam(value = "merged_count", defaultValue = "20") Integer mergedCount,
            @RequestParam(value = "col", defaultValue = "5") Integer col, @RequestHeader("agent_job_id") String taskId) {
        return imageSearchToolManager.compareImageFeature(taskId, mergedCount, col);
    }
    
    @ApiOperation("生成报告")
    @PostMapping("/report")
    public CommonResponse<AgentToolResponseDTO> getImagesBySimilarityReport(@RequestHeader("agent_job_id") String taskId) {
        return CommonResponse.<AgentToolResponseDTO>builder()
                .withData(imageSearchToolManager.getImagesBySimilarityReport(taskId))
                .build();
    }

    @ApiOperation("图像位置预测")
    @PostMapping("/loc-predict")
    public ImageLocPredictResponse predictImageLoc(@Valid @RequestBody ImageLocPredictRequest request) {
        return imageSearchToolManager.predictImageLoc(request);
    }
    
    @ApiOperation("获取Redis中的图像搜索数据")
    @PostMapping("/redis-data")
    public CommonResponse<ImageSearchRedisDataResponse> getRedisData(@RequestHeader("agent_job_id") String taskId) {
        return CommonResponse.<ImageSearchRedisDataResponse>builder()
                .withData(imageSearchToolManager.getRedisData(taskId))
                .build();
    }

}

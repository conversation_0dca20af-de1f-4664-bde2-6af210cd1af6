package com.patsnap.drafting.controller;

import com.patsnap.common.request.UserIdHolder;
import com.patsnap.core.spring.boot.autoconfigure.web.entity.CommonResponse;
import com.patsnap.drafting.config.AiTranslationConfig;
import com.patsnap.drafting.manager.aitranslation.AiTranslationManager;
import com.patsnap.drafting.manager.aitranslation.AiTranslationTermListManager;
import com.patsnap.drafting.model.aitranslation.AiTransContextBo;
import com.patsnap.drafting.request.aitask.AiTaskReqDTO;
import com.patsnap.drafting.request.aitranslation.AiTransExportReqDTO;
import com.patsnap.drafting.request.aitranslation.AiTransLangDetectDTO;
import com.patsnap.drafting.request.aitranslation.AiTransTaskCreateReqDTO;
import com.patsnap.drafting.request.aitranslation.AiTransTaskTestReqDTO;
import com.patsnap.drafting.request.aitranslation.ChangeTransReqDTO;
import com.patsnap.drafting.request.aitranslation.OriginalTransReqDTO;
import com.patsnap.drafting.request.aitranslation.TranslationTaskAsyncReqDTO;
import com.patsnap.drafting.request.aitranslation.TranslationTermDto;
import com.patsnap.drafting.request.aitranslation.TranslationTermPairDto;
import com.patsnap.drafting.response.aitranslation.AiTransResultResDTO;
import com.patsnap.drafting.response.aitranslation.TranslationKeywordResDTO;
import com.patsnap.drafting.util.ParagraphUtils;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

import org.redisson.api.RedissonClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.fasterxml.jackson.core.JsonProcessingException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import org.springframework.web.multipart.MultipartFile;


/**
 * <AUTHOR>
 * @date 2024/07/11
 */
@Api(tags = "AI翻译")
@RestController
@Slf4j
public class AiTranslationController {

    private final AiTranslationManager aiTranslationManager;
    private final AiTranslationTermListManager aiTranslationTermListManager;
    private final AiTranslationConfig aiTranslationConfig;
    private final RedissonClient redissonClient;


    public AiTranslationController(AiTranslationManager aiTranslationManager,
            AiTranslationTermListManager aiTranslationTermListManager,
            AiTranslationConfig aiTranslationConfig,
            RedissonClient redissonClient) {
        this.aiTranslationManager = aiTranslationManager;
        this.aiTranslationTermListManager = aiTranslationTermListManager;
        this.aiTranslationConfig = aiTranslationConfig;
        this.redissonClient = redissonClient;
    }

    @ApiOperation("语言检测")
    @PostMapping("/ai-translation/lang-detect")
    public CommonResponse<String> langDetect(
            @Valid @NotNull @RequestBody AiTransLangDetectDTO aidTransLangDetectDTO) {
        return CommonResponse.<String>builder()
                .withData(aiTranslationManager.langDetect(aidTransLangDetectDTO.getInput())).build();
    }

    @ApiOperation("创建翻译任务")
    @PostMapping("/ai-translation/task")
    public CommonResponse<String> createTask(@Valid @RequestBody AiTransTaskCreateReqDTO reqDTO) {
        return CommonResponse.<String>builder().withData(aiTranslationManager.createTask(reqDTO)).build();
    }

    @ApiOperation("提交用户词表")
    @PostMapping("/ai-translation/term")
    public CommonResponse<Object> submitTerm(@Valid @RequestBody TranslationTermDto termDto) {
        aiTranslationTermListManager.submitTerm(termDto);
        return CommonResponse.builder().build();
    }

    @ApiOperation("获取自定义用户词表")
    @GetMapping("/ai-translation/term/custom")
    public CommonResponse<TranslationTermDto> getCustomTerm() {
        return CommonResponse.<TranslationTermDto>builder()
                .withData(aiTranslationTermListManager.getTerm(null)).build();
    }

    @ApiOperation("流式获取翻译结果")
    @PostMapping(value = "/ai-translation/translation/sync", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux streamTranslation(@Valid @RequestBody AiTaskReqDTO request) {
        return aiTranslationManager.directTranslation(request);
    }

    @ApiOperation("提交异步翻译任务")
    @PostMapping("/ai-translation/translation/async")
    public CommonResponse<Object> submitTranslationTask(@Valid @RequestBody TranslationTaskAsyncReqDTO taskReq) {
        aiTranslationManager.submitTranslationTask(taskReq);
        return CommonResponse.builder().build();
    }

    @ApiOperation("获取异步翻译任务状态")
    @GetMapping("/ai-translation/translation/async/{task_id}/status")
    public CommonResponse<AiTransResultResDTO> getTranslationTaskStatus(@PathVariable("task_id") String taskId) {
        return CommonResponse.<AiTransResultResDTO>builder()
                .withData(aiTranslationManager.getTranslationTaskStatus(taskId)).build();
    }

    @ApiOperation("执行异步任务")
    @GetMapping("/ai-translation/translation/async/{task_id}")
    public CommonResponse<Object> runTask(@PathVariable("task_id") String taskId,
            @RequestParam(required = false) String userId) {
        aiTranslationManager.translationAsync(taskId);
        return CommonResponse.builder().build();
    }

    @ApiOperation("获取翻译任务的词表")
    @GetMapping("/ai-translation/term/{task_id}")
    public CommonResponse<List<TranslationKeywordResDTO>> taskTerms(@PathVariable("task_id") String taskId) {
        return CommonResponse.<List<TranslationKeywordResDTO>>builder()
                .withData(aiTranslationManager.getTranslationTaskKeywords(taskId)).build();
    }

    @ApiOperation("更改原文后流式输出翻译")
    @PutMapping(value = "/ai-translation/original", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux streamTranslationWithOriginal(@Valid @RequestBody OriginalTransReqDTO request) throws JsonProcessingException {
        return aiTranslationManager.streamTranslationWithOriginal(request);
    }

    @ApiOperation("更改单句翻译内容")
    @PutMapping("/ai-translation/translation")
    public CommonResponse<Object> updateTranslation(@Valid @RequestBody ChangeTransReqDTO request) {
        aiTranslationManager.updateTranslation(request);
        return CommonResponse.builder().build();
    }

    @ApiOperation("重新生成")
    @PostMapping(value = "/ai-translation/regenerate")
    public CommonResponse<Boolean> regenerate(@Valid @RequestBody AiTaskReqDTO request) {
        return CommonResponse.<Boolean>builder().withData(aiTranslationManager.regenerate(request)).build();
    }


    @ApiOperation("导出AI翻译")
//    @PostMapping("/ai-translation/export")
    public CommonResponse<String> exportAiTranslation(@Valid @RequestBody AiTransExportReqDTO request) {
        return CommonResponse.<String>builder().withData(aiTranslationManager.exportAiTranslation(request)).build();
    }

    @ApiOperation("导入用户自定义词表")
    @PostMapping("/ai-translation/term/import")
    public CommonResponse<Integer> importTerm(@RequestParam("file") MultipartFile file) {
        int importedCount = aiTranslationTermListManager.importTerm(file);
        return CommonResponse.<Integer>builder()
                .withData(importedCount)
                .build();
    }

    @ApiOperation("测试翻译任务")
    @PostMapping("/ai-translation/task/test")
    public CommonResponse<AiTransContextBo> testModelTask(@Valid @RequestBody AiTransTaskTestReqDTO reqDTO) {
        UserIdHolder.set("testModelUserId");
        TranslationTermDto termDto = new TranslationTermDto();
        List<TranslationTermPairDto> termPairs = new ArrayList<>();
        for (Map.Entry<String, String> entry : reqDTO.getTerms().entrySet()) {
            TranslationTermPairDto termPairDto = new TranslationTermPairDto();
            termPairDto.setChineseTerm(entry.getKey());
            termPairDto.setEnglishTerm(entry.getValue());
            termPairs.add(termPairDto);
        }
        termDto.setTermPair(termPairs);
        aiTranslationTermListManager.submitTerm(termDto);
        return CommonResponse.<AiTransContextBo>builder().withData(aiTranslationManager.testModelTask(reqDTO)).build();
    }

    @ApiOperation("获取分段文本")
    @PostMapping("/ai-translation/task/paragraph")
    public CommonResponse<List<String>> paragraph(@RequestBody AiTransTaskTestReqDTO reqDTO) {
        String text = reqDTO.getUserInput();
        text = ParagraphUtils.addParagraphPrefix(text);
        List<String> preParagraphs = ParagraphUtils.splitText(text,
                aiTranslationConfig.getParagraphMaxLength());
        List<String> paragraphs = new ArrayList<>(preParagraphs.size());
        StringBuilder realInput = new StringBuilder();
        for (String paragraph : preParagraphs) {
            if (!paragraph.startsWith(ParagraphUtils.PARAGRAPH_PREFIX)) {
                paragraph = ParagraphUtils.PARAGRAPH_PREFIX + paragraph;
            }
            paragraphs.add(paragraph);
            realInput.append(paragraph);
        }
        List<Integer> paragraphIndexes = ParagraphUtils.getParagraphIndexes(realInput.toString());
        log.info("paragraphIndexes: {}", paragraphIndexes);
        return CommonResponse.<List<String>>builder().withData(paragraphs).build();
    }

}

package com.patsnap.drafting.controller;

import com.patsnap.core.spring.boot.autoconfigure.web.entity.CommonResponse;
import com.patsnap.drafting.manager.company.CompanyTreeManager;
import com.patsnap.drafting.request.company.CompanyNameRequestDTO;
import com.patsnap.drafting.request.company.CompanyTreeRequestDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 公司树查询转发控制器
 */
@Api(tags = "公司树查询")
@RestController
@RequiredArgsConstructor
@RequestMapping("/company-tree")
public class CompanyTreeController {

    private final CompanyTreeManager companyTreeManager;

    @ApiOperation("查询公司名称建议")
    @PostMapping("/company-name")
    public CommonResponse<Object> getCompanyNameSuggestions(@Valid @RequestBody CompanyNameRequestDTO request) {
        return CommonResponse.<Object>builder()
                .withData(companyTreeManager.getCompanyNameSuggestions(request))
                .build();
    }
    
    @ApiOperation("查询公司树信息")
    @GetMapping
    public CommonResponse<Object> getCompanyTree(
            @RequestParam(value = "q") String query,
            @RequestParam(value = "active", required = false, defaultValue = "false") boolean active,
            @RequestParam(value = "rows", required = false, defaultValue = "50") Integer rows,
            @RequestParam(value = "exact_match", required = false, defaultValue = "false") boolean exactMatch,
            @RequestHeader(value = "User-Agent") String ua) {
        
        CompanyTreeRequestDTO request = new CompanyTreeRequestDTO();
        request.setQ(query);
        request.setRows(rows);
        request.setActive(active);
        request.setUa(ua);
        request.setExactMatch(exactMatch);
        
        return CommonResponse.<Object>builder()
                .withData(companyTreeManager.getCompanyTree(request))
                .build();
    }
} 
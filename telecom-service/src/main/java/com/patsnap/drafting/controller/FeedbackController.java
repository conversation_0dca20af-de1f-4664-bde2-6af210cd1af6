package com.patsnap.drafting.controller;

import com.patsnap.core.spring.boot.autoconfigure.web.entity.CommonResponse;
import com.patsnap.drafting.manager.feedback.FeedbackManager;
import com.patsnap.drafting.request.feedback.DraftingFeedbackRequestDto;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;

@Api(tags = "AI Drafting 反馈")
@RestController
@RequestMapping("/feedback")
@RequiredArgsConstructor
public class FeedbackController {

    private final FeedbackManager feedbackManager;

    /**
     * Drafting反馈提交（点赞、点踩）
     */
    @PostMapping(value = "/submit",
            produces = {MediaType.APPLICATION_JSON_VALUE})
    public CommonResponse<Void> submit(@RequestBody DraftingFeedbackRequestDto requestDto) {
        feedbackManager.submit(requestDto);
        return CommonResponse.<Void>builder().build();
    }
}

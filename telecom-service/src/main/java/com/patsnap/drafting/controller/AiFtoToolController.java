package com.patsnap.drafting.controller;

import com.patsnap.core.spring.boot.autoconfigure.web.entity.CommonResponse;
import com.patsnap.drafting.manager.ainoveltysearch.NoveltySearchAgentManager;
import com.patsnap.drafting.request.aitask.AiTaskReqDTO;
import com.patsnap.drafting.request.noveltyagent.FeatureExactionRequest;
import com.patsnap.drafting.response.ainoveltyagent.AiSearchAgentReportResponse;
import com.patsnap.drafting.response.ainoveltyagent.AiSearchAgentStartResponse;
import com.patsnap.drafting.response.ainoveltyagent.FeatureExactionAgentResponseDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

import static com.patsnap.drafting.constants.Constant.NOVELTY_AGENT_TASK_PREFIX;

@Api(tags = "AI FTO 查新,专为 AI Agent 提供的接口")
@RestController
@RequiredArgsConstructor
@RequestMapping("/fto-search/tool")
public class AiFtoToolController {
    
    private final NoveltySearchAgentManager noveltySearchManager;
    
    @ApiOperation("提取特征")
    @PostMapping("/feature-extract")
    public CommonResponse<FeatureExactionAgentResponseDTO> extractTechFeature(@RequestHeader("agent_job_id") String taskId,
            @Valid @RequestBody FeatureExactionRequest request) {
        return CommonResponse.<FeatureExactionAgentResponseDTO>builder()
                .withData(noveltySearchManager.extractTechFeature(request.getText(), NOVELTY_AGENT_TASK_PREFIX + taskId)).build();
    }

    @ApiOperation("语义搜索近似专利，并进行特征对比")
    @PostMapping("/search")
    public CommonResponse<AiSearchAgentStartResponse> startNoveltySearchAgent(@RequestHeader("agent_job_id") String taskId,
                                                                              @Valid @RequestBody FeatureExactionRequest extractRequest) {
        AiTaskReqDTO request = new AiTaskReqDTO();
        request.setText(extractRequest.getText());
        request.setTaskId(NOVELTY_AGENT_TASK_PREFIX + taskId);
        return CommonResponse.<AiSearchAgentStartResponse>builder().withData(noveltySearchManager.noveltySearchAgent(request)).build();
    }
    
    @ApiOperation("获取报告")
    @PostMapping("/report")
    public CommonResponse<AiSearchAgentReportResponse> getReport(@RequestHeader("agent_job_id") String taskId,
                                                                 @Valid @RequestBody FeatureExactionRequest extractRequest) {
        AiTaskReqDTO request = new AiTaskReqDTO();
        request.setTaskId(NOVELTY_AGENT_TASK_PREFIX + taskId);
        request.setText(extractRequest.getText());
        return CommonResponse.<AiSearchAgentReportResponse>builder().withData(noveltySearchManager.getReport(request)).build();
    }
    

}

package com.patsnap.drafting.controller;

import com.patsnap.core.spring.boot.autoconfigure.web.entity.CommonResponse;
import com.patsnap.drafting.manager.patent.PatentInfoManager;
import com.patsnap.drafting.request.patent.PatentImagesRequestDTO;
import com.patsnap.drafting.request.patentinfo.PatentInfoRequestDTO;
import com.patsnap.drafting.request.patentinfo.PatentSearchRequestDTO;
import com.patsnap.drafting.response.patentinfo.PatentSearchResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.Map;

/**
 * <AUTHOR> chendepeng
 * @Date 2025/3/13 13:39
 */
@Api(tags = "专利信息")
@RestController
@RequiredArgsConstructor
@RequestMapping("/patent")
public class PatentInfoController {

    private final PatentInfoManager patentManager;

    @ApiOperation("获取专利desc描述信息")
    @GetMapping("/{patent_id}/desc")
    public CommonResponse<String> getPatentDesc(@PathVariable("patent_id") String patentId, @RequestParam(value = "lang") String lang) {
        return patentManager.fetchPatentDescWithTranslate(patentId, lang);
    }

    @ApiOperation("获取专利权利要求")
    @GetMapping("/{patent_id}/claim")
    public CommonResponse<String> getPatentClaim(@PathVariable("patent_id") String patentId, @RequestParam(value = "lang") String lang) {
        return patentManager.fetchPatentClaimWithTranslate(patentId, lang);
    }

    @ApiOperation("获取专利official-image描述信息")
    @GetMapping("/{patent_id}/official-image")
    public CommonResponse<Object> getPatentOfficialImage(@PathVariable("patent_id") String patentId) {
        return patentManager.fetchPatentOfficialImage(patentId);
    }

    @ApiOperation("获取专利权利要求树信息")
    @GetMapping("/{patent_id}/clms-tree")
    public CommonResponse<Object> getPatentClmsTree(@PathVariable("patent_id") String patentId) {
        return patentManager.fetchPatentClmsTree(patentId);
    }

    @ApiOperation("批量获取专利基本信息")
    @PostMapping("/batch-basic-info")
    public CommonResponse<Object> batchGetPatentBasicInfo(@Valid @RequestBody PatentInfoRequestDTO request) {
        return CommonResponse.<Object>builder().withData(patentManager.batchFetchPatentBasicInfo(request)).build();
    }

    @ApiOperation("根据文本模糊查询专利信息")
    @PostMapping("/compare-search")
    public CommonResponse<PatentSearchResponse> searchPatentInfo(@Valid @RequestBody PatentSearchRequestDTO request) {
        return patentManager.searchPatentInfo(request);
    }

    @ApiOperation("批量获取专利图片")
    @PostMapping("/batch-images")
    public CommonResponse<Map<String, Map<String, String>>> batchGetPatentImages(@Valid @RequestBody PatentImagesRequestDTO request) {
        return CommonResponse.<Map<String, Map<String, String>>>builder()
                .withData(patentManager.batchFetchPatentImages(request))
                .build();
    }

    @ApiOperation("获取IPC详情")
    @GetMapping("/ipc-classification/detail")
    public CommonResponse<Object> getIpcClassificationDetail(@RequestParam(defaultValue = "ipc", required = false) String type,
                                                         @RequestParam(required = false) String patentId,
                                                         @RequestParam(defaultValue = "cid", required = true) String cid) {
        return patentManager.fetchClassificationDesc(cid);
    }
}

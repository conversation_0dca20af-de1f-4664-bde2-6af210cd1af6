package com.patsnap.drafting.controller;

import com.patsnap.drafting.manager.share.PublicShareManager;
import com.patsnap.drafting.request.share.FreeShareCreateRequest;
import com.patsnap.drafting.request.share.FreeShareInitRequest;
import com.patsnap.drafting.response.share.FreeShareCreateResponse;
import com.patsnap.drafting.response.share.FreeShareInitResponse;
import com.patsnap.spring.boot.autoconfigure.web.entity.CommonResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "公开分享的接口")
@RestController
@RequestMapping("/public-share")
@RequiredArgsConstructor
public class PublicShareController {
    
    private final PublicShareManager publicShareManager;
    

    @PostMapping("/init")
    @ApiOperation("公开分享获取基本信息")
    public CommonResponse<FreeShareInitResponse> initPublicShare(@RequestBody FreeShareInitRequest request) {
        return CommonResponse.<FreeShareInitResponse>builder()
                .withData(publicShareManager.initPublicShare(request))
                .build();
    }

    @PostMapping
    @ApiOperation("创建公开分享")
    public CommonResponse<FreeShareCreateResponse> createPublicShareLink(@RequestBody FreeShareCreateRequest request) {
        return CommonResponse.<FreeShareCreateResponse>builder()
                .withData(publicShareManager.createPublicShareLink(request))
                .build();
    }
    
    

    
    


}

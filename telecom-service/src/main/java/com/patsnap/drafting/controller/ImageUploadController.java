package com.patsnap.drafting.controller;

import com.patsnap.common.web.entity.CommonResponse;
import com.patsnap.drafting.manager.imagesearch.ImageUploadManager;
import com.patsnap.drafting.request.imagesearch.FileSignRequestDTO;
import com.patsnap.drafting.request.imagesearch.ImageUploadRequestDTO;
import com.patsnap.drafting.response.imagesearch.FileSignResponseDTO;
import com.patsnap.drafting.response.imagesearch.ImageUploadResponseDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;

/**
 * 图片上传控制器
 *
 * <AUTHOR> Assistant
 * @date 2024/12/19
 */
@Api(tags = "图片上传")
@RestController
@RequestMapping("/image-upload")
@RequiredArgsConstructor
@Slf4j
public class ImageUploadController {

    private final ImageUploadManager imageUploadManager;

    @ApiOperation("批量上传图片")
    @PostMapping("/batch")
    public CommonResponse<ImageUploadResponseDTO> uploadMultipleImages(
            @ApiParam(value = "图片文件列表", required = true)
            @RequestParam("files") MultipartFile[] files,
            @ApiParam(value = "文件夹路径，用于组织文件存储结构")
            @RequestParam(value = "folder_path", required = false, defaultValue = "ai_specification/image") String folderPath,
            @ApiParam(value = "文件名前缀，用于标识文件用途")
            @RequestParam(value = "file_prefix", required = false, defaultValue = "image") String filePrefix,
            @ApiParam(value = "是否生成唯一文件名")
            @RequestParam(value = "generate_unique_name", required = false, defaultValue = "true") Boolean generateUniqueName) {

        log.info("接收到批量图片上传请求，文件数量: {}, 文件夹路径: {}, 文件前缀: {}",
                files != null ? files.length : 0, folderPath, filePrefix);

        // 构建请求参数
        ImageUploadRequestDTO request = new ImageUploadRequestDTO();
        request.setFolderPath(folderPath);
        request.setFilePrefix(filePrefix);
        request.setGenerateUniqueName(generateUniqueName);

        // 调用业务逻辑处理上传
        ImageUploadResponseDTO result = imageUploadManager.uploadMultipleImages(files, request);

        log.info("批量图片上传完成，总数: {}, 成功: {}, 失败: {}", 
                result.getTotalCount(), result.getSuccessCount(), result.getFailedCount());

        return CommonResponse.<ImageUploadResponseDTO>builder()
                .withData(result)
                .build();
    }

    @ApiOperation("批量获取文件签名地址")
    @PostMapping("/sign")
    public CommonResponse<FileSignResponseDTO> signMultipleFiles(
            @ApiParam(value = "文件签名请求参数", required = true)
            @Valid @RequestBody FileSignRequestDTO request) {

        log.info("接收到批量文件签名请求，文件数量: {}", request.getS3Keys().size());

        // 调用业务逻辑处理签名
        FileSignResponseDTO result = imageUploadManager.signMultipleFiles(request);

        log.info("批量文件签名完成，总数: {}, 成功: {}, 失败: {}", 
                result.getTotalCount(), result.getSuccessCount(), result.getFailedCount());

        return CommonResponse.<FileSignResponseDTO>builder()
                .withData(result)
                .build();
    }

} 
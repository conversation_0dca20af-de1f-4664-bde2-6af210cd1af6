package com.patsnap.drafting.controller;

import com.patsnap.core.spring.boot.autoconfigure.web.entity.CommonResponse;
import com.patsnap.drafting.enums.common.OperateTypeEnum;
import com.patsnap.drafting.manager.aiftosearch.FtoSearchManager;
import com.patsnap.drafting.repository.aitask.entity.AnalyticsAiTaskHistoryPO;
import com.patsnap.drafting.request.aiftosearch.FtoSearchSubmitRequestDTO;
import com.patsnap.drafting.request.ainoveltysearch.*;
import com.patsnap.drafting.request.aitask.AiTaskReqDTO;
import com.patsnap.drafting.response.aiftosearch.FtoSearchReportTitleResponseDTO;
import com.patsnap.drafting.response.aiftosearch.FtoSearchSubmitResponseDTO;
import com.patsnap.drafting.response.ainoveltysearch.FeatureComparisonResponseDTO;
import com.patsnap.drafting.response.ainoveltysearch.FeatureExactionResponseDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PathVariable;
import java.util.List;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR> chendepeng
 * @Date 2025/4/3 14:36
 */
@Api(tags = "AI FTO 侵权查询")
@RestController
@RequiredArgsConstructor
@RequestMapping("/fto-search")
public class AiFtoSearchController {

    private final FtoSearchManager ftoSearchManager;

    @ApiOperation("任务提交")
    @PostMapping("/submit")
    public CommonResponse<FtoSearchSubmitResponseDTO> submit(@Valid @RequestBody FtoSearchSubmitRequestDTO request) {
        AiTaskReqDTO aiTaskReqDTO = new AiTaskReqDTO();
        aiTaskReqDTO.setTaskId(request.getTaskId());
        aiTaskReqDTO.setText(request.getInput());
        String lang = ftoSearchManager.getInputLang(aiTaskReqDTO);
        request.setInputLang(lang);
        return CommonResponse.<FtoSearchSubmitResponseDTO>builder().withData(ftoSearchManager.submit(request)).build();
    }

    @ApiOperation("提取特征")
    @PostMapping("/feature-extract")
    public CommonResponse<FeatureExactionResponseDTO> extractTechFeature(@Valid @RequestBody AiTaskReqDTO request) {
        return CommonResponse.<FeatureExactionResponseDTO>builder().withData(ftoSearchManager.extractTechFeature(request)).build();
    }

    @ApiOperation("文本总结")
    @PostMapping("/text-summary")
    public CommonResponse<String> summaryText(@Valid @RequestBody AiTaskReqDTO request) {
        return CommonResponse.<String>builder().withData(ftoSearchManager.summaryText(request)).build();
    }

    @ApiOperation("获取或总结标题")
    @PostMapping("/title")
    public CommonResponse<String> fetchOrGenerateTitle(@Valid @RequestBody AiTaskReqDTO request) {
        return CommonResponse.<String>builder().withData(ftoSearchManager.generateTitle(request)).build();
    }

    @ApiOperation("专利获取技术特征对比及得分")
    @PostMapping("/feature-comparison")
    public CommonResponse<FeatureComparisonResponseDTO> featureComparison(@Valid @RequestBody FeatureComparisonRequestDTO request) {
        request.setOperateType(OperateTypeEnum.REGENERATE.getValue());
        return CommonResponse.<FeatureComparisonResponseDTO>builder().withData(ftoSearchManager.featureComparison(request)).build();
    }

    @ApiOperation("更新技术要点")
    @PostMapping("/update-tech-essential")
    public CommonResponse<String> updateTechnicalEssential(@Valid @RequestBody AiTaskReqDTO request) {
        request.setOperateType(OperateTypeEnum.REGENERATE.getValue());
        return CommonResponse.<String>builder()
                .withData(ftoSearchManager.updateTechnicalEssential(request)).build();
    }

    @ApiOperation("确认技术特征")
    @PostMapping("/confirm-tech-feature")
    public CommonResponse<FeatureExactionResponseDTO> confirmTechnicalFeature(@Valid @RequestBody AiSearchConfirmFeatureReqDTO request) {
        request.setOperateType(OperateTypeEnum.REGENERATE.getValue());
        return CommonResponse.<FeatureExactionResponseDTO>builder()
                .withData(ftoSearchManager.confirmTechnicalFeature(request)).build();
    }

    @ApiOperation("获取技术要点与特征")
    @GetMapping("/get-tech-essential-feature/{task_id}")
    public CommonResponse<FeatureExactionResponseDTO> getTechEssentialAndFeatures(
            @PathVariable(value = "task_id") String taskId) {
        AiTaskReqDTO request = new AiTaskReqDTO();
        request.setTaskId(taskId);
        return CommonResponse.<FeatureExactionResponseDTO>builder()
                .withData(ftoSearchManager.extractTechFeature(request)).build();
    }

    @ApiOperation("专利查新检索agent执行")
    @PostMapping("/agent")
    public CommonResponse<String> startFtoSearchAgent(@Valid @RequestBody AiTaskReqDTO request) {
        return CommonResponse.<String>builder()
                .withData(ftoSearchManager.ftoSearchAgent(request)).build();
    }

    @ApiOperation("更新对比文献")
    @PostMapping("/update-similar-list")
    public CommonResponse<List<AiSearchFinalResult>> updateSimilarList(@Valid @RequestBody AiSearchFinalResultUpdateReqDTO request) {
        request.setOperateType(OperateTypeEnum.REGENERATE.getValue());
        return CommonResponse.<List<AiSearchFinalResult>>builder().withData(ftoSearchManager.updateSimilarList(request)).build();
    }

    @ApiOperation("获取相似专利列表")
    @PostMapping("/similar-list")
    public CommonResponse<List<AiSearchFinalResult>> getSimilarList(@Valid @RequestBody AiTaskReqDTO request) {
        return CommonResponse.<List<AiSearchFinalResult>>builder()
                .withData(ftoSearchManager.getSimilarList(request.getTaskId())).build();
    }

    @ApiOperation("获取专利查新agent执行结果")
    @GetMapping("/agent/result")
    public CommonResponse<AiSearchResultDTO> getFtoSearchResult(
            @RequestParam(value = "task_id") String taskId,
            @RequestParam(value = "is_finished") Boolean isFinished) {
        AiSearchResultReqDTO req = new AiSearchResultReqDTO();
        req.setTaskId(taskId);
        req.setIsFinished(isFinished);
        AiSearchResultDTO result = ftoSearchManager.fetchFtoSearchResult(req);
        return CommonResponse.<AiSearchResultDTO>builder()
                .withData(result).build();
    }

    @ApiOperation("终止专利查新agent执行")
    @PostMapping("/agent/terminate")
    public CommonResponse<AiSearchResultDTO> terminateFtoSearchResult(@RequestParam(value = "task_id") String taskId) {
        AiSearchResultReqDTO req = new AiSearchResultReqDTO();
        req.setTaskId(taskId);
        req.setIsFinished(Boolean.TRUE);
        return CommonResponse.<AiSearchResultDTO>builder()
                .withData(ftoSearchManager.fetchFtoSearchResult(req)).build();
    }

    @ApiOperation("获取报告标题")
    @GetMapping("/report/title/{task_id}")
    public CommonResponse<FtoSearchReportTitleResponseDTO> getReportTitle(@PathVariable(value = "task_id") String taskId) {
        AiTaskReqDTO request = new AiTaskReqDTO();
        request.setTaskId(taskId);
        return CommonResponse.<FtoSearchReportTitleResponseDTO>builder().withData(ftoSearchManager.generateReportTitle(request)).build();
    }

    @ApiOperation("处理历史任务内容")
    @GetMapping("/history/process")
    public CommonResponse<String> processHistoryTaskContents(
            @RequestParam(value = "task_id", required = false) String taskId) {
        String handleResult = ftoSearchManager.handleHistoryTaskContents(taskId);
        return CommonResponse.<String>builder().withData(handleResult).build();
    }
}

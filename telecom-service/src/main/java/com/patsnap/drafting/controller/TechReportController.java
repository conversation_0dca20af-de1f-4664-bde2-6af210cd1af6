package com.patsnap.drafting.controller;

import com.patsnap.common.web.entity.CommonResponse;
import com.patsnap.drafting.manager.techreport.TechReportManager;
import com.patsnap.drafting.manager.techreport.model.TechReportConfigDTO;
import com.patsnap.drafting.request.techreport.TechReportInitReqDTO;
import com.patsnap.drafting.response.techreport.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * 技术简报控制器
 * 通过对话创建和管理技术简报
 *
 * <AUTHOR>
 */
@Api(tags = "技术简报")
@RestController
@RequestMapping("/tech-report")
@RequiredArgsConstructor
public class TechReportController {

    private final TechReportManager techReportManager;

    @ApiOperation("识别用户需求并初始化技术简报")
    @PostMapping("/intent")
    public TechReportConfigDTO analyzeUserIntent(@RequestHeader("agent_job_id") String agentJobId,
                                                 @Valid @RequestBody TechReportInitReqDTO request) {
        return techReportManager.intentIdentity(agentJobId, request);
    }

    @ApiOperation("提取关键技术特征和指标")
    @PostMapping("/feature-extraction")
    public TechReportExtractFeaturesResDTO extractTechnicalFeatures(@RequestHeader("agent_job_id") String agentJobId,
            @Valid @RequestBody TechReportInitReqDTO request) {
        return techReportManager.extractFeatures(agentJobId, request);
    }

    @ApiOperation("智能推荐相关企业与技术")
    @PostMapping("/recommend")
    public TechReportRecommendResDTO recommendCompaniesAndTechnologies(@RequestHeader("agent_job_id") String agentJobId) {
        return techReportManager.recommend(agentJobId);
    }

    @ApiOperation("生成技术简报预览")
    @PostMapping("/preview")
    public TechReportPreviewWrapperDTO generateReportPreview(@RequestHeader("agent_job_id") String agentJobId) {
        return techReportManager.preview(agentJobId);
    }
    
    @ApiOperation("创建技术监控报告")
    @PostMapping("/create_tech_monitor")
    public  CommonResponse<String> createTechMonitor(@RequestHeader("agent_job_id") String agentJobId,
                                                     @RequestParam(value = "source_type", required = false) String sourceType) {
        return CommonResponse.<String>builder().withData(techReportManager.createTechMonitor(agentJobId, sourceType)).build();
    }
} 
package com.patsnap.drafting.controller;

import com.patsnap.core.spring.boot.autoconfigure.web.entity.CommonResponse;
import com.patsnap.drafting.client.model.NoveltySearchRetrievalAddElementResDTO;
import com.patsnap.drafting.client.model.ainovelty.AiNoveltySearchResponse;
import com.patsnap.drafting.request.ainoveltysearch.NoveltySearchElementResDTO;
import com.patsnap.drafting.client.model.NoveltySearchFeatureExtractResDTO;
import com.patsnap.drafting.enums.common.OperateTypeEnum;
import com.patsnap.drafting.manager.ainoveltysearch.NoveltySearchManager;
import com.patsnap.drafting.manager.ainoveltysearch.NoveltySearchReportManager;
import com.patsnap.drafting.request.ainoveltysearch.*;
import com.patsnap.drafting.request.aitask.AiTaskReqDTO;
import com.patsnap.drafting.response.ainoveltysearch.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

@Api(tags = "AI查新")
@RestController
@RequiredArgsConstructor
@RequestMapping("/novelty-search")
public class AiNoveltySearchController {

    private final NoveltySearchManager noveltySearchManager;

    private final NoveltySearchReportManager noveltySearchReportManager;
    
    @ApiOperation("任务提交")
    @PostMapping("/submit")
    public CommonResponse<NoveltySearchSubmitResponseDTO> submit(@Valid @RequestBody FeatureExactionRequestDTO request) {
        AiTaskReqDTO aiTaskReqDTO = new AiTaskReqDTO();
        aiTaskReqDTO.setTaskId(request.getTaskId());
        aiTaskReqDTO.setText(request.getInput());
        String lang = noveltySearchManager.getInputLang(aiTaskReqDTO);
        request.setInputLang(lang);
        return CommonResponse.<NoveltySearchSubmitResponseDTO>builder()
                .withData(noveltySearchManager.submit(request)).build();
    }

    @ApiOperation("重置任务")
    @PostMapping("/reset")
    public CommonResponse<String> reset(@Valid @RequestBody AiTaskReqDTO request) {
        return CommonResponse.<String>builder()
                .withData(noveltySearchManager.resetTask(request)).build();
    }

    @ApiOperation("文本总结")
    @PostMapping("/text-summary")
    public CommonResponse<String> summaryText(@Valid @RequestBody AiTaskReqDTO request) {
        return CommonResponse.<String>builder()
                .withData(noveltySearchManager.summaryText(request).getTechSolution()).build();
    }

    @ApiOperation("获取或总结标题")
    @PostMapping("/title")
    public CommonResponse<String> fetchOrGenerateTitle(@Valid @RequestBody AiTaskReqDTO request) {
        return CommonResponse.<String>builder().withData(noveltySearchManager.generateTitle(request)).build();
    }

    @ApiOperation("更新技术要点")
    @PostMapping("/update-tech-essential")
    public CommonResponse<String> updateTechnicalEssential(@Valid @RequestBody AiTaskReqDTO request) {
        request.setOperateType(OperateTypeEnum.REGENERATE.getValue());
        noveltySearchManager.summaryText(request);
        return CommonResponse.<String>builder()
                .withData(noveltySearchManager.updateTechnicalEssential(request)).build();
    }

    @ApiOperation("提取特征")
    @PostMapping("/feature-extract")
    public CommonResponse<NoveltySearchFeatureExtractResDTO> extractTechFeature(@Valid @RequestBody AiTaskReqDTO request) {
        return CommonResponse.<NoveltySearchFeatureExtractResDTO>builder()
                .withData(noveltySearchManager.extractTechFeature(request)).build();
    }

    @ApiOperation("获取技术要点与特征")
    @GetMapping("/get-tech-essential-feature/{task_id}")
    public CommonResponse<NoveltySearchFeatureExtractResDTO> getTechEssentialAndFeatures(
            @PathVariable(value = "task_id") String taskId) {
        AiTaskReqDTO request = new AiTaskReqDTO();
        request.setTaskId(taskId);
        return CommonResponse.<NoveltySearchFeatureExtractResDTO>builder()
                .withData(noveltySearchManager.extractTechFeature(request)).build();
    }

    @ApiOperation("确认技术特征")
    @PostMapping("/confirm-tech-feature")
    public CommonResponse<NoveltySearchFeatureExtractResDTO> confirmTechnicalFeature(@Valid @RequestBody AiNoveltySearchConfirmFeatureReqDTO request) {
        request.setOperateType(OperateTypeEnum.REGENERATE.getValue());
        return CommonResponse.<NoveltySearchFeatureExtractResDTO>builder()
                .withData(noveltySearchManager.confirmTechnicalFeature(request)).build();
    }

    @ApiOperation("获取检索要素")
    @GetMapping("/get-retrieval-elements/{task_id}")
    public CommonResponse<NoveltySearchElementResDTO> getRetrievalElements(
            @PathVariable(value = "task_id") String taskId) {
        AiTaskReqDTO request = new AiTaskReqDTO();
        request.setTaskId(taskId);
        return CommonResponse.<NoveltySearchElementResDTO>builder()
                .withData(noveltySearchManager.getRetrievalElements(request)).build();
    }

    @ApiOperation("更新检索要素")
    @PostMapping("/update-retrieval-elements")
    public CommonResponse<NoveltySearchElementResDTO> updateRetrievalElements(
            @Valid @RequestBody NoveltySearchUpdateElementReqDTO request) {
        request.setOperateType("regenerate");
        return CommonResponse.<NoveltySearchElementResDTO>builder()
                .withData(noveltySearchManager.updateRetrievalElements(request)).build();
    }

    @ApiOperation("专利查新检索agent执行")
    @PostMapping("/agent")
    public CommonResponse<String> startNoveltySearchAgent(@Valid @RequestBody AiTaskReqDTO request) {
        return CommonResponse.<String>builder().withData(noveltySearchManager.noveltySearchAgent(request)).build();
    }

    @ApiOperation("更新对比文献")
    @PostMapping("/update-similar-list")
    public CommonResponse<List<AiSearchFinalResult>> updateSimilarList(@Valid @RequestBody AiSearchFinalResultUpdateReqDTO request) {
        request.setOperateType(OperateTypeEnum.REGENERATE.getValue());
        return CommonResponse.<List<AiSearchFinalResult>>builder().withData(
                request.isPatentConfirm()
                        ? noveltySearchManager.preUpdateSimilarList(request)
                        : noveltySearchManager.confirmUpdateSimilarList(request)
        ).build();
    }

    @ApiOperation("获取相似专利列表")
    @PostMapping("/similar-list")
    public CommonResponse<List<AiSearchFinalResult>> getSimilarList(@Valid @RequestBody AiTaskReqDTO request) {
        return CommonResponse.<List<AiSearchFinalResult>>builder().withData(noveltySearchManager.getSimilarList(request.getTaskId())).build();
    }

    @ApiOperation("获取专利查新agent执行结果")
    @GetMapping("/agent/result")
    public CommonResponse<AiNoveltySearchResponse> getNoveltySearchResult(@RequestParam(value = "task_id") String taskId,
                                                                          @RequestParam(value = "is_finished") Boolean isFinished) {
        AiSearchResultReqDTO req = new AiSearchResultReqDTO();
        req.setTaskId(taskId);
        req.setIsFinished(isFinished);
        return CommonResponse.<AiNoveltySearchResponse>builder().withData(noveltySearchManager.fetchNoveltySearchResult(req)).build();
    }

    @ApiOperation("终止专利查新agent执行")
    @PostMapping("/agent/terminate")
    public CommonResponse<AiNoveltySearchResponse> terminateNoveltySearchResult(@RequestParam(value = "task_id") String taskId) {
        AiSearchResultReqDTO req = new AiSearchResultReqDTO();
        req.setTaskId(taskId);
        req.setIsFinished(Boolean.TRUE);
        return CommonResponse.<AiNoveltySearchResponse>builder().withData(noveltySearchManager.fetchNoveltySearchResult(req)).build();
    }

    @ApiOperation("获取用户手动添加或新计算的专利的技术特征对比及得分")
    @PostMapping("/feature-comparison")
    public CommonResponse<FeatureComparisonResponseDTO> featureComparison(@Valid @RequestBody FeatureComparisonRequestDTO request) {
        request.setOperateType(OperateTypeEnum.REGENERATE.getValue());
        return CommonResponse.<FeatureComparisonResponseDTO>builder().withData(
                request.isAddPatent()
                        ? noveltySearchManager.featureComparisonForAddPatent(request)
                        : noveltySearchManager.featureComparisonForCc(request)
        ).build();
    }

    @ApiOperation("获取报告标题")
    @GetMapping("/report/title/{task_id}")
    public CommonResponse<NoveltySearchReportTitleResponseDTO> getReportTitle(@PathVariable(value = "task_id") String taskId) {
        AiTaskReqDTO request = new AiTaskReqDTO();
        request.setTaskId(taskId);
        return CommonResponse.<NoveltySearchReportTitleResponseDTO>builder().withData(noveltySearchManager.generateReportTitle(request)).build();
    }

    @ApiOperation("获取对比文献公开情况")
    @GetMapping("/report/comparative-literature/{task_id}")
    public CommonResponse<List<NoveltySearchComparativeLiteratureResDTO>> getReportComparativeLiterature(@PathVariable(value = "task_id") String taskId) {
        return CommonResponse.<List<NoveltySearchComparativeLiteratureResDTO>>builder().withData(noveltySearchReportManager.getReportComparativeLiterature(taskId)).build();
    }

    @ApiOperation("获取评述报告")
    @GetMapping("/report/review-report/{task_id}")
    public CommonResponse<String> getReportReviewReport(@PathVariable(value = "task_id") String taskId) {
        return CommonResponse.<String>builder().withData(noveltySearchReportManager.getReportReviewReport(taskId)).build();
    }

    @ApiOperation("获取结论和建议")
    @GetMapping("/report/result/{task_id}")
    public CommonResponse<Boolean> getReportResult(@PathVariable(value = "task_id") String taskId) {
        AiTaskReqDTO request = new AiTaskReqDTO();
        request.setTaskId(taskId);
        return CommonResponse.<Boolean>builder().withData(noveltySearchManager.getReportResult(request)).build();
    }

    @PostMapping("/retrieval-element-extend")
    public NoveltySearchRetrievalAddElementResDTO retrievalElementExtend(@RequestBody NoveltySearchAddElementReqDTO request) {
        return noveltySearchManager.retrievalElementExtend(request);
    }

}

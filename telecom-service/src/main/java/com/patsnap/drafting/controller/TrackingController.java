package com.patsnap.drafting.controller;

import com.patsnap.core.spring.boot.autoconfigure.web.entity.CommonResponse;
import com.patsnap.drafting.manager.track.TrackingManager;
import com.patsnap.drafting.request.tracking.TrackingRequestDTO;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@RestController
@Api(tags = "Tracking API")
@RequestMapping(path = "/tracking")
public class TrackingController {

    @Autowired
    private TrackingManager trackingManager;

    /**
     * 保存埋点数据
     */
    @RequestMapping(path = "/add", method = RequestMethod.POST)
    public CommonResponse<Void> add(@RequestBody @Valid TrackingRequestDTO trackingRequestDTO) {
        trackingManager.addTracking(trackingRequestDTO.getModuleType(), trackingRequestDTO.getEventType(),
                trackingRequestDTO.getSourceType(), trackingRequestDTO.getOtherParam(),
                trackingRequestDTO.getEventDetailStr());
        return CommonResponse.<Void>builder().build();
    }
}

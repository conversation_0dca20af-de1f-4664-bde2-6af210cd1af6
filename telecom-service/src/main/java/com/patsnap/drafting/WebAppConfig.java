package com.patsnap.drafting;

import com.patsnap.core.spring.boot.autoconfigure.task.exector.ThreadPoolExecutorProperty;
import com.patsnap.core.spring.boot.autoconfigure.task.exector.ThreadPoolTaskExecutorBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.concurrent.Executor;

@Configuration
public class WebAppConfig implements WebMvcConfigurer {

    @Autowired
    private ThreadPoolTaskExecutorBuilder threadPoolTaskExecutorBuilder;

    @Bean("telecomTrackThreadPool")
    public ThreadPoolTaskExecutor eurekaTrackThreadPool() {
        return threadPoolTaskExecutorBuilder.build(new ThreadPoolExecutorProperty()
                .setCorePoolSize(5)
                .setQueueCapacity(500)
                .setMaxPoolSize(20)
                .setThreadName("telecomTrackThreadPool"));
    }
    
    @Bean("algorithmTaskExecutor")
    public Executor algorithmTaskExecutor() {
        return threadPoolTaskExecutorBuilder.build(new ThreadPoolExecutorProperty()
                .setCorePoolSize(Runtime.getRuntime().availableProcessors())
                .setMaxPoolSize(Runtime.getRuntime().availableProcessors() * 2)
                .setQueueCapacity(100)
                .setThreadName("Algorithm-Async-")
                .setAllowCoreThreadTimeOut(true)
                .setKeepAliveSeconds(120)
                .setAwaitTerminationSeconds(120));
    }
    
    /**
     * 默认异步任务执行器
     * 用于处理其他类型的异步任务
     *
     * @return 异步任务执行器
     */
    @Bean("taskExecutor")
    public Executor taskExecutor() {
        return threadPoolTaskExecutorBuilder.build(new ThreadPoolExecutorProperty()
                .setCorePoolSize(Runtime.getRuntime().availableProcessors())
                .setMaxPoolSize(Runtime.getRuntime().availableProcessors() * 2)
                .setQueueCapacity(100)
                .setThreadName("Default-Async-")
                .setAllowCoreThreadTimeOut(true)
                .setKeepAliveSeconds(120)
                .setAwaitTerminationSeconds(120));
    }
}

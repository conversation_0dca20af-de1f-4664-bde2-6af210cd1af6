package com.patsnap;

import com.patsnap.analytics.infrastructure.utils.SpringBeanUtil;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.Banner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.scheduling.annotation.EnableScheduling;

@SpringBootApplication(scanBasePackages = {"com.patsnap.analytics,com.patsnap.drafting","com.patsnap.core.common.bizsecurity"})
@MapperScan("com.patsnap.analytics.repository.*.dao,com.patsnap.drafting.repository.*.dao.mapper")
@EnableRetry
@EnableScheduling
public class Application {

    public static void main(String[] args) {
        SpringApplication app = new SpringApplication(Application.class);
        app.setBannerMode(Banner.Mode.CONSOLE);
        ConfigurableApplicationContext configurableApplicationContext = app.run(args);
        SpringBeanUtil.setApplicationContext(configurableApplicationContext);
        com.patsnap.core.common.bizsecurity.util.SpringBeanUtil.setApplicationContext(configurableApplicationContext);
    }
}

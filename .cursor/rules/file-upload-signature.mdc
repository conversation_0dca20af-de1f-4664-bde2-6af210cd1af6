---
description: 文件上传与获取文件地址的规则
globs: 
alwaysApply: false
---
# 文件上传与获取文件URL规则

## 概述

本项目提供完整的文件上传和URL签名服务，支持将文件上传到Amazon S3存储，并提供安全的签名URL用于文件访问。

## 核心架构

### 主要组件

1. **[FileManager.java](mdc:telecom-core/src/main/java/com/patsnap/drafting/manager/FileManager.java)** - 文件操作核心管理器
   - 负责文件上传到S3
   - 提供文件URL签名功能
   - 支持从签名URL中提取文件key

2. **[StorageClient.java](mdc:telecom-core/src/main/java/com/patsnap/drafting/client/StorageClient.java)** - 存储服务客户端
   - 与签名服务API交互
   - 支持S3和CDN两种存储方式
   - 处理签名URL的生成

3. **[ImageUploadManager.java](mdc:telecom-core/src/main/java/com/patsnap/drafting/manager/imagesearch/ImageUploadManager.java)** - 图片上传专用管理器
   - 处理多文件上传
   - 文件格式和大小验证
   - 批量文件签名功能

## 数据模型

### 请求模型
- **[ImageUploadRequestDTO.java](mdc:telecom-api/src/main/java/com/patsnap/drafting/request/imagesearch/ImageUploadRequestDTO.java)** - 图片上传请求参数
- **[FileSignRequestDTO.java](mdc:telecom-api/src/main/java/com/patsnap/drafting/request/imagesearch/FileSignRequestDTO.java)** - 文件签名请求参数

### 响应模型
- **[ImageUploadResponseDTO.java](mdc:telecom-api/src/main/java/com/patsnap/drafting/response/imagesearch/ImageUploadResponseDTO.java)** - 图片上传响应
- **[FileSignResponseDTO.java](mdc:telecom-api/src/main/java/com/patsnap/drafting/response/imagesearch/FileSignResponseDTO.java)** - 文件签名响应

### 存储模型
- **[SignedUrlResponse.java](mdc:telecom-core/src/main/java/com/patsnap/drafting/model/storage/SignedUrlResponse.java)** - 签名URL响应
- **[SignedUrl.java](mdc:telecom-core/src/main/java/com/patsnap/drafting/model/storage/SignedUrl.java)** - 签名URL信息
- **[SignatureResponse.java](mdc:telecom-core/src/main/java/com/patsnap/drafting/model/storage/SignatureResponse.java)** - 签名服务响应

## 工作流程

### 文件上传流程

1. **文件验证** - 检查文件格式、大小、数量限制
2. **生成存储路径** - 根据用户ID和配置生成S3 key
3. **获取上传签名URL** - 调用StorageClient获取PUT方法的签名URL
4. **执行文件上传** - 使用Apache HttpClient上传文件到S3
5. **生成访问URL** - 获取GET方法的签名URL用于文件访问
6. **返回结果** - 包含文件URL、S3 key等信息

### 文件签名流程

1. **验证S3 key格式** - 检查key的有效性和安全性
2. **调用签名服务** - 通过StorageClient获取签名URL
3. **处理响应** - 返回带过期时间的签名URL
4. **错误处理** - 对失败的文件进行错误记录

## 关键配置

### 配置项
```properties
# 存储配置
configs.com.patsnap.analytics.storage.use-s3=true
configs.com.patsnap.analytics.storage.bucket-domain=your-bucket-name
configs.com.patsnap.analytics.region=us-east-1
configs.com.patsnap.signature.url=https://signature-service-url
```

### 常量定义
- 文件过期时间：24小时 (86400秒)
- 最大文件大小：1MB
- 最大文件数量：20个
- 支持的图片格式：jpg, jpeg, png
- 默认存储路径：ai_drafting/images

## 代码示例

### 文件上传示例

```java
@Service
@RequiredArgsConstructor
public class FileUploadService {
    
    private final FileManager fileManager;
    
    public String uploadImage(byte[] imageBytes, String fileName) {
        // 上传文件并获取访问URL
        return fileManager.uploadFile2AmazonS3(imageBytes, fileName, ContentType.IMAGE_PNG);
    }
}
```

### 文件签名示例

```java
@Service
@RequiredArgsConstructor
public class FileSignService {
    
    private final FileManager fileManager;
    
    public String getFileUrl(String s3Key) {
        // 为S3文件生成签名URL
        return fileManager.signFile(s3Key);
    }
}
```

### 批量上传示例

```java
@Service
@RequiredArgsConstructor
public class BatchUploadService {
    
    private final ImageUploadManager imageUploadManager;
    
    public ImageUploadResponseDTO uploadMultipleImages(MultipartFile[] files, ImageUploadRequestDTO request) {
        return imageUploadManager.uploadMultipleImages(files, request);
    }
}
```

## 错误处理

### 错误码定义
参考 **[ImageUploadErrorCodeEnum.java](mdc:telecom-core/src/main/java/com/patsnap/drafting/exception/errorcode/ImageUploadErrorCodeEnum.java)**

- **140001-140020**: 文件相关错误（空文件、大小超限、格式不支持等）
- **140021-140040**: 上传相关错误（上传失败、超时、存储不可用等）
- **140041-140060**: 参数相关错误（参数无效、路径无效等）
- **140061-140080**: 权限相关错误

### 异常处理策略

1. **文件验证失败** - 抛出BizException，返回具体错误信息
2. **上传失败** - 使用RetryRunner进行重试，最多3次
3. **签名失败** - 记录日志并返回空字符串
4. **批量操作** - 部分失败不影响整体，返回成功和失败的详细信息

## 安全考虑

### 文件安全
- 限制文件类型和大小
- 验证文件内容完整性
- 使用用户ID隔离文件存储

### URL安全
- 签名URL带有过期时间
- 支持HTTPS访问
- 限制访问方法（GET/PUT）

### 路径安全
- 禁止路径遍历攻击（../, //等）
- 验证S3 key格式
- 支持的路径前缀：export/, agent/

## 最佳实践

### 开发规范

1. **文件命名**
   ```java
   // 生成唯一文件名
   String timestamp = String.valueOf(System.currentTimeMillis());
   String uniqueFileName = String.format("%s_%s_%s.%s", prefix, timestamp, baseName, extension);
   ```

2. **S3 Key生成**
   ```java
   // 标准S3 key格式：folder/userId/fileName
   String s3Key = String.format("%s/%s/%s", folderPath, userId, fileName);
   ```

3. **错误处理**
   ```java
   try {
       // 文件操作
   } catch (BizException e) {
       // 业务异常，直接抛出
       throw e;
   } catch (Exception e) {
       // 系统异常，包装后抛出
       throw new BizException(UPLOAD_FAILED);
   }
   ```

4. **日志记录**
   ```java
   log.info("文件上传成功，S3 key: {}, 访问URL: {}", s3Key, fileUrl);
   log.warn("文件签名失败: {}", s3Key);
   log.error("文件上传失败: {}", fileName, e);
   ```

### 性能优化

1. **连接池配置** - 使用HttpClientBuilder管理HTTP连接
2. **超时设置** - 合理设置连接、请求、读取超时时间
3. **重试机制** - 使用RetryRunner处理临时网络问题
4. **批量处理** - 支持批量文件签名，减少API调用次数

### 监控和维护

1. **关键指标监控**
   - 文件上传成功率
   - 上传耗时统计
   - 存储空间使用情况
   - 签名URL生成失败率

2. **日志分析**
   - 上传失败原因分析
   - 用户上传行为分析
   - 存储成本分析

3. **定期维护**
   - 清理过期文件
   - 监控存储配额
   - 更新签名服务配置

## 扩展功能

### 支持的扩展
- 多种存储后端（S3、腾讯云COS等）
- 文件类型扩展（文档、视频等）
- 缩略图生成
- 文件加密存储
- CDN加速访问

### 集成示例
- 与AI图像搜索系统集成
- 专利文档附件管理
- 用户头像上传
- 报告导出文件管理

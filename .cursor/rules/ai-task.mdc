---
description: AI任务系统的工作原理和完整开发指南
globs: 
alwaysApply: false
---
AI任务系统的深度分析和完整开发指南的编写。以下是核心要点总结：

  📋 任务系统工作原理

  1. 架构特点: 分层设计(Controller→Manager→Service→Repository)，支持130+种内容类型，采用
  步骤化处理模式
  1. 生命周期: Ready→Running→Complete/Failed状态转换，支持重试机制和分布式锁
  2. 双模式处理: 同步任务(Flux流式响应) + 异步任务(SQS消息队列)
  3. 内容管理: 多级缓存策略，版本控制，智能缓存注解
  4. 积分系统: 细粒度积分控制，AOP切面集成，异常安全退回

  🛠️ 新任务开发指南

  提供了完整的7步开发流程：
  1. 步骤枚举定义 - 定义任务执行步骤
  2. 内容类型扩展 - 添加新的内容类型和积分配置
  3. 任务类型注册 - 在枚举中注册新任务类型
  4. Manager层实现 - 核心业务逻辑处理
  5. Controller层 - REST API接口
  6. 配置管理 - 限制配置和权限设置
  7. 测试策略 - 单元测试和集成测试
  💡 具体代码示例
  步骤1: 定义任务步骤枚举

  // 文件: AiDocumentSummaryStepEnum.java
  package com.patsnap.drafting.enums.task;

  import lombok.Getter;
  import lombok.RequiredArgsConstructor;

  import java.util.List;

  @Getter
  @RequiredArgsConstructor
  public enum AiDocumentSummaryStepEnum implements TaskStep {

      DOCUMENT_INPUT("DOCUMENT_INPUT", List.of(
          AiTaskContentTypeEnum.USER_INPUT.getType(),
          AiTaskContentTypeEnum.TITLE.getType(),
          "document_content",
          "summary_length_config"
      )),

      DOCUMENT_ANALYSIS("DOCUMENT_ANALYSIS", List.of(
          "key_points_extraction",
          "document_structure_analysis"
      )),

      SUMMARY_GENERATION("SUMMARY_GENERATION", List.of(
          "document_summary_result",
          "summary_highlights"
      ));

      private final String step;
      private final List<String> subStepList;
  }

  步骤2: 扩展内容类型枚举

  // 在AiTaskContentTypeEnum.java末尾添加新的内容类型
  // AI 文档总结任务
  DOCUMENT_CONTENT("document_content", "文档内容", INPUT, String.class, 0),
  SUMMARY_LENGTH_CONFIG("summary_length_config", "总结长度配置", CONFIG, Integer.class,
  0),
  KEY_POINTS_EXTRACTION("key_points_extraction", "关键要点提取", OUTPUT, List.class,
  100),
  DOCUMENT_STRUCTURE_ANALYSIS("document_structure_analysis", "文档结构分析", OUTPUT,
  Map.class, 50),
  DOCUMENT_SUMMARY_RESULT("document_summary_result", "文档总结结果", OUTPUT,
  String.class, 200),
  SUMMARY_HIGHLIGHTS("summary_highlights", "总结要点", OUTPUT, List.class, 100),

  ;

  步骤3: 更新任务类型枚举

  // 在AiTaskTypeEnum.java中添加新任务类型
  AI_TRANSLATION("AI_TRANSLATION", 202501, AiTranslationStepEnum.values()),
  AI_PATENT_DISCLOSURE("AI_PATENT_DISCLOSURE", 202501, AiDisclosureStepEnum.values()),
  AI_SPECIFICATION("AI_SPECIFICATION", 202501, AiSpecificationStepEnum.values()),
  AI_NOVELTY_SEARCH("AI_NOVELTY_SEARCH", 202501, AiNoveltySearchStepEnum.values()),
  AI_FTO_SEARCH("AI_FTO_SEARCH", 202501, AiFtoSearchStepEnum.values()),
  AI_DOCUMENT_SUMMARY("AI_DOCUMENT_SUMMARY", 202501,
  AiDocumentSummaryStepEnum.values()); // 新增

  步骤4: 更新限制配置

  // 在Constant.java中添加新的限制配置
  public static final LimitConstants AI_DOCUMENT_SUMMARY_LIMIT = new
  LimitConstants("analytics_ai_document_summary_basic_limit");

  // 更新限制映射
  public static final Map<AiTaskTypeEnum, LimitConstants> AI_TASK_TYPE_LIMIT_MAP =
  Map.of(
          AiTaskTypeEnum.AI_TRANSLATION, AI_TRANSLATION_LIMIT,
          AiTaskTypeEnum.AI_PATENT_DISCLOSURE, AI_PATENT_DISCLOSURE_LIMIT,
          AiTaskTypeEnum.AI_NOVELTY_SEARCH, AI_NOVELTY_SEARCH_LIMIT,
          AiTaskTypeEnum.AI_FTO_SEARCH, AI_FTO_SEARCH_LIMIT,
          AiTaskTypeEnum.AI_DOCUMENT_SUMMARY, AI_DOCUMENT_SUMMARY_LIMIT // 新增
  );

  步骤5: 创建DTO类

  // 文件: DocumentSummaryRequestDTO.java
  package com.patsnap.drafting.request.aidocumentsummary;

  import io.swagger.annotations.ApiModel;
  import io.swagger.annotations.ApiModelProperty;
  import lombok.Data;

  import javax.validation.constraints.NotBlank;
  import javax.validation.constraints.NotNull;
  import javax.validation.constraints.Max;
  import javax.validation.constraints.Min;

  @Data
  @ApiModel("文档总结请求DTO")
  public class DocumentSummaryRequestDTO {

      @ApiModelProperty("任务ID")
      @NotBlank(message = "任务ID不能为空")
      private String taskId;

      @ApiModelProperty("文档内容")
      @NotBlank(message = "文档内容不能为空")
      private String documentContent;

      @ApiModelProperty("总结长度配置(字数)")
      @NotNull(message = "总结长度不能为空")
      @Min(value = 100, message = "总结长度不能少于100字")
      @Max(value = 2000, message = "总结长度不能超过2000字")
      private Integer summaryLength;

      @ApiModelProperty("总结类型: BRIEF-简要总结, DETAILED-详细总结, 
  BULLET_POINTS-要点总结")
      private String summaryType = "BRIEF";
  }

  // 文件: DocumentSummaryResponseDTO.java
  package com.patsnap.drafting.response.aidocumentsummary;

  import io.swagger.annotations.ApiModel;
  import io.swagger.annotations.ApiModelProperty;
  import lombok.Data;

  import java.util.List;

  @Data
  @ApiModel("文档总结响应DTO")
  public class DocumentSummaryResponseDTO {

      @ApiModelProperty("总结结果")
      private String summaryResult;

      @ApiModelProperty("关键要点")
      private List<String> keyPoints;

      @ApiModelProperty("文档结构信息")
      private DocumentStructureInfo structureInfo;

      @Data
      public static class DocumentStructureInfo {
          private Integer paragraphCount;
          private Integer wordCount;
          private String documentType;
          private List<String> mainSections;
      }
  }

  步骤6: 创建Manager层

  // 文件: AiDocumentSummaryManager.java
  package com.patsnap.drafting.manager.aidocumentsummary;

  import com.patsnap.drafting.annotation.CreditCheckLimit;
  import com.patsnap.drafting.annotation.DistributedLock;
  import com.patsnap.drafting.client.OpenAiClient;
  import com.patsnap.drafting.enums.task.AiTaskContentTypeEnum;
  import com.patsnap.drafting.enums.task.AsyncTaskStatusEnum;
  import com.patsnap.drafting.manager.AbstractGenerateStreamingContent;
  import com.patsnap.drafting.manager.aitask.AiTaskManager;
  import com.patsnap.drafting.model.GptResponseDTO;
  import com.patsnap.drafting.request.GenerateContentRequestDTO;
  import com.patsnap.drafting.request.aidocumentsummary.DocumentSummaryRequestDTO;
  import com.patsnap.drafting.response.aidocumentsummary.DocumentSummaryResponseDTO;
  import com.patsnap.drafting.response.CommonResponse;
  import lombok.RequiredArgsConstructor;
  import lombok.extern.slf4j.Slf4j;
  import org.springframework.stereotype.Service;
  import reactor.core.publisher.Flux;

  import java.util.Arrays;
  import java.util.HashMap;
  import java.util.List;
  import java.util.Map;

  @Slf4j
  @Service
  @RequiredArgsConstructor
  public class AiDocumentSummaryManager extends AbstractGenerateStreamingContent<String>
   {

      private final AiTaskManager aiTaskManager;
      private final OpenAiClient openAiClient;
      private final DocumentSummaryPromptBuilder promptBuilder;

      /**
       * 同步处理文档总结
       */
      @CreditCheckLimit(contentType = AiTaskContentTypeEnum.DOCUMENT_SUMMARY_RESULT)
      public Flux<CommonResponse<GptResponseDTO<String>>> processDocumentSummary(
              GenerateContentRequestDTO request) {
          log.info("开始处理文档总结任务, taskId: {}", request.getTaskId());
          return doGenerate(request);
      }

      /**
       * 异步处理文档总结
       */
      @DistributedLock(key = "#taskId")
      public void processDocumentSummaryAsync(String taskId) {
          log.info("开始异步处理文档总结任务, taskId: {}", taskId);

          try {
              // 1. 获取任务配置
              String documentContent = aiTaskManager.getTaskContent(taskId,
                  AiTaskContentTypeEnum.DOCUMENT_CONTENT);
              Integer summaryLength = aiTaskManager.getTaskContent(taskId,
                  AiTaskContentTypeEnum.SUMMARY_LENGTH_CONFIG);

              // 2. 构建请求
              DocumentSummaryRequestDTO request = new DocumentSummaryRequestDTO();
              request.setTaskId(taskId);
              request.setDocumentContent(documentContent);
              request.setSummaryLength(summaryLength);

              // 3. 分步处理
              processKeyPointsExtraction(taskId, request);
              processDocumentAnalysis(taskId, request);
              processSummaryGeneration(taskId, request);

              // 4. 更新任务状态
              aiTaskManager.updateTaskStatus(taskId, AsyncTaskStatusEnum.Complete);

          } catch (Exception e) {
              log.error("文档总结任务处理失败, taskId: {}", taskId, e);
              aiTaskManager.updateTaskStatus(taskId, AsyncTaskStatusEnum.Failed);
              throw e;
          }
      }

      /**
       * 关键要点提取
       */
      private void processKeyPointsExtraction(String taskId, DocumentSummaryRequestDTO 
  request) {
          String keyPointsPrompt =
  promptBuilder.buildKeyPointsExtractionPrompt(request);
          String keyPointsResult = callOpenAI(keyPointsPrompt);

          List<String> keyPoints = parseKeyPoints(keyPointsResult);
          aiTaskManager.updateTaskContent(taskId,
              AiTaskContentTypeEnum.KEY_POINTS_EXTRACTION, keyPoints);
      }

      /**
       * 文档结构分析
       */
      private void processDocumentAnalysis(String taskId, DocumentSummaryRequestDTO 
  request) {
          String analysisPrompt = promptBuilder.buildDocumentAnalysisPrompt(request);
          String analysisResult = callOpenAI(analysisPrompt);

          Map<String, Object> structureInfo = parseDocumentStructure(analysisResult);
          aiTaskManager.updateTaskContent(taskId,
              AiTaskContentTypeEnum.DOCUMENT_STRUCTURE_ANALYSIS, structureInfo);
      }

      /**
       * 总结生成
       */
      private void processSummaryGeneration(String taskId, DocumentSummaryRequestDTO 
  request) {
          // 获取之前分析的结果
          List<String> keyPoints = aiTaskManager.getTaskContent(taskId,
              AiTaskContentTypeEnum.KEY_POINTS_EXTRACTION);

          String summaryPrompt = promptBuilder.buildSummaryGenerationPrompt(request,
  keyPoints);
          String summaryResult = callOpenAI(summaryPrompt);

          aiTaskManager.updateTaskContent(taskId,
              AiTaskContentTypeEnum.DOCUMENT_SUMMARY_RESULT, summaryResult);
      }

      @Override
      protected StreamingModelBO getStreamingModelBO(GenerateContentRequestDTO request) 
  {
          // 从请求中提取文档内容和配置
          DocumentSummaryRequestDTO summaryRequest = extractSummaryRequest(request);
          String prompt = promptBuilder.buildStreamingSummaryPrompt(summaryRequest);

          return StreamingModelBO.builder()
              .prompt(prompt)
              .model("gpt-4")
              .temperature(0.3)
              .maxTokens(summaryRequest.getSummaryLength() * 2)
              .build();
      }

      // 辅助方法
      private String callOpenAI(String prompt) {
          // OpenAI调用逻辑
          return openAiClient.chat(buildChatRequest(prompt)).getContent();
      }

      private List<String> parseKeyPoints(String keyPointsResult) {
          // 解析关键要点
          return Arrays.asList(keyPointsResult.split("\n"));
      }

      private Map<String, Object> parseDocumentStructure(String analysisResult) {
          // 解析文档结构
          Map<String, Object> structure = new HashMap<>();
          // 实现解析逻辑
          return structure;
      }
  }

  步骤7: 创建Prompt Builder

  // 文件: DocumentSummaryPromptBuilder.java
  package com.patsnap.drafting.manager.aidocumentsummary;

  import com.patsnap.drafting.request.aidocumentsummary.DocumentSummaryRequestDTO;
  import org.springframework.stereotype.Component;

  import java.util.List;

  @Component
  public class DocumentSummaryPromptBuilder {

      public String buildKeyPointsExtractionPrompt(DocumentSummaryRequestDTO request) {
          return String.format("""
              请分析以下文档内容，提取关键要点：

              文档内容：
              %s

              请按以下要求提取要点：
              1. 提取3-5个核心要点
              2. 每个要点用一句话概括
              3. 按重要性排序
              4. 每行一个要点，以"-"开头

              要点提取：
              """, request.getDocumentContent());
      }

      public String buildDocumentAnalysisPrompt(DocumentSummaryRequestDTO request) {
          return String.format("""
              请分析以下文档的结构信息：

              文档内容：
              %s

              请分析并返回JSON格式的结构信息，包含：
              - paragraphCount: 段落数量
              - wordCount: 字数统计
              - documentType: 文档类型（技术文档/商业文档/学术论文等）
              - mainSections: 主要章节列表

              结构分析：
              """, request.getDocumentContent());
      }

      public String buildSummaryGenerationPrompt(DocumentSummaryRequestDTO request,
  List<String> keyPoints) {
          return String.format("""
              基于以下文档内容和关键要点，生成%d字左右的总结：

              文档内容：
              %s

              关键要点：
              %s

              总结要求：
              1. 字数控制在%d字左右
              2. 总结类型：%s
              3. 语言简洁准确
              4. 涵盖主要内容

              文档总结：
              """,
              request.getSummaryLength(),
              request.getDocumentContent(),
              String.join("\n", keyPoints),
              request.getSummaryLength(),
              request.getSummaryType());
      }

      public String buildStreamingSummaryPrompt(DocumentSummaryRequestDTO request) {
          return String.format("""
              请对以下文档进行流式总结，字数控制在%d字左右：

              %s

              请按段落逐一输出总结内容。
              """, request.getSummaryLength(), request.getDocumentContent());
      }
  }

  步骤8: 创建Controller层

  // 文件: AiDocumentSummaryController.java
  package com.patsnap.drafting.controller;

  import com.patsnap.drafting.annotation.FluxConvert;
  import com.patsnap.drafting.manager.aidocumentsummary.AiDocumentSummaryManager;
  import com.patsnap.drafting.manager.aitask.AiTaskManager;
  import com.patsnap.drafting.request.GenerateContentRequestDTO;
  import com.patsnap.drafting.request.aidocumentsummary.DocumentSummaryRequestDTO;
  import com.patsnap.drafting.request.aitask.AiTaskReqDTO;
  import com.patsnap.drafting.response.CommonResponse;
  import com.patsnap.drafting.response.aidocumentsummary.DocumentSummaryResponseDTO;
  import io.swagger.annotations.Api;
  import io.swagger.annotations.ApiOperation;
  import lombok.RequiredArgsConstructor;
  import lombok.extern.slf4j.Slf4j;
  import org.springframework.http.MediaType;
  import org.springframework.web.bind.annotation.*;
  import reactor.core.publisher.Flux;

  import javax.validation.Valid;

  @Slf4j
  @RestController
  @RequestMapping("/ai/document-summary")
  @RequiredArgsConstructor
  @Api(tags = "AI文档总结")
  public class AiDocumentSummaryController {

      private final AiDocumentSummaryManager documentSummaryManager;
      private final AiTaskManager aiTaskManager;

      @ApiOperation("同步文档总结")
      @PostMapping(value = "/process/sync", produces = 
  MediaType.TEXT_EVENT_STREAM_VALUE)
      @FluxConvert
      public Flux<String> processSummarySync(@Valid @RequestBody 
  GenerateContentRequestDTO request) {
          log.info("接收到同步文档总结请求, taskId: {}", request.getTaskId());
          return documentSummaryManager.processDocumentSummary(request);
      }

      @ApiOperation("异步文档总结任务提交")
      @PostMapping("/process/async")
      public CommonResponse<String> processSummaryAsync(@Valid @RequestBody 
  DocumentSummaryRequestDTO request) {
          log.info("接收到异步文档总结请求, taskId: {}", request.getTaskId());

          try {
              // 保存任务配置
              aiTaskManager.updateTaskContent(request.getTaskId(),
                  AiTaskContentTypeEnum.DOCUMENT_CONTENT, request.getDocumentContent());
              aiTaskManager.updateTaskContent(request.getTaskId(),
                  AiTaskContentTypeEnum.SUMMARY_LENGTH_CONFIG,
  request.getSummaryLength());

              // 提交异步任务
              documentSummaryManager.submitDocumentSummaryTask(request);

              return CommonResponse.success("文档总结任务已提交");

          } catch (Exception e) {
              log.error("提交文档总结任务失败, taskId: {}", request.getTaskId(), e);
              return CommonResponse.failure("任务提交失败: " + e.getMessage());
          }
      }

      @ApiOperation("获取文档总结结果")
      @GetMapping("/{taskId}/result")
      public CommonResponse<DocumentSummaryResponseDTO> getSummaryResult(@PathVariable 
  String taskId) {
          log.info("获取文档总结结果, taskId: {}", taskId);

          try {
              DocumentSummaryResponseDTO result =
  documentSummaryManager.getTaskResult(taskId);
              return CommonResponse.success(result);

          } catch (Exception e) {
              log.error("获取文档总结结果失败, taskId: {}", taskId, e);
              return CommonResponse.failure("获取结果失败: " + e.getMessage());
          }
      }

      @ApiOperation("获取任务进度")
      @GetMapping("/{taskId}/progress")
      public CommonResponse<Map<String, Object>> getTaskProgress(@PathVariable String
  taskId) {
          log.info("获取任务进度, taskId: {}", taskId);

          try {
              Map<String, Object> progress =
  documentSummaryManager.getTaskProgress(taskId);
              return CommonResponse.success(progress);

          } catch (Exception e) {
              log.error("获取任务进度失败, taskId: {}", taskId, e);
              return CommonResponse.failure("获取进度失败: " + e.getMessage());
          }
      }
  }


  以"AI文档总结"为例，提供了完整的：
  - 枚举定义: 步骤枚举、内容类型、任务类型
  - 业务实现: Manager、Prompt Builder、DTO类
  - 接口层: Controller with Swagger注解
  - 配置文件: Spring配置、属性配置
  - 测试代码: 单元测试、集成测试

  🎯 关键技术点

  - 响应式编程: WebFlux + Flux流式处理
  - 分布式系统: Redis分布式锁 + SQS消息队列
  - AOP切面: 积分控制、缓存管理、分布式锁
  - 版本兼容: dataVersion字段实现平滑升级
  - 异常处理: 完善的错误处理和恢复机制

---
description: 根据技术方案生成代码
globs: 
alwaysApply: false
---
# 目标
请你按照设计好的方案，生成代码。

# 背景知识
为了帮助你更好的生成代码，我已为你提供：
（1）项目代码
（2）需求文档：.cursor/prd/XXX.md（上下文@文件的方式给到也可以）
（3）技术方案：docs/XXX.md（上下文@文件的方式给到也可以）
（4）项目理解文档:.cursor/docs/项目梳理文档.md

# 核心任务
## 1. 文档分析与理解阶段  
在动手编写代码前完成以下分析：  
- 需求匹配度检查：  
  - 深入理解需求文档和方案设计文档，确认《技术方案》与《需求文档》在功能点、输入输出、异常场景上的完全一致性。  
  - 若发现矛盾请立即标记并提交备注。  
- 代码架构理解：  
  - 深入理解项目梳理文档和现有代码库的分层结构，确定新功能的插入位置。  
  - 列出可复用的工具类、异常处理机制和公共接口（如`utils.py`、`ErrorCode`枚举类）。  

## 2. 代码生成阶段
如果你已明确需求和技术方案，请你完成代码编写工作。

# 要求
1. 你必须遵循以下核心原则：
（1）你生成的代码必须参考当前项目的代码风格。
（2）如项目已有可用方法，必须考虑复用、或在现有方法上扩展、或进行方法重载，保证最小粒度改动，减少重复代码。
2. 你生成的代码必须符合《Java统一开发编程规范》中定义的规范。

# 输出
请你生成代码，并放到代码库的合适位置。

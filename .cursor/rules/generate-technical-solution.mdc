---
description: 根据需求产出技术方案
globs: 
alwaysApply: false
---
# 目标

请你根据需求文档，生成技术方案。注意你只需要输出详细的技术方案文档，现阶段不需改动代码。（此时需求文档已经以文档的形式放到了我们的项目中）

# 背景知识

为了帮助你更好的生成技术方案，我已为你提供：
（1）项目代码
（2）需求文档：.cursor/prd/XXX.md（上下文@文件的方式给到也可以）
（3）项目理解文档: .cursor/docs/项目梳理文档.md

# 核心任务

## 1. 文档分析与理解阶段

在完成方案设计前完成以下分析：

- 详细理解需求：
  - 请确认你深刻理解了 需求文档 中提到的所有需求描述、功能改动。
  - 若有不理解点或发现矛盾请立即标记并提交备注。
- 代码架构理解：
  - 深入理解项目梳理文档和现有代码库的分层结构，确定新功能的插入位置。
  - 列出可复用的工具类、异常处理机制和公共接口（如`utils.py`、`ErrorCode`枚举类）。

## 2. 方案设计阶段

请你根据需求进行详细的方案设计，并将生成的技术方案放置到项目docs目录下。该阶段无需生成代码。

# 要求

1. 你生成的技术方案必须严格按照项目中的 .cursor/docs/技术方案详细设计.md 来生成，并符合技术方案设计文档模板。

# 输出

请你输出技术方案，并将生成的技术方案放到项目的合适位置，无需生成代码。


---
description: 生成 commit message
globs: 
alwaysApply: false
---
使用中文语言写一个简明、清晰、信息丰富的提交信息，提交信息的格式必须是下面这种形式规范，并且以纯文本形式提供回答，回答的内容不需要包含在代码块中

<type>(<scope>): <subject>

<body>
其中各种形式规范的定义如下：

提交类型（type）：描述提交的目的或类型，一般使用下面几种常见类型之一：
feat: 引入新功能或增强功能。
fix: 解决和修复错误或问题。
chore: 对非用户界面元素进行更改。
refactor: 重新组织或优化代码而不改变行为。
ci: 调整CI/CD流程。
test: 增强或修改测试套件。
docs: 更新或修改文档。
revert: 回滚到以前的状态。
范围（scope）：当前本地git分支的名称
主题（subject）：简明扼要地描述变更的内容，一般限制在50个字符以内
主体（body）：详细描述变更的内容，可以使用多行
例如,一个示例返回如下所示：

feat(feat-haowei): 修改了用户数据处理类
新增了…功能
简单重构了…工具类

---
description: AI 大模型传递图片信息的规则
globs: 
alwaysApply: false
---
# AI大模型图片传递机制指南

本项目中AI大模型处理图片的完整传递链路和实现机制。

## 核心传递流程

### 1. 图片数据来源
图片数据主要来源包括：
- **URL图片**: 通过HTTP/HTTPS下载的网络图片
- **Base64编码**: 直接传递的Base64编码图片数据
- **S3存储**: 通过S3签名URL获取的图片
- **用户上传**: 通过文件上传接口上传的图片

### 2. 图片处理与转换

#### 图片下载与转换
参考 [ImageBase64Util.java](mdc:telecom-core/src/main/java/com/patsnap/drafting/util/ImageBase64Util.java):
```java
// 从URL下载图片并转换为Base64
public static String getBase64FromUrl(String imageUrl)
```

#### 图片合并处理
参考 [ImageMergeUtil.java](mdc:telecom-core/src/main/java/com/patsnap/drafting/util/ImageMergeUtil.java):
- 支持多张图片合并为一张图片
- 支持网格布局排列和序号标识
- 自动尺寸限制（最大8000x8000像素）
- 支持图片压缩（JPEG质量压缩）
- 内存管理和堆外内存主动释放

### 3. 多模态内容构建

#### 核心处理类
参考 [ImagePromptUtils.java](mdc:telecom-core/src/main/java/com/patsnap/drafting/util/ImagePromptUtils.java):

**提示词解析机制**:
```java
// 使用 <image>URL</image> 或 <image>base64Data</image> 格式标记图片
Pattern imagePattern = Pattern.compile("<image>(.*?)</image>");
```

**内容类型识别**:
```java
// 自动识别URL和Base64格式
private boolean isBase64Image(String imageUrl) {
    return !imageUrl.startsWith("http://") && !imageUrl.startsWith("https://");
}
```

#### 消息结构封装
参考 [ContentDTO.java](mdc:telecom-core/src/main/java/com/patsnap/drafting/client/model/ContentDTO.java) 和 [MessageDTO.java](mdc:telecom-core/src/main/java/com/patsnap/drafting/client/model/MessageDTO.java):

**文本内容**:
```java
ContentDTO.of(MessageDTO.ContentType.TEXT.getValue(), text)
```

**图片内容**:
```java
ContentDTO.of(
    MessageDTO.ContentType.IMAGE.getValue(),
    base64Data,
    mediaType,  // 如 "image/png", "image/jpeg"
    "base64"
)
```

### 4. AI模型请求构建

#### 模型请求封装
参考 [ModelCompletionDTO.java](mdc:telecom-core/src/main/java/com/patsnap/drafting/client/model/ModelCompletionDTO.java):
```java
ModelCompletionDTO modelCompletion = new ModelCompletionDTO();
modelCompletion.setModel(model.getName());
modelCompletion.setMaxTokens(model.getLimit());
modelCompletion.setTemperature(0.1);

// 构建包含文本和图片的消息内容
List<ContentDTO> contents = parsePromptContent(prompt);
List<MessageDTO> messages = new ArrayList<>();
messages.add(new MessageDTO(MessageDTO.Role.USER.getValue(), contents));
modelCompletion.setMessages(messages);
```

### 5. 图片数量限制

#### Claude模型限制
参考 [ImageSearchToolManager.java](mdc:telecom-core/src/main/java/com/patsnap/drafting/manager/imagesearch/ImageSearchToolManager.java):
```java
// 只取前19个分区，目前base64形式传递图片，Claude模型最多支持20张图片
if (imagePatentPartitionList.size() > 19) {
    imagePatentPartitionList = imagePatentPartitionList.subList(0, 19);
}
```

### 6. 实际应用场景

#### 图像相似度搜索
- 目标图片: 通过URL传递
- 候选图片: 多张图片合并为Base64传递
- 提示词构建: 使用特殊标记格式

#### 专利图像处理
参考 [DrawingNarratorRequest.java](mdc:telecom-api/src/main/java/com/patsnap/drafting/client/model/DrawingNarratorRequest.java):
```java
// 附图列表，列表元素是base64编码的图片
@JsonProperty("images")
private List<String> images;
```

#### 文档导出处理
参考 [AiSpecificationExportHandler.java](mdc:telecom-core/src/main/java/com/patsnap/drafting/transfer/export/handler/impl/AiSpecificationExportHandler.java):
- 图片Base64数据清理
- 图片尺寸计算和缩放
- Word文档中的图片插入

### 7. 最佳实践

#### 图片格式处理
- **优先使用PNG格式**: 保证图片质量
- **JPEG压缩**: 当需要减小文件大小时使用
- **Base64清理**: 移除Data URL前缀 (`data:image/png;base64,`)

#### 内存管理
- 主动释放BufferedImage的堆外内存
- 使用try-with-resources确保资源正确关闭
- 限制同时处理的图片数量

#### 错误处理
- 图片下载失败时插入空白占位符
- 无效Base64数据时显示错误提示
- 网络超时设置和重试机制

### 8. 关键配置参数

```java
// 图片尺寸限制
public static final int MAX_IMAGE_SIZE = 8000;  // 最大8000像素

// 文件大小限制
private static final int MAX_IMAGE_SIZE = 10 * 1024 * 1024; // 最大10MB

// 超时设置
private static final int CONNECT_TIMEOUT = 10000; // 10秒连接超时
private static final int READ_TIMEOUT = 30000;    // 30秒读取超时

// 图片数量限制
public static final int IMAGE_MAX_SIZE = 20;  // Claude模型最多20张图片
```

### 9. 调试和监控

#### 日志记录
- 图片下载过程记录
- Base64转换状态记录
- 内存使用情况记录
- 模型调用参数记录

#### 性能监控
- 图片处理耗时统计
- 内存使用量监控
- 网络请求成功率统计

这套机制确保了AI大模型能够高效、稳定地处理各种格式的图片数据，同时保证了系统的性能和可靠性。

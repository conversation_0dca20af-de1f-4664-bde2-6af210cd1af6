---
description: 
globs: 
alwaysApply: true
---

请用中文回答。接收到需求后，先列出计划，有不明白的问我。
# AI 编码助手规范

## 项目概述

本项目是一个基于 AI 的专利撰写和翻译服务，提供专利文档的智能撰写、翻译、分析等功能。项目采用 Java Spring Boot 框架开发，使用响应式编程模型（WebFlux）。

## 技术栈

- Java 17
- Spring Boot
- Spring WebFlux
- MyBatis Plus
- Redis
- PostgreSQL
- Maven
- Retrofit (用于API调用)

## 项目架构

项目采用多模块 Maven 结构，主要包含以下模块：
- telecom-api: 定义 DTO 和接口
- telecom-core: 核心业务逻辑
- telecom-service: Web 服务和控制器
- telecom-parent: 依赖管理

## 编码规范

### 1. 包结构规范

遵循以下包结构：
```
com.patsnap.drafting
├── annotation    // 自定义注解
├── aspect        // AOP 切面
├── client        // 外部服务客户端
│   └── api       // API 接口定义
│   └── model     // API 请求/响应模型
├── config        // 配置类
├── constants     // 常量定义
├── controller    // REST 控制器
├── enums         // 枚举类型
├── exception     // 异常处理
├── handler       // 处理器
├── listener      // 事件监听器
├── manager       // 业务管理类
├── model         // 业务模型
├── repository    // 数据访问层
├── request       // 请求 DTO
├── response      // 响应 DTO
├── startup       // 启动相关
├── transfer      // 数据转换
└── util          // 工具类
```

### 2. 命名规范

- **类名**：使用 PascalCase（首字母大写驼峰式）
  - Controller 类以 `Controller` 结尾
  - Service 类以 `Service` 结尾
  - Manager 类以 `Manager` 结尾
  - Repository 类以 `Repository` 结尾
  - Client 类以 `Client` 结尾
  - API 接口类以 `Api` 结尾
  - 配置类以 `Config` 结尾
  - 常量类以 `Constants` 结尾
  - 枚举类以 `Enum` 结尾
  - 异常类以 `Exception` 结尾
  - DTO 类以 `DTO` 或具体用途（如 `RequestDTO`、`ResponseDTO`）结尾

- **方法名**：使用 camelCase（首字母小写驼峰式）
  - 获取数据的方法以 `get` 开头
  - 保存数据的方法以 `save` 开头
  - 更新数据的方法以 `update` 开头
  - 删除数据的方法以 `delete` 开头
  - 查询数据的方法以 `query` 或 `find` 开头
  - 检查条件的方法以 `is` 或 `has` 开头
  - 转换数据的方法以 `convert` 或 `to` 开头

- **变量名**：使用 camelCase（首字母小写驼峰式）
  - 常量使用全大写，单词间用下划线分隔（如 `MAX_COUNT`）

### 3. 代码风格规范

- 使用 Lombok 简化代码，减少样板代码
  - 使用 `@Data` 注解生成 getter/setter
  - 使用 `@Builder` 注解实现建造者模式
  - 使用 `@RequiredArgsConstructor` 注解实现构造器注入
  - 使用 `@Slf4j` 注解添加日志对象

- 使用 Swagger 注解进行 API 文档化
  - 类上使用 `@Api(tags = "xxx")` 注解
  - 方法上使用 `@ApiOperation("xxx")` 注解
  - 参数上使用 `@ApiParam` 注解
  - 请求类的参数上，添加 @ApiModelProperty 与 @JsonProperty("蛇形参数名称") 注解

- 使用 Validation 注解进行参数校验
  - 使用 `@Valid` 注解标记需要校验的对象
  - 使用 `@NotNull`、`@NotEmpty`、`@NotBlank` 等注解进行非空校验
  - 使用 `@Size`、`@Min`、`@Max` 等注解进行大小校验

- 遵循 RESTful API 设计规范
  - 使用 HTTP 方法表示操作类型（GET、POST、PUT、DELETE）
  - 使用 URL 表示资源
  - 使用 HTTP 状态码表示操作结果
  - 使用 CommonResponse 封装统一响应格式

- 使用响应式编程模型处理异步请求
  - 使用 `Mono` 表示 0-1 个结果的异步序列
  - 使用 `Flux` 表示 0-N 个结果的异步序列

### 4. API 客户端规范

- 使用 Retrofit 进行 API 调用
  - 在 `RetrofitConfig` 类中配置 Retrofit 实例
  - 在 `com.patsnap.drafting.client.api` 包中定义 API 接口
  - 在 `com.patsnap.drafting.client` 包中实现 API 客户端
  - 使用 `@Bean` 注解将 API 接口注册为 Spring Bean

- API 接口定义规范
  - 使用 `@POST`、`@GET`、`@PUT`、`@DELETE` 等注解标记 HTTP 方法
  - 使用 `@Body` 注解标记请求体
  - 使用 `@Query` 注解标记查询参数
  - 使用 `@Path` 注解标记路径参数
  - 使用 `@Header` 注解标记请求头
  - 返回类型使用 `Call<T>` 包装

- API 客户端实现规范
  - 使用 `@Component` 注解标记为 Spring 组件
  - 使用 `@RequiredArgsConstructor` 注解实现构造器注入
  - 使用 `@Slf4j` 注解添加日志对象
  - 使用 try-catch 捕获异常并转换为业务异常
  - 记录请求和响应日志

### 5. 异常处理规范

- 使用自定义异常类表示业务异常
  - 继承 `RuntimeException` 或 `Exception`
  - 包含错误码和错误信息
  - 使用枚举类定义错误码和错误信息

- 使用全局异常处理器处理异常
  - 使用 `@ControllerAdvice` 注解标记全局异常处理器
  - 使用 `@ExceptionHandler` 注解标记异常处理方法
  - 返回统一的错误响应格式

### 6. 日志规范

- 使用 SLF4J 进行日志记录
  - 使用 `@Slf4j` 注解添加日志对象
  - 使用不同级别的日志方法（error、warn、info、debug、trace）
  - 使用占位符 `{}` 进行参数替换
  - 记录关键业务操作和异常信息

- 日志内容规范
  - 记录方法入参和出参
  - 记录异常堆栈信息
  - 记录关键业务操作
  - 记录性能指标

### 7. 配置规范

- 使用 `application.properties` 或 `application.yml` 进行配置
  - 使用 `@Value` 注解注入配置项
  - 使用 `@ConfigurationProperties` 注解绑定配置项到 Java 对象
  - 使用 Spring Profile 区分不同环境的配置

- 配置项命名规范
  - 使用小写字母和连字符（如 `configs.com.patsnap.open-api.url`）
  - 使用前缀区分不同模块的配置（如 `configs.com.patsnap.xxx`）

### 8. 单元测试规范

- 使用 JUnit 5 进行单元测试
  - 使用 `@Test` 注解标记测试方法
  - 使用 `@BeforeEach` 和 `@AfterEach` 注解标记前置和后置方法
  - 使用 `@DisplayName` 注解描述测试方法的功能

- 使用 Mockito 进行模拟
  - 使用 `@Mock` 注解模拟依赖对象
  - 使用 `@InjectMocks` 注解注入被测对象
  - 使用 `when().thenReturn()` 方法模拟方法调用
  - 使用 `verify()` 方法验证方法调用

## 业务模块规范

### 1. AI 撰写模块

- 专利说明书生成
- 专利权利要求书生成
- 专利公开内容生成
- 敏感词检查
- 专利引用管理

### 2. AI 翻译模块

- 文本语言检测
- 专利文档翻译
- 自定义术语表管理
- 异步翻译任务处理
- 翻译结果导出

### 3. AI 规范模块

- 专利分类
- 专利内容规范化
- 实施例管理

### 4. 历史记录模块

- 历史记录查询
- 历史内容详情

### 5. 积分系统模块

- 积分查询
- 积分消费
- 积分变更

## 数据流规范

1. 客户端请求 -> Controller
2. Controller -> Manager
3. Manager -> Service
4. Service -> Repository
5. Repository -> 数据库/外部服务

## AI 编码助手注意事项

1. 遵循项目的包结构和命名规范
2. 使用 Lombok 简化代码，减少样板代码
3. 使用 Swagger 注解进行 API 文档化
4. 使用 Validation 注解进行参数校验
5. 遵循 RESTful API 设计规范
6. 使用响应式编程模型处理异步请求
7. 使用 Retrofit 进行 API 调用
8. 使用自定义异常类表示业务异常
9. 使用 SLF4J 进行日志记录
10. 使用 Spring Profile 区分不同环境的配置
11. 编写单元测试验证代码功能
12. 注意敏感词检查和积分系统的安全措施
13. 考虑异步任务处理的状态管理
14. 注意专业术语的准确性
15. 遵循项目的数据流规范
16. 代码长度: 单个方法不超过50行，单个类不超过 600 行
17. 优先使用 Java17 新特性
18. 使用合适的设计模式
19. 不要使用 Map<String,Object> 来定义对象

## 代码示例

### Controller 示例

```java
@Api(tags = "AI说明书")
@RestController
@RequestMapping("/ai-specification")
@RequiredArgsConstructor
public class AiSpecificationController {

    private final SpecificationManager specificationManager;

    @ApiOperation("语言检测")
    @PostMapping("/lang-detect")
    public CommonResponse<SpecificationLangCheckResDTO> langDetect(
            @Valid @NotNull @RequestBody SpecificationInitDTO specificationInitDTO) {
        return CommonResponse.<SpecificationLangCheckResDTO>builder()
                .withData(specificationManager.langDetect(specificationInitDTO)).build();
    }
}
```

### API 客户端示例

```java
@Slf4j
@Component
@RequiredArgsConstructor
public class OpenApiClient {

    private final OpenApi openApi;

    public ImageSimilarSearchResponse searchImageBySingle(ImageSimilarSearchRequest request) {
        try {
            Response<ImageSimilarSearchResponse> response = openApi.searchImageBySingle(request).execute();
            if (response.isSuccessful()) {
                log.info("searchImageBySingle success, response: {}", JSON.toJSONString(response.body()));
                return response.body();
            } else {
                log.warn("searchImageBySingle failed, code: {}, message: {}", response.code(), response.message());
                throw new BizException(CLIENT_NOT_AVAILABLE);
            }
        } catch (IOException e) {
            log.error("Failed to searchImageBySingle, request: {}", JSON.toJSONString(request), e);
            throw new BizException(CLIENT_NOT_AVAILABLE);
        }
    }
}
```

### API 接口定义示例

```java
public interface OpenApi {

    /**
     * 专利图像检索-单图
     */
    @POST("search/patent/image-single")
    Call<ImageSimilarSearchResponse> searchImageBySingle(@Body ImageSimilarSearchRequest request);

    /**
     * 专利图像检索-多图
     */
    @POST("search/patent/image-multiple")
    Call<ImageSimilarSearchResponse> searchImageByMultiple(@Body ImageMultipleSimilarSearchRequest request);
}
```

### Retrofit 配置示例

```java
@Configuration
public class RetrofitConfig {

    @Value("${configs.com.patsnap.open-api.url}")
    private String openApiUrl;

    @Bean
    public OpenApi initOpenAPI() {
        return getRetrofit(openApiUrl + "/", VersionId.V_1_0).create(OpenApi.class);
    }

    private Retrofit getRetrofit(String url, String version) {
        // 创建自定义拦截器以添加Header
        Interceptor headerInterceptor = chain -> {
            Request originalRequest = chain.request();
            Request newRequest = originalRequest.newBuilder()
                    .addHeader(UserIdHolder.X_USER_ID, Optional.ofNullable(UserIdHolder.get()).orElse(StringUtils.EMPTY))
                    .addHeader(X_API_VERSION, version)
                    .method(originalRequest.method(), originalRequest.body())
                    .build();
            return chain.proceed(newRequest);
        };
        
        // 创建OkHttpClient并设置拦截器
        OkHttpClient okHttpClient = new OkHttpClient.Builder()
                .addInterceptor(headerInterceptor)
                .connectTimeout(300, TimeUnit.SECONDS)
                .readTimeout(300, TimeUnit.SECONDS)
                .build();
        
        // 创建并返回Retrofit实例
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.setPropertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE);
        return new Retrofit.Builder()
                .baseUrl(url)
                .addConverterFactory(JacksonConverterFactory.create(objectMapper))
                .client(okHttpClient)
                .build();
    }
}
```

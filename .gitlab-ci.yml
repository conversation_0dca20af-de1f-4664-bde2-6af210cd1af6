---
variables:
  PRIVA<PERSON>_DTR_URL: "955466075186.dkr.ecr.cn-northwest-1.amazonaws.com.cn"
  BUILD_IMAGE: ${PRIVATE_DTR_URL}/ops-basic/java17-service:20231108
  TEAM: "analytics"
  PROJECT_TYPE: "s"
  PROJECT_NAME: "telecom"
  LOCAL_DTR_REPOSTIRY: "local-dtr.zhihuiya.com"
  HC_PORT: "8080"
  HC_HTTP_REQUEST_METHOD: "GET"
  HC_HTTP_REQUEST_PATH: "/telecom/health/check"
  HC_PER_SEC: "180"

stages:
  - build
  - Dev
  - QA
  - Stage
  - Prod

#################################INIT###########################################
init:
  tags:
    - aws
    - docker
  stage: build
  only:
    - /^develop.*$/
    - /^release.*$/
    - /^master$/
  script:
    - ops_deploy init

#################################BUILD###########################################
build_dev:
  stage: build
  tags:
    - aws
    - docker
  only:
    - /^develop.*$/
  image: ${BUILD_IMAGE}
  script:
    - export CI_COMMIT_REF_NAME=$(echo $CI_COMMIT_REF_NAME | sed 's/\//./g')
    - mvn clean install -P docker,!cq -Dmaven.test.skip=true -Ddb.migration.skip=true -Dapp.build.dtr=$PRIVATE_DTR_URL -Dapp.build.team=$TEAM -Dapp.build.number=$CI_PIPELINE_ID -Dapp.build.service=$PROJECT_TYPE-$TEAM-$PROJECT_NAME -Dci.commit.ref.name=$CI_COMMIT_REF_NAME

#################################BUILD###########################################
build_release:
  stage: build
  tags:
    - aws
    - docker
  only:
    - /^master$/
    - /^release.*$/
    - /^hotfix.*$/
  image: ${BUILD_IMAGE}
  script:
    - export CI_COMMIT_REF_NAME=$(echo $CI_COMMIT_REF_NAME | sed 's/\//./g')
    - mvn clean install -P docker,!cq -Dmaven.test.skip=true -Ddb.migration.skip=true -Dapp.build.dtr=$PRIVATE_DTR_URL -Dapp.build.team=$TEAM -Dapp.build.number=$CI_PIPELINE_ID -Dapp.build.service=$PROJECT_TYPE-$TEAM-$PROJECT_NAME -Dci.commit.ref.name=$CI_COMMIT_REF_NAME
#################################BUILD###########################################
build_sonar:
  stage: build
  tags:
    - aws
    - docker
  only:
    - /^master$/
    - /^release.*$/
    - /^hotfix.*$/
  image: ${BUILD_IMAGE}
  when: manual
  script:
    - export CI_COMMIT_REF_NAME=$(echo $CI_COMMIT_REF_NAME | sed 's/\//./g')
    - mvn clean install -P docker,!cq -Dmaven.test.skip=true -Ddb.migration.skip=true -Dapp.build.dtr=$PRIVATE_DTR_URL -Dapp.build.team=$TEAM -Dapp.build.number=$CI_PIPELINE_ID -Dapp.build.service=$PROJECT_TYPE-$TEAM-$PROJECT_NAME -Dci.commit.ref.name=$CI_COMMIT_REF_NAME
    - mvn sonar:sonar -Dapp.build.number=$CI_PIPELINE_ID -Dapp.build.reversion=$CI_COMMIT_SHA -Dsonar.host.url=http://sonarqube.patsnap.info -Dsonar.login=sqa_07866b6dfc137f9c6352f9e377877289b1ac9b82

#################################DEPLOY###########################################
deploy_to_dev:
  tags:
    - aws
    - docker
  stage: Dev
  only:
    - /^develop.*$/
  environment:
    name: Dev
  variables:
    ENV: ci
    REGION: cn-northwest-1
    GIT_STRATEGY: none
  script:
    - ops_deploy deploy

deploy_to_qa_tx:
  tags:
    - aws
    - docker
  stage: QA
  only:
    - /^master$/
    - /^release.*$/
  environment:
    name: QA
  variables:
    ENV: qa
    REGION: ap-beijing
    GIT_STRATEGY: none
  when: manual
  script:
    - ops_deploy deploy

deploy_to_stage:
  tags:
    - aws
    - docker
  stage: Stage
  only:
    - /^master$/
    - /^release.*$/
    - /^hotfix.*$/
  environment:
    name: Stage
  variables:
    ENV: stage
    REGION: ap-beijing
    GIT_STRATEGY: none
  when: manual
  script:
    - ops_deploy deploy

deploy_to_prod_cn_tx:
  tags:
    - aws
    - docker
  stage: Prod
  only:
    - /^master$/
    - /^release.*$/
    - /^hotfix.*$/
  environment:
    name: Tencent CN NX EKS Production
  variables:
    ENV: prod
    REGION: ap-beijing
    GIT_STRATEGY: none
    K8STYPE: eks
  when: manual
  script:
    #    - if [ $CI_COMMIT_REF_NAME = 'master' ];then
    #      curl -X PUT qa-station-service.patsnap.info/station/release/light-service -H "Content-Type:application/json" -d "{\"region\":\"CN\",\"service_id\":${CI_PROJECT_ID},\"status\":3}";
    #      fi
    - ops_deploy deploy
#    - if [ $CI_COMMIT_REF_NAME = 'master' ];then
#      curl -X PUT qa-station-service.patsnap.info/station/release/light-service -H "Content-Type:application/json" -d "{\"region\":\"CN\",\"service_id\":${CI_PROJECT_ID},\"status\":1}";
#      fi

deploy_to_aws_us:
  tags:
    - aws
    - docker
  stage: Prod
  only:
    - /^master$/
    - /^release.*$/
    - /^hotfix.*$/
  environment:
    name: Aws US Production
  variables:
    ENV: prod
    REGION: us-east-new
    GIT_STRATEGY: none
    K8STYPE: eks
  when: manual
  script:
    #    - if [ $CI_COMMIT_REF_NAME = 'master' ];then
    #      curl -X PUT qa-station-service.patsnap.info/station/release/light-service -H "Content-Type:application/json" -d "{\"region\":\"US\",\"service_id\":${CI_PROJECT_ID},\"status\":3}";
    #      fi
    - ops_deploy deploy
#    - if [ $CI_COMMIT_REF_NAME = 'master' ];then
#      curl -X PUT qa-station-service.patsnap.info/station/release/light-service -H "Content-Type:application/json" -d "{\"region\":\"US\",\"service_id\":${CI_PROJECT_ID},\"status\":1}";
#      fi

deploy_to_aws_eu:
  tags:
    - aws
    - docker
  stage: Prod
  only:
    - /^master$/
    - /^release.*$/
    - /^hotfix.*$/
  environment:
    name: Aws EU Production
  variables:
    ENV: prod
    REGION: eu-central-new
    GIT_STRATEGY: none
  when: manual
  script:
#    - if [ $CI_COMMIT_REF_NAME = 'master' ];then
#      curl -X PUT qa-station-service.patsnap.info/station/release/light-service -H "Content-Type:application/json" -d "{\"region\":\"EU\",\"service_id\":${CI_PROJECT_ID},\"status\":3}";
#      fi
    - ops_deploy deploy
#    - if [ $CI_COMMIT_REF_NAME = 'master' ];then
#      curl -X PUT qa-station-service.patsnap.info/station/release/light-service -H "Content-Type:application/json" -d "{\"region\":\"EU\",\"service_id\":${CI_PROJECT_ID},\"status\":1}";
#      fi
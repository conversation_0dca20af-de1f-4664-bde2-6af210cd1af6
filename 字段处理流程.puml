@startuml
participant DataFetchService
entity DistributeService
entity FieldProcessor
entity DataConvertAndExpand
participant PatentApiService


DataFetchService -> DistributeService: 多个字段

note left FieldProcessor: 每个Processor\n操作是并行的
DistributeService -> FieldProcessor: 分发1-提交多线程任务
activate FieldProcessor #DarkSalmon
FieldProcessor -> PatentApiService: 调用api
PatentApiService -> FieldProcessor: 返回数据
FieldProcessor -> DataConvertAndExpand: 字段转换和扩展
DataConvertAndExpand ->FieldProcessor:返回修饰和转换后的数据
deactivate FieldProcessor

DistributeService -> FieldProcessor: 分发2-提交多线程任务
activate FieldProcessor #DarkSalmon
FieldProcessor -> PatentApiService: 调用api
PatentApiService -> FieldProcessor: 返回数据
FieldProcessor -> DataConvertAndExpand: 字段转换和扩展
DataConvertAndExpand ->FieldProcessor:返回修饰和转换后的数据
deactivate FieldProcessor
DistributeService -> FieldProcessor: 分发3 -提交多线程任务
activate FieldProcessor #DarkSalmon
FieldProcessor -> PatentApiService: 调用api
PatentApiService -> FieldProcessor: 返回数据
FieldProcessor -> DataConvertAndExpand: 字段转换和扩展
DataConvertAndExpand ->FieldProcessor:返回修饰和转换后的数据
deactivate FieldProcessor

FieldProcessor -> DistributeService: 多字段数据合并
DistributeService -> DataFetchService: 返回完整数据
@enduml